package shoaa.opticalsmartreader.ui;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;

import shoaa.opticalsmartreader.R;

public class BarcodeDialog extends Dialog {

    DialogInterface dialogInterface;
    String text = "";
    MainActivity mainActivity;


    public BarcodeDialog(MainActivity mainActivity, DialogInterface dialogInterface) {
        super(mainActivity);
        this.mainActivity = mainActivity;
        this.dialogInterface = dialogInterface;
    }

    @Override
    public void show() {
        super.show();
        LinearLayout bedLcl = findViewById(R.id.mainLayout);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        mainActivity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int widthLcl = (int) (displayMetrics.widthPixels * 0.7f);
        LinearLayout.LayoutParams paramsLcl = (LinearLayout.LayoutParams)
                bedLcl.getLayoutParams();
        paramsLcl.width = widthLcl;
        paramsLcl.gravity = Gravity.CENTER;
        Window window = getWindow();
        bedLcl.setLayoutParams(paramsLcl);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.barcode_dialog);
        EditText etBarcode = findViewById(R.id.etBarcode);
        Button btnEnter = findViewById(R.id.btnEnter);
        Button btnCancel = findViewById(R.id.btnCancel);
        btnEnter.setEnabled(false);
        btnCancel.setOnClickListener(view -> {
            dialogInterface.onFinish("");
            dismiss();
        });
        etBarcode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                text = charSequence.toString().trim();
                btnEnter.setEnabled(text.length() >= 6);
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        btnEnter.setOnClickListener(view -> {
            dialogInterface.onFinish(text);
            dismiss();
        });
    }
}