<component name="libraryTable">
  <library name="Gradle: com.google.android.datatransport:transport-api:2.2.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e58274942b86622c21235e1d3d89e054/transformed/jetified-transport-api-2.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8a630ffab1b921c6459f1ef3fd22bd75/transformed/jetified-transport-api-2.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ce7080640d153d4d0fa32fc2457ff36d/transformed/jetified-transport-api-2.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/589b89038028de2710814c0c14d0b7f2/transformed/jetified-transport-api-2.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/bc2f5458ce195f7bd8e799e53f766281/transformed/jetified-transport-api-2.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/589b89038028de2710814c0c14d0b7f2/transformed/jetified-transport-api-2.2.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/589b89038028de2710814c0c14d0b7f2/transformed/jetified-transport-api-2.2.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.datatransport/transport-api/2.2.1/44ab63fd6744b961c97d388fef15c888f99b561a/transport-api-2.2.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>