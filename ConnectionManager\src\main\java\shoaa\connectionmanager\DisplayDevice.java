package shoaa.connectionmanager;

import android.Manifest;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import androidx.core.app.ActivityCompat;

import shoaa.connectionmanager.usbserial.UsbDeviceItem;

public class DisplayDevice {
    private final Object mDevice;
    private String name;
    private String address;

    public DisplayDevice(Context context, Object dev) {
        this.mDevice = dev;
        if (dev instanceof BluetoothDevice) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S || ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                this.name = "BT: " + ((BluetoothDevice) dev).getName();
                this.address = ((BluetoothDevice) dev).getAddress();
            }
        } else {
            this.name = "USB: " + ((UsbDeviceItem) dev).getDevice().getManufacturerName();
            this.address = ((UsbDeviceItem) dev).getDevice().getDeviceName();
        }
    }

    public Object getDevice() {
        return mDevice;
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }
}
