package com.esmc.protocol.algorithm;

import com.esmc.protocol.utils.HexUtils;

import java.security.MessageDigest;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/algorithm/MD5.class */
public class MD5 {
    public static final byte[] digest(byte[] content) {
        try {
            MessageDigest msgDigest = MessageDigest.getInstance("MD5");
            msgDigest.update(content);
            return msgDigest.digest();
        } catch (Exception e) {
            return null;
        }
    }

    public static final String digestAsString(byte[] content) {
        byte[] result = digest(content);
        if (null == result) {
            return "";
        }
        return HexUtils.toHex(result);
    }
}
