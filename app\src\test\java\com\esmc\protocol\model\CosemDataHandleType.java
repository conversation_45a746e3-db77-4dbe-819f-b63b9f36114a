package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/CosemDataHandleType.class */
public enum CosemDataHandleType {
    VISIBLE_STRING,
    HEX_STRING,
    BIT_STRING,
    DATE_TIME,
    DATE,
    TIME,
    DECIMAL,
    ENERGY_ACTIVE,
    POWER_ACTIVE,
    VOL<PERSON><PERSON>,
    CURRE<PERSON>,
    POWER_FACTOR,
    CURRENCY,
    ANGLE,
    FREQUENCY,
    TEMPERATURE,
    DURATION_SECOND,
    DURATION_MINUTE,
    DURATION_HOUR,
    PERCENT,
    TAMPER_STATUS,
    METER_STATUS
}
