package shoaa.opticalsmartreader.ui;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.method.ScrollingMovementMethod;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import shoaa.opticalsmartreader.R;

public class SyncDialog extends Dialog {

    DialogInterface dialogInterface;
    MainActivity mainActivity;
    TextView txtData;
    Button btnExit;


    public SyncDialog(MainActivity mainActivity, DialogInterface dialogInterface) {
        super(mainActivity);
        this.mainActivity = mainActivity;
        this.dialogInterface = dialogInterface;
    }

    @Override
    public void show() {
        super.show();
        LinearLayout bedLcl = findViewById(R.id.mainLayout);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        mainActivity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        LinearLayout.LayoutParams paramsLcl = (LinearLayout.LayoutParams)
                bedLcl.getLayoutParams();
        paramsLcl.width = (int) (displayMetrics.widthPixels * 0.8f);
        paramsLcl.gravity = Gravity.CENTER;
        Window window = getWindow();
        bedLcl.setLayoutParams(paramsLcl);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.sync_dialog);
        txtData = findViewById(R.id.txtData);
        txtData.setMovementMethod(new ScrollingMovementMethod());
        btnExit = findViewById(R.id.btnExit);
        btnExit.setVisibility(View.GONE);

        btnExit.setOnClickListener(view -> {
            if (dialogInterface != null)
                dialogInterface.onFinish("");
            dismiss();
        });
    }

    public void showBtn() {
        mainActivity.runOnUiThread(() -> btnExit.setVisibility(View.VISIBLE));
    }

    public void addLine(String line) {
        mainActivity.runOnUiThread(() -> {
            String lastLine = "";
            String oldData = txtData.getText().toString();
            String newData = "";

            if (!oldData.isEmpty()) {
                lastLine = oldData.split("\n")[oldData.split("\n").length - 1];
            }
            if (oldData.isEmpty()) {
                newData = line;
            } else {
                try {
                    if (lastLine.startsWith(line.substring(0, 10))) {
                        String d = "";
                        if (oldData.contains("\n"))
                            d = oldData.substring(0, oldData.lastIndexOf("\n"));
                        else
                            d = oldData;

                        if (!d.isEmpty())
                            newData = d + "\n" + line;
                        else
                            newData = line;
                    } else {
                        newData = oldData + "\n" + line;
                    }
                } catch (Exception e) {
                    oldData = txtData.getText().toString();
                    if (oldData.isEmpty())
                        newData = line;
                    else
                        newData = oldData + "\n" + line;
                }
            }
            txtData.setText(newData);
        });
    }
}