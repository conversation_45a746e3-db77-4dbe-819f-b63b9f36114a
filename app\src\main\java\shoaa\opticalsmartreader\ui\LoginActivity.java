package shoaa.opticalsmartreader.ui;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;

import shoaa.opticalsmartreader.BuildConfig;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.logic.login.LoginManager;
import shoaa.opticalsmartreader.models.AppUser;

public class LoginActivity extends AppCompatActivity {
    ProgressDialog progressDialog = null;
    AlertDialog alertDialog = null;
    AlertDialog.Builder builder = null;
    TextInputEditText et_UserName;
    TextInputEditText et_Password;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        AppUser appUser = AppUser.getInstance(this);
        if (!appUser.getUserName().isEmpty() &&
                !appUser.getPassword().isEmpty() &&
                !appUser.getUSER_ID().isEmpty() &&
                !appUser.getBrHndsa().isEmpty() &&
                !appUser.getBrSectorCode().isEmpty() &&
                !appUser.getOFK().isEmpty() &&
                !appUser.getVERSION().isEmpty() &&
                !appUser.getSESSION_ID().isEmpty()) {
            Intent intent = new Intent(LoginActivity.this, MainActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_TASK_ON_HOME);
            startActivity(intent);
            LoginActivity.this.finish();
        }
        Button btnLogin = findViewById(R.id.btn_login);
        progressDialog = new ProgressDialog(LoginActivity.this);
        et_UserName = findViewById(R.id.user_name);
        et_Password = findViewById(R.id.password);
        ((TextView) findViewById(R.id.branch_name)).setText(BuildConfig.arName.replace("\t", "\n"));
        ((TextView) findViewById(R.id.copyright)).setText((getString(R.string.shoaa_company_app_version) + BuildConfig.VERSION_NAME));

        getWindow().setStatusBarColor(getResources().getColor(R.color.primary, getTheme()));

        btnLogin.setOnClickListener(view -> new Thread(() -> {

            runOnUiThread(() -> btnLogin.setEnabled(false));
            if (et_UserName.getText() == null || et_UserName.getText().toString().isEmpty() || et_UserName.getText().toString().trim().isEmpty()) {
                runOnUiThread(() -> {
                    Toast.makeText(LoginActivity.this, "ادخل اسم المستخدم", Toast.LENGTH_LONG).show();
                    btnLogin.setEnabled(true);
                });
                return;
            }
            if (et_Password.getText() == null || et_Password.getText().toString().isEmpty() || et_Password.getText().toString().trim().isEmpty()) {
                runOnUiThread(() -> {
                    Toast.makeText(LoginActivity.this, "ادخل كلمة المرور", Toast.LENGTH_LONG).show();
                    btnLogin.setEnabled(true);
                });
                return;
            }
            runOnUiThread(() -> {
                progressDialog = ProgressDialog.show(LoginActivity.this, "يرجي الانتظار",
                        "جاري تسجيل الدخول...",
                        true);
                progressDialog.setCancelable(false);
            });

            LoginManager.login(this, et_UserName.getText().toString().trim(), et_Password.getText().toString().trim(), new ApiResult() {
                @Override
                public void onSuccess() {
                    try {
                        if (progressDialog != null && progressDialog.isShowing())
                            runOnUiThread(() -> progressDialog.dismiss());
                    } catch (Exception ignored) {
                    }
                    AppUser loginUser = AppUser.getInstance(LoginActivity.this);
                    boolean valid = false;
                    if (loginUser.getVERSION().equalsIgnoreCase(BuildConfig.VERSION_NAME)) {
                        valid = true;
                    } else {
                        for (String version : BuildConfig.VALID_OLD_VERSIONS) {
                            if (loginUser.getVERSION().equalsIgnoreCase(version)) {
                                valid = true;
                                break;
                            }
                        }
                    }
                    if (valid) {
                        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_TASK_ON_HOME);
                        startActivity(intent);
                        LoginActivity.this.finish();
                    } else {
                        AppUser.getInstance(LoginActivity.this).logut(LoginActivity.this);
                        builder = new AlertDialog.Builder(LoginActivity.this)
                                .setTitle("نسخة البرنامج قديمة")
                                .setPositiveButton(android.R.string.ok, (dialog, which) -> {
                                    alertDialog.dismiss();
                                })
                                .setIcon(android.R.drawable.ic_dialog_alert)
                                .setCancelable(false)
                                .setMessage("يرجي تحديث نسخة البرنامج\nالي اصدار " + loginUser.getVERSION());
                        alertDialog = builder.show();
                    }
                }

                @Override
                public void onFailed(int code, String reason) {
                    AppUser.getInstance(LoginActivity.this).logut(LoginActivity.this);
                    runOnUiThread(() -> {
                        try {
                            if (progressDialog != null && progressDialog.isShowing())
                                runOnUiThread(() -> progressDialog.dismiss());
                        } catch (Exception ignored) {
                        }
                        builder = new AlertDialog.Builder(LoginActivity.this)
                                .setTitle("خطأ تسجيل الدخول")
                                .setPositiveButton(android.R.string.yes, (dialog, which) -> {
                                    alertDialog.dismiss();
                                })
                                .setIcon(android.R.drawable.ic_dialog_alert);
                        if (code == 0)
                            builder.setMessage("اسم المستخدم او كلمة المرور غير صحيحة");
                        else
                            builder.setMessage("غير قادر علي الاتصال");
                        alertDialog = builder.show();
                    });
                }

            });
            runOnUiThread(() -> btnLogin.setEnabled(true));
        }).start());
    }
}