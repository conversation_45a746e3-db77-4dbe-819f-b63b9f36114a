<component name="libraryTable">
  <library name="Gradle: androidx.fragment:fragment:1.3.6@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/831619a43dd3376e965180be91fd0ca3/transformed/fragment-1.3.6/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/831619a43dd3376e965180be91fd0ca3/transformed/fragment-1.3.6/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/831619a43dd3376e965180be91fd0ca3/transformed/fragment-1.3.6/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/831619a43dd3376e965180be91fd0ca3/transformed/fragment-1.3.6/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.fragment/fragment/1.3.6/25ece06338d39da1fdc9d8488aa57b5014866918/fragment-1.3.6-sources.jar!/" />
    </SOURCES>
  </library>
</component>