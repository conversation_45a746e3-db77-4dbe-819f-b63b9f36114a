<component name="libraryTable">
  <library name="Gradle: androidx.customview:customview:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/af7a29ab5b0028faf62c637550924ca8/transformed/customview-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d672d57788e634a5e0387da3f4de9c64/transformed/customview-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c2e2cabaedc19b99f8ca01ee29f29e03/transformed/customview-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3cd64e078e868be7ab1717cdd992bfa5/transformed/customview-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/fcdd98bc1886bceb239d2433ee5d1628/transformed/customview-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3cd64e078e868be7ab1717cdd992bfa5/transformed/customview-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/3cd64e078e868be7ab1717cdd992bfa5/transformed/customview-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.customview/customview/1.0.0/61f6a717d144dff3a6bda413d9abeeb2bca71581/customview-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>