package com.sewedy.electrometerparser.protocol.connection.bluetooth;

public class Connection {
    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    public static String getMeterPdu(byte[] bt) {
        char[] hexChars = new char[bt.length * 2];
        for (int j = 0; j < bt.length; j++) {
            int v = bt[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public byte[] setMeterPdu(String k) {
        int len = k.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(k.charAt(i), 16) << 4)
                    + Character.digit(k.charAt(i + 1), 16));
        }
        return data;
    }
}
