package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/DlmsResponseInfo.class */
public class DlmsResponseInfo {
    public static final int RESULT_FORMAT_ERROR = -1;
    public static final int RESULT_INVOKE_ID_ERROR = -2;
    public static final int RESULT_AA_ACCEPTED = 0;
    public static final int RESULT_ACTION_SUCCESS = 0;
    public static final int RESULT_ACTION_TEMPORARY_FAILURE = 2;
    public static final int RESULT_DATA_ACCESS_SUCCESS = 0;
    private int serviceType;
    private int associationResult;
    private int dataAccessResult;
    private int actionResult;
    private DlmsData dlmsData;

    public static DlmsResponseInfo serviceError() {
        DlmsResponseInfo info = new DlmsResponseInfo();
        info.setServiceType(-1);
        info.setActionResult(-1);
        return info;
    }

    public int getServiceType() {
        return this.serviceType;
    }

    public void setServiceType(int serviceType) {
        this.serviceType = serviceType;
    }

    public int getAssociationResult() {
        return this.associationResult;
    }

    public void setAssociationResult(int associationResult) {
        this.associationResult = associationResult;
    }

    public int getDataAccessResult() {
        return this.dataAccessResult;
    }

    public void setDataAccessResult(int dataAccessResult) {
        this.dataAccessResult = dataAccessResult;
    }

    public int getActionResult() {
        return this.actionResult;
    }

    public void setActionResult(int actionResult) {
        this.actionResult = actionResult;
    }

    public DlmsData getDlmsData() {
        return this.dlmsData;
    }

    public void setDlmsData(DlmsData dlmsData) {
        this.dlmsData = dlmsData;
    }
}
