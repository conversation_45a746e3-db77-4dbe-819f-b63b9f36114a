package shoaa.iskrareader.iskraemeco.optical.bluetooth.model;

public class ResponseModel {
    private String service;
    private String pdu;
    private String value;
    private String result;

    public String getService() {
        return this.service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getPdu() {
        return this.pdu;
    }

    public void setPdu(String pdu) {
        this.pdu = pdu;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}