package shoaa.opticalsmartreader;

import android.content.pm.PackageInfo;

import androidx.annotation.NonNull;

import com.google.firebase.remoteconfig.ConfigUpdate;
import com.google.firebase.remoteconfig.ConfigUpdateListener;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Scanner;

import shoaa.connectionmanager.Common;

public class Utils {

    public static String SHARED_PREFERENCE = "SAVE_METER_DATA";
    public static String METER_IMAGE_KEY = "IMAGE_BITMAP_KEY";
    public static String METER_DATA_KEY = "METER_DATA";

    public static void writeStringAsFile(File outDir, final String fileContents, String fileName) {
        try {
            if (!outDir.exists())
                outDir.mkdirs();
            FileWriter out = new FileWriter(new File(outDir, fileName));
            out.write(fileContents);
            out.close();
        } catch (IOException ignored) {
        }
    }

    public static String readFile(File file) throws IOException {
        if (!file.exists() || !file.canRead())
            return "";
        StringBuilder fileContents = new StringBuilder((int) file.length());
        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                fileContents.append(scanner.nextLine() + System.lineSeparator());
            }
            return fileContents.toString();
        }
    }


    public static void init() {
        try {
            FirebaseRemoteConfig mFirebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
            FirebaseRemoteConfigSettings configSettings = new FirebaseRemoteConfigSettings.Builder()
                    .setMinimumFetchIntervalInSeconds(BuildConfig.DEBUG ? 0 : 60 * 60 * 4)
                    .setFetchTimeoutInSeconds(5000)
                    .build();
            mFirebaseRemoteConfig.setConfigSettingsAsync(configSettings);
            PackageInfo packageInfo = MainApplication.getInstance().getPackageManager().getPackageInfo(MainApplication.getInstance().getPackageName(), 0);
            HashMap<String, Object> defaults = new HashMap<>();
            defaults.put("TIME_OUT_200", 200);
            defaults.put("TIME_OUT_300", 300);
            defaults.put("TIME_OUT_500", 500);
            defaults.put("TIME_OUT_800", 800);
            defaults.put("TIME_OUT_1000", 1000);
            defaults.put("TIME_OUT_1200", 1200);
            defaults.put("TIME_OUT_1500", 1500);
            defaults.put("TIME_OUT_2000", 2000);
            defaults.put("TIME_OUT_2500", 2500);
            defaults.put("CALCULATE_SEND_TIME", true);
            defaults.put("ENABLE_GLOBAL_VERSION_SELECTION", true);
            defaults.put("VC", packageInfo.versionCode);
            mFirebaseRemoteConfig.setDefaultsAsync(defaults);
            mFirebaseRemoteConfig.fetchAndActivate().addOnCompleteListener(task -> {
                if (task.isSuccessful()) {
                    try {
                        Common.TIME_OUT_200 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_200");
                        Common.TIME_OUT_300 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_300");
                        Common.TIME_OUT_500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_500");
                        Common.TIME_OUT_800 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_800");
                        Common.TIME_OUT_1000 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1000");
                        Common.TIME_OUT_1200 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1200");
                        Common.TIME_OUT_1500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1500");
                        Common.TIME_OUT_2000 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_2000");
                        Common.TIME_OUT_2500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_2500");
                        Common.CALCULATE_SEND_TIME = mFirebaseRemoteConfig.getBoolean("CALCULATE_SEND_TIME");
                        Common.ENABLE_GLOBAL_VERSION_SELECTION = mFirebaseRemoteConfig.getBoolean("ENABLE_GLOBAL_VERSION_SELECTION");
                        if (mFirebaseRemoteConfig.getLong("VC") > packageInfo.versionCode) {
                            System.exit(0);
                        }
                    } catch (Exception ignore) {
                    }
                }
            });
            mFirebaseRemoteConfig.addOnConfigUpdateListener(new ConfigUpdateListener() {
                @Override
                public void onUpdate(@NonNull ConfigUpdate configUpdate) {
                    try {
                        Common.TIME_OUT_200 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_200");
                        Common.TIME_OUT_300 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_300");
                        Common.TIME_OUT_500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_500");
                        Common.TIME_OUT_800 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_800");
                        Common.TIME_OUT_1000 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1000");
                        Common.TIME_OUT_1200 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1200");
                        Common.TIME_OUT_1500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_1500");
                        Common.TIME_OUT_2000 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_2000");
                        Common.TIME_OUT_2500 = (int) mFirebaseRemoteConfig.getLong("TIME_OUT_2500");
                        Common.CALCULATE_SEND_TIME = mFirebaseRemoteConfig.getBoolean("CALCULATE_SEND_TIME");
                        Common.ENABLE_GLOBAL_VERSION_SELECTION = mFirebaseRemoteConfig.getBoolean("ENABLE_GLOBAL_VERSION_SELECTION");
                        if (mFirebaseRemoteConfig.getLong("VC") > packageInfo.versionCode) {
                            System.exit(0);
                        }
                    } catch (Exception ignore) {
                    }
                }

                @Override
                public void onError(@NonNull FirebaseRemoteConfigException error) {
                }
            });
        } catch (Exception ignore) {
        }
    }

}

