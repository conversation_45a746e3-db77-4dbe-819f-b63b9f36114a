package shoaa.opticalsmartreader.logic.login;

import android.content.Context;

import androidx.annotation.NonNull;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import shoaa.opticalsmartreader.api.APIClient;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.models.AppUser;

public class LoginManager {
    public static void login(Context context, String userName, String password, ApiResult apiResult) {
        Call<LoginResponse> loginCall = APIClient.getService().login(userName, password, "0");
        loginCall.enqueue(new Callback<LoginResponse>() {
            @Override
            public void onResponse(@NonNull Call<LoginResponse> call, @NonNull Response<LoginResponse> response) {
                if (response.isSuccessful()) {
                    if (response.body() != null) {
                        if (response.body().getRESULT().equalsIgnoreCase("error")) {
                            apiResult.onFailed(0, "");
                            return;
                        }
                        AppUser.getInstance(context).setAppUser(context, userName, password, response.body());
                        if (apiResult != null) {
                            apiResult.onSuccess();
                        }
                    } else {
                        if (apiResult != null) {
                            apiResult.onFailed(1, "");
                        }
                    }
                } else {
                    if (apiResult != null) {
                        apiResult.onFailed(2, "");
                    }
                }
            }

            @Override
            public void onFailure(@NonNull Call<LoginResponse> call, @NonNull Throwable t) {
                if (apiResult != null)
                    apiResult.onFailed(1, "");
            }
        });

    }
}
