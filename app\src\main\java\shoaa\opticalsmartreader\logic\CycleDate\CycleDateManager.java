package shoaa.opticalsmartreader.logic.CycleDate;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import shoaa.opticalsmartreader.api.APIClient;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.logic.login.LoginManager;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.CycleDate;

public class CycleDateManager {

    public static CycleDate[] cycleDate = null;

    public static void loadCycleDate(Activity activity, AppUser loginUser, ApiResult apiResult) {
        try {
            Utils.appDatabase.cycleDateDao().delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        LoginManager.login(activity, loginUser.getUserName(), loginUser.getPassword(), new ApiResult() {
            @Override
            public void onSuccess() {
                getCycleDate(activity, loginUser, apiResult);
            }

            @Override
            public void onFailed(int code, String reason) {
                apiResult.onFailed(0, "");
            }
        });
    }

    private static void getCycleDate(Activity activity, AppUser loginUser, ApiResult apiResult) {
        Call<CycleDate[]> allBranches = APIClient.getService().getCycleDate(loginUser.getSESSION_ID(), "LUCycleDate", "0", "0");
        allBranches.enqueue(new Callback<CycleDate[]>() {
            @Override
            public void onResponse(@NonNull Call<CycleDate[]> call, @NonNull Response<CycleDate[]> response) {
                if (response.isSuccessful()) {
                    if (response.body() == null) {
                        cycleDate = null;
                        apiResult.onFailed(1, "");
                    } else {
                        new Thread(() -> {
                            cycleDate = response.body();
                            toDatabase(activity, cycleDate[0]);
                            apiResult.onSuccess();
                        }).start();
                    }
                } else {
                    cycleDate = null;
                    apiResult.onFailed(2, "");
                }
            }

            @Override
            public void onFailure(@NonNull Call<CycleDate[]> call, @NonNull Throwable t) {
                cycleDate = null;
                apiResult.onFailed(2, "");
            }
        });
    }


    public static CycleDate getCycleDate(Activity activity) {
        if (!Utils.appDatabase.isOpen())
            Utils.appDatabase = Room.databaseBuilder(activity.getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();

        return Utils.appDatabase.cycleDateDao().get();
    }

    private static void toDatabase(Activity activity, CycleDate cycleDate) {
        if (!Utils.appDatabase.isOpen())
            Utils.appDatabase = Room.databaseBuilder(activity.getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
        Utils.appDatabase.cycleDateDao().delete();
        Utils.appDatabase.cycleDateDao().insert(cycleDate);
    }
}
