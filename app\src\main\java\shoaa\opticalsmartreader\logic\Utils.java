package shoaa.opticalsmartreader.logic;

import android.content.SharedPreferences;

public class Utils {
    public static AppDatabase appDatabase;
    public static SharedPreferences sharedPreferences;

    public static String formatStringUIntNumber(String input) {
        return replaceNonstandardDigits(input).replaceAll("[^0-9]", "");
    }

    public static int getAsUInt(String input) {
        return Integer.parseInt(replaceNonstandardDigits(input).replaceAll("[^0-9]", ""));
    }

    public static int getAsInt(String input) {
        return Integer.parseInt(replaceNonstandardDigits(input).replaceAll("[^0-9-]", ""));
    }

    public static long getAsULong(String input) {
        return Long.parseLong(replaceNonstandardDigits(input).replaceAll("[^0-9]", ""));
    }

    public static long getAsLong(String input) {
        return Long.parseLong(replaceNonstandardDigits(input).replaceAll("[^0-9-]", ""));
    }

    public static float getAsFloat(String input) {
        return Float.parseFloat(replaceNonstandardDigits(input).replaceAll("[^0-9.-]", "").replaceAll("\\.\\.", "\\."));
    }

    public static float getAsUFloat(String input) {
        return Float.parseFloat(replaceNonstandardDigits(input).replaceAll("[^0-9.]", "").replaceAll("\\.\\.", "\\."));
    }

    public static String replaceNonstandardDigits(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (isNonstandardDigit(ch)) {
                int numericValue = Character.getNumericValue(ch);
                if (numericValue >= 0) {
                    builder.append(numericValue);
                }
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }

    private static boolean isNonstandardDigit(char ch) {
        return Character.isDigit(ch) && !(ch >= '0' && ch <= '9');
    }

}
