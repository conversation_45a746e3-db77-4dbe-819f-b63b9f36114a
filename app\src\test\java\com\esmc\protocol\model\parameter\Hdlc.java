package com.esmc.protocol.model.parameter;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/parameter/Hdlc.class */
public class Hdlc {
    private int clientAddress;
    private int serverAddress;

    public Hdlc(int client, int server) {
        this.clientAddress = client;
        this.serverAddress = server;
    }

    public int getClientAddress() {
        return this.clientAddress;
    }

    public int getServerAddress() {
        return this.serverAddress;
    }
}
