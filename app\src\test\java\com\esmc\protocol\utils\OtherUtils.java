package com.esmc.protocol.utils;

import com.esmc.protocol.model.DlmsData;

import java.io.ByteArrayInputStream;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/utils/OtherUtils.class */
public class OtherUtils {
    private static final String[] COSEM_TYPE_NAME = {"null", "Array", "Structure", DlmsData.TYPE_BOOL, DlmsData.TYPE_BITSTRING, DlmsData.TYPE_I32, DlmsData.TYPE_U32, "[7]undef", "[8]undef", DlmsData.TYPE_OCTETSTRING, DlmsData.TYPE_VISIBLESTRING, "[11]undef", "[12]undef", "Bcd", "[14]undef", DlmsData.TYPE_I8, DlmsData.TYPE_I16, DlmsData.TYPE_U8, DlmsData.TYPE_U16, "CompactArray", "Long64", "Long64Unsigned", DlmsData.TYPE_ENUM, DlmsData.TYPE_FLOAT32, "Float64", "Datetime", "Date", "Time"};

    public static byte[] getRandomByteArray(int length) {
        byte[] vs = new byte[length];
        Random random = new Random();
        random.nextBytes(vs);
        return vs;
    }

    public static String getRandomString(int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(3);
            switch (number) {
                case 0:
                    long result = Math.round((Math.random() * 25.0d) + 65.0d);
                    sb.append((char) result);
                    break;
                case 1:
                    long result2 = Math.round((Math.random() * 25.0d) + 97.0d);
                    sb.append((char) result2);
                    break;
                case 2:
                    sb.append(new Random().nextInt(10));
                    break;
            }
        }
        return sb.toString();
    }

    public static int getCosemDataTypeTag(String typeName) {
        for (int i = 0; i < COSEM_TYPE_NAME.length; i++) {
            if (COSEM_TYPE_NAME[i].equals(typeName)) {
                return i;
            }
        }
        return -1;
    }

    public static String getCosemDataTypeName(int typeTag) {
        if (typeTag < 0 || typeTag >= COSEM_TYPE_NAME.length) {
            return "";
        }
        return COSEM_TYPE_NAME[typeTag];
    }

    public static int getVariableLengthValue(ByteArrayInputStream input) {
        int len = input.read() & 255;
        if (len >= 128) {
            int bytes = len - 128;
            len = 0;
            for (int i = 0; i < bytes; i++) {
                len |= (input.read() & 255) << (((bytes - i) - 1) * 8);
            }
        }
        if (len > input.available()) {
            return -1;
        }
        return len;
    }

    public static boolean isObisString(String obis) {
        return Pattern.matches("^(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)$", obis);
    }

    public static void main(String[] args) {
        Pattern pattern = Pattern.compile("^(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)$");
        Matcher matcher = pattern.matcher("0.0.96.1.0.255i");
        if (matcher.find()) {
            System.out.println(matcher.groupCount());
            for (int i = 1; i <= matcher.groupCount(); i++) {
                System.out.println(matcher.group(i));
            }
            return;
        }
        System.out.println("not match");
    }
}
