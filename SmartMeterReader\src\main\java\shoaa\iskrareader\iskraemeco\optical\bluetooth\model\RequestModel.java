package shoaa.iskrareader.iskraemeco.optical.bluetooth.model;

public class RequestModel {
    private String service;
    private String pdu;
    private String item;

    public String getService() {
        return this.service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getPdu() {
        return this.pdu;
    }

    public void setPdu(String pdu) {
        this.pdu = pdu;
    }

    public String getItem() {
        return this.item;
    }

    public void setItem(String item) {
        this.item = item;
    }
}