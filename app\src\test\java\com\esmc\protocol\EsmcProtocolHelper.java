package com.esmc.protocol;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.esmc.protocol.model.AuthenticationMechanisms;
import com.esmc.protocol.model.DlmsCosemItem;
import com.esmc.protocol.model.DlmsData;
import com.esmc.protocol.model.DlmsResponseInfo;
import com.esmc.protocol.model.HdlcResponseInfo;
import com.esmc.protocol.model.ProtocolProfile;
import com.esmc.protocol.model.RequestInfo;
import com.esmc.protocol.model.ResponseInfo;
import com.esmc.protocol.model.SecurityControl;
import com.esmc.protocol.model.parameter.Dlms;
import com.esmc.protocol.model.parameter.Hdlc;
import com.esmc.protocol.utils.AuthenticationMechanismProvider;
import com.esmc.protocol.utils.MyConverter;
import com.esmc.protocol.utils.OtherUtils;
import com.esmc.protocol.wrap.CosemDataWrapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class EsmcProtocolHelper {
    private final String SERVICE_HANDSHAKE = "handshake";
    private final String SERVICE_GET = "get";
    private final String SERVICE_RELEASE = "release";
    private final String RESULT_CONTINUE = "continue";
    private final String RESULT_SUCCESS = "success";
    private ProtocolProfile profile;
    private final Hdlc hdlc = new Hdlc(1, 1);
    private final Dlms dlms = new Dlms(AuthenticationMechanisms.HLS5, new SecurityControl(false, 0, true, true, 0));
    private final CosemDataWrapper dataWrapper = new CosemDataWrapper();

    public static void main(String[] args) {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        System.out.println("handshake step 1:");
        System.out.println("request: {\"service\":\"handshake\",\"pdu\":null,\"item\":null}");
        EsmcProtocolHelper helper = new EsmcProtocolHelper();
        String response = helper.handle("{\"service\":\"handshake\",\"pdu\":null,\"item\":null}");
        System.out.println("response: " + response);
        System.out.println("handshake step 2:");
        System.out.println("input the response from meter:");
        String pdu = "";
        try {
            pdu = br.readLine().replace(" ", "");
        } catch (IOException e) {
            e.printStackTrace();
        }
        String request = "{\"service\":\"handshake\",\"pdu\":\"" + pdu + "\",\"item\":null}";
        System.out.println("request: " + request);
        String response2 = helper.handle(request);
        System.out.println("response: " + response2);
        System.out.println("handshake step 3:");
        System.out.println("input the response from meter:");
        try {
            pdu = br.readLine().replace(" ", "");
        } catch (IOException e2) {
            e2.printStackTrace();
        }
        String request2 = "{\"service\":\"handshake\",\"pdu\":\"" + pdu + "\",\"item\":null}";
        System.out.println("request: " + request2);
        String response3 = helper.handle(request2);
        System.out.println("response: " + response3);
        System.out.println("handshake step 4:");
        System.out.println("input the response from meter:");
        try {
            pdu = br.readLine().replace(" ", "");
        } catch (IOException e3) {
            e3.printStackTrace();
        }
        String request3 = "{\"service\":\"handshake\",\"pdu\":\"" + pdu + "\",\"item\":null}";
        System.out.println("request: " + request3);
        String response4 = helper.handle(request3);
        System.out.println("response: " + response4);
        do {
            System.out.println("get item:");
            System.out.println("input the item code:");
            try {
                pdu = br.readLine();
            } catch (IOException e4) {
                e4.printStackTrace();
            }
            String request4 = "{\"service\":\"get\",\"pdu\":null,\"item\":\"" + pdu + "\"}";
            System.out.println("request: " + request4);
            String response5 = helper.handle(request4);
            System.out.println("response: " + response5);
            System.out.println("input the response from meter:");
            try {
                pdu = br.readLine().replace(" ", "");
            } catch (IOException e5) {
                e5.printStackTrace();
            }
            String request5 = "{\"service\":\"get\",\"pdu\":\"" + pdu + "\",\"item\":\"" + pdu + "\"}";
            System.out.println("request: " + request5);
            String response6 = helper.handle(request5);
            System.out.println("response: " + response6);
            System.out.print("continue? (y-yes,n-no):");
            try {
                pdu = br.readLine();
            } catch (IOException e6) {
                e6.printStackTrace();
            }
        } while (pdu.contains("y"));
        System.out.println("release:");
        System.out.println("request: {\"service\":\"release\",\"pdu\":null,\"item\":null}");
        String response7 = helper.handle("{\"service\":\"release\",\"pdu\":null,\"item\":null}");
        System.out.println("response: " + response7);
        System.out.println("input the response from meter:");
        try {
            pdu = br.readLine().replace(" ", "");
        } catch (IOException e7) {
            e7.printStackTrace();
        }
        String request6 = "{\"service\":\"release\",\"pdu\":\"" + pdu + "\",\"item\":null}";
        System.out.println("request: " + request6);
        String response8 = helper.handle(request6);
        System.out.println("response: " + response8);
    }

    public String handle(String json) {
        RequestInfo requestInfo = (RequestInfo) JSON.parseObject(json, RequestInfo.class);
        if (requestInfo == null) {
            return "";
        }
        if (SERVICE_HANDSHAKE.equals(requestInfo.getService())) {
            return handshake(requestInfo);
        }
        if (SERVICE_GET.equals(requestInfo.getService())) {
            return itemGet(requestInfo);
        }
        return SERVICE_RELEASE.equals(requestInfo.getService()) ? release(requestInfo) : JSON.toJSONString(ResponseInfo.failedResponseInfo(requestInfo.getService()), SerializerFeature.WriteMapNullValue);
    }

    private String handshake(RequestInfo requestInfo) {
        String pdu = requestInfo.getPdu();
        if (pdu == null || pdu.isEmpty()) {
            this.profile = new ProtocolProfile();
            String pdu2 = HdlcHelper.request(0, null, this.profile, this.hdlc);
            if (pdu2.isEmpty()) {
                return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
            }
            ResponseInfo responseInfo = new ResponseInfo();
            responseInfo.setService(SERVICE_HANDSHAKE);
            responseInfo.setPdu(pdu2);
            responseInfo.setValue(null);
            responseInfo.setResult(RESULT_CONTINUE);
            return JSON.toJSONString(responseInfo, SerializerFeature.WriteMapNullValue);
        } else if (this.profile == null) {
            return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
        } else {
            HdlcResponseInfo hdlcResponseInfo = HdlcHelper.response(pdu, this.profile, this.hdlc);
            if (!hdlcResponseInfo.getResult()) {
                return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
            }
            if (0 == this.profile.getConnectState()) {
                if (hdlcResponseInfo.getHdlcFrameType() != 1) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                this.profile.setConnectState(1);
                String pdu3 = DlmsHelper.request(0, this.profile, this.dlms);
                if (pdu3.isEmpty()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                String pdu4 = HdlcHelper.request(2, MyConverter.hexStringToBytes(pdu3), this.profile, this.hdlc);
                if (pdu4.isEmpty()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                ResponseInfo responseInfo2 = new ResponseInfo();
                responseInfo2.setResult(RESULT_CONTINUE);
                responseInfo2.setService(SERVICE_HANDSHAKE);
                responseInfo2.setPdu(pdu4);
                return JSON.toJSONString(responseInfo2, SerializerFeature.WriteMapNullValue);
            } else if (1 == this.profile.getConnectState()) {
                if (hdlcResponseInfo.getHdlcFrameType() != 2) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                if (0 != DlmsHelper.response(hdlcResponseInfo.getInfo(), this.profile, this.dlms).getAssociationResult()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                this.profile.setConnectState(2);
                if (this.dlms.getAuthenticationMechanisms() == AuthenticationMechanisms.HLS3 || this.dlms.getAuthenticationMechanisms() == AuthenticationMechanisms.HLS4 || this.dlms.getAuthenticationMechanisms() == AuthenticationMechanisms.HLS5) {
                    MyConverter.hexStringToBytes(this.profile.getServer2Client());
                    byte[] vs = AuthenticationMechanismProvider.mechanismGmac(this.profile.getInvocationCounterClient(), this.profile);
                    DlmsCosemItem cosemItem = new DlmsCosemItem(15, "0.0.40.0.0.255", 1, "");
                    cosemItem.setData(new DlmsData(DlmsData.TYPE_OCTETSTRING, MyConverter.bytesToHexString(vs, false)));
                    this.profile.setCosemItem(cosemItem);
                    String pdu5 = HdlcHelper.request(2, MyConverter.hexStringToBytes(DlmsHelper.request(48, this.profile, this.dlms)), this.profile, this.hdlc);
                    ResponseInfo responseInfo3 = new ResponseInfo();
                    responseInfo3.setResult(RESULT_CONTINUE);
                    responseInfo3.setService(SERVICE_HANDSHAKE);
                    responseInfo3.setPdu(pdu5);
                    return JSON.toJSONString(responseInfo3, SerializerFeature.WriteMapNullValue);
                }
                this.profile.setConnectState(4);
                ResponseInfo responseInfo4 = new ResponseInfo();
                responseInfo4.setResult(RESULT_SUCCESS);
                responseInfo4.setService(SERVICE_HANDSHAKE);
                return JSON.toJSONString(responseInfo4, SerializerFeature.WriteMapNullValue);
            } else if (2 != this.profile.getConnectState()) {
                return "";
            } else {
                if (hdlcResponseInfo.getHdlcFrameType() != 2) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                DlmsResponseInfo dlmsResponseInfo = DlmsHelper.response(hdlcResponseInfo.getInfo(), this.profile, this.dlms);
                if (dlmsResponseInfo == null || 0 != dlmsResponseInfo.getActionResult() || null == dlmsResponseInfo.getDlmsData() || !DlmsData.TYPE_OCTETSTRING.equals(dlmsResponseInfo.getDlmsData().getDataType()) || dlmsResponseInfo.getDlmsData().getDataValue().isEmpty()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                byte[] ctosCipher = MyConverter.hexStringToBytes(dlmsResponseInfo.getDlmsData().getDataValue());
                if (!AuthenticationMechanismProvider.mechanismGmacValidate(ctosCipher, this.profile)) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
                }
                this.profile.setConnectState(4);
                ResponseInfo responseInfo5 = new ResponseInfo();
                responseInfo5.setResult(RESULT_SUCCESS);
                responseInfo5.setService(SERVICE_HANDSHAKE);
                return JSON.toJSONString(responseInfo5, SerializerFeature.WriteMapNullValue);
            }
        }
    }

    private String itemGet(RequestInfo requestInfo) {
        if (null == this.profile || null == requestInfo.getItem() || requestInfo.getItem().isEmpty()) {
            return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
        }
        String[] vs = requestInfo.getItem().split(":");
        if (vs.length != 3) {
            return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
        }
        try {
            int itemCls = Integer.parseInt(vs[0]);
            String itemObis = vs[1];
            if (!OtherUtils.isObisString(itemObis)) {
                return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
            }
            int serviceId = Integer.parseInt(vs[2]);
            DlmsCosemItem cosemItem = new DlmsCosemItem(itemCls, itemObis, serviceId, "");
            if (requestInfo.getPdu() == null || requestInfo.getPdu().isEmpty()) {
                this.profile.setCosemItem(cosemItem);
                String pdu = DlmsHelper.request(16, this.profile, this.dlms);
                if (pdu.isEmpty()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
                }
                String pdu2 = HdlcHelper.request(2, MyConverter.hexStringToBytes(pdu), this.profile, this.hdlc);
                ResponseInfo responseInfo = new ResponseInfo();
                responseInfo.setResult(RESULT_CONTINUE);
                responseInfo.setService(SERVICE_GET);
                responseInfo.setPdu(pdu2);
                return JSON.toJSONString(responseInfo, SerializerFeature.WriteMapNullValue);
            } else if (!cosemItem.equals(this.profile.getCosemItem())) {
                return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
            } else {
                HdlcResponseInfo hdlcResponseInfo = HdlcHelper.response(requestInfo.getPdu(), this.profile, this.hdlc);
                if (!hdlcResponseInfo.getResult() || 2 != hdlcResponseInfo.getHdlcFrameType()) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
                }
                DlmsResponseInfo dlmsResponseInfo = DlmsHelper.response(hdlcResponseInfo.getInfo(), this.profile, this.dlms);
                if (dlmsResponseInfo.getDataAccessResult() != 0) {
                    return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
                }
                Object dataValue = this.dataWrapper.getCosemDataDisplayValue(requestInfo.getItem(), dlmsResponseInfo.getDlmsData());
                ResponseInfo responseInfo2 = new ResponseInfo();
                responseInfo2.setResult(RESULT_SUCCESS);
                responseInfo2.setService(SERVICE_GET);
                responseInfo2.setValue(dataValue);
                return JSON.toJSONString(responseInfo2, SerializerFeature.WriteMapNullValue);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_GET), SerializerFeature.WriteMapNullValue);
        }
    }

    private String release(RequestInfo requestInfo) {
        String pdu = requestInfo.getPdu();
        if (pdu == null || pdu.isEmpty()) {
            String pdu2 = HdlcHelper.request(8, null, this.profile, this.hdlc);
            if (pdu2.isEmpty()) {
                return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_HANDSHAKE), SerializerFeature.WriteMapNullValue);
            }
            resetAllState();
            ResponseInfo responseInfo = new ResponseInfo();
            responseInfo.setService(SERVICE_RELEASE);
            responseInfo.setPdu(pdu2);
            responseInfo.setValue(null);
            responseInfo.setResult(RESULT_CONTINUE);
            return JSON.toJSONString(responseInfo, SerializerFeature.WriteMapNullValue);
        }
        HdlcResponseInfo hdlcResponseInfo = HdlcHelper.response(requestInfo.getPdu(), this.profile, this.hdlc);
        if (!hdlcResponseInfo.getResult() || 1 != hdlcResponseInfo.getHdlcFrameType()) {
            return JSON.toJSONString(ResponseInfo.failedResponseInfo(SERVICE_RELEASE), SerializerFeature.WriteMapNullValue);
        }
        resetAllState();
        ResponseInfo responseInfo2 = new ResponseInfo();
        responseInfo2.setService(SERVICE_RELEASE);
        responseInfo2.setPdu(null);
        responseInfo2.setValue(null);
        responseInfo2.setResult(RESULT_SUCCESS);
        return JSON.toJSONString(responseInfo2, SerializerFeature.WriteMapNullValue);
    }

    private void resetAllState() {
        this.profile = null;
    }
}
