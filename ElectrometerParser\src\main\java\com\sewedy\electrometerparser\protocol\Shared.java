package com.sewedy.electrometerparser.protocol;

import android.util.Log;

import com.google.gson.annotations.SerializedName;

import org.apache.commons.lang3.ArrayUtils;

import java.nio.ByteOrder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class Shared {

//    public static String getFormattedDate(String dateInString) {
//        try {
//            if (TextUtils.isEmpty(dateInString))
//                return "NF";
//
//            String pattern = "yyyy/MM/dd hh:mm:ss";
//            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
//            SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy", Locale.ENGLISH);
//            Date date = formatter.parse(dateInString);
//            return dateFormat.format(date);
//        } catch (Exception e) {
//            return getFormattedDate(dateInString,"yyyy-MMM-dd");
//        }
//    }

    public static String getFormattedDate(String date) {
        if (date.contains("NF"))
            return "NF";
        if (date.contains("1999") || date.contains("2000"))
            return "NF";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        Date parsedDate = null;
        try {
            parsedDate = sdf.parse(date);
        } catch (ParseException e) {
            sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                parsedDate = sdf.parse(date);
                long millis = parsedDate.getTime();
                return sdf.format(millis);
            } catch (ParseException parseException) {
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    parsedDate = sdf.parse(date);
                    long millis = parsedDate.getTime();
                    return sdf.format(millis);
                } catch (Exception p) {
                    p.printStackTrace();
                }

            }
        }
        long millis = parsedDate.getTime();
        return sdf.format(millis);
    }


//    public static String getFormattedDate(String dateInString,String dateFormatString) {
//        try {
//            if (TextUtils.isEmpty(dateInString))
//                return "NF";
//
//            String pattern = "yyyy-MM-dd hh:mm:ss";
//            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
//            Date date = dateFormat.parse(dateInString);
//            long millis = date.getTime();
//            return dateFormat.format(millis);
//        } catch (Exception e) {
//            return dateInString;
//        }
//    }

    enum Modes {
        Readout(0x00),
        ReadData(6),
        Configure(7);

        private final int id;

        Modes(int id) {
            this.id = id;
        }

        public int getId() {
            return this.id;
        }
    }

    public enum EventsCode {
        Firmware_Reserved(0),
        Meter_Data_Read(1),
        Power_Up(2),
        Power_Down(3),
        MD_Clear_by_Switch(4),
        Tamper_Alarms_Reset(5),
        MD_Clear_by_Command(6),
        Data_Corrupt_Tariff_and_Payment(20),
        Data_Corrupt_File_System_and_EEPROM(21),
        Data_Corrupt_Metering_and_ADE(22),
        Data_Corrupt_Display_and_LCD(23),
        Data_Corrupt_RF_application_and_driver(24),
        Data_Corrupt_GPRS_application_and_driver(25),
        Data_Corrupt_RFID_application_and_driver(26),
        Data_Corrupt_Control_System(27),
        EEPROM_Communication_Error_Start(28),
        ADE_Communication_Error_Start(29),
        ADE_Driver_Error_Start_No_Interrupt(30),
        RF_Chip_Communication_Error_Start(31),
        RFID_Chip_Communication_Error_Start(32),
        GPRS_Chip_Communication_Error_Start(33),
        Relay_Error_Start(34),
        //Future_Use(35),
        Battery_Low_Start(36),
        Low_Credit_First_Alarm_Start(37),
        Low_Credit_Second_Alarm_Start(38),
        No_Credit_with_friendly_Start(39),
        No_Credit_Start(40),
        Over_Load_Channel_A_Start(41),
        Over_Load_Channel_B_Start(42),
        Over_Load_Channel_C_Start(43),
        Over_Volt_Channel_A_Start(44),
        Over_Volt_Channel_B_Start(45),
        Over_Volt_Channel_C_Start(46),
        Under_Volt_Channel_A_Start(47),
        Under_Volt_Channel_B_Start(48),
        Under_Volt_Channel_C_Start(49),
        Phase_Sequence_Start(50),
        //Phase_Sequence_Channel_B_Start(51),
        //Phase_Sequence_Channel_C_Start(52),
        Cover_Open_Start(53),
        Terminal_Open_Start(54),
        Magnetic_High_Start(55),
        Reverse_Current_Channel_A_Start(56),
        Reverse_Current_Channel_B_Start(57),
        Reverse_Current_Channel_C_Start(58),
        Fault_Energy_Bypass_Start(59),
        //Fault_Energy_Channel_B_Start(60),
        //Fault_Energy_Channel_C_Start(61),
        Missing_Potential_Channel_A_Start(62),
        Missing_Potential_Channel_B_Start(63),
        Missing_Potential_Channel_C_Start(64),
        //GPRS_Module_Cover_Open_Start(65),
        External_Cover_Open_Start(65),
        Contract_Capacity_Start(66),

        EEPROM_Communication_Error_Stop(128),
        ADE_Communication_Error_Stop(129),
        ADE_Driver_Error_Stop_No_Interrupt(130),
        RF_Chip_Communication_Error_Stop(131),
        RFID_Chip_Communication_Error_Stop(132),
        GPRS_Chip_Communication_Error_Stop(133),
        Relay_Error_Stop(134),
        //Future_Use(                                 135),
        Battery_Low_Stop(136),
        Low_Credit_First_Alarm_Stop(137),
        Low_Credit_Second_Alarm_Stop(138),
        No_Credit_With_Friendly_Stop(139),
        No_Credit_Stop(140),
        Over_Load_Channel_A_Stop(141),
        Over_Load_Channel_B_Stop(142),
        Over_Load_Channel_C_Stop(143),
        Over_Volt_Channel_A_Stop(144),
        Over_Volt_Channel_B_Stop(145),
        Over_Volt_Channel_C_Stop(146),
        Under_Volt_Channel_A_Stop(147),
        Under_Volt_Channel_B_Stop(148),
        Under_Volt_Channel_C_Stop(149),
        Phase_Sequence_Stop(150),
        //Phase_Sequence_Channel_B_Stop(151),
        //Phase_Sequence_Channel_C_Stop(152),
        Cover_Open_Stop(153),
        Terminal_Open_Stop(154),
        Magnetic_High_Stop(155),
        Reverse_Current_Channel_A_Stop(156),
        Reverse_Current_Channel_B_Stop(157),
        Reverse_Current_Channel_C_Stop(158),
        Fault_Energy_Bypass_Stop(159),
        //Fault_Energy_Channel_B_Stop(160),
        //Fault_Energy_Channel_C_Stop(161),
        Missing_Potential_Channel_A_Stop(162),
        Missing_Potential_Channel_B_Stop(163),
        Missing_Potential_Channel_C_Stop(164),
        External_Cover_Open_Stop(165),
        //GPRS_Module_Cover_Open_Stop(165),

        Contract_Capacity_Stop(166),
        //Relay_Disconnected(168),
        //Relay_Connected(169),
        Relay_Connected(173),
        Relay_Disconnected(174),
        New_Tariff_Configration(175);
        private int id;

        EventsCode(int id) {
            this.id = id;
        }


        public int getId() {
            return this.id;
        }
    }

    public enum UserRequestMeterReplyDataPackets {
        BphRecords(10),
        EventRecords(11),
        MoneyRecords(12),
        ProfileRecords(13),
        ConfigureRecords(14),
        TariffRawData(31),
        MeteringRawData(32),
        ControlRawData(33),
        newControlRawData(60),
        NewDataList(100),
        VacationConfiguration(40),
        FriendlyConfiguration(41),
        PaymentConfiguration(42),
        TariffConfiguration(43),
        TaxConfiguration(44),
        BillingConfiguration(45),
        DisplayControlConfiguration(46),
        MDConfiguration(47),
        LimiterConfiguration(48),
        TamperConfiguration(49),
        OperatingPointConfiguration(50),
        IDConfiguration(51),
        ActionsOfTampersAndAlarmsConfiguration(52),
        TimeConfiguration(53),
        DateConfiguration(54),
        BatteryConfiguration(55),
        MagneticConfiguration(56),
        RFIDCardUniqueIDConfiguration(57),
        GPRSConfiguration(58),
        OverLoadAlarmConfiguration(59);

        private final int id;

        UserRequestMeterReplyDataPackets(int id) {
            this.id = id;
        }

        public int getId() {
            return this.id;
        }
    }

    public abstract static class MeterPacket {
        private byte[] packetContent;

        public MeterPacket(byte[] PacketContent) {
            this.packetContent = PacketContent;
        }

    }

    public abstract static class MeterDataPacket extends MeterPacket {
        public MeterDataPacket(byte[] PacketContent) {
            super(PacketContent);
        }

        public static byte[] GetMeterDataPacketDataBlock(byte[] MeterDataPacketContent) {
            ArrayList<Byte> PacketList = new ArrayList<>(Arrays.asList(ArrayUtils.toObject(MeterDataPacketContent)));
            PacketList.remove(0);
            PacketList.subList(PacketList.size() - 5, PacketList.size()).clear();

            return Utils.convertBuffArrayListToArr(PacketList);
        }

        public static byte[] getBlockWithinBlocks(byte[] MeterDataPacketContent, int id) {
            ArrayList<Byte> PacketList = new ArrayList<>(Arrays.asList(ArrayUtils.toObject(MeterDataPacketContent)));
            ArrayList<Byte> newPacket;
            byte length = PacketList.get(0);
            newPacket = loop(0, length, PacketList);

            while (newPacket.get(2) != id) {
                newPacket = loop(newPacket.size() + 1, newPacket.size() + 2, PacketList);
            }
            return Utils.convertBuffArrayListToArr(newPacket);
        }

        public static ArrayList<Byte> loop(int start, int length, ArrayList<Byte> list) {
            ArrayList<Byte> newPacket = new ArrayList<>();
            for (int i = start; i < length + 5; i++) {
                newPacket.add(list.get(i));
            }
            return newPacket;
        }

        public static boolean IsValidMeterDataPacket(byte[] PacketContent) {
            try {
                // check two packet
                // check less than five length
                if (PacketContent[0] != (byte) 2)
                    return false;

                if (PacketContent[PacketContent.length - 5] != (byte) 33)
                    return false;

                if (PacketContent[PacketContent.length - 4] != (byte) 13)
                    return false;

                if (PacketContent[PacketContent.length - 3] != (byte) 10)
                    return false;

                if (PacketContent[PacketContent.length - 2] != (byte) 3)
                    return false;

                byte ResXor = 0;
                for (int i = 1; i < PacketContent.length - 1; i++)
                    ResXor = (byte) (ResXor ^ PacketContent[i]);

                byte CancelParityMask = 255 - 128;          // full byte - most significant bit  --->   01111111
                ResXor = (byte) (ResXor & CancelParityMask);

                if (PacketContent[PacketContent.length - 1] != ResXor)
                    return false;

                return true;
            } catch (Exception e) {
                return false;
            }
        }
    }

    public static class DataNewDataListPacket extends MeterDataPacket {
        private final static int BphRecordsIndex = 37;
        public static ByteOrder endianType = null;
        public static byte bphRecords;
        public static short eventRecords;
        public static byte moneyTransactionRecords;
        public static short profileRecords;
        public static byte configureMeterRecords;
        public static String meterSerialNumber, meterSerialNumberRepresentation, meterFirmwareVersion;
        public static int MaxBphRecordsPerPacket = 31;//30;
        public static int MaxEventRecordsPerPacket = 24;//25;
        public static int MaxMoneyTransactionRecordsPerPacket = 20;
        public static int MaxProfileRecordsPerPacket = 34;//35;
        public static int MaxConfigureMeterRecordsPerPacket = 34;//30;
        public static boolean IsLittleEndian;
        public static MeterType meterType;
        public boolean _TARIFF_SYS = true;
        public boolean _TRF_BP_HISTORY = true;
        public boolean _TRF_USE_MD_KW = true;
        public boolean _TRF_USE_MD_KVA = true;
        public boolean _TRF_USE_MD_A = true;
        public boolean _PAYMENT_SYS = true;
        public boolean _PYMT_TAX = true;
        public boolean _PYMT_FRNDLY = true;
        public boolean _PYMT_VACATION_TRF = true;
        public boolean _PYMT_MONY_TRANS = true;
        public boolean _PYMT_LOW_TWO_LVL = true;
        public boolean _MTR_MIS_POT_TMPR_FETUR = false;
        public boolean _MTR_LOAD_PROFILE_FETUR = true;
        public boolean _MTR_ENABLE_LMT_FETUR = true;
        public boolean _MTR_PH_SEQ_TMPR = false;
        public byte _MTR_DRV_CODE_FETUR = 15; // Four Bits 0, 1, 2, 3
        public byte _MTR_NUM_OF_CH = 0;
        public boolean _MTR_INDIRECT = false;
        //---------------------------------------------
        public boolean _BTRY_SEL0 = true;
        public boolean _BTRY_SEL1 = false;
        public boolean _BTRY_SEL2 = false;

        //----------------------------------------------------------------------
        public boolean _CTRL_MGNT_SW = true;
        public boolean _CTRL_ACTIONS = true;
        public boolean _CTRL_EVNT_LOG = true;
        public boolean _CTRL_CFG_METER_LOG = true;
        public boolean _CTRL_MD_SW = true;
        public boolean _CTRL_SUPER_CAP = false;    // Not Used
        public boolean _CTRL_BTRY_NON_CHG = false; // Not Used
        public boolean _CTRL_BTRY_CHG = false;     // Not Used
        public boolean _CTRL_CVR_SW = true;
        public boolean _CTRL_TRMNL_SW = true;
        public boolean _CTRL_UP_SW = true;
        public boolean _CTRL_DN_SW = true;
        public boolean _CTRL_TMPR_LED = true;
        public boolean _CTRL_LOW_CRDT_LED = true;
        public boolean _CTRL_RLY = true;
        public boolean _CTRL_BZR = true;
        public boolean _CTRL_ALRM_ICON = true;
        public boolean _CTRL_RTC = true;
        public boolean _CTRL_MGNT_SENSOR = false;
        public boolean _CTRL_GPRS_MODULE_CVR_SW = false;
        public boolean _CTRL_ENCLOSURE_COVER_OPEN = false;
        public boolean isIndirect = false;
        //----------------------------------------------------------------------
        public int PacketSize = 53;
        public int MainMeterDataSize = 44;
        byte minute;
        byte hour;
        byte day;
        byte month;
        byte year;
        byte _MTR_TYPE_FETUR = 1;
        boolean _MTR_REACTIVE_FETUR = false;
        boolean _MTR_RVS_TMPR_FETUR = true;
        boolean _MTR_ERTH_TMPR_FETUR = true;
        private int EndianTypeIndex = 4;
        private int EndianTypeLength = 1;
        private int MinuteIndex = 5;
        private int MinuteLength = 1;
        private int HourIndex = 6;
        private int HourLength = 1;
        private int DayIndex = 7;
        private int DayLength = 1;
        private int MonthIndex = 8;
        private int MonthLength = 1;
        private int YearIndex = 9;
        private int YearLength = 1;
        private int TemplateIDIndex = 16;
        private int TemplateIDLength = 2;
        private int TariffAndPaymentFeaturesIndex = 18;
        private int TariffAndPaymentFeaturesLength = 4;
        private int MeteringFeaturesIndex = 22;
        private int MeteringFeaturesLength = 4;
        private int ControlFeaturesIndex = 26;
        private int ControlFeaturesLength = 4;
        //------------------------------------------------------
        private int DisplayFeaturesIndex = 30;
        private int DisplayFeaturesLength = 2;
        private int CommunicationFeaturesIndex = 32;
        private int CommunicationFeaturesLength = 4;
        private int BphRecordsLength = 1;
        private int EventRecordsIndex = 38;
        private int EventRecordsLength = 2;
        private int MoneyTransactionRecordsIndex = 40;
        private int MoneyTransactionRecordsLength = 1;
        private int ProfileRecordsIndex = 41;
        private int ProfileRecordsLength = 2;
        private int ConfigureMeterRecordsIndex = 43;
        private int ConfigureMeterRecordsLength = 1;
        private int MeterSerialNumberIndex = 44;
        private int MeterSerialNumberLength = 4;
        private short templateID;
        private byte screenOrder = 0;
        private byte lcd = 0; // there bits 1, 2, 3
        private boolean _RFID_FEATURE = true;
        private boolean _PredefinedCards = false;
        private boolean _PostdefinedCards = false;
        //----------------------------------------------------------------------
        private boolean _CardType = false;
        private boolean _GPRS_FEATURE = false;

        public DataNewDataListPacket() {
            super(null);
        }

        public DataNewDataListPacket(boolean IsSinglePhase) {
            super(null);
            if (IsSinglePhase == true) {
                _MTR_TYPE_FETUR = (byte) 1; // Single Phase
                IsLittleEndian = false;
                endianType = ByteOrder.BIG_ENDIAN;
                _MTR_PH_SEQ_TMPR = false;
                _MTR_MIS_POT_TMPR_FETUR = false;
                _MTR_REACTIVE_FETUR = false;
                _MTR_INDIRECT = false;
                _MTR_NUM_OF_CH = 2;
                _MTR_DRV_CODE_FETUR = 15;
                lcd = 0;

                MaxProfileRecordsPerPacket = 34;
            } else {
                _MTR_TYPE_FETUR = (byte) 0; // Three Phase
                IsLittleEndian = true;
                endianType = ByteOrder.LITTLE_ENDIAN;
                _CTRL_GPRS_MODULE_CVR_SW = true;
                _MTR_PH_SEQ_TMPR = true;
                _MTR_MIS_POT_TMPR_FETUR = true;
                _MTR_REACTIVE_FETUR = true;
                _MTR_INDIRECT = false;
                _MTR_NUM_OF_CH = 3;
                _MTR_DRV_CODE_FETUR = 14;
                lcd = 2;

                MaxProfileRecordsPerPacket = 17;
            }
            Calendar cal = Calendar.getInstance();
            minute = (byte) ((byte) cal.get(Calendar.MINUTE));
            hour = (byte) cal.get(Calendar.HOUR);
            day = (byte) cal.get(Calendar.DAY_OF_MONTH);
            month = (byte) cal.get(Calendar.MONTH);
            year = (byte) (cal.get(Calendar.YEAR) - 2000);
        }

        public DataNewDataListPacket(byte[] PacketArray) {
            super(PacketArray);
            // i want to put each group in single structure and change the way i split the data, i want to return bit pattern as string and use this pattern
            try {
                int x = ReadFromByteArray(PacketArray, EndianTypeIndex, EndianTypeLength, endianType);
                endianType = x == 0 ? ByteOrder.LITTLE_ENDIAN : ByteOrder.BIG_ENDIAN;

                //Converter.IsLittleEndian = endianType == ByteOrder.LITTLE_ENDIAN ? true : false;
                meterSerialNumber = Utils.bytesToHex(PacketArray).substring(88, 96);
                meterFirmwareVersion = Utils.bytesToAscii(Utils.bytesToHex(PacketArray).substring(20, 30));
                meterType = getMeterType(meterFirmwareVersion);
                if (meterType == MeterType.InDirect) {
                    isIndirect = true;
                }
                //                    endianType = ByteOrder.LITTLE_ENDIAN;
                //                    endianType = ByteOrder.BIG_ENDIAN;
                IsLittleEndian = meterType == MeterType.Direct || meterType == MeterType.InDirect;


                minute = (byte) ReadFromByteArray(PacketArray, MinuteIndex, MinuteLength, endianType);
                hour = (byte) ReadFromByteArray(PacketArray, HourIndex, HourLength, endianType);
                day = (byte) ReadFromByteArray(PacketArray, DayIndex, DayLength, endianType);
                month = (byte) ReadFromByteArray(PacketArray, MonthIndex, MonthLength, endianType);
                year = (byte) ReadFromByteArray(PacketArray, YearIndex, YearLength, endianType);

//                meterSerialNumberRepresentation = String.valueOf(ReadFromByteArray(PacketArray, MeterSerialNumberIndex, MeterSerialNumberLength, endianType));
                meterSerialNumberRepresentation = String.valueOf(Utils.converArrtoInt(PacketArray, MeterSerialNumberIndex, endianType));

                int TariffAndPaymentFeatures = ReadFromByteArray(PacketArray, TariffAndPaymentFeaturesIndex, TariffAndPaymentFeaturesLength, ByteOrder.LITTLE_ENDIAN); //LittleEndian fixed because this is an array
                _TARIFF_SYS = (TariffAndPaymentFeatures & (int) Math.pow(2, 0)) == 0 ? false : true; //0
                _TRF_BP_HISTORY = (TariffAndPaymentFeatures & (int) Math.pow(2, 1)) == 0 ? false : true; //1
                _TRF_USE_MD_KW = (TariffAndPaymentFeatures & (int) Math.pow(2, 2)) == 0 ? false : true; //2
                _TRF_USE_MD_KVA = (TariffAndPaymentFeatures & (int) Math.pow(2, 3)) == 0 ? false : true; //3
                _TRF_USE_MD_A = (TariffAndPaymentFeatures & (int) Math.pow(2, 4)) == 0 ? false : true; //4
                _PAYMENT_SYS = (TariffAndPaymentFeatures & (int) Math.pow(2, 5)) == 0 ? false : true; //5
                _PYMT_TAX = (TariffAndPaymentFeatures & (int) Math.pow(2, 6)) == 0 ? false : true; //6
                _PYMT_FRNDLY = (TariffAndPaymentFeatures & (int) Math.pow(2, 7)) == 0 ? false : true; //7
                _PYMT_VACATION_TRF = (TariffAndPaymentFeatures & (int) Math.pow(2, 8)) == 0 ? false : true; //8
                _PYMT_MONY_TRANS = (TariffAndPaymentFeatures & (int) Math.pow(2, 9)) == 0 ? false : true; //9
                _PYMT_LOW_TWO_LVL = (TariffAndPaymentFeatures & (int) Math.pow(2, 10)) == 0 ? false : true; //10

                int MeteringFeatures = ReadFromByteArray(PacketArray, MeteringFeaturesIndex, MeteringFeaturesLength, ByteOrder.LITTLE_ENDIAN); //LittleEndian fixed because this is an array
                _MTR_TYPE_FETUR = (byte) ((MeteringFeatures & (int) Math.pow(2, 0)) == 0 ? 0 : 1); //0

                if (_MTR_TYPE_FETUR == 1)
                    MaxProfileRecordsPerPacket = 34;
                else
                    MaxProfileRecordsPerPacket = 17;

                _MTR_REACTIVE_FETUR = (MeteringFeatures & (int) Math.pow(2, 1)) == 0 ? false : true; //1
                _MTR_RVS_TMPR_FETUR = (MeteringFeatures & (int) Math.pow(2, 2)) == 0 ? false : true; //2
                _MTR_ERTH_TMPR_FETUR = (MeteringFeatures & (int) Math.pow(2, 3)) == 0 ? false : true; //3
                _MTR_MIS_POT_TMPR_FETUR = (MeteringFeatures & (int) Math.pow(2, 4)) == 0 ? false : true; //4

                _MTR_LOAD_PROFILE_FETUR = (MeteringFeatures & (int) Math.pow(2, 5)) == 0 ? false : true; //5
                _MTR_ENABLE_LMT_FETUR = (MeteringFeatures & (int) Math.pow(2, 6)) == 0 ? false : true; //6
                _MTR_PH_SEQ_TMPR = (MeteringFeatures & (int) Math.pow(2, 7)) == 0 ? false : true; //7
                _MTR_DRV_CODE_FETUR = (byte) ((MeteringFeatures & (int) (Math.pow(2, 8) + Math.pow(2, 9) + Math.pow(2, 10) + Math.pow(2, 11))) >> 8); // 8, 9, 10, 11
                _MTR_NUM_OF_CH = (byte) ((MeteringFeatures & (int) (Math.pow(2, 12) + Math.pow(2, 13))) == 0 ? 0 : 1); //12,13
                _MTR_INDIRECT = (MeteringFeatures & (int) Math.pow(2, 14)) == 0 ? false : true; //14

                int ControlFeatures = ReadFromByteArray(PacketArray, ControlFeaturesIndex, ControlFeaturesLength, ByteOrder.LITTLE_ENDIAN); //LittleEndian fixed because this is an array
                _BTRY_SEL0 = (ControlFeatures & (int) Math.pow(2, 0)) == 0 ? false : true; //0
                _BTRY_SEL1 = (ControlFeatures & (int) Math.pow(2, 1)) == 0 ? false : true; //1
                _BTRY_SEL2 = (ControlFeatures & (int) Math.pow(2, 2)) == 0 ? false : true; //2
                _CTRL_MGNT_SW = (ControlFeatures & (int) Math.pow(2, 3)) == 0 ? false : true; //3
                _CTRL_ACTIONS = (ControlFeatures & (int) Math.pow(2, 4)) == 0 ? false : true; //4
                _CTRL_EVNT_LOG = (ControlFeatures & (int) Math.pow(2, 5)) == 0 ? false : true; //5
                _CTRL_CFG_METER_LOG = (ControlFeatures & (int) Math.pow(2, 6)) == 0 ? false : true; //6
                _CTRL_MD_SW = (ControlFeatures & (int) Math.pow(2, 7)) == 0 ? false : true; //7
                //_CTRL_SUPER_CAP; //
                //_CTRL_BTRY_NON_CHG; //
                //_CTRL_BTRY_CHG; //
                _CTRL_CVR_SW = (ControlFeatures & (int) Math.pow(2, 8)) == 0 ? false : true; //8
                _CTRL_TRMNL_SW = (ControlFeatures & (int) Math.pow(2, 9)) == 0 ? false : true; //9
                _CTRL_UP_SW = (ControlFeatures & (int) Math.pow(2, 10)) == 0 ? false : true; //10
                _CTRL_DN_SW = (ControlFeatures & (int) Math.pow(2, 11)) == 0 ? false : true; //11
                _CTRL_TMPR_LED = (ControlFeatures & (int) Math.pow(2, 12)) == 0 ? false : true; //12
                _CTRL_LOW_CRDT_LED = (ControlFeatures & (int) Math.pow(2, 13)) == 0 ? false : true; //13
                _CTRL_RLY = (ControlFeatures & (int) Math.pow(2, 14)) == 0 ? false : true; //14
                _CTRL_BZR = (ControlFeatures & (int) Math.pow(2, 15)) == 0 ? false : true; //15
                _CTRL_ALRM_ICON = (ControlFeatures & (int) Math.pow(2, 16)) == 0 ? false : true; //16
                _CTRL_RTC = (ControlFeatures & (int) Math.pow(2, 17)) == 0 ? false : true; //17
                _CTRL_MGNT_SENSOR = (ControlFeatures & (int) Math.pow(2, 18)) == 0 ? false : true; //18
                _CTRL_GPRS_MODULE_CVR_SW = (ControlFeatures & (int) Math.pow(2, 19)) == 0 ? false : true; //19

                short DisplayFeatures = (short) ReadFromByteArray(PacketArray, DisplayFeaturesIndex, DisplayFeaturesLength, ByteOrder.LITTLE_ENDIAN); //LittleEndian fixed because this is an array
                screenOrder = (byte) ((DisplayFeatures & (int) Math.pow(2, 0)) == 0 ? 0 : 1); //0
                lcd = (byte) (DisplayFeatures & (int) (Math.pow(2, 1) + Math.pow(2, 2) + Math.pow(2, 3) + Math.pow(2, 11))); // 1, 2, 3

                int CommunicationFeatures = ReadFromByteArray(PacketArray, CommunicationFeaturesIndex, CommunicationFeaturesLength, ByteOrder.LITTLE_ENDIAN); //LittleEndian fixed because this is an array
                _RFID_FEATURE = (CommunicationFeatures & (int) Math.pow(2, 0)) == 0 ? false : true; //0
                _PredefinedCards = (CommunicationFeatures & (int) Math.pow(2, 1)) == 0 ? false : true; //1
                _PostdefinedCards = (CommunicationFeatures & (int) Math.pow(2, 2)) == 0 ? false : true; //2
                _CardType = (CommunicationFeatures & (int) Math.pow(2, 3)) == 0 ? false : true; //3
                _GPRS_FEATURE = (CommunicationFeatures & (int) Math.pow(2, 8)) == 0 ? false : true; //8

                bphRecords = (byte) ReadFromByteArray(PacketArray, BphRecordsIndex, EndianTypeLength, endianType);
                //eventRecords = (short)ReadFromByteArray(PacketArray, EventRecordsIndex, EventRecordsLength, endianType);
                eventRecords = Utils.converArrtoShort(PacketArray, EventRecordsIndex, endianType);
                moneyTransactionRecords = (byte) ReadFromByteArray(PacketArray, MoneyTransactionRecordsIndex, MoneyTransactionRecordsLength, endianType);
                profileRecords = (short) ((ReadFromRecordsByteArray(PacketArray, ProfileRecordsIndex, ProfileRecordsLength, endianType)) & 0xff);
                configureMeterRecords = (byte) ReadFromByteArray(PacketArray, ConfigureMeterRecordsIndex, ConfigureMeterRecordsLength, endianType);

                Log.d("TAG", "========================== ");
                Log.d("TAG", "bphRecords: " + bphRecords);
                Log.d("TAG", "moneyTransactionRecords: " + moneyTransactionRecords);
                Log.d("TAG", "profileRecords: " + profileRecords);
                Log.d("TAG", "configureMeterRecords: " + configureMeterRecords);
                Log.d("TAG", "========================== ");
                Log.d("TAG", "meterFirmwareVersion: " + meterFirmwareVersion);
                Log.d("TAG", "endianType: " + endianType);
            } catch (Exception ignored) {

            }
        }

        public static MeterType getMeterType(String firmware) {
            if (firmware.toCharArray()[0] == 'c') {
                return MeterType.InDirect;
            } else if (firmware.toCharArray()[0] == 'd' || firmware.toCharArray()[0] == 'a') {
                return MeterType.Direct;
            } else {
                return MeterType.Single;
            }
        }

        public boolean CheckDataNewDataListPacket(byte[] PacketContnt) {
            if (PacketContnt.length != PacketSize)
                return false;

            if (!IsValidMeterDataPacket(PacketContnt))
                return false;

            byte[] DataBlock = GetMeterDataPacketDataBlock(PacketContnt);

            // first two bytes indicates packet size and alwayes it sent in little endian method
            int DataBlockSize = DataBlock[0] | (DataBlock[1] << 8);

            String dataBlockSize = Utils.bytesToHex(new byte[]{(byte) DataBlockSize});
            String dataBlockLength = Utils.bytesToHex(new byte[]{(byte) DataBlock.length});
            if (!dataBlockSize.equals(dataBlockLength)) {
                return false;
            }

            // thired byte is packet type
            if (DataBlock[2] != 30)
                return false;


            return true;
        }

        private int ReadFromByteArray(byte[] arr, int index, int count, ByteOrder EndianType) {
            int value = 0;

            for (int i = 0; i < count; i++) {
                value = value << 8;
                value = value | arr[EndianType == ByteOrder.BIG_ENDIAN ? i + index : count - i - 1 + index];
            }

            return value;
        }

        private int ReadFromRecordsByteArray(byte[] arr, int index, int count, ByteOrder EndianType) {
            int value = 0;

            for (int i = 0; i < count; i++) {
//                value = value << 8;
                value = value | arr[EndianType == ByteOrder.BIG_ENDIAN ? i + index : count - i - 1 + index];
            }

            return value;
        }

        public enum MeterType {
            Single, Direct, InDirect
        }
    }

    public static abstract class UserPacket {
        public abstract byte[] ComposePacket();
    }

    public abstract static class UserDataPacket extends UserPacket {
        public static byte[] AddDataPacketPrefixAndPostfix(byte[] DataBlock) {
            ArrayList<Byte> DataPacketList = new ArrayList<>(Arrays.asList(ArrayUtils.toObject(DataBlock)));
            DataPacketList.add(0, (byte) 2);
            DataPacketList.add((byte) 33);
            DataPacketList.add((byte) 13);
            DataPacketList.add((byte) 10);
            DataPacketList.add((byte) 3);

            byte ResXor = 0;
            for (int i = 1; i < DataPacketList.size(); i++)
                ResXor = (byte) (ResXor ^ DataPacketList.get(i));

            byte CancelParityMask = (byte) (255 - 128);          // full byte - most significant bit  --->   01111111
            ResXor = (byte) (ResXor & CancelParityMask);

            DataPacketList.add(ResXor);           //BCC

            return Utils.convertBuffArrayListToArr(DataPacketList);
        }
    }

    public static class DataRequestDataPacket extends UserDataPacket {
        ByteOrder endianType;
        UserRequestMeterReplyDataPackets requestReplyWithDataPacket;
        private int index;
        private int count;

        public DataRequestDataPacket(UserRequestMeterReplyDataPackets RequestReplyWithDataPacket, ByteOrder EndianType, int index, int count) {
            this.index = index;
            this.count = count;
            this.endianType = EndianType;
            this.requestReplyWithDataPacket = RequestReplyWithDataPacket;
        }

        public DataRequestDataPacket(UserRequestMeterReplyDataPackets RequestReplyWithDataPacket, ByteOrder EndianType) {
            this.endianType = EndianType;
            this.requestReplyWithDataPacket = RequestReplyWithDataPacket;
        }

        @Override
        public byte[] ComposePacket() {
            short DataBlockSizeForRecordsRequest = 7;
            short DataBlockSizeForRawData = 3;

            byte[] DataBlock;

            if (requestReplyWithDataPacket == UserRequestMeterReplyDataPackets.BphRecords ||
                    requestReplyWithDataPacket == UserRequestMeterReplyDataPackets.EventRecords ||
                    requestReplyWithDataPacket == UserRequestMeterReplyDataPackets.MoneyRecords ||
                    requestReplyWithDataPacket == UserRequestMeterReplyDataPackets.ProfileRecords ||
                    requestReplyWithDataPacket == UserRequestMeterReplyDataPackets.ConfigureRecords) {
                DataBlock = new byte[DataBlockSizeForRecordsRequest];
            } else {
                DataBlock = new byte[DataBlockSizeForRawData];
            }


            DataBlock[0] = (byte) DataBlock.length;              // size: first byte
            DataBlock[1] = (byte) (DataBlock.length >>> 8);       // size: second byte
            DataBlock[2] = (byte) requestReplyWithDataPacket.getId();    //type

            if (DataBlock.length == DataBlockSizeForRecordsRequest) {
                //index
                DataBlock[endianType == ByteOrder.LITTLE_ENDIAN ? 3 : 4] = (byte) index;
                DataBlock[endianType == ByteOrder.LITTLE_ENDIAN ? 4 : 3] = (byte) (index >>> 8);

                //count
                DataBlock[endianType == ByteOrder.LITTLE_ENDIAN ? 5 : 6] = (byte) count;
                DataBlock[endianType == ByteOrder.LITTLE_ENDIAN ? 6 : 5] = (byte) (count >>> 8);
            }

            // Add Prefix and Postfix

            return UserDataPacket.AddDataPacketPrefixAndPostfix(DataBlock);
        }
    }

    public static class DataReplyWithDataPacket extends MeterDataPacket {
        public DataReplyWithDataPacket(byte[] PacketArray) {
            super(PacketArray);
        }

        public static boolean CheckDataReplyWithDataPacket(byte[] PacketContent, UserRequestMeterReplyDataPackets UserRequestMeterReplyDataPacket) {
            if (!IsValidMeterDataPacket(PacketContent))
                return false;

            byte[] DataBlock = GetMeterDataPacketDataBlock(PacketContent);

            // first two bytes indicates packet size and alwayes it sent in little endian method
            int DataBlockSize = DataBlock[0] | (DataBlock[1] << 8);
            String dataBlockSize = Utils.bytesToHex(new byte[]{(byte) DataBlockSize});
            String dataBlockLength = Utils.bytesToHex(new byte[]{(byte) DataBlock.length});
            if (!dataBlockSize.equals(dataBlockLength)) {
                return false;
            }


            // thired byte is packet type
            if (DataBlock[2] != (byte) UserRequestMeterReplyDataPacket.getId())
                return false;


            return true;
        }

        public byte[] GetPacketInnerData(byte[] PacketArray) {
            byte[] InnerDataAndDataBlockPrefix = MeterDataPacket.GetMeterDataPacketDataBlock(PacketArray);
            ArrayList<Byte> lstInnerData = new ArrayList<>(Arrays.asList(ArrayUtils.toObject(InnerDataAndDataBlockPrefix)));
            ;
            lstInnerData.remove(0); // remove first byte of data block size
            lstInnerData.remove(0); // remove second byte of data block size
            lstInnerData.remove(0); // remove packet type

            byte[] InnerData = Utils.convertBuffArrayListToArr(lstInnerData);
            return InnerData;
        }
    }

    public static class DataEndCommunicationPacket extends UserDataPacket {
        private final int Type = 0;

        @Override
        public byte[] ComposePacket() {
            ArrayList<Byte> lstDataBlockContent = new ArrayList<>();

            int DataBlockSize = 3;

            lstDataBlockContent.add((byte) DataBlockSize);               // Size : First Byte
            lstDataBlockContent.add((byte) (DataBlockSize >>> 8));        // Size : Second Byte
            lstDataBlockContent.add((byte) Type);                        // Type

            byte[] arrDataPacketContent = UserDataPacket.AddDataPacketPrefixAndPostfix(Utils.convertBuffArrayListToArr(lstDataBlockContent));
            return arrDataPacketContent;
        }
    }

    public static class BillingPeriodHistory {
        @SerializedName("Active Energy")
        public String activeEnergy;
        @SerializedName("Consumption Bill")
        public String consumptionBill;
        @SerializedName("MonthPF")
        public String monthPF;
        @SerializedName("MD (A)")
        public String mdA;
        @SerializedName("MD (W)")
        public String mdW;
        @SerializedName("Reactive Energy")
        public String reactiveEnergy;
        @SerializedName("Date")
        public String date;
        @SerializedName("debit recalc")
        public String debitRecalc;
        @SerializedName("Tax Customer Service")
        public String taxCustomerService;
        @SerializedName("Money Balance")
        public String moneyBalance;
    }

    public static class Event {
        @SerializedName("ser")
        public String ser;
        @SerializedName("Event")
        public String event;
        //@SerializedName("Meter Status")
        //public String meterStatus;
        @SerializedName("DateTime")
        public String dateTime;
    }


    public static class MoneyTransaction implements Comparable<MoneyTransaction> {
        @SerializedName("Money Balance")
        public String balance;
        @SerializedName("Charge Money")
        public String charge;
        @SerializedName("Charge Discharge Number")
        public String chargeDisNum;
        @SerializedName("Type")
        public String type;
        @SerializedName("Interface")
        public String interFace;
        @SerializedName("DateTime")
        public String dateTime;

        @Override
        public int compareTo(MoneyTransaction m) {
            if (Integer.parseInt(chargeDisNum) == Integer.parseInt(m.chargeDisNum))
                return 0;
            else if (Integer.parseInt(chargeDisNum) > Integer.parseInt(m.chargeDisNum))
                return 1;
            else
                return -1;
        }
    }

    public static class DataOne {
        @SerializedName("Name")
        public String name;
        @SerializedName("Total Value")
        public String totalValue;
        @SerializedName("Total Reset")
        public String totalReset;
        @SerializedName("Meter Date")
        public String meterDate;

        @SerializedName("Ph1 Value")
        public String ph1val;
        @SerializedName("Ph1 Reset")
        public String ph1Reset;
        @SerializedName("Ph2 Value")
        public String ph2val;
        @SerializedName("Ph2 Reset")
        public String ph2Reset;
        @SerializedName("Ph3 Value")
        public String ph3val;
        @SerializedName("Ph3 Reset")
        public String ph3Reset;
    }


    public static class DataTwo {
        @SerializedName("Watts Value")
        public String wattsValue;
        @SerializedName("Watts Time")
        public String wattsTime;
        @SerializedName("Watts Period")
        public String wattsPeriod;
        @SerializedName("Amperes Value")
        public String amperesValue;
        @SerializedName("Amperes Time")
        public String amperesTime;
        @SerializedName("Amperes Period")
        public String amperesPeriod;
        @SerializedName("Time Stamp")
        public String timeStamp;

        @SerializedName("VAs Value")
        public String vAsValue;
        @SerializedName("VAs Time")
        public String vAsTime;
        @SerializedName("VAs Period")
        public String vAsPeriod;
    }

    public static class DtAvgPowerFactor {
        @SerializedName("CurrentPowerFactor")
        public String currentPowerFactor;
        @SerializedName("PreviousPowerFactor")
        public String previousPowerFactor;
        @SerializedName("WattsValue")
        public String wattsValue;
        @SerializedName("amperesValue")
        public String AmperesValue;
        @SerializedName("MeterDate")
        public String meterDate;
    }

    public static class MeteringData {
        @SerializedName("one")
        public ArrayList<DataOne> dataOne;
        @SerializedName("two")
        public ArrayList<DataTwo> dataTwo;
    }

    public static class ProfileRecord {
        @SerializedName("ser")
        public String ser;
        @SerializedName("CurrentChannelA")
        public String CurrentChannelA;
        @SerializedName("VoltageChannelA")
        public String VoltageChannelA;
        @SerializedName("PowerFactorChannelA")
        public String PowerFactorChannelA;
        @SerializedName("TotalConsumation")
        public String TotalConsumation;
        @SerializedName("ProfileDateTime")
        public String ProfileDateTime;

        @SerializedName("TotalActive")
        public String TotalActive;
        @SerializedName("TotalReActive")
        public String TotalReActive;
        @SerializedName("CurrentChannelB")
        public String CurrentChannelB;
        @SerializedName("CurrentChannelC")
        public String CurrentChannelC;
        @SerializedName("VoltageChannelB")
        public String VoltageChannelB;
        @SerializedName("VoltageChannelC")
        public String VoltageChannelC;
        @SerializedName("PowerFactorChannelB")
        public String PowerFactorChannelB;
        @SerializedName("PowerFactorChannelC")
        public String PowerFactorChannelC;
    }

    public static class ConfigureMeter {
        @SerializedName("OperatorID")
        public String OperatorID;
        @SerializedName("TemplateID")
        public String TemplateID;
        @SerializedName("Interface")
        public String Interface;
        @SerializedName("ConfMeterDateTime")
        public String ConfMeterDateTime;
    }

    public static class TarrifPaymentData {
        @SerializedName("rowDatatbls")
        public ArrayList<RowDatatbl> rowDatatbls;
        @SerializedName("tarifftbls")
        public ArrayList<Tarifftbl> tarifftbls;
        @SerializedName("daystbls")
        public ArrayList<Daystbl> daystbls;
    }

    public static class RowDatatbl {
        @SerializedName("Data Element")
        public String dataElement;
        @SerializedName("Value")
        public String value;
    }

    public static class Tarifftbl {
        @SerializedName("Tariff No.")
        public String tariffNo;
        @SerializedName("Consumption In KWh")
        public String consumptionInKWh;
        @SerializedName("Consumption of previous In KWh")
        public String consumptionofpreviousInKWh;
    }

    public static class Daystbl {
        @SerializedName("Day")
        public String day;
        @SerializedName("Consumption In KWh")
        public String consumptionInKWh;
        @SerializedName("Billing")
        public String billing;
    }

    public static class ControlTemperDT1 {
        @SerializedName("DataCorruption")
        public String DataCorruption;
        @SerializedName("HardwareErrors")
        public String HardwareErrors;
        @SerializedName("BatteryLowAlarm")
        public String BatteryLowAlarm;
        @SerializedName("LowCreditFirstAlarm")
        public String LowCreditFirstAlarm;
        @SerializedName("LowCreditSecondAlarm")
        public String LowCreditSecondAlarm;
        @SerializedName("NoCreditWithFriendlyAlarm")
        public String NoCreditWithFriendlyAlarm;
        @SerializedName("NoCreditAlarmcount")
        public String NoCreditAlarmcount;
        @SerializedName("OverLoadAlarm")
        public String OverLoadAlarm;
        @SerializedName("OverVoltAlarm")
        public String OverVoltAlarm;
        @SerializedName("UnderVoltAlarm")
        public String UnderVoltAlarm;
        @SerializedName("PhaseSequence")
        public String PhaseSequence;
        @SerializedName("CoverOpen")
        public String CoverOpen;
        @SerializedName("TerminalOpen")
        public String TerminalOpen;
        @SerializedName("MagneticInterferenceHigh")
        public String MagneticInterferenceHigh;
        @SerializedName("ReverseCurrent")
        public String ReverseCurrent;
        @SerializedName("FaultEnergy")
        public String FaultEnergy;
        @SerializedName("MissingPotential")
        public String MissingPotential;
        @SerializedName("GPRSModuleCoverOpenTamperCount")
        public String GPRSModuleCoverOpenTamperCount;
        @SerializedName("Relay")
        public String Relay;
        @SerializedName("Buzzer")
        public String Buzzer;
        @SerializedName("AlarmIconOnLcd")
        public String AlarmIconOnLcd;
        @SerializedName("TamperLed")
        public String TamperLed;
        @SerializedName("CreditLed")
        public String CreditLed;
        @SerializedName("ReadData")
        public String ReadData;
        @SerializedName("MdReset")
        public String MdReset;
        @SerializedName("RfUniqueId")
        public String RfUniqueId;
        @SerializedName("power_on_hours")
        public String power_on_hours;
        @SerializedName("GPRS_Signal_Strength")
        public String GPRS_Signal_Strength;
        @SerializedName("battery_level")
        public String battery_level;
        @SerializedName("MGNT_Level")
        public String MGNT_Level;
        @SerializedName("Power_Interrupt_Counter")
        public String Power_Interrupt_Counter;
        @SerializedName("Time Stamp")
        public String TimeStamp;
        @SerializedName("EventName")
        public String EventName;
    }

    public static class NewControlData {
        @SerializedName("activityType")
        public String activityType;
        @SerializedName("installingTechnicanCode")
        public String installingTechnicanCode;
        @SerializedName("installingDateAndTime")
        public String installingDateAndTime;
        @SerializedName("lastChargeDateTime")
        public String lastChargeDateTime;
        @SerializedName("removedTampers")
        public List<RemovedTampers> RemovedTampers;
    }

    public static class RemovedTampers {
        @SerializedName("tamperId")
        public String tamperId;
        @SerializedName("techID")
        public String techID;
        @SerializedName("transactionID")
        public String transactionID;
        @SerializedName("resetEventDateTime")
        public String resetEventDateTime;
    }

}
