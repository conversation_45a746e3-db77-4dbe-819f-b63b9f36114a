<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/my_custom_background"
    android:backgroundTint="@color/primary_dark"
    android:orientation="vertical"
    android:padding="15dp">

    <LinearLayout
        android:id="@+id/mainLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:text="جاري المزامنة يرجي الانتظار"
            android:textColor="@color/on_primary"
            android:textSize="24sp"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/txtData"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/my_custom_background_2"
            android:gravity="bottom|start"
            android:maxLines="15"
            android:padding="10dp"
            android:scrollbars="vertical"
            android:stackFromBottom="true"
            android:textColor="@color/on_primary"
            android:textDirection="rtl"
            android:textSize="16sp"
            android:textStyle="bold"
            android:transcriptMode="alwaysScroll" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />


            <Button
                android:id="@+id/btnExit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_weight="2"
                android:text="انهاء" />

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />
        </LinearLayout>
    </LinearLayout>


</androidx.appcompat.widget.LinearLayoutCompat>