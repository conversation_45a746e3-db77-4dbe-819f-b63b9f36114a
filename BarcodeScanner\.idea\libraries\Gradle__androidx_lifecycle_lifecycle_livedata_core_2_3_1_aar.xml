<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a4c43c80ab23dc2613922d2230d2aeaa/transformed/lifecycle-livedata-core-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6f7d527932ec192b06d0ebc677d561d6/transformed/lifecycle-livedata-core-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3b805bcb75294e59acfc5425c5e17ac7/transformed/lifecycle-livedata-core-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/de3c82bd9d3609e99d51197cb9cae83e/transformed/lifecycle-livedata-core-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e33936765208c3a86926611888840cb5/transformed/lifecycle-livedata-core-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/de3c82bd9d3609e99d51197cb9cae83e/transformed/lifecycle-livedata-core-2.3.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/de3c82bd9d3609e99d51197cb9cae83e/transformed/lifecycle-livedata-core-2.3.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-livedata-core/2.3.1/38ecd5651d87b6db994df01f93fc72d6e59b846a/lifecycle-livedata-core-2.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>