package com.esmc.protocol.wrap;

import com.esmc.protocol.model.CosemDataHandleType;
import com.esmc.protocol.model.DlmsData;
import com.esmc.protocol.model.ScalerUnit;
import com.esmc.protocol.model.StatusObject;
import com.esmc.protocol.utils.HexUtils;
import com.esmc.protocol.utils.MyConverter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Locale;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/wrap/CosemDataWrapper.class */
public class CosemDataWrapper {
    private static final HashMap<String, CosemDataHandleType> cosemItemHandleTypeMap = new HashMap<>();
    private static final HashMap<CosemDataHandleType, ScalerUnit> cosemDataScalerUnitMap = new HashMap<>();

    static {
        cosemItemHandleTypeMap.put("1:0.0.96.1.0.255:2", CosemDataHandleType.VISIBLE_STRING);
        cosemItemHandleTypeMap.put("8:0.0.1.0.0.255:2", CosemDataHandleType.DATE_TIME);
        cosemItemHandleTypeMap.put("3:1.0.15.8.0.255:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("1:0.0.19.130.0.255:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.130.2.255:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.96.54.70.255:2", CosemDataHandleType.DECIMAL);
        cosemItemHandleTypeMap.put("1:0.0.96.55.0.255:2", CosemDataHandleType.DATE);
        cosemItemHandleTypeMap.put("1:0.0.96.55.1.255:2", CosemDataHandleType.TIME);
        cosemItemHandleTypeMap.put("1:0.0.96.54.29.255:2", CosemDataHandleType.DECIMAL);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.255:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.101:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.102:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.103:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.104:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.105:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.106:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.107:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.108:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.109:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.110:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.111:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("3:1.0.15.9.0.112:2", CosemDataHandleType.ENERGY_ACTIVE);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.255:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.101:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.102:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.103:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.104:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.105:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.106:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.107:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.108:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.109:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.110:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.111:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("1:0.0.19.133.0.112:2", CosemDataHandleType.CURRENCY);
        cosemItemHandleTypeMap.put("4:1.0.15.6.0.255:2", CosemDataHandleType.POWER_ACTIVE);
        cosemItemHandleTypeMap.put("4:1.0.15.6.0.255:5", CosemDataHandleType.DATE_TIME);
        cosemItemHandleTypeMap.put("3:1.0.13.7.0.255:2", CosemDataHandleType.POWER_FACTOR);
        cosemItemHandleTypeMap.put("1:0.0.96.14.1.255:2", CosemDataHandleType.DECIMAL);
        cosemItemHandleTypeMap.put("1:0.0.96.1.7.255:2", CosemDataHandleType.VISIBLE_STRING);
        cosemItemHandleTypeMap.put("1:0.0.96.55.10.255:2", CosemDataHandleType.DATE);
        cosemItemHandleTypeMap.put("1:0.0.96.55.11.255:2", CosemDataHandleType.TIME);
        cosemItemHandleTypeMap.put("1:0.0.96.54.66.255:2", CosemDataHandleType.VISIBLE_STRING);
        cosemItemHandleTypeMap.put("1:1.0.0.2.0.255:2", CosemDataHandleType.VISIBLE_STRING);
        cosemItemHandleTypeMap.put("1:0.0.96.54.0.255:2", CosemDataHandleType.DATE);
        cosemItemHandleTypeMap.put("1:0.0.96.54.20.255:2", CosemDataHandleType.VISIBLE_STRING);
        cosemItemHandleTypeMap.put("3:1.0.31.7.0.255:2", CosemDataHandleType.CURRENT);
        cosemItemHandleTypeMap.put("3:1.0.91.7.0.255:2", CosemDataHandleType.CURRENT);
        cosemItemHandleTypeMap.put("3:1.0.32.7.0.255:2", CosemDataHandleType.VOLTAGE);
        cosemItemHandleTypeMap.put("1:0.0.96.54.72.255:2", CosemDataHandleType.TAMPER_STATUS);
        cosemItemHandleTypeMap.put("1:0.0.96.54.71.255:2", CosemDataHandleType.METER_STATUS);
        cosemDataScalerUnitMap.put(CosemDataHandleType.ENERGY_ACTIVE, new ScalerUnit(-2, "kWh"));
        cosemDataScalerUnitMap.put(CosemDataHandleType.CURRENCY, new ScalerUnit(0, "EGP"));
        cosemDataScalerUnitMap.put(CosemDataHandleType.POWER_ACTIVE, new ScalerUnit(-3, "kW"));
        cosemDataScalerUnitMap.put(CosemDataHandleType.POWER_FACTOR, new ScalerUnit(-3, ""));
        cosemDataScalerUnitMap.put(CosemDataHandleType.CURRENT, new ScalerUnit(-3, "A"));
        cosemDataScalerUnitMap.put(CosemDataHandleType.VOLTAGE, new ScalerUnit(-1, "V"));
    }

    public static void main(String[] args) {
        DlmsData data = new DlmsData(DlmsData.TYPE_OCTETSTRING, "07D0010106022D1FFF800000");
        CosemDataWrapper wrapper = new CosemDataWrapper();
        System.out.println(wrapper.getDateTimeValue(data));
        DlmsData data2 = new DlmsData(DlmsData.TYPE_FLOAT32, "3F8FBE76");
        System.out.println(wrapper.getCurrencyValue(data2));
        DlmsData data3 = new DlmsData(DlmsData.TYPE_I16, "FFFF");
        System.out.println(wrapper.getDecimalValue(data3));
    }

    public Object getCosemDataDisplayValue(String item, DlmsData data) {
        if (!cosemItemHandleTypeMap.containsKey(item)) {
            return data.getDataValue();
        }
        CosemDataHandleType handleType = cosemItemHandleTypeMap.get(item);
        switch (AnonymousClass1.$SwitchMap$com$esmc$protocol$model$CosemDataHandleType[handleType.ordinal()]) {
            case 1:
                return getVisibleStringValue(data);
            case 2:
                return getDateTimeValue(data);
            case 3:
                return getEnergyActiveValue(data);
            case 4:
                return getCurrencyValue(data);
            case 5:
                return getDecimalValue(data);
            case 6:
                return getDateValue(data);
            case 7:
                return getTimeValue(data);
            case 8:
                return getPowerActiveValue(data);
            case HexUtils.time_type_mmhhdd /* 9 */:
                return getPowerFactorValue(data);
            case HexUtils.time_type_wwddmmyy /* 10 */:
                return getCurrentValue(data);
            case HexUtils.time_type_ssmmhh /* 11 */:
                return getVoltageValue(data);
            case HexUtils.time_type_mmhh /* 12 */:
                return getTampersStatusValue(data);
            case HexUtils.time_type_ddmm /* 13 */:
                return getMeterStatusValue(data);
            default:
                return data.getDataValue();
        }
    }

    private Object getMeterStatusValue(DlmsData data) {
        if (!DlmsData.TYPE_U16.equals(data.getDataType())) {
            return null;
        }
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        long code = bi.longValue();
        StatusObject object = new StatusObject();
        object.setMeterStatus(Long.toString(code));
        object.setRelayStatus((code & 2) == 2 ? "Disconnected" : "Connected");
        object.setBatteryStatus((code & 64) == 64 ? "Low Voltage" : "Normal");
        return object;
    }

    private String getTampersStatusValue(DlmsData data) {
        if (!DlmsData.TYPE_U16.equals(data.getDataType())) {
            return "";
        }
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        long code = bi.longValue();
        String status = "";
        if ((code & 1) == 1) {
            status = status + "top cover open,";
        }
        if ((code & 2) == 2) {
            status = status + "terminal cover open,";
        }
        if ((code & 4) == 4) {
            status = status + "current reversal,";
        }
        if ((code & 8) == 8) {
            status = status + "current unbalance,";
        }
        if ((code & 16) == 16) {
            status = status + "overload,";
        }
        if (status.isEmpty()) {
            return "normal";
        }
        return status.substring(0, status.length() - 1);
    }

    private String getVoltageValue(DlmsData data) {
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.VOLTAGE) ? cosemDataScalerUnitMap.get(CosemDataHandleType.VOLTAGE) : new ScalerUnit(-1, "V");
        double f = bi.shortValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f)) + " " + su.getUnit();
    }

    private String getCurrentValue(DlmsData data) {
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.CURRENT) ? cosemDataScalerUnitMap.get(CosemDataHandleType.CURRENT) : new ScalerUnit(-3, "A");
        double f = bi.shortValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f)) + " " + su.getUnit();
    }

    private String getPowerFactorValue(DlmsData data) {
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.POWER_FACTOR) ? cosemDataScalerUnitMap.get(CosemDataHandleType.POWER_FACTOR) : new ScalerUnit(-3, "");
        double f = bi.shortValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f));
    }

    private String getPowerActiveValue(DlmsData data) {
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.POWER_ACTIVE) ? cosemDataScalerUnitMap.get(CosemDataHandleType.POWER_ACTIVE) : new ScalerUnit(-3, "kW");
        double f = bi.intValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f)) + " " + su.getUnit();
    }

    private String getTimeValue(DlmsData data) {
        String s = data.getDataValue();
        if (null == s || s.length() != 8) {
            return null;
        }
        ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(s));
        int hour = input.read();
        int minute = input.read();
        int second = input.read();
        if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
            return null;
        }
        return String.format(Locale.getDefault(), "%02d:%02d:%02d", Integer.valueOf(hour), Integer.valueOf(minute), Integer.valueOf(second));
    }

    private String getDateValue(DlmsData data) {
        String s = data.getDataValue();
        if (null == s || s.length() < 10) {
            return null;
        }
        try {
            ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(s));
            byte[] temp = new byte[2];
            if (input.read(temp) != 2) {
                return null;
            }
            int year = ((temp[0] & 255) * 256) + (temp[1] & 255);
            int month = input.read();
            int day = input.read();
            if (year == 65535 || month < 1 || month > 12 || day < 1 || day > 31) {
                return null;
            }
            return String.format(Locale.getDefault(), "%02d-%02d-%04d", Integer.valueOf(day), Integer.valueOf(month), Integer.valueOf(year));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getDecimalValue(DlmsData data) {
        if (DlmsData.TYPE_I8.equals(data.getDataType())) {
            return Byte.toString((byte) Integer.parseInt(data.getDataValue(), 16));
        }
        if (DlmsData.TYPE_I16.equals(data.getDataType())) {
            return Short.toString((short) Integer.parseInt(data.getDataValue(), 16));
        }
        if (DlmsData.TYPE_I32.equals(data.getDataType())) {
            BigInteger bi = new BigInteger(data.getDataValue(), 16);
            return Integer.toString(bi.intValue());
        } else if (!DlmsData.TYPE_U8.equals(data.getDataType()) && !DlmsData.TYPE_U16.equals(data.getDataType()) && !DlmsData.TYPE_U32.equals(data.getDataType())) {
            return data.getDataValue();
        } else {
            BigInteger bi2 = new BigInteger(data.getDataValue(), 16);
            return Long.toString(bi2.longValue());
        }
    }

    private String getVisibleStringValue(DlmsData data) {
        String s = MyConverter.hexStringToString(data.getDataValue());
        return s.replaceAll("��", "");
    }

    private Object getDateTimeValue(DlmsData data) {
        String s = data.getDataValue();
        if (null == s || s.length() != 24) {
            return null;
        }
        try {
            ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(s));
            byte[] temp = new byte[2];
            if (input.read(temp) != 2) {
                return null;
            }
            int year = ((temp[0] & 255) * 256) + (temp[1] & 255);
            int month = input.read();
            int day = input.read();
            if (input.read() < 0) {
                return null;
            }
            int hour = input.read();
            int minute = input.read();
            int second = input.read();
            if (year == 65535 || month < 1 || month > 12 || day < 1 || day > 31 || hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
                return null;
            }
            return String.format(Locale.getDefault(), "%02d-%02d-%04d %02d:%02d:%02d", Integer.valueOf(day), Integer.valueOf(month), Integer.valueOf(year), Integer.valueOf(hour), Integer.valueOf(minute), Integer.valueOf(second));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getEnergyActiveValue(DlmsData data) {
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.ENERGY_ACTIVE) ? cosemDataScalerUnitMap.get(CosemDataHandleType.ENERGY_ACTIVE) : new ScalerUnit(-2, "kWh");
        double f = bi.longValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f)) + " " + su.getUnit();
    }

    private String getCurrencyValue(DlmsData data) {
        ScalerUnit su = cosemDataScalerUnitMap.containsKey(CosemDataHandleType.CURRENCY) ? cosemDataScalerUnitMap.get(CosemDataHandleType.CURRENCY) : new ScalerUnit(0, "");
        if (DlmsData.TYPE_FLOAT32.equals(data.getDataType())) {
            byte[] vs = MyConverter.hexStringToBytes(data.getDataValue());
            int temp = ((vs[0] << 24) & (-16777216)) | ((vs[1] << 16) & 16711680) | ((vs[2] << 8) & 65280) | (vs[3] & 255);
            double f = Float.intBitsToFloat(temp) * Math.pow(10.0d, su.getScaler());
            DecimalFormat format = new DecimalFormat("0.00");
            return format.format(f) + " " + su.getUnit();
        }
        BigInteger bi = new BigInteger(data.getDataValue(), 16);
        double f2 = bi.longValue() * Math.pow(10.0d, su.getScaler());
        String pattern = "%." + (su.getScaler() < 0 ? Integer.valueOf(Math.abs(su.getScaler())) : "") + "f";
        return String.format(pattern, Double.valueOf(f2)) + " " + su.getUnit();
    }

    /* renamed from: com.esmc.protocol.wrap.CosemDataWrapper$1  reason: invalid class name */
    /* loaded from: EsmcProtocol.jar:com/esmc/protocol/wrap/CosemDataWrapper$1.class */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$com$esmc$protocol$model$CosemDataHandleType = new int[CosemDataHandleType.values().length];

        static {
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.VISIBLE_STRING.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.DATE_TIME.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.ENERGY_ACTIVE.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.CURRENCY.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.DECIMAL.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.DATE.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.TIME.ordinal()] = 7;
            } catch (NoSuchFieldError e7) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.POWER_ACTIVE.ordinal()] = 8;
            } catch (NoSuchFieldError e8) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.POWER_FACTOR.ordinal()] = 9;
            } catch (NoSuchFieldError e9) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.CURRENT.ordinal()] = 10;
            } catch (NoSuchFieldError e10) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.VOLTAGE.ordinal()] = 11;
            } catch (NoSuchFieldError e11) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.TAMPER_STATUS.ordinal()] = 12;
            } catch (NoSuchFieldError e12) {
            }
            try {
                $SwitchMap$com$esmc$protocol$model$CosemDataHandleType[CosemDataHandleType.METER_STATUS.ordinal()] = 13;
            } catch (NoSuchFieldError e13) {
            }
        }
    }
}
