package com.sewedy.electrometerparser.protocol;

public class MainMeterDataParser {
    private static MainMeterDataParser instance = null;
    Shared.DataNewDataListPacket dataNewDataListPacket;
    byte[] packetArray;
    String customerCode;

    MainMeterDataParser() {
    }

    synchronized public static MainMeterDataParser getInstance() {
        if (instance == null) {
            instance = new MainMeterDataParser();
        }
        return instance;
    }

    public static void reset() {
        instance = new MainMeterDataParser();
    }

    public void parseData(byte[] packetArray) {
        this.packetArray = packetArray;
        this.dataNewDataListPacket = new Shared.DataNewDataListPacket(packetArray);
    }

    public Shared.DataNewDataListPacket.MeterType getMeterType() {
        return Shared.DataNewDataListPacket.meterType;
    }

    public Shared.DataNewDataListPacket getData() {
        return this.dataNewDataListPacket;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public byte[] getPacketArray() {
        return packetArray;
    }

    public void setPacketArray(byte[] packetArray) {
        this.packetArray = packetArray;
    }
}
