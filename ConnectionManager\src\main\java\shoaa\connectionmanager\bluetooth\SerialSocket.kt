package shoaa.connectionmanager.bluetooth

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import java.io.IOException
import java.security.InvalidParameterException
import java.util.Arrays
import java.util.UUID
import java.util.concurrent.Executors

/**
 * Created by Islam Darwish
 */
class SerialSocket(context: Context, device: BluetoothDevice) : Runnable {
    private val disconnectBroadcastReceiver: BroadcastReceiver
    private val context: Context
    private val device: BluetoothDevice
    private var listener: SerialListener? = null
    private var socket: BluetoothSocket? = null
    private var connected = false

    init {
        if (context is Activity) throw InvalidParameterException("expected non UI context")
        this.context = context
        this.device = device
        disconnectBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val action = intent.action
                if (action != null) {
                    if (action == BluetoothAdapter.ACTION_STATE_CHANGED) {
                        val state =
                            intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                        if (state == BluetoothAdapter.STATE_OFF) {
                            _disconnect()
                        }
                    }
                }
            }
        }
    }

    private fun _disconnect() {
        if (listener != null) listener!!.onSerialIoError(IOException("background disconnect"))
        disconnect() // disconnect now, else would be queued until UI re-attached
    }

    val name: String
        get() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    return ""
                }
            }
            return if (device.name != null) device.name else device.address
        }

    /**
     * connect-success and most connect-errors are returned asynchronously to listener
     */
    fun connect(listener: SerialListener?) {
        this.listener = listener
        val filter = IntentFilter()
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(disconnectBroadcastReceiver, filter, Context.RECEIVER_EXPORTED)
        } else {
            context.registerReceiver(disconnectBroadcastReceiver, filter)
        }
        Executors.newSingleThreadExecutor().submit(this)
    }

    fun disconnect() {
        listener = null // ignore remaining data and errors
        // connected = false; // run loop will reset connected
        if (socket != null) {
            try {
                socket!!.close()
            } catch (ignored: Exception) {
            }
            socket = null
        }
        try {
            context.unregisterReceiver(disconnectBroadcastReceiver)
        } catch (ignored: Exception) {
        }
    }

    @Throws(IOException::class)
    fun write(data: ByteArray) {
        if (!connected) throw IOException("not connected")
        socket!!.outputStream.write(data)
    }

    override fun run() { // connect & read
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    return
                }
            }
            socket = device.createRfcommSocketToServiceRecord(BLUETOOTH_SPP)
            //            socket = device.createRfcommSocketToServiceRecord(device.getUuids()[0].getUuid());
            socket!!.connect()
            if (listener != null) listener!!.onSerialConnect()
        } catch (e: Exception) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(
                            context,
                            Manifest.permission.BLUETOOTH_CONNECT
                        ) != PackageManager.PERMISSION_GRANTED
                    ) {
                        return
                    }
                }
                //                 socket = device.createRfcommSocketToServiceRecord(BLUETOOTH_SPP);
                socket = device.createRfcommSocketToServiceRecord(device.uuids[0].uuid)
                socket!!.connect()
                if (listener != null) listener!!.onSerialConnect()
            } catch (e1: Exception) {
                if (listener != null) listener!!.onSerialConnectError(e)
                try {
                    socket!!.close()
                } catch (ignored: Exception) {
                }
                socket = null
                return
            }
        }
        connected = true
        try {
            val buffer = ByteArray(1024)
            var len: Int
            while (true) {
                len = socket!!.inputStream.read(buffer)
                val data = Arrays.copyOf(buffer, len)
                if (listener != null) listener!!.onSerialRead(data)
            }
        } catch (e: Exception) {
            connected = false
            if (listener != null) listener!!.onSerialIoError(e)
            try {
                socket!!.close()
            } catch (ignored: Exception) {
            }
            socket = null
        }
    }

    companion object {
        private val BLUETOOTH_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }
}
