package com.sewedy.electrometerparser.protocol;

import static com.sewedy.electrometerparser.protocol.Shared.DataReplyWithDataPacket.CheckDataReplyWithDataPacket;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.BphRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ConfigureRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ControlRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.MeteringRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.MoneyRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.ProfileRecords;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.TariffRawData;
import static com.sewedy.electrometerparser.protocol.Shared.UserRequestMeterReplyDataPackets.newControlRawData;

import android.bluetooth.BluetoothSocket;
import android.util.Log;

import com.sewedy.electrometerparser.protocol.connection.bluetooth.ConnectionHandler;
import com.sewedy.electrometerparser.protocol.connection.bluetooth.ConnectionTypes;
import com.sewedy.electrometerparser.protocol.connection.bluetooth.DataCallback;
import com.sewedy.electrometerparser.protocol.connection.bluetooth.Packet;

import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Locale;

public class ZpaMeterDataRetriever {
    StringBuilder buffer = new StringBuilder();
    int readRecords = 0;
    int successRate = 0;
    Parser parser;
    MeterData meterData;
    Status upcomingStatus = Status.PARSE_METER_CONNECTION_STEP_1;
    GetSho3a3MeterDataCallback sho3a3MeterDataCallback;
    boolean isBeforeFinished = false;
    private int numOfRecords = -1;
    private ConnectionHandler connectionHandler;
    DataCallback dataCallback = new DataCallback() {
        @Override
        public void onResult(Packet data) {
            try {
                switch (upcomingStatus) {
                    case PARSE_METER_CONNECTION_STEP_1:
                        parseMeterConnectionStep1(data);
                        break;
                    case PARSE_CONNECTION_DATA_STEP2:
                        parseMeterConnectionStep2(data);
                        break;
                    case PARSE_METERING:
                        parseMeteringData(data);
                        break;
                    case PARSE_TAREF:
                        parseTarriffData(data);
                        break;
                    case PARSE_TAMPER:
                        parseTamperData(data);
                        break;
                    case PARSE_METER_CONFIG:
                        parseConfigurations(data);
                        break;
                    case PARSE_PROFILE:
                        parseProfiles(data);
                        break;
                    case PARSE_MONEY:
                        parseMoneyTransactions(data);
                        break;
                    case PARSE_BILLING:
                        parseBillingData(data);
                        break;
                    case PARSE_CONTROL:
                        parseControlData(data);
                        break;
                }
            } catch (Exception e) {
                meterData.setMessage("Error:" + e.getMessage());
            }
        }

    };

    public ZpaMeterDataRetriever(BluetoothSocket bluetoothSocket) {
        parser = new Parser();
        connectionHandler = new ConnectionHandler(bluetoothSocket, dataCallback);
    }

    public void getAllData(GetSho3a3MeterDataCallback sho3a3MeterDataCallback) {
        try {
            this.sho3a3MeterDataCallback = sho3a3MeterDataCallback;
            meterData = MeterData.getInstance();
            isBeforeFinished = false;
            finalizeCurrentProcess();
            writeBaudRate300();
        } catch (Exception e) {
            meterData.setMessage("Error:" + e.getMessage());
        }
    }

    private void writeBaudRate300() {
        connectionHandler.writingZpa300(ConnectionTypes.baudTran_300_7_ZPA, 0);
        Utils.sleep(30);
        writeMeterConnectionStep1();
    }

    private void writeMeterConnectionStep1() {
        upcomingStatus = Status.PARSE_METER_CONNECTION_STEP_1;
        byte[] requestPacket = {47, 63, 33, 13, 10};
        Log.d("TAG", "Start**************");
        connectionHandler.writing(requestPacket, 16, false);
    }

    private void parseMeterConnectionStep1(Packet packetModel) {
        byte[] data = Utils.hexStringToByteArray(packetModel.getData());
        String customerCode = "";
        for (int i = 5; i < data.length - 2; i++) {
            customerCode += (char) (data[i] & 0xFF);
        }
        meterData.setCustomerCode(customerCode);
        Log.d("CustomerCode", customerCode);
        writeMeterConnectionStep2();
    }


    private void writeMeterConnectionStep2() {
        upcomingStatus = Status.PARSE_CONNECTION_DATA_STEP2;
        byte[] arrAcknowledgement = {6, 48, 52, 54, 13, 10};
        connectionHandler.zpaWritingWith4800(arrAcknowledgement, 53, false);
//        connectionHandler.writing(arrAcknowledgement, 53, false);
    }

    private void parseMeterConnectionStep2(Packet packetModel) {
        MainMeterDataParser.getInstance().parseData(Utils.hexStringToByteArray(packetModel.getData()));
        Shared.DataNewDataListPacket newDataListPkt = new Shared
                .DataNewDataListPacket(Utils.hexStringToByteArray(packetModel.getData()));
        upcomingStatus = Status.PARSE_METERING;
        writeMeteringCommand();

    }

    private void writeMeteringCommand() {
        Log.d("TAG", "write Metering Command ");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(MeteringRawData,
                Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        connectionHandler.writing(new byte[]{0x02, 0x04, 0x00, 0x20, 0x00, 0x21, 0x0d, 0x0a, 0x03, 0x01}, 0, false);

    }

    private void writeTarriffCommand() {
        Log.d("TAG", "write Tarrif Command ");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(TariffRawData, Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        connectionHandler.writing(arrRequestData, 0, false);
    }

    private void parseMeteringData(Packet packetModel) {
        try {
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packetModel.getData()), MeteringRawData)) {
                Log.d("TAG", "Parse Metering Data: success");
                Shared.MeteringData meteringData = parser.splitMeteringData(MainMeterDataParser.getInstance().getData(),
                        Utils.hexStringToByteArray(packetModel.getData()));
                meterData.setMeteringData(meteringData);
            } else {
                Log.d("TAG", "Parse Metering Data: fail");
            }
        } catch (Exception e) {
            successRate++;
            e.printStackTrace();
        } finally {
            upcomingStatus = Status.PARSE_TAREF;
            writeTarriffCommand();
        }


    }

    private void parseTarriffData(Packet packetModel) {
        try {
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packetModel.getData()), TariffRawData)) {
                Log.d("TAG", "Parse Tarriff Data: success");
                Shared.TarrifPaymentData tarrifPaymentData = parser.SplitTarifAndPaymentData(Utils.hexStringToByteArray(packetModel.getData()), MainMeterDataParser.getInstance().getData());
                meterData.setTarrifPaymentData(tarrifPaymentData);
            } else {
                Log.d("TAG", "Parse Tarriff Data: failed");
            }

        } catch (Exception e) {
            successRate++;
            e.printStackTrace();
        } finally {
            upcomingStatus = Status.PARSE_TAMPER;
            writeTamperCommand();
        }
    }

    private void writeTamperCommand() {
        Log.d("TAG", "write Tamper Command");
        Shared.DataRequestDataPacket RequestDataPacketPkt = new Shared.DataRequestDataPacket(ControlRawData, Shared.DataNewDataListPacket.endianType);
        byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
        connectionHandler.writing(arrRequestData, 0, false);
    }

    private void parseTamperData(Packet packetModel) {
        try {
            Shared.ControlTemperDT1 controlTemperDT1 = null;
            if (CheckDataReplyWithDataPacket(Utils.hexStringToByteArray(packetModel.getData()), ControlRawData)) {
                Log.d("TAG", "Parse Control Temper Data: success");
                controlTemperDT1 = parser.SplitControlAndTamperData(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(packetModel.getData()));
                meterData.setControlTemperDT1(controlTemperDT1);
            } else {
                Log.d("TAG", "Parse Control Temper Data: failed");
            }
        } catch (Exception e) {
            successRate++;
            e.printStackTrace();
        } finally {
            upcomingStatus = Status.PARSE_BILLING;
            writeBillingHistory();
        }
    }

    private void writeBillingHistory() {
        readRecords = 0;
        numOfRecords = -1;
        Log.d("BillingRecords:", MainMeterDataParser.getInstance().getData().bphRecords + "");
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().bphRecords != 0) {
            readMultipleRecords(BphRecords);
        } else {
            upcomingStatus = Status.PARSE_MONEY;
            buffer = new StringBuilder();
            writeMoneyTransactions();
        }
    }

    private void parseBillingData(Packet packetModel) {
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            ArrayList<Shared.BillingPeriodHistory> billingPeriodHistories = parser.SplitBPH(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(buffer.toString()));
            if (billingPeriodHistories == null) {
                successRate++;
            } else {
                meterData.setBillingPeriodHistories(billingPeriodHistories);
            }
            upcomingStatus = Status.PARSE_MONEY;
            buffer = new StringBuilder();
            writeMoneyTransactions();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_BILLING;
            writeBillingHistory();
        }
    }

    private void writeMoneyTransactions() {
        readRecords = 0;
        numOfRecords = -1;
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().moneyTransactionRecords != 0) {
            readMultipleRecords(MoneyRecords);
        } else {
            upcomingStatus = Status.PARSE_PROFILE;
            buffer = new StringBuilder();
            writeProfiles();
        }
    }

    private void parseMoneyTransactions(Packet packetModel) {
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            ArrayList<Shared.MoneyTransaction> moneyTransactions = parser.SplitMoneyTransaction(Utils.hexStringToByteArray(buffer.toString()));
            if (moneyTransactions == null) {
                successRate++;
            } else {
                meterData.setMoneyTransactions(moneyTransactions);
            }
            // TODO change here
//            upcomingStatus = Status.PARSE_PROFILE;
//            buffer = new StringBuilder();
//            writeProfiles();

            upcomingStatus = Status.PARSE_CONTROL;
            buffer = new StringBuilder();
            writeControlData();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_MONEY;
            writeMoneyTransactions();
        }
    }

    private boolean isNewControlData() {
        String firmware = MainMeterDataParser.getInstance().getData().meterFirmwareVersion;
        String[] parts = firmware.split("\\.");
        if (parts[0].toLowerCase(Locale.ROOT).equals("f")
                || parts[0].toLowerCase(Locale.ROOT).equals("a")
                || parts[0].toLowerCase(Locale.ROOT).equals("b")
                || parts[0].toLowerCase(Locale.ROOT).equals("n")
                || parts[0].toLowerCase(Locale.ROOT).equals("c"))
            return false;
        else {
            switch (parts[0].toLowerCase(Locale.ROOT)) {
//                case "n": {
//                    int model = Integer.parseInt(parts[1]);
//                    return model < 0 || model > 328;
//                }
                case "d":
                case "2": {
                    int model = Integer.parseInt(parts[1]);
                    return model < 0 || model > 458;
                }
            }
        }
        return false;
    }

    private void writeControlData() {
        readRecords = 0;
        numOfRecords = -1;
        if (!isNewControlData()) {
            if (successRate != 0) {
                meterData.setResult("False");
                meterData.setMessage("Failed");
            } else {
                meterData.setResult("True");
                isBeforeFinished = true;
                this.sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
            }
        } else {
            Log.d("TAG", "new Control Data");
            Shared.DataRequestDataPacket RequestDataPacketPkt =
                    new Shared.DataRequestDataPacket(newControlRawData,
                            Shared.DataNewDataListPacket.endianType);
            byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
            connectionHandler.writing(arrRequestData, 0, true);
        }
    }

    private void parseControlData(Packet packetModel) {
        Shared.NewControlData newControlData = parser.SplitNewControlData(MainMeterDataParser.getInstance().getData(), Utils.hexStringToByteArray(packetModel.getData()));
        if (newControlData == null) {
            Log.d("TAG", "getNewControlData: parsing fail");
            successRate++;
        } else {
            meterData.setNewControlData(newControlData);
        }

        if (successRate != 0) {
            meterData.setResult("False");
            meterData.setMessage("Failed");
        } else {
            meterData.setResult("True");
            isBeforeFinished = true;
            this.sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
        }

    }

    private void writeProfiles() {
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().profileRecords != 0) {
            readMultipleRecords(ProfileRecords);
        } else {
            upcomingStatus = Status.PARSE_METER_CONFIG;
            buffer = new StringBuilder();
            writeConfigData();
        }
    }

    private void parseProfiles(Packet packetModel) {
        readRecords = 0;
        numOfRecords = -1;
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            ArrayList<Shared.ProfileRecord> profileRecords = parser.SplitProfileRecord(Utils.hexStringToByteArray(buffer.toString()));
            if (profileRecords == null) {
                successRate++;
            } else {
                meterData.setProfileRecords(profileRecords);
            }
            upcomingStatus = Status.PARSE_METER_CONFIG;
            buffer = new StringBuilder();
            writeConfigData();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_PROFILE;
            writeProfiles();
        }
    }

    private void writeConfigData() {
        if (null != MainMeterDataParser.getInstance().getData() && MainMeterDataParser.getInstance().getData().configureMeterRecords != 0) {
            readMultipleRecords(ConfigureRecords);
        } else {
            upcomingStatus = Status.PARSE_CONTROL;
            writeControlData();
        }
    }

    private void parseConfigurations(Packet packetModel) {
        readRecords = 0;
        numOfRecords = -1;
        if (readRecords >= numOfRecords) {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            ArrayList<Shared.ConfigureMeter> configureMeters = parser.splitConfigureMeter(Utils.hexStringToByteArray(buffer.toString()));
            if (configureMeters == null) {
                successRate++;
            } else {
                meterData.setConfigureMeters(configureMeters);
            }
            buffer = new StringBuilder();
            upcomingStatus = Status.PARSE_CONTROL;
            writeControlData();
        } else {
            byte[] arr = Utils.hexStringToByteArray(packetModel.getData());
            Shared.DataReplyWithDataPacket ReplyWithDataPkt = new Shared.DataReplyWithDataPacket(arr);
            buffer.append(Utils.bytesToHex(ReplyWithDataPkt.GetPacketInnerData(arr)));
            upcomingStatus = Status.PARSE_METER_CONFIG;
            writeConfigData();
        }
    }

    public Shared.DataNewDataListPacket.MeterType getMeterType() {
        return Shared.DataNewDataListPacket.meterType;
    }

    private void readMultipleRecords(Shared.UserRequestMeterReplyDataPackets userRequestMeterReplyDataPacket) {
        try {
            int recordsCount = 0;
            int maxNumberOfRecords = 0;
            switch (userRequestMeterReplyDataPacket) {
                case BphRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().bphRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxBphRecordsPerPacket;
                    break;

                case EventRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().eventRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxEventRecordsPerPacket;
                    break;

                case MoneyRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().moneyTransactionRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxMoneyTransactionRecordsPerPacket;
                    break;

                case ProfileRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().profileRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxProfileRecordsPerPacket;
                    break;

                case ConfigureRecords:
                    recordsCount = MainMeterDataParser.getInstance().getData().configureMeterRecords;
                    maxNumberOfRecords = Shared.DataNewDataListPacket.MaxConfigureMeterRecordsPerPacket;
                    break;
                default:
                    recordsCount = 1;
                    maxNumberOfRecords = 1;
                    break;
            }
            if (userRequestMeterReplyDataPacket == ProfileRecords) {
                readRecords = recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0) - 1;
            }

            numOfRecords = recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0);

            if (readRecords < recordsCount / maxNumberOfRecords + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0)) {
                ByteOrder endian = MainMeterDataParser.getInstance().getData().endianType;
                Shared.DataRequestDataPacket RequestDataPacketPkt =
                        new Shared.DataRequestDataPacket(userRequestMeterReplyDataPacket,
                                endian,
                                readRecords * maxNumberOfRecords,
                                (readRecords + 1) * maxNumberOfRecords > recordsCount ? recordsCount % maxNumberOfRecords : maxNumberOfRecords);
                byte[] arrRequestData = RequestDataPacketPkt.ComposePacket();
                Log.d("TAG", userRequestMeterReplyDataPacket.name() + " " + Utils.bytesToHex(arrRequestData));
                readRecords++;
                connectionHandler.writing(arrRequestData, 0, false);
            }
            boolean isFinishReadingMultipleRecords = readRecords >= ((recordsCount / maxNumberOfRecords) + (recordsCount % maxNumberOfRecords > 0 ? 1 : 0));
            if (isFinishReadingMultipleRecords) {
                readRecords = 0;
                numOfRecords = -1;
            }
        } catch (Exception exception) {
            readRecords = 0;
            numOfRecords = -1;
        }
    }

    private void finalizeCurrentProcess() {
        new java.util.Timer().schedule(
                new java.util.TimerTask() {
                    @Override
                    public void run() {
                        if (!isBeforeFinished) {
                            meterData.setResult("False");
                            meterData.setMessage("Failed");
                            sho3a3MeterDataCallback.onGetSho3a3MeterData(meterData);
                        }
                    }
                },
                50000
        );
    }

    enum Status {
        PARSE_METER_CONNECTION_STEP_1,
        PARSE_CONNECTION_DATA_STEP2,
        PARSE_METERING,
        PARSE_TAREF,
        PARSE_TAMPER,
        PARSE_METER_CONFIG,
        PARSE_CONTROL,
        PARSE_PROFILE,
        PARSE_MONEY,
        PARSE_BILLING
    }
}
