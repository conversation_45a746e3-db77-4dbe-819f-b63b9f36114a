package shoaa.connectionmanager.usbserial;

import android.content.Context;
import android.hardware.usb.UsbDeviceConnection;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.common.primitives.Bytes;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import shoaa.common.Utils;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.TextUtil;
import shoaa.connectionmanager.usbserial.driver.UsbSerialDriver;
import shoaa.connectionmanager.usbserial.driver.UsbSerialPort;
import shoaa.connectionmanager.usbserial.driver.UsbSerialProber;
import shoaa.connectionmanager.usbserial.util.SerialInputOutputManager;

public class UsbHandler implements SerialInputOutputManager.Listener {

    public boolean connected = false;
    Handler mainLooper = new Handler(Looper.getMainLooper());
    String session = "";
    private List<Byte> packet = new ArrayList<>();
    private int mReadTimeOut;
    private int mExpectedLength;
    private int mSendTimeOut;
    private int mRetryCount;
    private int mTryCount;
    private long mSendTime;
    private long mReadTime;
    private SerialInputOutputManager usbIoManager;
    private UsbSerialPort usbSerialPort;
    private int portNum;
    private UsbDeviceItem deviceItem;
    private BaudRate baudRate = BaudRate.BAUD_9600_8_N_1;

    /*
     * Serial + UI
     */
    public int connect(UsbDeviceItem deviceItem) {
        this.deviceItem = deviceItem;
        this.portNum = deviceItem.getPort();
        UsbSerialDriver driver = UsbSerialProber.getDefaultProber().probeDevice(deviceItem.getDevice());
        if (driver == null) {
            driver = CustomProber.getCustomProber().probeDevice(deviceItem.getDevice());
        }
        if (driver == null) {
            return -1;
        }
        if (driver.getPorts().size() < portNum) {
            return -2;
        }
        usbSerialPort = driver.getPorts().get(portNum);
        UsbDeviceConnection usbConnection = ConnectionManager.Companion.getInstance().usbManager.openDevice(driver.getDevice());
        if (usbConnection == null) {
            if (!ConnectionManager.Companion.getInstance().usbManager.hasPermission(driver.getDevice()))
                return -3;
            else return -4;
        }

        try {
            usbSerialPort.open(usbConnection);
            try {
                usbSerialPort.setParameters(baudRate.getBaud(), baudRate.dataBit(), baudRate.stopBit(), baudRate.parity());
            } catch (UnsupportedOperationException e) {
                return -5;
            }
            usbIoManager = new SerialInputOutputManager(usbSerialPort, this);
            usbIoManager.start();
            connected = true;
            return 0;
        } catch (Exception e) {
            disconnect();
            return -5;
        }
    }

    public void disconnect() {
        connected = false;
        if (usbIoManager != null) {
            usbIoManager.setListener(null);
            usbIoManager.stop();
        }
        usbIoManager = null;
        try {
            usbSerialPort.close();
        } catch (IOException ignored) {
        }
        usbSerialPort = null;
    }

    /*
     * Serial
     */
    @Override
    public void onNewData(byte[] data) {
        mainLooper.post(() -> {
            receive(data);
        });
    }

    @Override
    public void onRunError(Exception e) {
        mainLooper.post(this::disconnect);
    }


    private void receive(byte[] data) {
        packet.addAll(Bytes.asList(data));
        mReadTime = System.currentTimeMillis();
    }


    private String send(String hexString) {
        CompletableFuture<String> supply = CompletableFuture.supplyAsync(() -> {
            while (mTryCount <= mRetryCount) {
                try {
                    session += "send: " + hexString + "\n";
                    usbSerialPort.write(TextUtil.INSTANCE.fromHexString(hexString), mSendTimeOut);
                    int wait = ConnectionManager.Companion.getInstance().calculateSendingTimeMs(baudRate, TextUtil.INSTANCE.fromHexString(hexString).length);
                    try {
                        Thread.sleep(wait);
                    } catch (Exception ignore) {
                    }
                    mSendTime = System.currentTimeMillis();
                    while ((System.currentTimeMillis() <= mSendTime + mSendTimeOut) && mReadTime == 0) {
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    if (mReadTime == 0) {
                        mTryCount++;
                        continue;
                    }
                    while ((System.currentTimeMillis() <= mReadTime + mReadTimeOut)) {
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    break;
                } catch (IOException e) {
                    mTryCount++;
                    try {
                        Thread.sleep(500);
                    } catch (Exception ignored) {
                    }
                }
            }
            String res = TextUtil.INSTANCE.toHexString(Bytes.toArray(packet)).replaceAll(" ", "");
            session += "res: " + res + "\n\n";
            return res;
        });
        try {
            return supply.get();
        } catch (Exception e) {
            session += e.getMessage();
            return "";
        }
    }

    public String sendAsync(String hexString, int retryCount, int sendTimeOut, int readTimeOut) {
        if (!connected) {
            session += "not connected" + "\n\n";
            return "";
        }
        packet = new ArrayList<>();
        mTryCount = 0;
        mReadTime = 0;
        mSendTime = 0;
        mRetryCount = retryCount;
        mSendTimeOut = sendTimeOut;
        mReadTimeOut = readTimeOut;
        return send(hexString.replaceAll(" ", ""));
    }

    public void sendAsyncNoRes(  String hexString) {
        try {
            session += "sendAsyncNoRes: " + hexString + "\n";
            usbSerialPort.write(TextUtil.INSTANCE.fromHexString(hexString), mSendTimeOut);
            int wait = ConnectionManager.Companion.getInstance().calculateSendingTimeMs(baudRate, TextUtil.INSTANCE.fromHexString(hexString).length);
            try {
                Thread.sleep(wait);
            } catch (Exception ignore) {
            }
        } catch (Exception ignored) {
        }
    }
    public boolean reset() {
        try {
            disconnect();
            Thread.sleep(1000);
            try {
                connected = false;
                return connect(this.deviceItem) == 0;
            } catch (Exception e) {
                connected = false;
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public boolean setBaudRateAsync(@NotNull BaudRate baudRate) {
        try {
            session += baudRate.name() + "\n\n";
            usbSerialPort.setParameters(baudRate.getBaud(), baudRate.dataBit(), baudRate.stopBit(), baudRate.parity());
            this.baudRate = baudRate;
            return true;
        } catch (IOException e) {
            session += e.getMessage() + "\n\n";
            return false;
        }
    }


    private String sendWithLength(String hexString) {
        CompletableFuture<String> supply = CompletableFuture.supplyAsync(() -> {
            while (mTryCount <= mRetryCount) {
                try {
                    if (!connected) {
                        return "";
                    }
                    usbSerialPort.write(TextUtil.INSTANCE.fromHexString(hexString), mSendTimeOut);
                    int wait = ConnectionManager.Companion.getInstance().calculateSendingTimeMs(baudRate, TextUtil.INSTANCE.fromHexString(hexString).length);
                    try {
                        Thread.sleep(wait);
                    } catch (Exception ignore) {
                    }
                    mSendTime = System.currentTimeMillis();
                    while ((System.currentTimeMillis() <= mSendTime + mSendTimeOut) && mReadTime == 0) {
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    if (mReadTime == 0) {
                        mTryCount++;
                        continue;
                    }
                    while ((System.currentTimeMillis() <= mReadTime + mReadTimeOut)
                            && (mExpectedLength == -1 || packet.size() < mExpectedLength)) {
                        if (mExpectedLength == -1 && packet != null && packet.size() > 1) {
                            mExpectedLength = (packet.get(1) & 0xFF) + ((packet.get(2) & 0xFF) * (256));
//                            expectedLength += 5;
                            mExpectedLength += 6;
                        }
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    break;
                } catch (IOException e) {
                    mTryCount++;
                    try {
                        Thread.sleep(500);
                    } catch (Exception ignored) {
                    }
                }
            }
            String res = TextUtil.INSTANCE.toHexString(Bytes.toArray(packet)).replaceAll(" ", "");
            session += "sendWithLength (" + mExpectedLength + ") : " + hexString + "\n";
            session += "res (" + res.trim().length() / 2 + "): " + res + "\n\n";
            return res;
        });
        try {
            return supply.get();
        } catch (Exception e) {
            session += e.getMessage();
            return "";
        }
    }

    public String sendWithLengthAsync(String hexString, int retryCount, int sendTimeOut, int expectedLength) {
        if (!connected) {
            session += "not connected";
            return "";
        }
        packet = new ArrayList<>();
        mTryCount = 0;
        mReadTime = 0;
        mSendTime = 0;
        mRetryCount = retryCount;
        mSendTimeOut = sendTimeOut;
        mReadTimeOut = expectedLength > 0 ? 800 : 2000;
        mExpectedLength = expectedLength > 0 ? expectedLength : -1;
        String res = "";
        while ((res.length() / 2 < (mExpectedLength - 1) || mExpectedLength == -1) && mTryCount <= mRetryCount) {
            res = sendWithLength(hexString.replaceAll(" ", ""));
            mTryCount++;
        }
//        try {
//            return res.substring(0, expectedLength != 0 ? expectedLength * 2 : ((expectedLength * 2) + 2));
//        } catch (Exception e) {
//            return res;
//        }
        if (res.length() / 2 != (expectedLength)) {
            Log.e("TAG", "unexpected Length : ex: " + expectedLength + ", Res Len :" + res.length() / 2);
        }
        return res;
    }


    private String sendAndSwitchBaudRateWithLength(String hexString, BaudRate oldBaudRate, BaudRate newBaudRate) {
        CompletableFuture<String> supply = CompletableFuture.supplyAsync(() -> {
            while (mTryCount <= mRetryCount) {
                try {
                    if (!connected) {
                        return "";
                    }
                    setBaudRateAsync(oldBaudRate);
                    try {
                        Thread.sleep(10);
                    } catch (Exception ignore) {
                    }
                    int wait = ConnectionManager.Companion.getInstance().calculateSendingTimeMs(baudRate, TextUtil.INSTANCE.fromHexString(hexString).length);
                    usbSerialPort.write(TextUtil.INSTANCE.fromHexString(hexString), mSendTimeOut);
                    session += "sendWithLength (" + mExpectedLength + ") : " + hexString + "\n";
                    try {
                        Thread.sleep(wait);
                    } catch (Exception ignore) {
                    }
                    setBaudRateAsync(newBaudRate );
                    try {
                        Thread.sleep(10);
                    } catch (Exception ignore) {
                    }
                    mSendTime = System.currentTimeMillis();
                    while ((System.currentTimeMillis() <= mSendTime + mSendTimeOut) && mReadTime == 0) {
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    if (mReadTime == 0) {
                        mTryCount++;
                        continue;
                    }
                    while ((System.currentTimeMillis() <= mReadTime + mReadTimeOut)
                            && (mExpectedLength == -1 || packet.size() < mExpectedLength)) {
                        if (mExpectedLength == -1 && packet != null && packet.size() > 1) {
                            mExpectedLength = (packet.get(1) & 0xFF) + ((packet.get(2) & 0xFF) * (256));
//                            expectedLength += 5;
                            mExpectedLength += 6;
                        }
                        try {
                            Thread.sleep(50);
                        } catch (Exception ignored) {
                        }
                    }
                    break;
                } catch (IOException e) {
                    mTryCount++;
                    try {
                        Thread.sleep(500);
                    } catch (Exception ignored) {
                    }
                }
            }
            String res = TextUtil.INSTANCE.toHexString(Bytes.toArray(packet)).replaceAll(" ", "");
            session += "sendWithLength (" + mExpectedLength + ") : " + hexString + "\n";
            session += "res (" + res.trim().length() / 2 + "): " + res + "\n\n";
            return res;
        });
        try {
            return supply.get();
        } catch (Exception e) {
            session += e.getMessage();
            return "";
        }
    }

    public String sendAndSwitchBaudRateWithLengthAsync(String hexString, int retryCount, int sendTimeOut, int expectedLength, BaudRate oldBaudRate, BaudRate newBaudRate) {
        if (!connected) {
            session += "not connected";
            return "";
        }
        packet = new ArrayList<>();
        mTryCount = 0;
        mReadTime = 0;
        mSendTime = 0;
        mRetryCount = retryCount;
        mSendTimeOut = sendTimeOut;
        mReadTimeOut = expectedLength > 0 ? 800 : 2000;
        mExpectedLength = expectedLength > 0 ? expectedLength : -1;
        String res = "";
        while ((res.length() / 2 < (mExpectedLength - 1) || mExpectedLength == -1) && mTryCount <= mRetryCount) {
            res = sendAndSwitchBaudRateWithLength(hexString.replaceAll(" ", ""), oldBaudRate, newBaudRate);
            mTryCount++;
        }
//        try {
//            return res.substring(0, expectedLength != 0 ? expectedLength * 2 : ((expectedLength * 2) + 2));
//        } catch (Exception e) {
//            return res;
//        }
        if (res.length() / 2 != (expectedLength)) {
            Log.e("TAG", "unexpected Length : ex: " + expectedLength + ", Res Len :" + res.length() / 2);
        }
        return res;
    }

    public void writeSession(@NotNull Context context) {
        if (!session.isEmpty()) {
            Utils.writeStringAsFile(context, session, "USB.txt");
        }
        session = "";
    }

}
