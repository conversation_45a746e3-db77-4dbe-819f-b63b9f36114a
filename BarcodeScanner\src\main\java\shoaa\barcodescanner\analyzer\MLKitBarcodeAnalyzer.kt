package shoaa.barcodescanner.analyzer

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewTreeObserver
import androidx.camera.core.*
import androidx.camera.core.internal.utils.ImageUtil
import androidx.camera.core.internal.utils.ImageUtil.CodecFailedException
import androidx.camera.view.PreviewView
import com.google.mlkit.vision.barcode.BarcodeScannerOptions
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.CompletableFuture


class MLKitBarcodeAnalyzer(
    private val listener: ScanningResultListener,
    private val cacheDir: File?,
) : ImageAnalysis.Analyzer {
    private lateinit var previewView: PreviewView
    private lateinit var context: Context
    private var scan: Boolean = false
    private var zoom: Boolean = true
    var fullBitmap: Bitmap? = null

    private var captureRes: Bitmap? = null
    private var startCapture: Boolean = false

    var barcodeTargetCount = 3
    var barcodeMap: HashMap<String, Int> = HashMap()
    fun setPreviewView(previewView: PreviewView): MLKitBarcodeAnalyzer {
        this.previewView = previewView
        return this
    }

    fun setContext(context: Context): MLKitBarcodeAnalyzer {
        this.context = context
        return this
    }

    fun setScan(scan: Boolean): MLKitBarcodeAnalyzer {
        this.scan = scan
        return this
    }

    fun setZoom(zoom: Boolean): MLKitBarcodeAnalyzer {
        this.zoom = zoom
        return this
    }

    fun getListener(): ScanningResultListener {
        return listener
    }

    private inline fun View.afterMeasured(crossinline block: () -> Unit) {
        if (measuredWidth > 0 && measuredHeight > 0) {
            block()
        } else {
            viewTreeObserver.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (measuredWidth > 0 && measuredHeight > 0) {
                        viewTreeObserver.removeOnGlobalLayoutListener(this)
                        block()
                    }
                }
            })
        }
    }

    private var camera: Camera? = null
    fun setCamera(camera: Camera): MLKitBarcodeAnalyzer {
        this.camera = camera
        return this
    }

    @SuppressLint("ClickableViewAccessibility")
    fun initFocus(camera: Camera): MLKitBarcodeAnalyzer {
        previewView.afterMeasured {
            previewView.setOnTouchListener { _, event ->
                return@setOnTouchListener when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        true
                    }

                    MotionEvent.ACTION_UP -> {
                        val autoFocusPoint = SurfaceOrientedMeteringPointFactory(1f, 1f)
                            .createPoint(0.7f, 0.5f)
                        try {
                            camera.cameraControl.startFocusAndMetering(
                                FocusMeteringAction.Builder(
                                    autoFocusPoint,
                                    FocusMeteringAction.FLAG_AF
                                ).apply {
                                    //focus only when the user tap the preview
                                    disableAutoCancel()
                                }.build()
                            )
                        } catch (e: CameraInfoUnavailableException) {
                            Log.d("ERROR", "cannot access camera", e)
                        }
                        true
                    }

                    else -> false // Unhandled event.
                }
            }
        }
        return this
    }


    private var count = 0

    fun capture(): Bitmap {
        val supply = CompletableFuture.supplyAsync {
            captureRes = null
            startCapture = true
            while (captureRes == null) {
                Thread.sleep(100)
            }
            captureRes!!
        }
        return supply.get()
    }

    @ExperimentalGetImage
    override fun analyze(imageProxy: ImageProxy) {
        // Get a handler that can be used to post to the main thread
        if (startCapture) {
            startCapture = false
            val data = imageToJpegByteArray(imageProxy)
            if (data != null) {
                captureRes = rotateBitmap(
                    BitmapFactory.decodeByteArray(data, 0, data.size),
                    imageProxy.imageInfo.rotationDegrees
                )
                startCapture = true
            } else {
                startCapture = true
            }
        }
        if (count >= 1) {
            imageProxy.close()
        } else {
            try {
                Thread {
                    count++
                    val data = imageToJpegByteArray(imageProxy)
                    if (data != null) {
                        fullBitmap = rotateBitmap(
                            BitmapFactory.decodeByteArray(data, 0, data.size),
                            imageProxy.imageInfo.rotationDegrees
                        )
                        if (scan) {
                            Handler(Looper.getMainLooper()).post {
                                val bm: Bitmap? = previewView.bitmap
                                if (bm != null) {
                                    val statusbarHeight = getStatusBarHeight(context)

                                    val croppedBitmap = Bitmap.createBitmap(
                                        bm,
                                        ((bm.width * 0.5f).toInt() / 2) - 20,
                                        ((bm.height * 0.7f).toInt() - statusbarHeight) + 20,
                                        (bm.width * 0.5f).toInt(),
                                        (bm.height * 0.07f).toInt()
                                    )
                                    val image = InputImage.fromBitmap(croppedBitmap, 0)
                                    val options = BarcodeScannerOptions.Builder()
                                        .build()
                                    val scanner = BarcodeScanning.getClient(options)
                                    scanner.process(image)
                                        .addOnSuccessListener { barcodes ->
                                            barcodes.firstOrNull().let { barcode ->
                                                val rawValue = barcode?.rawValue
                                                rawValue?.let {
                                                    if (barcodeMap.containsKey(it)) {
                                                        var count = barcodeMap[it] ?: 0
                                                        count++
                                                        barcodeMap[it] = count
                                                    } else {
                                                        barcodeMap[it] = 1
                                                    }
                                                    val filteredBarcodes =
                                                        barcodeMap.filterValues { i -> i >= barcodeTargetCount }
                                                    if (filteredBarcodes.isNotEmpty()) {
                                                        var maxEntry: Map.Entry<String, Int>? = null
                                                        for (entry in filteredBarcodes.entries) {
                                                            if (maxEntry == null || entry.value > maxEntry.value) {
                                                                maxEntry = entry
                                                            }
                                                        }
                                                        if (maxEntry != null) {
                                                            val file = File(cacheDir, "barcode.jpg")
                                                            val fOut = FileOutputStream(file)
                                                            if (zoom) {
                                                                bm.compress(
                                                                    Bitmap.CompressFormat.JPEG,
                                                                    100,
                                                                    fOut
                                                                )
                                                            } else {
                                                                fullBitmap!!.compress(
                                                                    Bitmap.CompressFormat.JPEG,
                                                                    100,
                                                                    fOut
                                                                )
                                                            }
                                                            fOut.flush()
                                                            fOut.close()


                                                            val parts =
                                                                maxEntry.key.split(Regex("(?<=\\D)(?=\\d)|(?<=\\d)(?=\\D)"))
                                                            val finalOut = arrayListOf<String>()
                                                            for (x in parts) {
                                                                if (x.matches("\\d+".toRegex())) {
                                                                    finalOut.add(x)
                                                                }
                                                            }
                                                            val bc =
                                                                finalOut[finalOut.size - 1]

                                                            var barcodelong = 0L
                                                            try {
                                                                barcodelong = bc.toLong()
                                                            } catch (ignore: Exception) {

                                                                if (bc.length > 18) {
                                                                    val bc1 =
                                                                        bc.substring(
                                                                            bc.length - 19
                                                                        )
                                                                    try {
                                                                        barcodelong =
                                                                            bc.toLong()
                                                                    } catch (ignore1: Exception) {
                                                                        val bc2 = bc1.substring(1)
                                                                        try {
                                                                            barcodelong =
                                                                                bc2.toLong()
                                                                        } catch (ignore2: Exception) {
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            barcodeMap = HashMap()
                                                            listener.onScanned(
                                                                barcodelong.toString(),
                                                                file.absolutePath
                                                            )
                                                        } else {
                                                            barcodeMap = HashMap()
                                                            listener.onScanned(
                                                                "0",
                                                                ""
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                            imageProxy.close()
                                            count--
                                        }
                                        .addOnFailureListener {
                                            imageProxy.close()
                                            count--
                                        }
                                } else {
                                    imageProxy.close()
                                    count--
                                }
                            }
                        } else {
                            imageProxy.close()
                            count--
                        }
                    } else {
                        imageProxy.close()
                        count--
                    }
                }.start()
            } catch (e: Exception) {
                Log.e("TAG", "analyze: " + e.message)
                imageProxy.close()
                count--
            }
        }
    }

    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId: Int =
            context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    @SuppressLint("RestrictedApi")
    fun imageToJpegByteArray(image: ImageProxy): ByteArray? {
        var data: ByteArray? = null
        when (image.format) {
            ImageFormat.JPEG -> {
                data = ImageUtil.jpegImageToJpegByteArray(image)
            }

            ImageFormat.YUV_420_888 -> {
                try {
                    data = ImageUtil.yuvImageToJpegByteArray(image, null, 100, 0)
                } catch (e: CodecFailedException) {
                    e.printStackTrace()
                }
            }

            else -> {
                Log.w(
                    this.javaClass.name,
                    "Unrecognized image format: " + image.format
                )
            }
        }
        return data
    }

    fun getResizedBitmap(bm: Bitmap, newWidth: Int, newHeight: Int): Bitmap {
        val width = bm.width
        val height = bm.height
        val scaleWidth = newWidth.toFloat() / width
        val scaleHeight = newHeight.toFloat() / height
        // CREATE A MATRIX FOR THE MANIPULATION
        val matrix = Matrix()
        // RESIZE THE BIT MAP
        matrix.postScale(scaleWidth, scaleHeight)

        // "RECREATE" THE NEW BITMAP
        val resizedBitmap = Bitmap.createBitmap(
            bm, 0, 0, width, height, matrix, false
        )
        bm.recycle()
        return resizedBitmap
    }

    private fun rotateBitmap(
        bitmap: Bitmap, rotationDegrees: Int
    ): Bitmap {
        val matrix = Matrix()

        // Rotate the image back to straight.
        matrix.postRotate(rotationDegrees.toFloat())

        // Mirror the image along the X or Y axis.
        val rotatedBitmap =
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)

        // Recycle the old bitmap if it has changed.
        if (rotatedBitmap != bitmap) {
            bitmap.recycle()
        }
        return rotatedBitmap
    }
}