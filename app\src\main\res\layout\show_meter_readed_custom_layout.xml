<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layoutDirection="rtl"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp"
            android:textStyle="bold">
            <!-- Customer ID -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="رقم الإشتراك : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                />

            <TextView
                android:id="@+id/customer_id_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <!-- Manteka -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="منطقه : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                />/>

            <TextView
                android:id="@+id/manteka_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <!-- Yomea -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="يوميه : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                /> />

            <TextView
                android:id="@+id/yomea_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <!-- Hesab -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="حساب : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                /> />

            <TextView
                android:id="@+id/hesab_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <!-- Far3y -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="فرعى : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                /> />

            <TextView
                android:id="@+id/far3y_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <!-- Address -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="العنوان : "
                android:textSize="@dimen/_15sdp"
                android:textStyle="bold"
                android:gravity="center_vertical"
                /> />

            <TextView
                android:id="@+id/address_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:gravity="center_vertical"
                android:textSize="@dimen/_14sdp"
                />

        </LinearLayout>

        <!-- ImageView at the top -->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            android:src="@drawable/my_custom_background" />

    </LinearLayout>
</ScrollView>