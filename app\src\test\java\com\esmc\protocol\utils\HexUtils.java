package com.esmc.protocol.utils;

import com.esmc.protocol.model.DlmsResponseInfo;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/utils/HexUtils.class */
public class HexUtils {
    public static final int time_type_ssmmhhddmmyy = 1;
    public static final int time_type_mmhhddmmyy = 2;
    public static final int time_type_ddmmyy = 3;
    public static final int time_type_mmhhddmm = 4;
    public static final int time_type_hhddmmyy = 5;
    public static final int time_type_mmyy = 6;
    public static final int time_type_hhddmm = 7;
    public static final int time_type_ssmmhhddwwmmyy = 8;
    public static final int time_type_mmhhdd = 9;
    public static final int time_type_wwddmmyy = 10;
    public static final int time_type_ssmmhh = 11;
    public static final int time_type_mmhh = 12;
    public static final int time_type_ddmm = 13;
    public static final int time_type_ssmmhhddmmyy_app = 14;
    public static final int time_type_hhmm = 15;
    public static final int time_type_mmdd = 16;
    public static final int time_type_yymmddhhmmss = 21;
    public static final int time_type_yymmddhhmm = 22;
    public static final int time_type_yymmdd = 23;
    public static final int time_type_mmddhhmm = 24;
    public static final int time_type_mmddhhmmss = 25;
    public static final int time_type_ddhh = 26;
    public static final int time_type_yymmddww = 27;
    public static final int time_type_hhmmss = 28;
    public static final int time_type_yyyymmddhhmm = 29;
    private static final byte[] highDigits;
    private static final byte[] lowDigits;

    static {
        byte[] digits = {48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 65, 66, 67, 68, 69, 70};
        byte[] high = new byte[256];
        byte[] low = new byte[256];
        for (int i = 0; i < 256; i++) {
            high[i] = digits[i >>> 4];
            low[i] = digits[i & 15];
        }
        highDigits = high;
        lowDigits = low;
    }

    public static String hexDumpCompact(ByteBuffer in) {
        if (null == in) {
            return "null";
        }
        int size = in.remaining();
        if (size == 0) {
            return "empty";
        }
        StringBuffer out = new StringBuffer(in.remaining() * 2);
        int pos = in.position();
        while (size > 0) {
            int byteValue = in.get(pos) & 255;
            out.append((char) highDigits[byteValue]);
            out.append((char) lowDigits[byteValue]);
            pos++;
            size--;
        }
        return out.toString();
    }

    public static final String hexDumpCompact(byte[] in, int offset, int length) {
        if (null == in) {
            return "null";
        }
        int size = in.length;
        if (size == 0 || length > size || offset < 0 || offset > size || offset + length > size) {
            return "empty";
        }
        StringBuffer out = new StringBuffer(length * 2);
        while (length > 0) {
            offset++;
            int byteValue = in[offset] & 255;
            out.append((char) highDigits[byteValue]);
            out.append((char) lowDigits[byteValue]);
            length--;
        }
        return out.toString();
    }

    public static final String toHex(byte[] in, int offset, int len) {
        if (null == in || offset < 0 || len < 0) {
            return "empty";
        }
        if (offset + len > in.length) {
            len = in.length;
        }
        StringBuffer out = new StringBuffer(len * 2);
        while (len > 0) {
            offset++;
            int byteValue = in[offset] & 255;
            out.append((char) highDigits[byteValue]);
            out.append((char) lowDigits[byteValue]);
            len--;
        }
        return out.toString();
    }

    public static final String toHex(byte[] in) {
        return toHex(in, 0, in.length);
    }

    public static final String toHex(byte byteValue) {
        StringBuffer out = new StringBuffer(2);
        int index = byteValue & 255;
        out.append((char) highDigits[index]);
        out.append((char) lowDigits[index]);
        return out.toString();
    }

    public static final String toHex(short shortValue) {
        StringBuffer out = new StringBuffer(5);
        int index = (shortValue >> 8) & 255;
        out.append((char) highDigits[index]);
        out.append((char) lowDigits[index]);
        int index2 = shortValue & 255;
        out.append((char) highDigits[index2]);
        out.append((char) lowDigits[index2]);
        return out.toString();
    }

    public static final String toHex(int i) {
        StringBuffer out = new StringBuffer(8);
        int index = (i >> 24) & 255;
        out.append((char) highDigits[index]);
        out.append((char) lowDigits[index]);
        int index2 = (i >> 16) & 255;
        out.append((char) highDigits[index2]);
        out.append((char) lowDigits[index2]);
        int index3 = (i >> 8) & 255;
        out.append((char) highDigits[index3]);
        out.append((char) lowDigits[index3]);
        int index4 = i & 255;
        out.append((char) highDigits[index4]);
        out.append((char) lowDigits[index4]);
        return out.toString();
    }

    private static byte char2Byte(char c) {
        if (c >= '0' && c <= '9') {
            return (byte) (c - '0');
        }
        if (c >= 'A' && c <= 'F') {
            return (byte) ((c - 'A') + 10);
        }
        if (c < 'a' || c > 'f') {
            return (byte) 0;
        }
        return (byte) ((c - 'a') + 10);
    }

    public static ByteBuffer toByteBuffer(String str) {
        ByteBuffer buff = ByteBuffer.wrap(new byte[str.length() / 2]);
        for (int i = 0; i < str.length() - 1; i += 2) {
            char c1 = str.charAt(i);
            char c2 = str.charAt(i + 1);
            byte b1 = (byte) ((char2Byte(c1) << 4) | (15 & char2Byte(c2)));
            buff.put(b1);
        }
        buff.flip();
        return buff;
    }

    public static ByteBuffer toByteBuffer(byte[] in) {
        ByteBuffer buff = ByteBuffer.wrap(in);
        return buff;
    }

    public static final byte[] toArray(String hexs) {
        ByteBuffer buff = ByteBuffer.wrap(new byte[hexs.length() / 2]);
        for (int i = 0; i < hexs.length() - 1; i += 2) {
            char c1 = hexs.charAt(i);
            char c2 = hexs.charAt(i + 1);
            byte b1 = (byte) ((char2Byte(c1) << 4) | (15 & char2Byte(c2)));
            buff.put(b1);
        }
        buff.flip();
        return buff.array();
    }

    public static int strToInt(byte[] value, int offset, int len) {
        int result = 0;
        int end = offset + len;
        for (int i = offset; i < end; i++) {
            int c = value[i] - 48;
            if (c < 0 || c > 9) {
                throw new RuntimeException("String to int convert exception,format error.");
            }
            result = (result * 10) + c;
        }
        return result;
    }

    public static short bytesToShort(byte[] buffer, int posStart) {
        short dest = (short) (buffer[posStart + 0] & 255);
        return (short) (dest | ((buffer[posStart + 1] & 255) << 8));
    }

    public static void shortToBytes(short src, byte[] buffer, int posStart) {
        buffer[posStart + 0] = (byte) (src & 255);
        buffer[posStart + 1] = (byte) ((src >>> 8) & 255);
    }

    public static int bytesToInt(byte[] buffer, int posStart) {
        int dest = buffer[posStart] & 255;
        return dest | (buffer[posStart + 1] & 255) | (buffer[posStart + 2] & 255) | (buffer[posStart + 3] & 255);
    }

    public static void intToBytes(int src, byte[] buffer, int posStart) {
        buffer[posStart] = (byte) (src & 15);
        buffer[posStart + 1] = (byte) ((src >>> 8) & 255);
        buffer[posStart + 2] = (byte) ((src >>> 16) & 255);
        buffer[posStart + 3] = (byte) ((src >>> 24) & 255);
    }

    public static int bytesToInt3(byte[] buffer, int posStart) {
        int dest = buffer[posStart] & 255;
        return dest | (buffer[posStart + 1] & 255) | (buffer[posStart + 2] & 255);
    }

    public static short htons(short src) {
        short dest = (short) ((src & 255) << 8);
        return (short) (dest | ((src & 65280) >>> 8));
    }

    public static int htoni(int src) {
        int dest = (src & 255) << 24;
        return dest | ((src & 65280) << 8) | ((src & 16711680) >>> 8) | ((src & (-16777216)) >>> 24);
    }

    public static final byte[] cat(byte[] src1, byte[] src2) {
        if (null == src1 && null == src2) {
            return null;
        }
        if (null == src1 || src1.length == 0) {
            return src2;
        }
        if (null == src2 || src2.length == 0) {
            return src1;
        }
        ByteBuffer buf = ByteBuffer.allocate(src1.length + src2.length);
        buf.put(src1).put(src2);
        return buf.array();
    }

    private static int asciiToBin(char c) {
        if (c >= 'a') {
            return ((c - 'a') + 10) & 15;
        }
        if (c >= 'A') {
            return ((c - 'A') + 10) & 15;
        }
        return (c - '0') & 15;
    }

    public static String bytesToNumberString(byte[] buffer, int offset, int len, int radix) {
        if (radix == 16) {
            StringBuilder sb = new StringBuilder("");
            if (buffer == null || buffer.length <= 0) {
                return null;
            }
            for (int i = 0; i < len; i++) {
                int v = buffer[offset + i] & 255;
                String hv = Integer.toHexString(v);
                if (hv.length() < 2) {
                    sb.append(0);
                }
                sb.append(hv);
            }
            return sb.toString();
        } else if (radix == 10) {
            if (len == 1) {
                return String.valueOf(buffer[offset] & 255);
            }
            if (len == 2) {
                return String.valueOf((int) bytesToShort(buffer, offset));
            }
            if (len == 4) {
                return String.valueOf(bytesToInt(buffer, offset));
            }
            return null;
        } else if (radix != 2) {
            return null;
        } else {
            String str = new String();
            int i2 = 0;
            while (i2 < len) {
                while (0 < 8) {
                    int b = (buffer[offset + i2] >>> 0) & 1;
                    str = str + String.valueOf(b);
                    i2++;
                }
            }
            return str;
        }
    }

    public static String bytesToNumberStringReverse(byte[] buffer, int offset, int len, int radix) {
        if (radix == 16) {
            StringBuilder sb = new StringBuilder("");
            if (buffer == null || buffer.length <= 0) {
                return null;
            }
            for (int i = len - 1; i >= 0; i--) {
                int v = buffer[offset + i] & 255;
                String hv = Integer.toHexString(v);
                if (hv.length() < 2) {
                    sb.append(0);
                }
                sb.append(hv);
            }
            return sb.toString();
        } else if (radix == 10) {
            if (len == 1) {
                return String.valueOf(buffer[offset] & 255);
            }
            if (len == 2) {
                return String.valueOf((int) bytesToShort(buffer, offset));
            }
            if (len == 4) {
                return String.valueOf(bytesToInt(buffer, offset));
            }
            return null;
        } else if (radix != 2) {
            return null;
        } else {
            String str = new String();
            int i2 = 0;
            while (i2 < len) {
                while (0 < 8) {
                    int b = (buffer[offset + i2] >>> 0) & 1;
                    str = str + String.valueOf(b);
                    i2++;
                }
            }
            return str;
        }
    }

    public static void numberStringToBytes(String str, int radix, byte[] buffer, int offset) {
        if (str != null) {
            if (radix == 16) {
                int j = 0;
                for (int i = 0; i < str.length() / 2; i++) {
                    int j2 = j + 1;
                    char c0 = str.charAt(j);
                    j = j2 + 1;
                    char c1 = str.charAt(j2);
                    buffer[offset + i] = (byte) ((asciiToBin(c0) << 4) | asciiToBin(c1));
                }
            } else if (radix == 10) {
                intToBytes(Integer.valueOf(str, 10).intValue(), buffer, offset);
            } else if (radix == 2) {
                intToBytes(Integer.valueOf(str, 2).intValue(), buffer, offset);
            }
        }
    }

    public static void numberStringToBytesReverse(String str, int radix, byte[] buffer, int offset) {
        if (str != null) {
            if (radix == 16) {
                int j = 0;
                for (int i = (str.length() / 2) - 1; i >= 0; i--) {
                    int j2 = j + 1;
                    char c0 = str.charAt(j);
                    j = j2 + 1;
                    char c1 = str.charAt(j2);
                    buffer[offset + i] = (byte) ((asciiToBin(c0) << 4) | asciiToBin(c1));
                }
            } else if (radix == 10) {
                intToBytes(Integer.valueOf(str, 10).intValue(), buffer, offset);
            } else if (radix == 2) {
                intToBytes(Integer.valueOf(str, 2).intValue(), buffer, offset);
            }
        }
    }

    public static String asciiByteToString(byte[] buffer, int offset, int len) {
        if (buffer.length - offset < len) {
            return null;
        }
        String strValue = "";
        for (int i = 0; i < len; i++) {
            char cTemp = (char) buffer[offset + i];
            strValue = strValue + String.valueOf(cTemp);
        }
        return strValue.trim();
    }

    public static void stringToAsciiByte(String str, byte[] buffer, int offset, int len) {
        if (str != null) {
            char[] chars = str.toCharArray();
            int strLen = str.length();
            for (int i = 0; i < strLen; i++) {
                buffer[((offset + len) - i) - 1] = (byte) chars[i];
            }
        }
    }

    public static int stringToAsciiByteOrder(String str, byte[] buffer, int offset) {
        int len = 0;
        if (str == null) {
            return 0;
        }
        char[] chars = str.toCharArray();
        int strLen = str.length();
        for (int i = 0; i < strLen; i++) {
            buffer[offset + i] = (byte) chars[i];
            len++;
        }
        return len;
    }

    public static boolean isInvalidData(byte[] buffer, int offset, int len, byte invalid) {
        for (int n = 0; n < len; n++) {
            if (buffer[offset + n] != invalid) {
                return true;
            }
        }
        return false;
    }

    public static byte binToBcd(byte bin) {
        if (bin > 99) {
            return (byte) 0;
        }
        return (byte) (((bin / 10) * 16) + (bin % 10));
    }

    public static byte bcdToBin(byte bcd) {
        byte b0;
        byte b1 = (byte) ((bcd & 255) / 16);
        if (b1 <= 9 && (b0 = (byte) ((bcd & 255) % 16)) <= 9) {
            return (byte) ((b1 * 10) + b0);
        }
        return (byte) 0;
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [long] */
    /* JADX WARN: Type inference failed for: r0v9, types: [long] */
    public static long bcdToBin(byte[] buffer, int offset, int len) {
        char c = 0;
        for (int n = len - 1; n >= 0; n--) {
            char c2 = 1;
            for (int m = 0; m < n; m++) {
                c2 *= 'd';
            }
            c += bcdToBin(buffer[offset + n]) * c2;
        }
        return c;
    }

    public static int bcdToBin(byte[] buffer, int offset, int len, byte invalid) {
        if (len > 4) {
            return 0;
        }
        if (!isInvalidData(buffer, offset, len, invalid)) {
            return -10;
        }
        int value = 0;
        for (int n = len - 1; n >= 0; n--) {
            int pow = 1;
            for (int m = 0; m < n; m++) {
                pow *= 100;
            }
            value += bcdToBin(buffer[offset + n]) * pow;
        }
        return value;
    }

    /* JADX WARN: Type inference failed for: r0v18, types: [double] */
    /* JADX WARN: Type inference failed for: r0v25, types: [double] */
    public static double bcdToBin(byte[] buffer, int offset, int len, int countRadixPoint, byte invalid) {
        if (len > 6 || countRadixPoint > 10) {
            return 0.0d;
        }
        if (!isInvalidData(buffer, offset, len, invalid)) {
            return -10.0d;
        }
        BigInteger value = new BigInteger("0");
        for (int n = len - 1; n >= 0; n--) {
            int pow = 1;
            for (int m = 0; m < n; m++) {
                pow *= 100;
            }
            new BigInteger("0");
            BigInteger value1 = BigInteger.valueOf(bcdToBin(buffer[offset + n])).multiply(BigInteger.valueOf(pow));
            value = value.add(value1);
        }
        char c = 0;
        if (countRadixPoint >= 0) {
            for (int m2 = 0; m2 < countRadixPoint; m2++) {
                c *= 0;
            }
            return value.doubleValue() / c;
        }
        for (int m3 = 0; m3 < (-1) * countRadixPoint; m3++) {
            c *= 0;
        }
        return value.doubleValue() * c;
    }

    /* JADX WARN: Type inference failed for: r0v17, types: [double] */
    /* JADX WARN: Type inference failed for: r0v24, types: [double] */
    public static double bcdToBinReverse(byte[] buffer, int offset, int len, int countRadixPoint, byte invalid) {
        if (len > 6 || countRadixPoint > 10) {
            return 0.0d;
        }
        if (!isInvalidData(buffer, offset, len, invalid)) {
            return -10.0d;
        }
        BigInteger value = new BigInteger("0");
        for (int n = 0; n < len; n++) {
            int pow = 1;
            for (int m = len - 1; m > n; m--) {
                pow *= 100;
            }
            new BigInteger("0");
            BigInteger value1 = BigInteger.valueOf(bcdToBin(buffer[offset + n])).multiply(BigInteger.valueOf(pow));
            value = value.add(value1);
        }
        char c = 0;
        if (countRadixPoint >= 0) {
            for (int m2 = 0; m2 < countRadixPoint; m2++) {
                c *= 0;
            }
            return value.doubleValue() / c;
        }
        for (int m3 = 0; m3 < (-1) * countRadixPoint; m3++) {
            c *= 0;
        }
        return value.doubleValue() * c;
    }

    public static void binToBcd(int value, byte[] buffer, int offset) {
        int n = 0;
        while (value > 0) {
            buffer[offset + n] = binToBcd((byte) (value % 100));
            value /= 100;
            n++;
        }
    }

    public static void binToBcdReverse(int value, byte[] buffer, int offset, int len) {
        for (int n = len; value > 0 && n - offset > 0; n--) {
            buffer[n] = binToBcd((byte) (value % 100));
            value /= 100;
        }
    }

    public static void binToBcdReverse1(int value, byte[] buffer, int offset, int len) {
        for (int n = len; value > 0 && n > 0; n--) {
            buffer[(offset + n) - 1] = binToBcd((byte) (value % 100));
            value /= 100;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v5, types: [long] */
    /* JADX WARN: Type inference failed for: r7v1, types: [long] */
    /* JADX WARN: Type inference failed for: r7v2 */
    /* JADX WARN: Type inference failed for: r7v3 */
    /* JADX WARN: Unknown variable types count: 1 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static void binToBcd(long r7, byte[] r9, int r10) {
        /*
            r0 = 0
            r11 = r0
        L_0x0003:
            r0 = r7
            r1 = 0
            int r0 = (r0 > r1 ? 1 : (r0 == r1 ? 0 : -1))
            if (r0 <= 0) goto L_0x0025
            r0 = r9
            r1 = r10
            r2 = r11
            int r1 = r1 + r2
            r2 = r7
            r3 = 100
            long r2 = r2 % r3
            int r2 = (int) r2
            byte r2 = (byte) r2
            byte r2 = binToBcd(r2)
            r0[r1] = r2
            r0 = r7
            r1 = 100
            long r0 = r0 / r1
            r7 = r0
            int r11 = r11 + 1
            goto L_0x0003
        L_0x0025:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esmc.protocol.utils.HexUtils.binToBcd(long, byte[], int):void");
    }

    public static void binToBcd(double value, int len, int countRadixPoint, byte[] buffer, int offset) {
        int pow = 1;
        for (int i = 0; i < countRadixPoint; i++) {
            pow *= 10;
        }
        if (len <= 4) {
            int iValue = (int) (value * pow);
            binToBcd(iValue, buffer, offset);
        } else if (len > 4 && len <= 6) {
            long iValue2 = (long) (value * pow);
            binToBcd(iValue2, buffer, offset);
        }
    }

    public static void BinToBcd(String value, int intCount, int PointCount, byte[] buffer, int offset) {
        String intData;
        int point = value.indexOf(".");
        String pointData = "";
        if (point > 0) {
            intData = value.substring(0, point);
            pointData = value.substring(point + 1, value.length());
        } else {
            intData = value;
        }
        for (int i = intData.length(); i < intCount; i++) {
            intData = "0" + intData;
        }
        for (int i2 = pointData.length(); i2 < PointCount; i2++) {
            pointData = "0" + pointData;
        }
        String valueData = intData + pointData;
        int n = 0;
        for (int i3 = valueData.length(); i3 >= 2; i3 -= 2) {
            byte bValue = Byte.valueOf(valueData.substring(i3 - 2, i3)).byteValue();
            buffer[offset + n] = binToBcd(bValue);
            n++;
        }
    }

    public static void BinToBcdWithS(String value, int intCount, int PointCount, byte[] buffer, int offset) {
        String intData;
        double dValue = Double.valueOf(value).doubleValue();
        int s = 0;
        if (dValue < 0.0d) {
            double d = 0.0d - dValue;
            s = 1;
        }
        int point = value.indexOf(".");
        String pointData = "";
        if (point > 0) {
            if (s == 1) {
                intData = value.substring(1, point);
            } else {
                intData = value.substring(0, point);
            }
            pointData = value.substring(point + 1, value.length());
        } else if (s == 1) {
            intData = value.substring(1, value.length());
        } else {
            intData = value;
        }
        for (int i = intData.length(); i < intCount; i++) {
            intData = "0" + intData;
        }
        for (int i2 = pointData.length(); i2 < PointCount; i2++) {
            pointData = "0" + pointData;
        }
        String valueData = intData + pointData;
        int n = 0;
        for (int i3 = valueData.length(); i3 >= 2; i3 -= 2) {
            byte bValue = Byte.valueOf(valueData.substring(i3 - 2, i3)).byteValue();
            buffer[offset + n] = binToBcd(bValue);
            n++;
        }
        int sOffset = (offset + ((intCount + PointCount) / 2)) - 1;
        if (s == 1) {
            buffer[sOffset] = (byte) (buffer[sOffset] | 16);
        } else {
            buffer[sOffset] = (byte) (buffer[sOffset] & 15);
        }
    }

    public static void main(String[] args) {
        byte[] buffer = {0, 16};
        double a = bcdToBinWithS(buffer, 0, 2, 3, (byte) -1);
        System.out.println(a);
    }

    /* JADX WARN: Type inference failed for: r0v10, types: [double] */
    public static double bcdToBinWithS(byte[] buffer, int offset, int len, int countRadixPoint, byte invalid) {
        if (!isInvalidData(buffer, offset, len, invalid)) {
            return -10.0d;
        }
        byte s = (byte) (buffer[(offset + len) - 1] & 128);
        byte[] temp = new byte[len];
        System.arraycopy(buffer, offset, temp, 0, len);
        char bcdToBin = (char) bcdToBin(temp, 0, len, countRadixPoint, (byte) -1);
        double d = bcdToBin;
        if (s == 1) {
            double d2 = 0 - bcdToBin;
            d = d2;
        }
        return d;
    }

    public static Date bcdToTime(byte[] buffer, int offset, int type, byte invalid) {
        if (type == 2) {
            if (buffer.length - offset < 5) {
                return null;
            }
            Calendar calendar = Calendar.getInstance();
            int offset2 = offset + 1;
            calendar.set(12, bcdToBin(buffer[offset]));
            int offset3 = offset2 + 1;
            calendar.set(11, bcdToBin(buffer[offset2]));
            int offset4 = offset3 + 1;
            calendar.set(5, bcdToBin(buffer[offset3]));
            int offset5 = offset4 + 1;
            calendar.set(2, bcdToBin(buffer[offset4]) - 1);
            int i = offset5 + 1;
            calendar.set(1, bcdToBin(buffer[offset5]) + 2000);
            return calendar.getTime();
        } else if (type == 8) {
            if (buffer.length - offset < 6) {
                return null;
            }
            Calendar calendar2 = Calendar.getInstance();
            int offset6 = offset + 1;
            calendar2.set(13, bcdToBin(buffer[offset]));
            int offset7 = offset6 + 1;
            calendar2.set(12, bcdToBin(buffer[offset6]));
            int offset8 = offset7 + 1;
            calendar2.set(11, bcdToBin(buffer[offset7]));
            int offset9 = offset8 + 1;
            calendar2.set(5, bcdToBin(buffer[offset8]));
            int offset10 = offset9 + 1;
            calendar2.set(2, bcdToBin((byte) (buffer[offset9] & 31)) - 1);
            int i2 = offset10 + 1;
            calendar2.set(1, bcdToBin(buffer[offset10]) + 2000);
            return calendar2.getTime();
        } else if (type == 14) {
            if (buffer.length - offset < 6) {
                return null;
            }
            Calendar calendar3 = Calendar.getInstance();
            int offset11 = offset + 1;
            calendar3.set(13, bcdToBin(buffer[offset]));
            int offset12 = offset11 + 1;
            calendar3.set(12, bcdToBin(buffer[offset11]));
            int offset13 = offset12 + 1;
            calendar3.set(11, bcdToBin(buffer[offset12]));
            int offset14 = offset13 + 1;
            calendar3.set(5, bcdToBin(buffer[offset13]));
            int offset15 = offset14 + 1;
            calendar3.set(2, bcdToBin((byte) (buffer[offset14] & 31)) - 1);
            int i3 = offset15 + 1;
            calendar3.set(1, bcdToBin(buffer[offset15]) + 2000);
            return calendar3.getTime();
        } else if (type == 4) {
            if (buffer.length - offset < 4) {
                return null;
            }
            Calendar calendar4 = Calendar.getInstance();
            int offset16 = offset + 1;
            calendar4.set(12, bcdToBin(buffer[offset]));
            int offset17 = offset16 + 1;
            calendar4.set(11, bcdToBin(buffer[offset16]));
            int offset18 = offset17 + 1;
            calendar4.set(5, bcdToBin(buffer[offset17]));
            int i4 = offset18 + 1;
            calendar4.set(2, bcdToBin(buffer[offset18]) - 1);
            return calendar4.getTime();
        } else if (type == 1) {
            if (buffer.length - offset < 6) {
                return null;
            }
            Calendar calendar5 = Calendar.getInstance();
            int offset19 = offset + 1;
            calendar5.set(13, bcdToBin(buffer[offset]));
            int offset20 = offset19 + 1;
            calendar5.set(12, bcdToBin(buffer[offset19]));
            int offset21 = offset20 + 1;
            calendar5.set(11, bcdToBin(buffer[offset20]));
            int offset22 = offset21 + 1;
            calendar5.set(5, bcdToBin(buffer[offset21]));
            int offset23 = offset22 + 1;
            calendar5.set(2, bcdToBin(buffer[offset22]) - 1);
            int i5 = offset23 + 1;
            calendar5.set(1, bcdToBin(buffer[offset23]) + 2000);
            return calendar5.getTime();
        } else if (type == 21) {
            if (buffer.length - offset < 6) {
                return null;
            }
            Calendar calendar6 = Calendar.getInstance();
            int offset24 = offset + 1;
            calendar6.set(1, bcdToBin(buffer[offset]) + 2000);
            int offset25 = offset24 + 1;
            calendar6.set(2, bcdToBin(buffer[offset24]) - 1);
            int offset26 = offset25 + 1;
            calendar6.set(5, bcdToBin(buffer[offset25]));
            int offset27 = offset26 + 1;
            calendar6.set(11, bcdToBin(buffer[offset26]));
            int offset28 = offset27 + 1;
            calendar6.set(12, bcdToBin(buffer[offset27]));
            int i6 = offset28 + 1;
            calendar6.set(13, bcdToBin(buffer[offset28]));
            return calendar6.getTime();
        } else if (type == 22) {
            if (buffer.length - offset < 5) {
                return null;
            }
            Calendar calendar7 = Calendar.getInstance();
            int offset29 = offset + 1;
            calendar7.set(1, bcdToBin(buffer[offset]) + 2000);
            int offset30 = offset29 + 1;
            calendar7.set(2, bcdToBin(buffer[offset29]) - 1);
            int offset31 = offset30 + 1;
            calendar7.set(5, bcdToBin(buffer[offset30]));
            int offset32 = offset31 + 1;
            calendar7.set(11, bcdToBin(buffer[offset31]));
            int i7 = offset32 + 1;
            calendar7.set(12, bcdToBin(buffer[offset32]));
            return calendar7.getTime();
        } else if (type == 10) {
            if (buffer.length - offset < 4) {
                return null;
            }
            Calendar calendar8 = Calendar.getInstance();
            int offset33 = offset + 1;
            calendar8.set(7, bcdToBin(buffer[offset]));
            int offset34 = offset33 + 1;
            calendar8.set(5, bcdToBin(buffer[offset33]));
            int offset35 = offset34 + 1;
            calendar8.set(2, bcdToBin(buffer[offset34]) - 1);
            int i8 = offset35 + 1;
            calendar8.set(1, bcdToBin(buffer[offset35]) + 2000);
            return calendar8.getTime();
        } else if (type == 11) {
            if (buffer.length - offset < 3) {
                return null;
            }
            Calendar calendar9 = Calendar.getInstance();
            int offset36 = offset + 1;
            calendar9.set(13, bcdToBin(buffer[offset]));
            int offset37 = offset36 + 1;
            calendar9.set(12, bcdToBin(buffer[offset36]));
            int i9 = offset37 + 1;
            calendar9.set(11, bcdToBin(buffer[offset37]));
            return calendar9.getTime();
        } else if (type == 12) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar10 = Calendar.getInstance();
            calendar10.set(13, 0);
            int offset38 = offset + 1;
            calendar10.set(12, bcdToBin(buffer[offset]));
            int i10 = offset38 + 1;
            calendar10.set(11, bcdToBin(buffer[offset38]));
            return calendar10.getTime();
        } else if (type == 15) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar11 = Calendar.getInstance();
            int offset39 = offset + 1;
            calendar11.set(11, bcdToBin(buffer[offset]));
            int i11 = offset39 + 1;
            calendar11.set(12, bcdToBin(buffer[offset39]));
            return calendar11.getTime();
        } else if (type == 26) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar12 = Calendar.getInstance();
            int offset40 = offset + 1;
            calendar12.set(5, bcdToBin(buffer[offset]));
            int i12 = offset40 + 1;
            calendar12.set(11, bcdToBin(buffer[offset40]));
            return calendar12.getTime();
        } else if (type == 6) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar13 = Calendar.getInstance();
            int offset41 = offset + 1;
            calendar13.set(2, bcdToBin(buffer[offset]) - 1);
            int i13 = offset41 + 1;
            calendar13.set(1, bcdToBin(buffer[offset41]) + 2000);
            return calendar13.getTime();
        } else if (type == 27) {
            if (buffer.length - offset < 4) {
                return null;
            }
            Calendar calendar14 = Calendar.getInstance();
            int offset42 = offset + 1;
            calendar14.set(1, bcdToBin(buffer[offset]) + 2000);
            int offset43 = offset42 + 1;
            calendar14.set(2, bcdToBin(buffer[offset42]));
            int offset44 = offset43 + 1;
            calendar14.set(5, bcdToBin(buffer[offset43]));
            int i14 = offset44 + 1;
            calendar14.set(4, bcdToBin(buffer[offset44]));
            return calendar14.getTime();
        } else if (type == 28) {
            if (buffer.length - offset < 3) {
                return null;
            }
            Calendar calendar15 = Calendar.getInstance();
            int offset45 = offset + 1;
            calendar15.set(11, bcdToBin(buffer[offset]));
            int offset46 = offset45 + 1;
            calendar15.set(12, bcdToBin(buffer[offset45]));
            int i15 = offset46 + 1;
            calendar15.set(13, bcdToBin(buffer[offset46]));
            return calendar15.getTime();
        } else if (type == 9) {
            if (buffer.length - offset < 3) {
                return null;
            }
            Calendar calendar16 = Calendar.getInstance();
            int offset47 = offset + 1;
            calendar16.set(12, bcdToBin(buffer[offset]));
            int offset48 = offset47 + 1;
            calendar16.set(11, bcdToBin(buffer[offset47]));
            int i16 = offset48 + 1;
            calendar16.set(5, bcdToBin(buffer[offset48]));
            return calendar16.getTime();
        } else if (type == 13) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar17 = Calendar.getInstance();
            int offset49 = offset + 1;
            calendar17.set(5, bcdToBin(buffer[offset]));
            int i17 = offset49 + 1;
            calendar17.set(2, bcdToBin(buffer[offset49]) - 1);
            return calendar17.getTime();
        } else if (type == 16) {
            if (buffer.length - offset < 2) {
                return null;
            }
            Calendar calendar18 = Calendar.getInstance();
            int offset50 = offset + 1;
            calendar18.set(2, bcdToBin(buffer[offset]) - 1);
            int i18 = offset50 + 1;
            calendar18.set(5, bcdToBin(buffer[offset50]));
            return calendar18.getTime();
        } else if (type != 3 || buffer.length - offset < 3) {
            return null;
        } else {
            Calendar calendar19 = Calendar.getInstance();
            int offset51 = offset + 1;
            calendar19.set(5, bcdToBin(buffer[offset]));
            int offset52 = offset51 + 1;
            calendar19.set(2, bcdToBin(buffer[offset51]) - 1);
            int i19 = offset52 + 1;
            calendar19.set(1, bcdToBin(buffer[offset52]) + 2000);
            return calendar19.getTime();
        }
    }

    public static void timeToBcd(String tm, byte[] buffer, int offset, int type) {
        if (type == 1) {
            DateFormat fmt = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
            Date d = null;
            try {
                d = fmt.parse(tm);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            Calendar cal = Calendar.getInstance();
            cal.setTime(d);
            int year = cal.get(1);
            String sYear = Integer.toString(year);
            String subYear = sYear.substring(2, 4);
            int year2 = Integer.valueOf(subYear).intValue();
            int month = cal.get(2) + 1;
            int day = cal.get(5);
            int hour = cal.get(11);
            int minute = cal.get(12);
            int second = cal.get(13);
            int offset2 = offset + 1;
            buffer[offset] = binToBcd((byte) second);
            int offset3 = offset2 + 1;
            buffer[offset2] = binToBcd((byte) minute);
            int offset4 = offset3 + 1;
            buffer[offset3] = binToBcd((byte) hour);
            int offset5 = offset4 + 1;
            buffer[offset4] = binToBcd((byte) day);
            int offset6 = offset5 + 1;
            buffer[offset5] = binToBcd((byte) month);
            int i = offset6 + 1;
            buffer[offset6] = binToBcd((byte) year2);
        } else if (type == 21) {
            DateFormat fmt2 = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
            Date d2 = null;
            try {
                d2 = fmt2.parse(tm);
            } catch (ParseException e2) {
                e2.printStackTrace();
            }
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(d2);
            int year3 = cal2.get(1);
            String sYear2 = Integer.toString(year3);
            String subYear2 = sYear2.substring(2, 4);
            int year4 = Integer.valueOf(subYear2).intValue();
            int month2 = cal2.get(2) + 1;
            int day2 = cal2.get(5);
            int hour2 = cal2.get(11);
            int minute2 = cal2.get(12);
            int second2 = cal2.get(13);
            int offset7 = offset + 1;
            buffer[offset] = binToBcd((byte) year4);
            int offset8 = offset7 + 1;
            buffer[offset7] = binToBcd((byte) month2);
            int offset9 = offset8 + 1;
            buffer[offset8] = binToBcd((byte) day2);
            int offset10 = offset9 + 1;
            buffer[offset9] = binToBcd((byte) hour2);
            int offset11 = offset10 + 1;
            buffer[offset10] = binToBcd((byte) minute2);
            int i2 = offset11 + 1;
            buffer[offset11] = binToBcd((byte) second2);
        } else if (type == 11) {
            DateFormat fmt3 = new SimpleDateFormat("HH:mm:ss");
            Date d3 = null;
            try {
                d3 = fmt3.parse(tm);
            } catch (ParseException e3) {
                e3.printStackTrace();
            }
            Calendar cal3 = Calendar.getInstance();
            cal3.setTime(d3);
            int hour3 = cal3.get(11);
            int minute3 = cal3.get(12);
            int second3 = cal3.get(13);
            int offset12 = offset + 1;
            buffer[offset] = binToBcd((byte) second3);
            int offset13 = offset12 + 1;
            buffer[offset12] = binToBcd((byte) minute3);
            int i3 = offset13 + 1;
            buffer[offset13] = binToBcd((byte) hour3);
        } else {
            if (type == 12) {
                DateFormat fmt4 = new SimpleDateFormat("HH:mm:ss");
                Date d4 = null;
                try {
                    d4 = fmt4.parse(tm);
                } catch (ParseException e4) {
                    e4.printStackTrace();
                }
                Calendar cal4 = Calendar.getInstance();
                cal4.setTime(d4);
                int hour4 = cal4.get(11);
                int minute4 = cal4.get(12);
                int offset14 = offset + 1;
                buffer[offset] = binToBcd((byte) minute4);
                offset = offset14 + 1;
                buffer[offset14] = binToBcd((byte) hour4);
            }
            if (type == 15) {
                DateFormat fmt5 = new SimpleDateFormat("HH:mm:ss");
                Date d5 = null;
                try {
                    d5 = fmt5.parse(tm);
                } catch (ParseException e5) {
                    e5.printStackTrace();
                }
                Calendar cal5 = Calendar.getInstance();
                cal5.setTime(d5);
                int hour5 = cal5.get(11);
                int minute5 = cal5.get(12);
                int offset15 = offset + 1;
                buffer[offset] = binToBcd((byte) hour5);
                offset = offset15 + 1;
                buffer[offset15] = binToBcd((byte) minute5);
            }
            if (type == 26) {
                DateFormat fmt6 = new SimpleDateFormat("dd HH");
                Date d6 = null;
                try {
                    d6 = fmt6.parse(tm);
                } catch (ParseException e6) {
                    e6.printStackTrace();
                }
                Calendar cal6 = Calendar.getInstance();
                cal6.setTime(d6);
                int day3 = cal6.get(5);
                int hour6 = cal6.get(11);
                int offset16 = offset + 1;
                buffer[offset] = binToBcd((byte) day3);
                offset = offset16 + 1;
                buffer[offset16] = binToBcd((byte) hour6);
            }
            if (type == 22) {
                DateFormat fmt7 = new SimpleDateFormat("yy-MM-dd HH:mm");
                Date d7 = null;
                try {
                    d7 = fmt7.parse(tm);
                } catch (ParseException e7) {
                    e7.printStackTrace();
                }
                Calendar cal7 = Calendar.getInstance();
                cal7.setTime(d7);
                int year5 = cal7.get(1);
                String sYear3 = Integer.toString(year5);
                String subYear3 = sYear3.substring(2, 4);
                int year6 = Integer.valueOf(subYear3).intValue();
                int month3 = cal7.get(2) + 1;
                int day4 = cal7.get(5);
                int hour7 = cal7.get(11);
                int minute6 = cal7.get(12);
                int offset17 = offset + 1;
                buffer[offset] = binToBcd((byte) year6);
                int offset18 = offset17 + 1;
                buffer[offset17] = binToBcd((byte) month3);
                int offset19 = offset18 + 1;
                buffer[offset18] = binToBcd((byte) day4);
                int offset20 = offset19 + 1;
                buffer[offset19] = binToBcd((byte) hour7);
                int i4 = offset20 + 1;
                buffer[offset20] = binToBcd((byte) minute6);
            } else if (type == 10) {
                DateFormat fmt8 = new SimpleDateFormat("yyyy-MM-dd");
                Date d8 = null;
                try {
                    d8 = fmt8.parse(tm);
                } catch (ParseException e8) {
                    e8.printStackTrace();
                }
                Calendar cal8 = Calendar.getInstance();
                cal8.setTime(d8);
                int year7 = cal8.get(1);
                String sYear4 = Integer.toString(year7);
                String subYear4 = sYear4.substring(2, 4);
                int year8 = Integer.valueOf(subYear4).intValue();
                int month4 = cal8.get(2) + 1;
                int day5 = cal8.get(5);
                int week = cal8.get(7);
                int offset21 = offset + 1;
                buffer[offset] = binToBcd((byte) week);
                int offset22 = offset21 + 1;
                buffer[offset21] = binToBcd((byte) day5);
                int offset23 = offset22 + 1;
                buffer[offset22] = binToBcd((byte) month4);
                int i5 = offset23 + 1;
                buffer[offset23] = binToBcd((byte) year8);
            } else {
                if (type == 9) {
                    DateFormat fmt9 = new SimpleDateFormat("dd HH:mm");
                    Date d9 = null;
                    try {
                        d9 = fmt9.parse(tm);
                    } catch (ParseException e9) {
                        e9.printStackTrace();
                    }
                    Calendar cal9 = Calendar.getInstance();
                    cal9.setTime(d9);
                    int day6 = cal9.get(5);
                    int hour8 = cal9.get(11);
                    int min = cal9.get(12);
                    int offset24 = offset + 1;
                    buffer[offset] = binToBcd((byte) min);
                    int offset25 = offset24 + 1;
                    buffer[offset24] = binToBcd((byte) hour8);
                    offset = offset25 + 1;
                    buffer[offset25] = binToBcd((byte) day6);
                }
                if (type == 13) {
                    DateFormat fmt10 = new SimpleDateFormat("yyyy-MM-dd");
                    Date d10 = null;
                    try {
                        d10 = fmt10.parse(tm);
                    } catch (ParseException e10) {
                        e10.printStackTrace();
                    }
                    Calendar cal10 = Calendar.getInstance();
                    cal10.setTime(d10);
                    int day7 = cal10.get(5);
                    int mon = cal10.get(2) + 1;
                    int offset26 = offset + 1;
                    buffer[offset] = binToBcd((byte) day7);
                    offset = offset26 + 1;
                    buffer[offset26] = binToBcd((byte) mon);
                }
                if (type == 16) {
                    DateFormat fmt11 = new SimpleDateFormat("yyyy-MM-dd");
                    Date d11 = null;
                    try {
                        d11 = fmt11.parse(tm);
                    } catch (ParseException e11) {
                        e11.printStackTrace();
                    }
                    Calendar cal11 = Calendar.getInstance();
                    cal11.setTime(d11);
                    int day8 = cal11.get(5);
                    int mon2 = cal11.get(2) + 1;
                    int offset27 = offset + 1;
                    buffer[offset] = binToBcd((byte) mon2);
                    offset = offset27 + 1;
                    buffer[offset27] = binToBcd((byte) day8);
                }
                if (type == 3) {
                    DateFormat fmt12 = new SimpleDateFormat("yyyy-MM-dd");
                    Date d12 = null;
                    try {
                        d12 = fmt12.parse(tm);
                    } catch (ParseException e12) {
                        e12.printStackTrace();
                    }
                    Calendar cal12 = Calendar.getInstance();
                    cal12.setTime(d12);
                    int year9 = cal12.get(1);
                    String sYear5 = Integer.toString(year9);
                    String subYear5 = sYear5.substring(2, 4);
                    int year10 = Integer.valueOf(subYear5).intValue();
                    int month5 = cal12.get(2) + 1;
                    int day9 = cal12.get(5);
                    int offset28 = offset + 1;
                    buffer[offset] = binToBcd((byte) day9);
                    int offset29 = offset28 + 1;
                    buffer[offset28] = binToBcd((byte) month5);
                    int i6 = offset29 + 1;
                    buffer[offset29] = binToBcd((byte) year10);
                } else if (type == 8) {
                    DateFormat fmt13 = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
                    Date d13 = null;
                    try {
                        d13 = fmt13.parse(tm);
                    } catch (ParseException e13) {
                        e13.printStackTrace();
                    }
                    Calendar cal13 = Calendar.getInstance();
                    cal13.setTime(d13);
                    int year11 = cal13.get(1);
                    String sYear6 = Integer.toString(year11);
                    String subYear6 = sYear6.substring(2, 4);
                    int year12 = Integer.valueOf(subYear6).intValue();
                    int month6 = cal13.get(2) + 1;
                    int day10 = cal13.get(5);
                    int hour9 = cal13.get(11);
                    int minute7 = cal13.get(12);
                    int second4 = cal13.get(13);
                    int week2 = cal13.get(7) - 1;
                    int offset30 = offset + 1;
                    buffer[offset] = binToBcd((byte) second4);
                    int offset31 = offset30 + 1;
                    buffer[offset30] = binToBcd((byte) minute7);
                    int offset32 = offset31 + 1;
                    buffer[offset31] = binToBcd((byte) hour9);
                    int offset33 = offset32 + 1;
                    buffer[offset32] = binToBcd((byte) day10);
                    int offset34 = offset33 + 1;
                    buffer[offset33] = (byte) ((binToBcd((byte) week2) << 5) | (binToBcd((byte) month6) & 31));
                    int i7 = offset34 + 1;
                    buffer[offset34] = binToBcd((byte) year12);
                } else if (type == 2) {
                    DateFormat fmt14 = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
                    Date d14 = null;
                    try {
                        d14 = fmt14.parse(tm);
                    } catch (ParseException e14) {
                        e14.printStackTrace();
                    }
                    Calendar cal14 = Calendar.getInstance();
                    cal14.setTime(d14);
                    int year13 = cal14.get(1);
                    String sYear7 = Integer.toString(year13);
                    String subYear7 = sYear7.substring(2, 4);
                    int year14 = Integer.valueOf(subYear7).intValue();
                    int month7 = cal14.get(2) + 1;
                    int day11 = cal14.get(5);
                    int hour10 = cal14.get(11);
                    int minute8 = cal14.get(12);
                    int offset35 = offset + 1;
                    buffer[offset] = binToBcd((byte) minute8);
                    int offset36 = offset35 + 1;
                    buffer[offset35] = binToBcd((byte) hour10);
                    int offset37 = offset36 + 1;
                    buffer[offset36] = binToBcd((byte) day11);
                    int offset38 = offset37 + 1;
                    buffer[offset37] = binToBcd((byte) month7);
                    int i8 = offset38 + 1;
                    buffer[offset38] = binToBcd((byte) year14);
                }
            }
        }
    }

    public static void timeToBcd(Date date, byte[] buffer, int offset, int type) {
        if (type == 1) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            int year = cal.get(1);
            String sYear = Integer.toString(year);
            String subYear = sYear.substring(2, 4);
            int year2 = Integer.valueOf(subYear).intValue();
            int month = cal.get(2) + 1;
            int day = cal.get(5);
            int hour = cal.get(11);
            int minute = cal.get(12);
            int second = cal.get(13);
            int offset2 = offset + 1;
            buffer[offset] = binToBcd((byte) second);
            int offset3 = offset2 + 1;
            buffer[offset2] = binToBcd((byte) minute);
            int offset4 = offset3 + 1;
            buffer[offset3] = binToBcd((byte) hour);
            int offset5 = offset4 + 1;
            buffer[offset4] = binToBcd((byte) day);
            int offset6 = offset5 + 1;
            buffer[offset5] = binToBcd((byte) month);
            int i = offset6 + 1;
            buffer[offset6] = binToBcd((byte) year2);
        } else if (type == 21) {
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date);
            int year3 = cal2.get(1);
            String sYear2 = Integer.toString(year3);
            String subYear2 = sYear2.substring(2, 4);
            int year4 = Integer.valueOf(subYear2).intValue();
            int month2 = cal2.get(2) + 1;
            int day2 = cal2.get(5);
            int hour2 = cal2.get(11);
            int minute2 = cal2.get(12);
            int second2 = cal2.get(13);
            int offset7 = offset + 1;
            buffer[offset] = binToBcd((byte) year4);
            int offset8 = offset7 + 1;
            buffer[offset7] = binToBcd((byte) month2);
            int offset9 = offset8 + 1;
            buffer[offset8] = binToBcd((byte) day2);
            int offset10 = offset9 + 1;
            buffer[offset9] = binToBcd((byte) hour2);
            int offset11 = offset10 + 1;
            buffer[offset10] = binToBcd((byte) minute2);
            int i2 = offset11 + 1;
            buffer[offset11] = binToBcd((byte) second2);
        } else if (type == 29) {
            Calendar cal3 = Calendar.getInstance();
            cal3.setTime(date);
            int year5 = cal3.get(1);
            int month3 = cal3.get(2) + 1;
            int day3 = cal3.get(5);
            int hour3 = cal3.get(11);
            int minute3 = cal3.get(12);
            binToBcdReverse1(year5, buffer, offset, 2);
            int offset12 = offset + 2;
            int offset13 = offset12 + 1;
            buffer[offset12] = binToBcd((byte) month3);
            int offset14 = offset13 + 1;
            buffer[offset13] = binToBcd((byte) day3);
            int offset15 = offset14 + 1;
            buffer[offset14] = binToBcd((byte) hour3);
            int i3 = offset15 + 1;
            buffer[offset15] = binToBcd((byte) minute3);
        } else if (type == 11) {
            Calendar cal4 = Calendar.getInstance();
            cal4.setTime(date);
            int hour4 = cal4.get(11);
            int minute4 = cal4.get(12);
            int second3 = cal4.get(13);
            int offset16 = offset + 1;
            buffer[offset] = binToBcd((byte) second3);
            int offset17 = offset16 + 1;
            buffer[offset16] = binToBcd((byte) minute4);
            int i4 = offset17 + 1;
            buffer[offset17] = binToBcd((byte) hour4);
        } else {
            if (type == 12) {
                Calendar cal5 = Calendar.getInstance();
                cal5.setTime(date);
                int hour5 = cal5.get(11);
                int minute5 = cal5.get(12);
                int offset18 = offset + 1;
                buffer[offset] = binToBcd((byte) minute5);
                offset = offset18 + 1;
                buffer[offset18] = binToBcd((byte) hour5);
            }
            if (type == 15) {
                Calendar cal6 = Calendar.getInstance();
                cal6.setTime(date);
                int hour6 = cal6.get(11);
                int minute6 = cal6.get(12);
                int offset19 = offset + 1;
                buffer[offset] = binToBcd((byte) hour6);
                offset = offset19 + 1;
                buffer[offset19] = binToBcd((byte) minute6);
            }
            if (type == 26) {
                Calendar cal7 = Calendar.getInstance();
                cal7.setTime(date);
                int day4 = cal7.get(5);
                int hour7 = cal7.get(11);
                int offset20 = offset + 1;
                buffer[offset] = binToBcd((byte) day4);
                offset = offset20 + 1;
                buffer[offset20] = binToBcd((byte) hour7);
            }
            if (type == 6) {
                Calendar cal8 = Calendar.getInstance();
                cal8.setTime(date);
                int month4 = cal8.get(2) + 1;
                int year6 = cal8.get(1);
                String sYear3 = Integer.toString(year6);
                String subYear3 = sYear3.substring(2, 4);
                int year7 = Integer.valueOf(subYear3).intValue();
                int offset21 = offset + 1;
                buffer[offset] = binToBcd((byte) month4);
                offset = offset21 + 1;
                buffer[offset21] = binToBcd((byte) year7);
            }
            if (type == 22) {
                Calendar cal9 = Calendar.getInstance();
                cal9.setTime(date);
                int year8 = cal9.get(1);
                String sYear4 = Integer.toString(year8);
                String subYear4 = sYear4.substring(2, 4);
                int year9 = Integer.valueOf(subYear4).intValue();
                int month5 = cal9.get(2) + 1;
                int day5 = cal9.get(5);
                int hour8 = cal9.get(11);
                int minute7 = cal9.get(12);
                int offset22 = offset + 1;
                buffer[offset] = binToBcd((byte) year9);
                int offset23 = offset22 + 1;
                buffer[offset22] = binToBcd((byte) month5);
                int offset24 = offset23 + 1;
                buffer[offset23] = binToBcd((byte) day5);
                int offset25 = offset24 + 1;
                buffer[offset24] = binToBcd((byte) hour8);
                int i5 = offset25 + 1;
                buffer[offset25] = binToBcd((byte) minute7);
            } else if (type == 10) {
                Calendar cal10 = Calendar.getInstance();
                cal10.setTime(date);
                int year10 = cal10.get(1);
                String sYear5 = Integer.toString(year10);
                String subYear5 = sYear5.substring(2, 4);
                int year11 = Integer.valueOf(subYear5).intValue();
                int month6 = cal10.get(2) + 1;
                int day6 = cal10.get(5);
                int week = cal10.get(7);
                int offset26 = offset + 1;
                buffer[offset] = binToBcd((byte) week);
                int offset27 = offset26 + 1;
                buffer[offset26] = binToBcd((byte) day6);
                int offset28 = offset27 + 1;
                buffer[offset27] = binToBcd((byte) month6);
                int i6 = offset28 + 1;
                buffer[offset28] = binToBcd((byte) year11);
            } else {
                if (type == 9) {
                    Calendar cal11 = Calendar.getInstance();
                    cal11.setTime(date);
                    int day7 = cal11.get(5);
                    int hour9 = cal11.get(11);
                    int min = cal11.get(12);
                    int offset29 = offset + 1;
                    buffer[offset] = binToBcd((byte) min);
                    int offset30 = offset29 + 1;
                    buffer[offset29] = binToBcd((byte) hour9);
                    offset = offset30 + 1;
                    buffer[offset30] = binToBcd((byte) day7);
                }
                if (type == 13) {
                    Calendar cal12 = Calendar.getInstance();
                    cal12.setTime(date);
                    int day8 = cal12.get(5);
                    int mon = cal12.get(2) + 1;
                    int offset31 = offset + 1;
                    buffer[offset] = binToBcd((byte) day8);
                    offset = offset31 + 1;
                    buffer[offset31] = binToBcd((byte) mon);
                }
                if (type == 16) {
                    Calendar cal13 = Calendar.getInstance();
                    cal13.setTime(date);
                    int day9 = cal13.get(2) + 1;
                    int mon2 = cal13.get(5);
                    int offset32 = offset + 1;
                    buffer[offset] = binToBcd((byte) day9);
                    offset = offset32 + 1;
                    buffer[offset32] = binToBcd((byte) mon2);
                }
                if (type == 3) {
                    Calendar cal14 = Calendar.getInstance();
                    cal14.setTime(date);
                    int year12 = cal14.get(1);
                    String sYear6 = Integer.toString(year12);
                    String subYear6 = sYear6.substring(2, 4);
                    int year13 = Integer.valueOf(subYear6).intValue();
                    int month7 = cal14.get(2) + 1;
                    int day10 = cal14.get(5);
                    int offset33 = offset + 1;
                    buffer[offset] = binToBcd((byte) day10);
                    int offset34 = offset33 + 1;
                    buffer[offset33] = binToBcd((byte) month7);
                    int i7 = offset34 + 1;
                    buffer[offset34] = binToBcd((byte) year13);
                } else if (type == 8) {
                    Calendar cal15 = Calendar.getInstance();
                    cal15.setTime(date);
                    int year14 = cal15.get(1);
                    String sYear7 = Integer.toString(year14);
                    String subYear7 = sYear7.substring(2, 4);
                    int year15 = Integer.valueOf(subYear7).intValue();
                    int month8 = cal15.get(2) + 1;
                    int day11 = cal15.get(5);
                    int hour10 = cal15.get(11);
                    int minute8 = cal15.get(12);
                    int second4 = cal15.get(13);
                    int week2 = cal15.get(7) - 1;
                    int offset35 = offset + 1;
                    buffer[offset] = binToBcd((byte) second4);
                    int offset36 = offset35 + 1;
                    buffer[offset35] = binToBcd((byte) minute8);
                    int offset37 = offset36 + 1;
                    buffer[offset36] = binToBcd((byte) hour10);
                    int offset38 = offset37 + 1;
                    buffer[offset37] = binToBcd((byte) day11);
                    int offset39 = offset38 + 1;
                    buffer[offset38] = (byte) ((binToBcd((byte) week2) << 5) | (binToBcd((byte) month8) & 31));
                    int i8 = offset39 + 1;
                    buffer[offset39] = binToBcd((byte) year15);
                } else if (type == 2) {
                    Calendar cal16 = Calendar.getInstance();
                    cal16.setTime(date);
                    int year16 = cal16.get(1);
                    String sYear8 = Integer.toString(year16);
                    String subYear8 = sYear8.substring(2, 4);
                    int year17 = Integer.valueOf(subYear8).intValue();
                    int month9 = cal16.get(2) + 1;
                    int day12 = cal16.get(5);
                    int hour11 = cal16.get(11);
                    int minute9 = cal16.get(12);
                    int offset40 = offset + 1;
                    buffer[offset] = binToBcd((byte) minute9);
                    int offset41 = offset40 + 1;
                    buffer[offset40] = binToBcd((byte) hour11);
                    int offset42 = offset41 + 1;
                    buffer[offset41] = binToBcd((byte) day12);
                    int offset43 = offset42 + 1;
                    buffer[offset42] = binToBcd((byte) month9);
                    int i9 = offset43 + 1;
                    buffer[offset43] = binToBcd((byte) year17);
                }
            }
        }
    }

    public static short convertDataItemIdStringToShort(String dataItemId) {
        if (dataItemId.length() != 4) {
            return (short) 0;
        }
        byte[] bFn = new byte[2];
        numberStringToBytes(dataItemId, 16, bFn, 0);
        return htons(bytesToShort(bFn, 0));
    }

    public static String convertDataItemIdShortToString(short dataItemId) {
        byte[] bFn = new byte[2];
        shortToBytes(htons(dataItemId), bFn, 0);
        String sDataItemId = bytesToNumberString(bFn, 0, 2, 16);
        return sDataItemId;
    }

    public static int convertDataItemIdStringToInt(String dataItemId) {
        if (dataItemId.length() != 8) {
            return 0;
        }
        byte[] bFn = new byte[4];
        numberStringToBytes(dataItemId, 16, bFn, 0);
        return htoni(bytesToInt(bFn, 0));
    }

    public static String convertDataItemIdIntToString(int dataItemId) {
        byte[] bFn = new byte[4];
        intToBytes(htoni(dataItemId), bFn, 0);
        String sDataItemId = bytesToNumberString(bFn, 0, 4, 16);
        return sDataItemId;
    }

    public static void formatIpAddress(String ipAddress, byte[] buffer, int offset, byte type) {
        int position1 = ipAddress.indexOf(".");
        int position2 = ipAddress.indexOf(".", position1 + 1);
        int position3 = ipAddress.indexOf(".", position2 + 1);
        byte[] ip = {(byte) (Integer.valueOf(ipAddress.substring(0, position1)).intValue() & 255), (byte) (Integer.valueOf(ipAddress.substring(position1 + 1, position2)).intValue() & 255), (byte) (Integer.valueOf(ipAddress.substring(position2 + 1, position3)).intValue() & 255), (byte) (Integer.valueOf(ipAddress.substring(position3 + 1, ipAddress.length())).intValue() & 255)};
        if (type == 0) {
            for (int i = 0; i < 4; i++) {
                offset++;
                buffer[offset] = ip[i];
            }
            return;
        }
        for (int i2 = 0; i2 < 4; i2++) {
            offset++;
            buffer[offset] = ip[3 - i2];
        }
    }

    public static String parseIpAddress(byte[] buffer, int offset, byte type) {
        String ip;
        if (buffer.length - offset < 4) {
            return "";
        }
        if (type == 0) {
            int offset2 = offset + 1;
            String ip1 = bytesToNumberString(buffer, offset, 1, 10);
            int offset3 = offset2 + 1;
            String ip2 = bytesToNumberString(buffer, offset2, 1, 10);
            int offset4 = offset3 + 1;
            String ip3 = bytesToNumberString(buffer, offset3, 1, 10);
            int i = offset4 + 1;
            String ip4 = bytesToNumberString(buffer, offset4, 1, 10);
            ip = ip1 + "." + ip2 + "." + ip3 + "." + ip4;
        } else {
            int offset5 = offset + 1;
            String ip42 = bytesToNumberString(buffer, offset, 1, 10);
            int offset6 = offset5 + 1;
            String ip32 = bytesToNumberString(buffer, offset5, 1, 10);
            int offset7 = offset6 + 1;
            String ip22 = bytesToNumberString(buffer, offset6, 1, 10);
            int i2 = offset7 + 1;
            String ip12 = bytesToNumberString(buffer, offset7, 1, 10);
            ip = ip12 + "." + ip22 + "." + ip32 + "." + ip42;
        }
        return ip;
    }

    public static String intStringToSubBinString(String s, int beginIndex, int endIndex) {
        BigInteger bigInt = new BigInteger(s);
        String binString = bigInt.toString(2);
        int len = binString.length();
        for (int n = 0; n < 8 - len; n++) {
            binString = "0" + binString;
        }
        return binString.substring(beginIndex, endIndex);
    }

    /* JADX WARN: Type inference failed for: r0v17, types: [double] */
    public static double parseSData(byte[] buffer, int offset, int len, int countRadixPoint, byte invalid) {
        if (len > 5 || countRadixPoint > 10) {
            return 0.0d;
        }
        if (isInvalidData(buffer, offset, len, invalid)) {
            return -10.0d;
        }
        int s = (buffer[(offset + len) - 1] & 240) >> 4;
        double value = bcdToBin(buffer, offset, len, invalid);
        char pow = (char) ((value % Math.pow(10.0d, (len * 2) - 1)) / Math.pow(10.0d, countRadixPoint));
        double value2 = pow;
        if (s == 1) {
            double value3 = 0 - pow;
            value2 = value3;
        }
        return value2;
    }

    public static String getBit(byte[] buffer, int offset, int pos) {
        int index = (pos - 1) / 8;
        int bit = (pos - 1) % 8;
        int val = (buffer[offset + index] >>> bit) & 1;
        return "" + val;
    }

    public static int parseSG8(byte[] buffer, int offset) {
        if (buffer.length - offset < 2) {
            return 0;
        }
        int value = bcdToBin(buffer, offset, 2, (byte) -18);
        return value;
    }

    public static double parseSG10(byte[] buffer, int offset) {
        if (buffer.length - offset < 3) {
            return 0.0d;
        }
        double value = bcdToBin(buffer, offset, 3, 0, (byte) -18);
        return value;
    }

    public static double parseSG27(byte[] buffer, int offset) {
        if (buffer.length - offset < 4) {
            return 0.0d;
        }
        double value = bcdToBin(buffer, offset, 4, 0, (byte) -18);
        return value;
    }

    public static double parseSG13(byte[] buffer, int offset) {
        if (buffer.length - offset < 4) {
            return 0.0d;
        }
        double value = bcdToBin(buffer, offset, 4, 4, (byte) -18);
        return value;
    }

    /* JADX WARN: Type inference failed for: r0v15, types: [double] */
//    public static double parseSG6(byte[] buffer, int offset) {
//        if (buffer.length - offset < 2) {
//            return 0.0d;
//        }
//        if (isInvalidData(buffer, offset, 2, (byte) -18)) {
//            return -10.0d;
//        }
//        System.arraycopy(buffer, offset, temp, 0, 2);
//        byte s = (byte) ((temp[1] & 128) >> 7);
//        byte[] temp = {0, (byte) (temp[1] & Byte.MAX_VALUE)};
//        char bcdToBin = (char) bcdToBin(temp, 0, 2, 2, (byte) -18);
//        double value = bcdToBin;
//        if (s == 1) {
//            double value2 = 0 - bcdToBin;
//            value = value2;
//        }
//        return value;
//    }

    public static void formatSG2(double value, byte[] buffer, int offset) {
        int s = 0;
        if (value < 0.0d) {
            s = 1;
            value = 0.0d - value;
        }
        String sTemp = String.format("%1$.2e", Double.valueOf(value));
        String sPart1 = sTemp.substring(0, sTemp.indexOf("e"));
        String sSign = sTemp.substring(sTemp.indexOf("e") + 1, sTemp.indexOf("e") + 2);
        String sPart2 = sTemp.substring(sTemp.indexOf("e") + 2);
        Float fVal = Float.valueOf(Float.valueOf(sPart1).floatValue() * 100.0f);
        int nVal1 = fVal.intValue();
        int g = 0;
        int nVal2 = Integer.valueOf(sPart2).intValue();
        if (sSign.equalsIgnoreCase("-")) {
            nVal2 = (-1) * nVal2;
        }
        switch (nVal2 - 2) {
            case -3:
                g = 7;
                break;
            case DlmsResponseInfo.RESULT_INVOKE_ID_ERROR /* -2 */:
                g = 6;
                break;
            case -1:
                g = 5;
                break;
            case 0:
                g = 4;
                break;
            case 1:
                g = 3;
                break;
            case 2:
                g = 2;
                break;
            case 3:
                g = 1;
                break;
            case 4:
                g = 0;
                break;
        }
        int a = nVal1 / 100;
        int nVal12 = nVal1 % 100;
        int b = nVal12 / 10;
        int c = nVal12 % 10;
        buffer[offset] = (byte) ((b << 4) | c);
        buffer[offset + 1] = (byte) ((g << 5) | (s << 4) | a);
    }

    public static void formatSG6(double value, byte[] buffer, int offset) {
        int iValue = (int) (value * 100.0d);
        if (value < 0.0d) {
            iValue = ((0 - iValue) & 32767) | (1 << 15);
        }
        binToBcd(iValue, buffer, offset);
    }

    public static double parseSG26(byte[] buffer, int offset) {
        if (buffer.length - offset < 2) {
            return 0.0d;
        }
        double value = bcdToBin(buffer, offset, 2, 3, (byte) -18);
        return value;
    }

    public static void formatSG26(double value, byte[] buffer, int offset) {
        int iValue = (int) (value * 1000.0d);
        binToBcd(iValue, buffer, offset);
    }

    public static float byteToFloat(byte[] b, int index) {
        int l = b[index + 0] & 255;
        return Float.intBitsToFloat((int) ((((int) ((((int) (l | (b[index + 1] << 8))) & 65535) | (b[index + 2] << 16))) & 16777215) | (b[index + 3] << 24)));
    }

    public static boolean checkBcd(byte[] buffer, int offset, int len) {
        for (int i = 0; i < len; i++) {
            byte b1 = (byte) (buffer[i + offset] / 16);
            byte b0 = (byte) (buffer[i + offset] % 16);
            if (!(b0 == 14 && b1 == 14) && (b0 > 9 || b1 > 9)) {
                return false;
            }
        }
        return true;
    }

    public static long bytesToLong(byte[] buffer, int posStart) {
        long dest = buffer[posStart + 0] & 255;
        return dest | ((buffer[posStart + 1] & 255) << 8) | ((buffer[posStart + 2] & 255) << 16) | ((buffer[posStart + 3] & 255) << 24) | ((buffer[posStart + 4] & 255) << 32) | ((buffer[posStart + 5] & 255) << 40);
    }

    public static void longToBytes(long src, byte[] buffer, int posStart) {
        buffer[posStart] = (byte) (src & 255);
        buffer[posStart + 1] = (byte) ((src >>> 8) & 255);
        buffer[posStart + 2] = (byte) ((src >>> 16) & 255);
        buffer[posStart + 3] = (byte) ((src >>> 24) & 255);
        buffer[posStart + 4] = (byte) ((src >>> 32) & 255);
        buffer[posStart + 5] = (byte) ((src >>> 40) & 255);
    }

    public static String decodeIpAddress(byte[] buffer, int offset, byte type) {
        String ip;
        if (buffer.length - offset < 4) {
            return "";
        }
        if (type == 0) {
            int offset2 = offset + 1;
            String ip1 = bytesToNumberString(buffer, offset, 1, 10);
            int offset3 = offset2 + 1;
            String ip2 = bytesToNumberString(buffer, offset2, 1, 10);
            int offset4 = offset3 + 1;
            String ip3 = bytesToNumberString(buffer, offset3, 1, 10);
            int i = offset4 + 1;
            String ip4 = bytesToNumberString(buffer, offset4, 1, 10);
            ip = ip1 + "." + ip2 + "." + ip3 + "." + ip4;
        } else {
            int offset5 = offset + 1;
            String ip42 = bytesToNumberString(buffer, offset, 1, 10);
            int offset6 = offset5 + 1;
            String ip32 = bytesToNumberString(buffer, offset5, 1, 10);
            int offset7 = offset6 + 1;
            String ip22 = bytesToNumberString(buffer, offset6, 1, 10);
            int i2 = offset7 + 1;
            String ip12 = bytesToNumberString(buffer, offset7, 1, 10);
            ip = ip12 + "." + ip22 + "." + ip32 + "." + ip42;
        }
        return ip;
    }

    public static String decodeApn(byte[] buffer, int offset, int len, boolean reverse) {
        StringBuffer appender = new StringBuffer();
        for (int i = 0; i < len; i++) {
            appender.append((char) buffer[offset + i]);
        }
        if (reverse) {
            appender.reverse();
        }
        return appender.toString();
    }
}
