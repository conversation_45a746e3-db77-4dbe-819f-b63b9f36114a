buildscript {
    ext.kotlin_version = '1.9.23'

    dependencies {
        classpath 'com.android.tools.build:gradle:8.3.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.10"

    }
}
// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.3.2' apply false
    id 'com.android.library' version '8.3.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
}
allprojects {
    buildDir = "\\build\\"
}
task clean(type: Delete) {
    delete rootProject.buildDir
}
