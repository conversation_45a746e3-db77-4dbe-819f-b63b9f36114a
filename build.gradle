buildscript {
    ext.kotlin_version = '1.9.23'
    
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // Temporarily commented out to resolve SSL issues
        // classpath 'com.google.gms:google-services:4.3.15'
        // classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.5'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.7.10"

    }
}
// Top-level build file where you can add configuration options common to all sub-projects/modules.
allprojects {
    buildDir = "\\build\\"
}
task clean(type: Delete) {
    delete rootProject.buildDir
}
