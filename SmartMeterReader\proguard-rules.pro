# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep specific classes from being obfuscated
#-keepclassmembers class shoaa.smartmeterreader.MeterReader {
#    public *;
#}


# Keep only necessary Android entry points (prevent crashes)
#-keep public class * extends android.app.Activity
#-keep public class * extends android.app.Service
#-keep public class * extends android.content.BroadcastReceiver
#-keep public class * extends android.content.ContentProvider
#-keep public class * implements android.os.Parcelable

# Retain methods called via reflection
#-keepclassmembers class * {
#    *;
#}

# Obfuscate everything else
#-dontskipnonpubliclibraryclasses
#-dontskipnonpubliclibraryclassmembers
#
## Enable obfuscation for all methods
#-printmapping mapping.txt
#
## Retrofit rules
#-keep class retrofit2.** { *; }
#-keepattributes Signature
#-keepattributes RuntimeVisibleAnnotations
#
## Dagger Hilt rules
#-keep class dagger.** { *; }
#-keep class javax.inject.** { *; }