package com.sewedy.electrometerparser.protocol.connection.bluetooth;

import android.bluetooth.BluetoothSocket;
import android.text.TextUtils;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

public class ConnectedThread extends Thread {
    DataCallback dataCallback;
    int packetLength = -1;
    int offset = 0;
    private BluetoothSocket mmSocket;
    private Connection conn;
    private boolean isRead;
    private byte[] writtenPacket;
    private boolean isDebuggable = false;
    private boolean isProbPacket = true;
    private Packet packetModel;
    private int expectedLength = 0;

    public ConnectedThread(BluetoothSocket socket, DataCallback dataCallback, long initialMillis) throws IOException {
        mmSocket = socket;
        this.dataCallback = dataCallback;
        conn = new Connection();
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public byte[] getWrittenPacket() {
        return writtenPacket;
    }

    public void setWrittenPacket(byte[] writtenPacket) {
        this.writtenPacket = writtenPacket;
    }

    public boolean isDebuggable() {
        return isDebuggable;
    }

    public void setDebuggable(boolean debuggable) {
        isDebuggable = debuggable;
    }

    public boolean isProbPacket() {
        return isProbPacket;
    }

    public void setProbPacket(boolean probPacket) {
        isProbPacket = probPacket;
    }

    public int getExpectedLength() {
        return expectedLength;
    }

    public void setExpectedLength(int expectedLength) {
        this.expectedLength = expectedLength;
    }

    public synchronized void run() {
        if (isRead()) {
            byte[] buffer = new byte[2048];
            byte[] data = null;
            packetModel = new Packet();
            int len;
            try {
                if (isProbPacket()) {
                    packetLength = 12;
                    offset = 0;
                    while (packetLength > offset) {
                        len = mmSocket.getInputStream().read(buffer);
                        offset += len;
                        data = Arrays.copyOf(buffer, len);
                        String result = conn.getMeterPdu(data);
                        result = (TextUtils.isEmpty(packetModel.getData()) ? "" : packetModel.getData()) + result;
                        packetModel.setData(result);
                    }
                    packetLength = -1;
                    offset = 1;
                } else {
                    if (getExpectedLength() > 0) {
                        packetLength = getExpectedLength();
                    }
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    if (isDebuggable) {
                        while (packetLength > offset || packetLength == -1) {
                            len = mmSocket.getInputStream().read(buffer);
                            offset += len;
                            data = Arrays.copyOf(buffer, len);
                            outputStream.write(data);
                            if (packetLength == -1 && null != outputStream.toByteArray() && outputStream.toByteArray().length > 1) {
                                packetLength = (outputStream.toByteArray()[1] & 0xFF) + ((outputStream.toByteArray()[2] & 0xFF) * (256));
                                packetLength += 5;
//                                Log.d("Packet length", packetLength + "");
                            }
                        }
                    } else {
                        while (packetLength > offset || packetLength == -1) {
                            len = mmSocket.getInputStream().read(buffer);
                            offset += len;
                            data = Arrays.copyOf(buffer, len);
                            outputStream.write(data);
                            if (packetLength == -1 && null != outputStream.toByteArray() && outputStream.toByteArray().length > 1) {
                                packetLength = (outputStream.toByteArray()[1] & 0xFF) + ((outputStream.toByteArray()[2] & 0xFF) * (256));
                                packetLength += 5;
//                                Log.d("Packet length", packetLength + "");
                            }
                        }
                    }
                    String result = conn.getMeterPdu(outputStream.toByteArray());
                    packetModel.setData(result);
                    packetLength = -1;
                    offset = 0;
                    //                    Log.d("TAG", "packet data retrieved success!!");
                }
                Log.d("TAG", "prob res: " + packetModel.getData());
                dataCallback.onResult(packetModel);
            } catch (Exception e) {
                dataCallback.onResult(null);
//                Log.d("error", e.getMessage());
                return;
            }
        } else {
            if (getWrittenPacket() == null || getWrittenPacket().length == 0) {
                return;
            }
            try {
                mmSocket.getOutputStream().write(getWrittenPacket());

            } catch (IOException e) {
//                Log.d("error", e.getMessage());
            }
        }
    }
}
