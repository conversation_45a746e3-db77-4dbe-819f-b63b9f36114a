package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/SecurityControl.class */
public class SecurityControl {
    private boolean compression;
    private int keySet;
    private boolean encryption;
    private boolean authentication;
    private int securitySuiteId;

    public SecurityControl(byte value) {
        this.compression = (value & Byte.MIN_VALUE) == -128;
        this.keySet = (value & 64) == 64 ? 1 : 0;
        this.encryption = (value & 32) == 32;
        this.authentication = (value & 16) == 16;
        this.securitySuiteId = value & 15;
    }

    public SecurityControl(boolean compression, int keySet, boolean encryption, boolean authentication, int securitySuiteId) {
        this.compression = compression;
        this.keySet = keySet;
        this.encryption = encryption;
        this.authentication = authentication;
        this.securitySuiteId = securitySuiteId;
    }

    public int getSecuritySuiteId() {
        return this.securitySuiteId;
    }

    public boolean isAuthentication() {
        return this.authentication;
    }

    public boolean isEncryption() {
        return this.encryption;
    }

    public int getKeySet() {
        return this.keySet;
    }

    public boolean isCompression() {
        return this.compression;
    }

    public byte getByteValue() {
        byte value = (byte) this.securitySuiteId;
        if (this.compression) {
            value = (byte) (value | Byte.MIN_VALUE);
        }
        if (this.keySet == 1) {
            value = (byte) (value | 64);
        }
        if (this.encryption) {
            value = (byte) (value | 32);
        }
        if (this.authentication) {
            value = (byte) (value | 16);
        }
        return value;
    }
}
