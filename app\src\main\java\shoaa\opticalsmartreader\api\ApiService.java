package shoaa.opticalsmartreader.api;


import java.util.HashMap;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import shoaa.opticalsmartreader.logic.login.LoginResponse;
import shoaa.opticalsmartreader.models.Branch;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.CycleDate;

public interface ApiService {
    @GET("APILogIns")
    Call<LoginResponse> login(@Query("UserName") String UserName, @Query("UserPassWord") String UserPassWord, @Query("User_SerialNumber") String User_SerialNumber);

    @GET("APIShoaaGetAllClients")
    Call<Client[]> getAllClients(@Query("UserSessionID") String UserSessionID, @Query("User_SerialNumber") String User_SerialNumber);

    @GET("APIGetSQLTables")
    Call<CycleDate[]> getCycleDate(@Query("UserSessionID") String UserSessionID, @Query("TableName") String TableName, @Query("Hndsa") String Hndsa, @Query("User_SerialNumber") String User_SerialNumber);

    @GET("APIGetSQLTables")
    Call<Branch[]> getBranches(@Query("UserSessionID") String UserSessionID, @Query("TableName") String TableName, @Query("Hndsa") String Hndsa, @Query("User_SerialNumber") String User_SerialNumber);

    @POST("APISaveShoaaDatas")
    Call<HashMap<String, Object>> sendMeterData(@Body HashMap<String, Object> body);
}
