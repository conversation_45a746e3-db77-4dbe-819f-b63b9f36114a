allprojects {
    buildscript {
        repositories {
            maven {
                url "https://repo1.maven.org/maven2"
                allowInsecureProtocol = false
            }
            maven {
                url "https://dl.google.com/dl/android/maven2/"
                allowInsecureProtocol = false
            }
            gradlePluginPortal()
            google()
            mavenCentral()
        }
    }
    
    repositories {
        maven {
            url "https://repo1.maven.org/maven2"
            allowInsecureProtocol = false
        }
        maven {
            url "https://dl.google.com/dl/android/maven2/"
            allowInsecureProtocol = false
        }
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation"
    }
}
