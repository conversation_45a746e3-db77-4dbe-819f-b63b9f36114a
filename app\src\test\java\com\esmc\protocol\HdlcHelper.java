package com.esmc.protocol;

import com.esmc.protocol.model.HdlcResponseInfo;
import com.esmc.protocol.model.ProtocolProfile;
import com.esmc.protocol.model.parameter.Hdlc;
import com.esmc.protocol.utils.CrcChecksum;
import com.esmc.protocol.utils.HexUtils;
import com.esmc.protocol.utils.MyConverter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/HdlcHelper.class */
public class HdlcHelper {
    public static final int FRAME_SNRM = 0;
    public static final int FRAME_UA = 1;
    public static final int FRAME_I = 2;
    public static final int FRAME_RNR = 3;
    public static final int FRAME_RR = 4;
    public static final int FRAME_DM = 5;
    public static final int FRAME_FRMR = 6;
    public static final int FRAME_UI = 7;
    public static final int FRAME_DISC = 8;
    private static final byte[] LLC_SEND = {-26, -26, 0};
    private static final byte[] LLC_RECV = {-26, -25, 0};

    public static String request(int frame, byte[] pdu, ProtocolProfile profile, Hdlc hdlc) {
        switch (frame) {
            case 0:
                return packSnrm(hdlc);
            case 2:
                return packI(pdu, profile, hdlc);
            case 8:
                return packDisc(hdlc);
            default:
                return "";
        }
    }

    public static HdlcResponseInfo response(String pdu, ProtocolProfile profile, Hdlc hdlc) {
        byte[] vs = MyConverter.hexStringToBytes(pdu);
        if (!validate(vs, hdlc)) {
            return new HdlcResponseInfo(false);
        }
        HdlcResponseInfo responseInfo = new HdlcResponseInfo();
        byte ctrl = vs[5];
        if (!updateSequenceNumberAndFrameType(ctrl, profile, responseInfo)) {
            return responseInfo;
        }
        if (2 == responseInfo.getHdlcFrameType() || 7 == responseInfo.getHdlcFrameType()) {
            if (vs.length < 11) {
                responseInfo.setResult(false);
                return responseInfo;
            } else if (!Arrays.equals(Arrays.copyOfRange(vs, 8, 11), LLC_RECV)) {
                responseInfo.setResult(false);
                return responseInfo;
            } else {
                byte[] info = new byte[(vs.length - 11) - 3];
                System.arraycopy(vs, 11, info, 0, info.length);
                responseInfo.setResult(true);
                responseInfo.setInfo(MyConverter.bytesToHexString(info, false));
            }
        }
        if (1 == responseInfo.getHdlcFrameType()) {
            if (vs.length < 11 || null == profile) {
                responseInfo.setResult(true);
                return responseInfo;
            }
            byte[] info2 = new byte[vs.length - 11];
            System.arraycopy(vs, 8, info2, 0, info2.length);
            if (!updateOptionParameters(info2, profile)) {
                responseInfo.setResult(false);
                return responseInfo;
            }
            responseInfo.setResult(true);
        }
        return responseInfo;
    }

    private static boolean validate(byte[] vs, Hdlc hdlc) {
        if (vs.length < 9 || vs[0] != 126 || vs[vs.length - 1] != 126 || vs[1] != -96) {
            return false;
        }
        byte serverAddr = (byte) convertToAddress(hdlc.getServerAddress());
        byte clientAddr = (byte) convertToAddress(hdlc.getClientAddress());
        if (serverAddr != vs[4] || clientAddr != vs[3]) {
            return false;
        }
        byte b = vs[2];
        if (b + 2 != vs.length) {
            return false;
        }
        long crc = vs[vs.length - 3] & 255;
        long crc2 = crc | ((vs[vs.length - 2] << 8) & 65280);
        long calcCrc = CrcChecksum.checkCrc16(vs, 1, vs.length - 4);
        if (calcCrc != crc2) {
            return false;
        }
        if (b <= 7) {
            return true;
        }
        long hcs = vs[6] & 255;
        long hcs2 = hcs | ((vs[7] << 8) & 65280);
        long calcHcs = CrcChecksum.checkCrc16(vs, 1, 5);
        if (calcHcs != hcs2) {
            return false;
        }
        return true;
    }

    private static boolean updateSequenceNumberAndFrameType(byte ctrl, ProtocolProfile profile, HdlcResponseInfo responseInfo) {
        int rsn = (ctrl >> 5) & 7;
        int ssn = (ctrl >> 1) & 7;
        if ((ctrl & 1) == 0) {
            responseInfo.setHdlcFrameType(2);
            if (null == profile) {
                responseInfo.setResult(false);
                return false;
            } else if (ssn != profile.getSequenceNumberRecv()) {
                responseInfo.setResult(false);
                return false;
            } else if (rsn != profile.getSequenceNumberSend()) {
                responseInfo.setResult(false);
                return false;
            } else {
                profile.setSequenceNumberRecv((ssn + 1) % 8);
                return true;
            }
        } else if ((ctrl & 15) == 5 || (ctrl & 15) == 1) {
            responseInfo.setHdlcFrameType((ctrl & 15) == 5 ? 3 : 4);
            if (null == profile) {
                responseInfo.setResult(false);
                return false;
            } else if (rsn == profile.getSequenceNumberSend()) {
                return true;
            } else {
                responseInfo.setResult(false);
                return false;
            }
        } else {
            int code = ctrl & 239;
            switch (code) {
                case 3:
                    responseInfo.setHdlcFrameType(7);
                    return true;
                case HexUtils.time_type_hhmm /* 15 */:
                    responseInfo.setHdlcFrameType(5);
                    return true;
                case 99:
                    responseInfo.setHdlcFrameType(1);
                    return true;
                case 135:
                    responseInfo.setHdlcFrameType(6);
                    return true;
                default:
                    return false;
            }
        }
    }

    private static boolean updateOptionParameters(byte[] info, ProtocolProfile profile) {
        int pv;
        if (info.length < 3 || info[0] != -127 || info[1] != Byte.MIN_VALUE) {
            return false;
        }
        int pos = 3;
        int state = 0;
        int paramId = 0;
        int paramLen = 0;
        while (pos < info.length) {
            switch (state) {
                case 0:
                    paramId = info[pos];
                    if (paramId == 5 || paramId == 6 || paramId == 7 || paramId == 8) {
                        pos++;
                        state = 1;
                        break;
                    } else {
                        return false;
                    }
                case 1:
                    paramLen = info[pos];
                    if (paramLen <= 4) {
                        pos++;
                        if (pos + paramLen <= info.length) {
                            state = 2;
                            break;
                        } else {
                            return false;
                        }
                    } else {
                        return false;
                    }
                case 2:
                    if (paramLen == 1) {
                        pv = info[pos];
                        pos++;
                    } else if (paramLen == 2) {
                        pv = ((info[pos] << 8) & 65280) | (info[pos + 1] & 255);
                        pos += 2;
                    } else if (paramLen == 3) {
                        pv = ((info[pos] << 16) & 16711680) | ((info[pos + 1] << 8) & 65280) | (info[pos + 2] & 255);
                        pos += 3;
                    } else {
                        pv = ((info[pos] << 24) & (-16777216)) | ((info[pos + 1] << 16) & 16711680) | ((info[pos + 2] << 8) & 65280) | (info[pos + 3] & 255);
                        pos += 4;
                    }
                    if (paramId == 5) {
                        if (pv < profile.getMaxInfoLengthReceive()) {
                            profile.setMaxInfoLengthReceive(pv);
                        }
                    } else if (paramId == 6 && pv < profile.getMaxInfoLengthTransmit()) {
                        profile.setMaxInfoLengthTransmit(pv);
                    }
                    state = 0;
                    break;
                default:
                    return false;
            }
        }
        return true;
    }

    private static int convertToAddress(int addr) {
        int temp = addr << 1;
        return (temp | 1) & 255;
    }

    private static int getControlByteIFrame(ProtocolProfile profile) {
        int snr = profile.getSequenceNumberRecv();
        int sns = profile.getSequenceNumberSend();
        int value = 16 | ((snr & 7) << 5);
        return value | ((sns & 7) << 1);
    }

    private static String packSnrm(Hdlc hdlc) {
        byte[] vs = {126, -96, 32, 3, 3, -109, -2, -55, -127, Byte.MIN_VALUE, 20, 5, 2, 7, -18, 6, 2, 7, -18, 7, 4, 0, 0, 0, 1, 8, 4, 0, 0, 0, 1, -75, -44, 126};
        vs[3] = (byte) convertToAddress(hdlc.getServerAddress());
        vs[4] = (byte) convertToAddress(hdlc.getClientAddress());
        long crc = CrcChecksum.checkCrc16(vs, 1, 5);
        vs[6] = (byte) (crc & 255);
        vs[7] = (byte) ((crc >> 8) & 255);
        long crc2 = CrcChecksum.checkCrc16(vs, 1, 30);
        vs[31] = (byte) (crc2 & 255);
        vs[32] = (byte) ((crc2 >> 8) & 255);
        return MyConverter.bytesToHexString(vs, false);
    }

    private static String packI(byte[] pdu, ProtocolProfile profile, Hdlc hdlc) {
        try {
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            stream.write(126);
            stream.write(160);
            stream.write(0);
            stream.write(convertToAddress(hdlc.getServerAddress()));
            stream.write(convertToAddress(hdlc.getClientAddress()));
            stream.write(getControlByteIFrame(profile));
            stream.write(0);
            stream.write(0);
            stream.write(LLC_SEND);
            stream.write(pdu);
            stream.write(0);
            stream.write(0);
            stream.write(126);
            byte[] vs = stream.toByteArray();
            vs[2] = (byte) (vs.length - 2);
            long crc = CrcChecksum.checkCrc16(vs, 1, 5);
            vs[6] = (byte) (crc & 255);
            vs[7] = (byte) ((crc >> 8) & 255);
            long crc2 = CrcChecksum.checkCrc16(vs, 1, vs.length - 4);
            vs[vs.length - 3] = (byte) (crc2 & 255);
            vs[vs.length - 2] = (byte) ((crc2 >> 8) & 255);
            profile.setSequenceNumberSend((profile.getSequenceNumberSend() + 1) % 8);
            return MyConverter.bytesToHexString(vs, false);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String packDisc(Hdlc hdlc) {
        byte[] vs = {126, -96, 7, 3, 3, 83, Byte.MIN_VALUE, -41, 126};
        vs[3] = (byte) convertToAddress(hdlc.getServerAddress());
        vs[4] = (byte) convertToAddress(hdlc.getClientAddress());
        long crc = CrcChecksum.checkCrc16(vs, 1, 5);
        vs[6] = (byte) (crc & 255);
        vs[7] = (byte) ((crc >> 8) & 255);
        return MyConverter.bytesToHexString(vs, false);
    }
}
