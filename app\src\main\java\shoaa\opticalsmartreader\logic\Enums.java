package shoaa.opticalsmartreader.logic;

public class Enums {


    public enum ActivityType {
        HOME {
            @Override
            public String toString() {
                return "منزلي";
            }
        },
        SHOP {
            @Override
            public String toString() {
                return "تجاري";
            }
        },
        POWER {
            @Override
            public String toString() {
                return "قوي محركة";
            }
        }
    }

    public enum FactoryCodes {
        ESMC {
            @Override
            public String toString() {
                return "المصرية";
            }
        }, ISKRA {
            @Override
            public String toString() {
                return "اسكرا";
            }
        }, ELECTROMETER {
            @Override
            public String toString() {
                return "اليكتروميتر";
            }
        }, MAASARA {
            @Override
            public String toString() {
                return "المعصرة";
            }
        }, HAY2A {
            @Override
            public String toString() {
                return "الهيئة العربية";
            }
        }, GLOBAL {
            @Override
            public String toString() {
                return "جلوبال";
            }
        }, GPI {
            @Override
            public String toString() {
                return "جيزة باور";
            }
        },
    }
}
