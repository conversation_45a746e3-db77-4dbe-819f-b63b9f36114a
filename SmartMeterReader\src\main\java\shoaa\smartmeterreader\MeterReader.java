package shoaa.smartmeterreader;

import android.content.Context;

import shoaa.common.DateUtil;
import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.electrometerreader.ElectrometerReader;
import shoaa.electrometerreader.ElectrometerResponse;
import shoaa.esmcreader.EsmcReader;
import shoaa.esmcreader.EsmcResponse;
import shoaa.globalreader.GlobalReader;
import shoaa.globalreader.GlobalResponse;
import shoaa.globalreader.GlobalVersion;
import shoaa.gpireader.GpiReader;
import shoaa.gpireader.GpiResponse;
import shoaa.hay2areader.Hay2aReader;
import shoaa.hay2areader.Hay2aResponse;
import shoaa.iskrareader.IskraReader;
import shoaa.iskrareader.IskraResponse;
import shoaa.maasarareader.MaasaraReader;
import shoaa.maasarareader.MaasaraResponse;

/**
 * Created by Islam Darwish
 */

public class MeterReader {


    public static ReadingResponse read(Context context, MeterCo meterCo, GlobalVersion globalVersion, ProgressCallback progressCallback) {

        switch (meterCo) {
            case ISKRA:
                IskraResponse iskraResponse = (IskraResponse) IskraReader.read(context, progressCallback);
                if (iskraResponse.readingResult == ReadingResult.SUCCESS) {
                    if (iskraResponse.getMeterId() != null &&
                            iskraResponse.getMeterId().equalsIgnoreCase("0") &&
                            iskraResponse.getCardId().equalsIgnoreCase("0") &&
                            iskraResponse.getCustomerId().equalsIgnoreCase("0") &&
                            iskraResponse.getRechargeAmount().equalsIgnoreCase("0") &&
                            iskraResponse.getRechargeNumber().equalsIgnoreCase("0") &&
                            iskraResponse.getTotalConsumptionKw().equalsIgnoreCase("0") &&
                            iskraResponse.getTotalConsumptionMoney().equalsIgnoreCase("0")) {
                        iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
//                        try {
//                            if (Double.parseDouble(iskraResponse.getTotalConsumptionKw().replaceAll("[^0-9.-]", "")) <
//                                    (Double.parseDouble(iskraResponse.getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(iskraResponse.getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", "")))) {
//                                iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
                    try {
                        if (!(Integer.parseInt(iskraResponse.getCurrentTarrifInstalling()) <= 7 &&
                                Integer.parseInt(iskraResponse.getCurrentTarrifInstalling()) >= 1)) {
                            iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
                    try {
                        if (Double.parseDouble(iskraResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) >= 100000) {
                            iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        iskraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }

                }
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return iskraResponse;
            case GLOBAL:
                GlobalResponse globalResponse = (GlobalResponse) GlobalReader.read(context, globalVersion, progressCallback);
                if (globalResponse.readingResult == ReadingResult.SUCCESS) {
                    try {
                        if (!(Integer.parseInt(globalResponse.getTarrifID()) <= 7 &&
                                Integer.parseInt(globalResponse.getTarrifID()) >= 1)) {
                            globalResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        globalResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }

                    try {
                        if (Double.parseDouble(globalResponse.getTotalConsum().replaceAll("[^0-9.-]", "")) <
                                (Double.parseDouble(globalResponse.getConsm1().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm2().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm3().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm4().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm5().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm6().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm7().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm8().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm9().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm10().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm11().replaceAll("[^0-9.-]", ""))
                                        + Double.parseDouble(globalResponse.getConsm12().replaceAll("[^0-9.-]", "")))) {
                            globalResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        globalResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }

                }
                try {
                    Thread.sleep(200);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return globalResponse;
            case ESMC:
                EsmcResponse esmcResponse = (EsmcResponse) EsmcReader.read(context, progressCallback);
                if (esmcResponse.readingResult == ReadingResult.SUCCESS) {
                    if (esmcResponse.getMeterId() != null &&
                            esmcResponse.getMeterId().equalsIgnoreCase("0") &&
                            esmcResponse.getCardId().equalsIgnoreCase("0") &&
                            esmcResponse.getCustomerId().equalsIgnoreCase("0") &&
                            esmcResponse.getRechargeAmount().equalsIgnoreCase("0") &&
                            esmcResponse.getRechargeNumber().equalsIgnoreCase("0") &&
                            esmcResponse.getTotalConsumptionKw().equalsIgnoreCase("0") &&
                            esmcResponse.getTotalConsumptionMoney().equalsIgnoreCase("0")) {
                        esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
//                        try {
//                            if (Double.parseDouble(esmcResponse.getTotalConsumptionKw().replaceAll("[^0-9.-]", "")) <
//                                    (Double.parseDouble(esmcResponse.getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(esmcResponse.getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", "")))) {
//                                esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
                    try {
                        if (!(Integer.parseInt(esmcResponse.getCurrentTarrifInstalling()) <= 7 &&
                                Integer.parseInt(esmcResponse.getCurrentTarrifInstalling()) >= 1)) {
                            esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception e) {
                        esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
                    try {
                        if (Double.parseDouble(esmcResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) >= 100000) {
                            esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        esmcResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }

                }
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return esmcResponse;
            case GPI:
                GpiResponse gpiResponse = (GpiResponse) GpiReader.read(context, progressCallback);
                if (gpiResponse.readingResult == ReadingResult.SUCCESS) {
                    if (gpiResponse.getMeter_ID() != null &&
                            gpiResponse.getMeter_ID().equalsIgnoreCase("0") &&
                            gpiResponse.getCardID().equalsIgnoreCase("0") &&
                            gpiResponse.getCardID().equalsIgnoreCase("0") &&
                            gpiResponse.getRecharge_number().equalsIgnoreCase("0") &&
                            gpiResponse.getRecharge_Amount().equalsIgnoreCase("0") &&
                            gpiResponse.getTotal_consumption_kw().equalsIgnoreCase("0") &&
                            gpiResponse.getTotal_consumption_mony().equalsIgnoreCase("0")) {
                        gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
//                        try {
//                            if (Double.parseDouble(gpiResponse.getTotal_consumption_kw().replaceAll("[^0-9.-]", "")) <
//                                    (Double.parseDouble(gpiResponse.getMonth_1_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_2_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_3_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_4_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_5_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_6_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_7_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_8_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_9_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_10_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_11_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(gpiResponse.getMonth_12_consumption_kWh().replaceAll("[^0-9.-]", "")))) {
//                                gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
                    try {
                        if (!(Integer.parseInt(gpiResponse.getItem_13_Current_tarrif_installing()) <= 7 &&
                                Integer.parseInt(gpiResponse.getItem_13_Current_tarrif_installing()) >= 1)) {
                            gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception e) {
                        gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
                    try {
                        if (Double.parseDouble(gpiResponse.getCurrent_month_consumption_KW().replaceAll("[^0-9.-]", "")) >= 100000) {
                            gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                    } catch (Exception ignore) {
                        gpiResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    }
                }
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return gpiResponse;
            case MAASARA:
                MaasaraResponse maasaraResponse = (MaasaraResponse) MaasaraReader.read(context, progressCallback);
                if (maasaraResponse.readingResult == ReadingResult.SUCCESS) {
//                        if (maasaraResponse.getMeterId() != null &&
//                                maasaraResponse.getMeterId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getCardId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getCustomerId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getRechargeAmount().equalsIgnoreCase("0") &&
//                                maasaraResponse.getRechargeNumber().equalsIgnoreCase("0") &&
//                                maasaraResponse.getTotalConsumptionKw().equalsIgnoreCase("0") &&
//                                maasaraResponse.getTotalConsumptionMoney().equalsIgnoreCase("0")) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (Double.parseDouble(maasaraResponse.getTotalConsumptionKw().replaceAll("[^0-9.-]", "")) <
//                                    (Double.parseDouble(maasaraResponse.getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", "")))) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (!(Integer.parseInt(maasaraResponse.getCurrentTarrifInstalling()) <= 7 &&
//                                    Integer.parseInt(maasaraResponse.getCurrentTarrifInstalling()) >= 1)) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception e) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (Double.parseDouble(maasaraResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) >= 100000) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }


//                        if (maasaraResponse.getMeterId() != null &&
//                                maasaraResponse.getMeterId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getCardId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getCustomerId().equalsIgnoreCase("0") &&
//                                maasaraResponse.getRechargeAmount().equalsIgnoreCase("0") &&
//                                maasaraResponse.getRechargeNumber().equalsIgnoreCase("0") &&
//                                maasaraResponse.getTotalConsumptionKw().equalsIgnoreCase("0") &&
//                                maasaraResponse.getTotalConsumptionMoney().equalsIgnoreCase("0")) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (Double.parseDouble(maasaraResponse.getTotal_consumption_kw().replaceAll("[^0-9.-]", "")) <
//                                    (Double.parseDouble(maasaraResponse.getMonth_1_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_2_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_3_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_4_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_5_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_6_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_7_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_8_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_9_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_10_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_11_consumption_kWh().replaceAll("[^0-9.-]", ""))
//                                            + Double.parseDouble(maasaraResponse.getMonth_12_consumption_kWh().replaceAll("[^0-9.-]", "")))) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (!(Integer.parseInt(maasaraResponse.getCurrentTarrifInstalling()) <= 7 &&
//                                    Integer.parseInt(maasaraResponse.getCurrentTarrifInstalling()) >= 1)) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception e) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (Double.parseDouble(maasaraResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) >= 100000) {
//                                maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            maasaraResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
                }
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return maasaraResponse;
            case Hay2a:
                Hay2aResponse hay2aResponse = (Hay2aResponse) Hay2aReader.read(context, progressCallback);
                try {
                    Thread.sleep(500);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return hay2aResponse;
            case ELECTROMETER:
                ElectrometerResponse electrometerResponse = (ElectrometerResponse) ElectrometerReader.read(context, progressCallback);
                try {
                    if (electrometerResponse.readingResult == ReadingResult.SUCCESS) {
                        if (electrometerResponse.getCurrentTarrifInstalling() == null ||
                                electrometerResponse.getCurrentTarrifInstalling().equalsIgnoreCase("NF")) {
                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                        if (electrometerResponse.getMeterId() != null &&
                                electrometerResponse.getMeterId().equalsIgnoreCase("0") &&
                                electrometerResponse.getCardId().equalsIgnoreCase("0") &&
                                electrometerResponse.getCustomerId().equalsIgnoreCase("0") &&
                                electrometerResponse.getRechargeAmount().equalsIgnoreCase("0") &&
                                electrometerResponse.getRechargeNumber().equalsIgnoreCase("0") &&
                                electrometerResponse.getTotalConsumptionKw().equalsIgnoreCase("0") &&
                                electrometerResponse.getTotalConsumptionMoney().equalsIgnoreCase("0")) {
                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }

                        if (electrometerResponse.getMeterStatus() == null ||
                                electrometerResponse.getMeterStatus().equalsIgnoreCase("0") ||
                                electrometerResponse.getRelayStatus() == null ||
                                electrometerResponse.getRelayStatus().equalsIgnoreCase("0") ||
                                electrometerResponse.getBatteryStatus() == null ||
                                electrometerResponse.getBatteryStatus().equalsIgnoreCase("0") ||
                                electrometerResponse.getTopCoverStatus() == null ||
                                electrometerResponse.getTopCoverStatus().equalsIgnoreCase("0") ||
                                electrometerResponse.getSideCoverStatus() == null ||
                                electrometerResponse.getSideCoverStatus().equalsIgnoreCase("0")
                        ) {
                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }

                        try {
                            if (!DateUtil.isValidDate(electrometerResponse.getMeterDateAndTime()) ||
                                    DateUtil.parse(electrometerResponse.getMeterDateAndTime()).before(DateUtil.parse("01-01-2000"))) {
                                electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            }
                        } catch (Exception e) {
                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
                        try {
                            if (!(Integer.parseInt(electrometerResponse.getCurrentTarrifInstalling()) <= 7 &&
                                    Integer.parseInt(electrometerResponse.getCurrentTarrifInstalling()) >= 1)) {
                                electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            }
                        } catch (Exception e) {
                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        }
//                        try {
//                            if (Double.parseDouble(electrometerResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) == 554502.659) {
//                                electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                        try {
//                            if (Double.parseDouble(electrometerResponse.getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", "")) >= 1000000) {
//                                electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
//                        } catch (Exception ignore) {
//                            electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                        }
//                            try {
//                                if (Double.parseDouble(electrometerResponse.getTotalConsumptionKw().replaceAll("[^0-9.-]", "")) <
//                                        (Double.parseDouble(electrometerResponse.getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""))
//                                                + Double.parseDouble(electrometerResponse.getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", "")))) {
//                                    electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                                }
//                            } catch (Exception ignore) {
//                                electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
//                            }
                    }
                    try {
                        Thread.sleep(500);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    electrometerResponse = new ElectrometerResponse();
                    electrometerResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                }
                ConnectionManager.Companion.getInstance().writeSession(context);
                return electrometerResponse;
            default:
                return null;
        }
    }
}
