<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:background="@android:color/white"
    tools:context=".ui.BarcodeScanningActivity">

    <FrameLayout
        android:id="@+id/captureLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <androidx.camera.view.PreviewView
            android:id="@+id/cameraPreview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <shoaa.barcodescanner.ui.custom.ViewFinderOverlay
            android:id="@+id/overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layerType="software" />

        <ImageView
            android:id="@+id/ivFlashControl"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="top|end"
            android:layout_margin="16dp"
            android:background="@drawable/flash_button_background"
            android:contentDescription="@string/turn_flash_on_off"
            android:padding="12dp"
            android:src="@drawable/ic_round_flash_on" />

        <ImageView
            android:id="@+id/ivCapture"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:layout_gravity="center|bottom"
            android:layout_margin="16dp"
            android:background="@drawable/flash_button_background"
            android:contentDescription="@string/turn_flash_on_off"
            android:padding="12dp"
            android:src="@drawable/capture"
            android:visibility="visible" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/confirmLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <ImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#000000" />

        <ImageView
            android:id="@+id/ivDone"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="top|end"
            android:layout_margin="16dp"
            android:background="@drawable/flash_button_background"
            android:contentDescription="@string/image_ok"
            android:foregroundTint="#ffffff"
            android:padding="12dp"
            android:src="@drawable/done" />

        <ImageView
            android:id="@+id/ivCancel"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="top|start"
            android:layout_margin="16dp"
            android:background="@drawable/flash_button_background"
            android:contentDescription="@string/image_cancel"
            android:foregroundTint="#ffffff"
            android:padding="12dp"
            android:src="@drawable/close" />

    </FrameLayout>


</FrameLayout>