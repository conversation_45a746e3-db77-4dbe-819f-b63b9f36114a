package shoaa.connectionmanager

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.hardware.usb.UsbDevice

interface ConnectionInterface {
    fun connectAsync(context: Context, device: BluetoothDevice, timeOut: Int): Int
    fun connectAsync(context: Context, device: UsbDevice, timeOut: Int): Int
    fun reset(): Boolean
    fun disconnect()
    fun setBaudRateAsync(baudRate: BaudRate): Boolean
    fun sendAsync(hexString: String, retryCount: Int, sendTimeOut: Int, readTimeOut: Int): String
    fun sendWithLengthAsync(
        hexString: String,
        retryCount: Int,
        sendTimeOut: Int,
        expectedLength: Int
    ): String
}