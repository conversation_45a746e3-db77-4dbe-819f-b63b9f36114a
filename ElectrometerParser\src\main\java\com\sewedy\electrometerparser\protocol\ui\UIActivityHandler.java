package com.sewedy.electrometerparser.protocol.ui;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.widget.Toast;

public class UIActivityHandler implements UIHandler {
    private final Activity activity;
    private ProgressDialog progressDialog;
    private AlertDialog alertdialog;

    public UIActivityHandler(Activity activity) {
        this.activity = activity;
    }

    public void showProgressDialog() {
        activity.runOnUiThread(new Runnable() {
            public void run() {
                if (progressDialog == null)
                    progressDialog = new ProgressDialog(activity);
                progressDialog.setMessage("Loading...");
                progressDialog.setCancelable(false);
                progressDialog.setTitle("Info");
                progressDialog.show();
            }
        });
    }

    public void showMessage(final String title, final String msg) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (alertdialog == null) {
                    AlertDialog.Builder builder = new AlertDialog.Builder(activity);
                    builder.setCancelable(true);
                    alertdialog = builder.create();
                }
                alertdialog.setTitle(title);
                alertdialog.setMessage(msg);
                alertdialog.show();
            }
        });
    }

    public void showError(final String error) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(activity, error, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public void stopProgressDialog() {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (progressDialog != null) {
                    progressDialog.dismiss();
                }
            }
        });
    }
}
