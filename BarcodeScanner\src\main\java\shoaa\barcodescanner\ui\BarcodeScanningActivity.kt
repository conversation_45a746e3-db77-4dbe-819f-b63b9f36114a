package shoaa.barcodescanner.ui

import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.util.Size
import android.view.OrientationEventListener
import android.view.Surface
import android.view.View
import android.view.Window
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.TorchState
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.core.resolutionselector.ResolutionStrategy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import shoaa.barcodescanner.BuildConfig
import shoaa.barcodescanner.R
import shoaa.barcodescanner.analyzer.MLKitBarcodeAnalyzer
import shoaa.barcodescanner.analyzer.ScanningResultListener
import shoaa.barcodescanner.databinding.ActivityBarcodeScanningBinding
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


class BarcodeScanningActivity : AppCompatActivity() {


    private lateinit var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>
    private lateinit var binding: ActivityBarcodeScanningBinding

    /** Blocking camera operations are performed using this executor */
    private lateinit var cameraExecutor: ExecutorService
    private lateinit var analyzer: ImageAnalysis.Analyzer
    private var flashEnabled = false
    private var scanBarcode = true
    private var zoom = true
    private lateinit var cuptureLayout: FrameLayout
    private lateinit var confirmLayout: FrameLayout
    private lateinit var imageView: ImageView
    private lateinit var confirmBtn: ImageView
    private lateinit var cancelBtn: ImageView
    var bitmap: Bitmap? = null
    var camera: Camera? = null
    private val thread = Thread {
        try {
            if (!BuildConfig.DEBUG)
                Thread.sleep(10000)
//            (analyzer as MLKitBarcodeAnalyzer).getListener().onScanned("", null)
            runOnUiThread { binding.ivCapture.visibility = View.VISIBLE }

        } catch (_: Exception) {
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = ActivityBarcodeScanningBinding.inflate(layoutInflater)
        scanBarcode = intent.getBooleanExtra("scan", true)
        zoom = intent.getBooleanExtra("zoom", true)
        cuptureLayout = binding.captureLayout
        confirmLayout = binding.confirmLayout
        confirmBtn = binding.ivDone
        cancelBtn = binding.ivCancel
        imageView = binding.image
        confirmLayout.visibility = View.GONE
        cuptureLayout.visibility = View.VISIBLE

        setContentView(binding.root)
        if (thread.isAlive)
            thread.interrupt()
        if (scanBarcode) {
            binding.ivCapture.visibility = View.GONE
        } else {
            binding.ivCapture.visibility = View.VISIBLE
        }

        cancelBtn.setOnClickListener {
            bitmap = null
            confirmLayout.visibility = View.GONE
            cuptureLayout.visibility = View.VISIBLE
        }
        confirmBtn.setOnClickListener {
            var file: File? = null
            if (bitmap != null && this.getExternalFilesDir("Images") != null) {
                file = File(this.getExternalFilesDir("Images"), "barcode.jpg")
                val fOut = FileOutputStream(file)
                bitmap!!.compress(Bitmap.CompressFormat.JPEG, 100, fOut)
                fOut.flush()
                fOut.close()
            }
            (analyzer as MLKitBarcodeAnalyzer).getListener().onScanned(
                "",
                file?.absolutePath
            )
        }

        cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        // Initialize our background executor
        cameraExecutor = Executors.newSingleThreadExecutor()

        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()
            bindPreview(cameraProvider)
        }, ContextCompat.getMainExecutor(this))


        binding.ivCapture.setOnClickListener {
            var dialog: ProgressDialog? = null
            dialog = ProgressDialog.show(
                this, "",
                "يرجي الانتظار", true
            )
            dialog.setCancelable(false)
            dialog?.show()
            Thread {
                bitmap = if (zoom)
                    binding.cameraPreview.bitmap
                else
                    (analyzer as MLKitBarcodeAnalyzer).capture()
                runOnUiThread {
                    if (dialog?.isShowing == true) {
                        dialog.dismiss()
                    }
                    if (camera!!.cameraInfo.hasFlashUnit()) {
                        camera!!.cameraControl.enableTorch(false)
                    }
                    confirmLayout.visibility = View.VISIBLE
                    cuptureLayout.visibility = View.GONE
                    imageView.setImageBitmap(bitmap)
                }
            }.start()
        }
        if (scanBarcode) {
            binding.overlay.post {
                binding.overlay.setViewFinder()
            }
        }
        thread.start()
    }

    private fun bindPreview(cameraProvider: ProcessCameraProvider?) {

        if (isDestroyed || isFinishing) {
            //This check is to avoid an exception when trying to re-bind use cases but user closes the activity.
            //java.lang.IllegalArgumentException: Trying to create use case mediator with destroyed lifecycle.
            return
        }

        cameraProvider?.unbindAll()

        val preview: Preview = Preview.Builder()
            .build()
        val cameraSelector: CameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .build()
        val screenSize = Size(binding.cameraPreview.width, binding.cameraPreview.height)
        val resolutionSelector = ResolutionSelector.Builder().setResolutionStrategy(
            ResolutionStrategy(
                screenSize,
                ResolutionStrategy.FALLBACK_RULE_CLOSEST_LOWER_THEN_HIGHER
            )
        ).build()

        val imageAnalysis = ImageAnalysis.Builder()
            .setResolutionSelector(resolutionSelector)
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()

        val orientationEventListener = object : OrientationEventListener(this as Context) {
            override fun onOrientationChanged(orientation: Int) {
                // Monitors orientation values to determine the target rotation value
                val rotation: Int = when (orientation) {
                    in 45..134 -> Surface.ROTATION_270
                    in 135..224 -> Surface.ROTATION_180
                    in 225..314 -> Surface.ROTATION_90
                    else -> Surface.ROTATION_0
                }
                imageAnalysis.targetRotation = rotation
            }
        }
        orientationEventListener.enable()

        //switch the analyzers here, i.e. MLKitBarcodeAnalyzer
        class ScanningListener : ScanningResultListener {

            fun isDark(bitmap: Bitmap): Boolean {
                var dark = false
                val darkThreshold = bitmap.width * bitmap.height * 0.45f
                var darkPixels = 0
                val pixels = IntArray(bitmap.width * bitmap.height)
                bitmap.getPixels(pixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)
                for (pixel in pixels) {
                    val r: Int = Color.red(pixel)
                    val g: Int = Color.green(pixel)
                    val b: Int = Color.blue(pixel)
                    val luminance = 0.299 * r + 0.0f + 0.587 * g + 0.0f + 0.114 * b + 0.0f
                    if (luminance < 45) {
                        darkPixels++
                    }
                }
                if (darkPixels >= darkThreshold) {
                    dark = true
                }
                return dark
            }

            override fun onScanned(result: String, path: String?) {
                runOnUiThread {
                    thread.interrupt()
                    imageAnalysis.clearAnalyzer()
                    cameraProvider?.unbindAll()
                    val data = Intent()
                    data.data = Uri.parse(result)

                    if (path != null) {
                        data.putExtra("path", path)
                    }
                    setResult(RESULT_OK, data)
                    finish()
                }
            }
        }

        analyzer =
            MLKitBarcodeAnalyzer(ScanningListener(), this.getExternalFilesDir("Images"))
        (analyzer as MLKitBarcodeAnalyzer).setPreviewView(binding.cameraPreview).setContext(this)
            .setScan(scanBarcode)
            .setZoom(zoom)

        imageAnalysis.setAnalyzer(cameraExecutor, analyzer)

        preview.setSurfaceProvider(binding.cameraPreview.surfaceProvider)

        camera =
            cameraProvider?.bindToLifecycle(this, cameraSelector, imageAnalysis, preview)
        if (camera != null) {
            (analyzer as MLKitBarcodeAnalyzer).setCamera(camera!!)
            (analyzer as MLKitBarcodeAnalyzer).initFocus(camera!!)
            if (camera!!.cameraInfo.zoomState.value != null) {
                camera!!.cameraControl.setZoomRatio(camera!!.cameraInfo.zoomState.value!!.minZoomRatio)
            }
            if (camera!!.cameraInfo.hasFlashUnit()) {
                binding.ivFlashControl.visibility = View.VISIBLE

                binding.ivFlashControl.setOnClickListener {
                    camera!!.cameraControl.enableTorch(!flashEnabled)
                }

                camera!!.cameraInfo.torchState.observe(this) {
                    it?.let { torchState ->
                        if (torchState == TorchState.ON) {
                            flashEnabled = true
                            binding.ivFlashControl.setImageResource(R.drawable.ic_round_flash_on)
                        } else {
                            flashEnabled = false
                            binding.ivFlashControl.setImageResource(R.drawable.ic_round_flash_off)
                        }
                    }
                }
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        // Shut down our background executor
        cameraExecutor.shutdown()
    }

}