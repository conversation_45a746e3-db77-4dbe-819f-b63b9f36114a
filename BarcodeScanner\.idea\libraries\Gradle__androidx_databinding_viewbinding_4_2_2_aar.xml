<component name="libraryTable">
  <library name="Gradle: androidx.databinding:viewbinding:4.2.2@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8dc6c73990328a98eb30d76e48a6e90b/transformed/jetified-viewbinding-4.2.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0df32f88d4fb8e2ab06722d38148f476/transformed/jetified-viewbinding-4.2.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2a41cea954d7f76de0b4e32e6e7fc6ef/transformed/jetified-viewbinding-4.2.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/52b82b5a0c5146214ddbcc1a2f8a9c0e/transformed/jetified-viewbinding-4.2.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6147183670cfa949ea4a8198ef6b0da1/transformed/jetified-viewbinding-4.2.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/52b82b5a0c5146214ddbcc1a2f8a9c0e/transformed/jetified-viewbinding-4.2.2/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/52b82b5a0c5146214ddbcc1a2f8a9c0e/transformed/jetified-viewbinding-4.2.2/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.databinding/viewbinding/4.2.2/b0a8d54e5511102687ebae0eaef8d8843ffb6761/viewbinding-4.2.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>