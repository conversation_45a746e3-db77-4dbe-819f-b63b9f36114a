package shoaa.opticalsmartreader.ui;

import android.content.Context;
import android.content.Intent;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import shoaa.opticalsmartreader.MainApplication;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.logic.ClientsRecyclerViewAdapter;
import shoaa.opticalsmartreader.logic.CycleDate.CycleDateManager;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.MiniDatabaseMeterData;

public class CustomersDataFragment extends Fragment implements ClientsRecyclerViewAdapter.ItemClickListener {
    Button btnMomarsa;
    RecyclerView recyclerView;
    TextView txtNoDataSync;
    TextView txtNoData;
    SearchView searchView;
    TextView txtLoadingData;
    TextView txtNoSearchResult;
    Spinner filterSpinner;
    Spinner handasaSpinner;
    Spinner mantekaSpinner;
    Spinner yawmeyaSpinner;
    ArrayAdapter<String> readingStateAdapter;
    String[] readingStateStringArray = {"الكل", "غير مقروء", "اعادة كشف", "مقروء", "مغلق", "معطل", "كسر", "سرقه تيار", "مرفوع", "غير موجود", "", "مصيف", "غير متصل", "ممتنع", "", "مخالفة", "هدم و بناء"};
    private final List<Client> fullClientsList = new ArrayList<>();
    private final List<Client> filterdClientsList = new ArrayList<>();
    private ClientsRecyclerViewAdapter clientsRecyclerViewAdapter = null;

    private int selectedHandasa = 0;
    private int selectedManteka = 0;
    private int selectedYomeya = 0;

    private List<String> handasat = new ArrayList<>();
    private List<String> manatek = new ArrayList<>();
    private List<String> yawmeyat = new ArrayList<>();

    private boolean hiddenState = true;

    String searchText = "";
    Thread filterThread;


    public CustomersDataFragment() {
        // Required empty public constructor
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View layout = inflater.inflate(R.layout.fragment_customers_data, container, false);
        searchView = layout.findViewById(R.id.searchView);
        recyclerView = layout.findViewById(R.id.recyclerView);
        txtNoData = layout.findViewById(R.id.txtNoData);
        txtNoDataSync = layout.findViewById(R.id.txtNoDataSync);
        txtLoadingData = layout.findViewById(R.id.txtLoadingData);
        txtNoSearchResult = layout.findViewById(R.id.txtNoResult);
        recyclerView.setLayoutManager(new WrapContentLinearLayoutManager(((MainActivity) MainApplication.getInstance().getCurrentActivity()), LinearLayoutManager.VERTICAL, false));
        filterSpinner = layout.findViewById(R.id.filterSpinner);
        handasaSpinner = layout.findViewById(R.id.handasaSpinner);
        yawmeyaSpinner = layout.findViewById(R.id.yawmeyaSpinner);
        mantekaSpinner = layout.findViewById(R.id.mantekaSpinner);
        btnMomarsa = layout.findViewById(R.id.btn_momarsa);
        hiddenState = false;
        handasaSpinner.getBackground().setColorFilter(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        yawmeyaSpinner.getBackground().setColorFilter(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        mantekaSpinner.getBackground().setColorFilter(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        filterSpinner.getBackground().setColorFilter(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);

        btnMomarsa.setOnClickListener(v -> {
            if (CycleDateManager.getCycleDate(requireActivity()) == null || CycleDateManager.getCycleDate(requireActivity()).CycleYear == 0 || CycleDateManager.getCycleDate(requireActivity()).CycleMonth == 0) {
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                    Toast.makeText(((MainActivity) MainApplication.getInstance().getCurrentActivity()), "يجب المزامنة اولا", Toast.LENGTH_LONG).show();
                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.sync);
                });
            } else {
                Intent myIntent = new Intent(((MainActivity) MainApplication.getInstance().getCurrentActivity()), MomarsaActivity.class);
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).startActivity(myIntent);
            }
        });
        readingStateAdapter = new ArrayAdapter<String>(((MainActivity) MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, readingStateStringArray) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);

                String state = readingStateStringArray[position];
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                if (state.isEmpty()) text1.setVisibility(View.GONE);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                view.setBackgroundColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary));
                view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                String state;
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                try {
                    state = readingStateStringArray[position];
                    if (state.isEmpty()) text1.setVisibility(View.GONE);
                    text1.setText(state);
                    view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                    view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTextColor(AppCompatResources.getColorStateList(((MainActivity) MainApplication.getInstance().getCurrentActivity()), R.color.on_primary));
                } catch (Exception ignored) {
                }
                return view;
            }
        };
        filterSpinner.setAdapter(readingStateAdapter);
        recyclerView.getRecycledViewPool().setMaxRecycledViews(0, 0);
        searchView.setOnClickListener(view -> searchView.onActionViewExpanded());
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                searchText = newText;
                startFilter();
                return false;
            }
        });

        handasaSpinner.setSelection(0);
        handasaSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (selectedHandasa != position) {
                    mantekaSpinner.setSelection(0);
                    yawmeyaSpinner.setSelection(0);
                    selectedHandasa = position;
                    mantekaSpinner.setEnabled(position > 0);
                    yawmeyaSpinner.setEnabled(false);
                    reloadSpinners(0);
                    startFilter();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
        mantekaSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int position, long l) {
                if (selectedManteka != position) {
                    yawmeyaSpinner.setSelection(0);
                    selectedManteka = position;
                    yawmeyaSpinner.setEnabled(position > 0);
                    reloadSpinners(1);
                    startFilter();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        yawmeyaSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                if (selectedYomeya != i) {
                    selectedYomeya = i;
                    startFilter();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        filterSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                startFilter();
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        loadClientsData();
        return layout;
    }


    public void loadClientsData() {
        fullClientsList.clear();
        fullClientsList.addAll(Utils.appDatabase.clientDao().getAll());

        loadUI();
    }

    private void loadUI() {
        handasat = new ArrayList<>();
        manatek = new ArrayList<>();
        yawmeyat = new ArrayList<>();
        handasat.add(0, "هندسة");
        manatek.add(0, "منطقة");
        yawmeyat.add(0, "يومية");
        selectedHandasa = 0;
        selectedManteka = 0;
        selectedYomeya = 0;
        for (Client client : fullClientsList) {
            if (!handasat.contains(String.valueOf(client.getBrHndsa()))) {
                handasat.add(String.valueOf(client.getBrHndsa()));
            }
            if (!manatek.contains(String.valueOf(client.getMntka()))) {
                manatek.add(String.valueOf(client.getMntka()));
            }
            if (!yawmeyat.contains(String.valueOf(client.getDay()))) {
                yawmeyat.add(String.valueOf(client.getDay()));
            }
        }

        handasat = sortData(handasat);
        manatek = sortData(manatek);
        yawmeyat = sortData(yawmeyat);
        Collections.sort(fullClientsList);
        reloadSpinners(2);
        filterdClientsList.clear();
        filterdClientsList.addAll(fullClientsList);
        clientsRecyclerViewAdapter = new ClientsRecyclerViewAdapter(((MainActivity) MainApplication.getInstance().getCurrentActivity()), filterdClientsList, CustomersDataFragment.this);
        recyclerView.setAdapter(clientsRecyclerViewAdapter);

        try {
            handasaSpinner.setSelection(0);
            mantekaSpinner.setSelection(0);
            yawmeyaSpinner.setSelection(0);
            filterSpinner.setSelection(0);
            searchView.setQuery("", false);
            searchView.clearFocus();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CycleDateManager.getCycleDate(requireActivity()) == null || CycleDateManager.getCycleDate(requireActivity()).CycleYear == 0 || CycleDateManager.getCycleDate(requireActivity()).CycleMonth == 0) {
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                Toast.makeText(((MainActivity) MainApplication.getInstance().getCurrentActivity()), "يجب المزامنة اولا", Toast.LENGTH_LONG).show();
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.sync);
            });
        } else {
            updateFullClientsList();
        }
    }

    private void reloadSpinners(int spinnerPos) {
        handasat.clear();
        manatek.clear();
        yawmeyat.clear();
        handasat.add(0, "هندسة");
        manatek.add(0, "منطقة");
        yawmeyat.add(0, "يومية");
        if (selectedHandasa == 0) {
            selectedManteka = 0;
            selectedYomeya = 0;
        }
        if (selectedManteka == 0) {
            selectedYomeya = 0;
        }

        if (spinnerPos == 0) {
            selectedManteka = 0;
            selectedYomeya = 0;
            mantekaSpinner.setEnabled(selectedHandasa > 0);
            yawmeyaSpinner.setEnabled(false);
        } else if (spinnerPos == 1) {
            selectedYomeya = 0;
            yawmeyaSpinner.setEnabled(selectedManteka > 0);
        }

        for (Client client : fullClientsList) {
            if (!handasat.contains(String.valueOf(client.getBrHndsa()))) {
                handasat.add(String.valueOf(client.getBrHndsa()));
            }
        }
        for (Client client : fullClientsList) {
            if (!manatek.contains(String.valueOf(client.getMntka())) && selectedHandasa > 0 && client.getBrHndsa() == Integer.parseInt(handasat.get(selectedHandasa))) {
                manatek.add(String.valueOf(client.getMntka()));
            }
        }
        for (Client client : fullClientsList) {
            if (!yawmeyat.contains(String.valueOf(client.getDay())) && selectedManteka > 0 && client.getMntka().equalsIgnoreCase(manatek.get(selectedManteka))) {
                yawmeyat.add(String.valueOf(client.getDay()));
            }
        }
        sortData(handasat);
        sortData(manatek);
        sortData(yawmeyat);

        SpinnerAdapter handasaAdaptor = new ArrayAdapter<String>(((MainActivity) MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, handasat) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                try {
                    String handasa = handasat.get(position);
                    text1.setText(handasa);
                    if (handasa.isEmpty()) text1.setVisibility(View.GONE);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
                    text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                    view.setBackgroundColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                } catch (Exception ignored) {
                }

                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                try {
                    String handasa = handasat.get(position);
                    if (handasa.isEmpty()) text1.setVisibility(View.GONE);
                    text1.setText(handasa);
                    view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                    text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTextColor(AppCompatResources.getColorStateList(((MainActivity) MainApplication.getInstance().getCurrentActivity()), R.color.on_primary));
                } catch (Exception ignored) {
                }
                return view;
            }
        };

        SpinnerAdapter mantekaAdaptor = new ArrayAdapter<String>(((MainActivity) MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, manatek) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                try {
                    String manteka = manatek.get(position);
                    text1.setText(manteka);
                    if (manteka.isEmpty()) text1.setVisibility(View.GONE);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
                    text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                    view.setBackgroundColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                    view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                } catch (Exception ignored) {
                }
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                try {
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    try {
                        String manteka = manatek.get(position);
                        if (manteka.isEmpty()) text1.setVisibility(View.GONE);
                        text1.setText(manteka);
                        view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                        text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                        text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                        text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                        view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setTextColor(AppCompatResources.getColorStateList(((MainActivity) MainApplication.getInstance().getCurrentActivity()), R.color.on_primary));
                    } catch (Exception ignored) {
                    }
                } catch (Exception ignored) {
                }
                return view;
            }
        };

        SpinnerAdapter yawmeyaAdaptor = new ArrayAdapter<String>(((MainActivity) MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, yawmeyat) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                try {
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    try {
                        String yawmeya = yawmeyat.get(position);
                        text1.setText(yawmeya);
                        if (yawmeya.isEmpty()) text1.setVisibility(View.GONE);
                        text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
                        text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                        text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                        view.setBackgroundColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary));
                        view.findViewById(R.id.text2).setVisibility(View.GONE);
                        view.findViewById(R.id.image).setVisibility(View.GONE);
                        view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                    } catch (Exception ignored) {
                    }
                } catch (Exception ignore) {
                }
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                try {
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    try {
                        String yawmeya = yawmeyat.get(position);
                        if (yawmeya.isEmpty()) text1.setVisibility(View.GONE);
                        text1.setText(yawmeya);
                        view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                        text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
                        text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                        text1.setGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setTextColor(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                        view.setForegroundGravity(View.TEXT_ALIGNMENT_CENTER);
                        text1.setTextColor(AppCompatResources.getColorStateList(((MainActivity) MainApplication.getInstance().getCurrentActivity()), R.color.on_primary));
                    } catch (Exception ignore) {
                    }
                } catch (Exception ignored) {
                }
                return view;
            }
        };

        handasaSpinner.setAdapter(handasaAdaptor);
        mantekaSpinner.setAdapter(mantekaAdaptor);
        yawmeyaSpinner.setAdapter(yawmeyaAdaptor);
        handasaSpinner.setSelection(selectedHandasa);
        mantekaSpinner.setSelection(selectedManteka);
        yawmeyaSpinner.setSelection(selectedYomeya);
    }

    private void startFilter() {
        new Thread(() -> {
            while (filterThread != null && filterThread.isAlive()) {
                filterThread.interrupt();
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException ignored) {
            }
            filterThread = new Thread(() -> reFilter(searchText));
            filterThread.start();
        }).start();
    }

    private void reFilter(String s) {
        try {
            synchronized (s) {
                List<MiniDatabaseMeterData> miniDatabaseMeterData = Utils.appDatabase.metersDataDao().getAllMini();

                filterdClientsList.clear();
                filterdClientsList.addAll(fullClientsList);
                filterdClientsList.removeIf(client -> {
                    String searchText = replaceNonstandardDigits(s);
                    if (searchText == null || searchText.isEmpty())
                        return false;
                    return !String.valueOf(client.getMeterID()).contains(searchText)
                            && (client.getCustomerMobile() == null || !client.getCustomerMobile().contains(searchText))
                            && (client.getAddress() == null || !client.getAddress().contains(searchText))
                            && (client.getReference() == null || !client.getReference().contains(searchText))
                            && (client.getActivityName() == null || !client.getActivityName().contains(searchText))
                            && (client.getCustomerID() == null || !client.getCustomerID().contains(searchText))
                            && (client.getPlaceName() == null || !client.getPlaceName().contains(searchText))
                            && (client.getName() == null || !client.getName().contains(searchText))
                            && (client.getCardID() == null || !client.getCardID().contains(searchText));
                });
                int filterIndex = filterSpinner.getSelectedItemPosition();
                if (filterIndex == 1) {
//                    filterdClientsList.removeIf(client -> {
//                        boolean matchWeb = client.getReadingCount() != 0;
//                        boolean matchRead = miniDatabaseMeterData.stream().noneMatch(miniDatabaseMeterData1 -> miniDatabaseMeterData1.COVER_METER_ID == client.getMeterID() && miniDatabaseMeterData1.FACTORY_CODE == client.FactoryCode && miniDatabaseMeterData1.READING_CODE == 0);
//                        return !(matchRead || matchWeb);
//                    });
                    filterdClientsList.removeIf(client -> client.getReadingCount() != 0);
                } else if (filterIndex == 2) {
                    filterdClientsList.removeIf(client -> !(client.series == 1002 && client.getReadingCount() > 0));
                } else if (filterIndex > 2) {
                    filterdClientsList.removeIf(client -> {
                        boolean matchWeb = client.getKTCodeSHoaa() == filterIndex - 3 && client.getReadingCount() > 0;
                        boolean matchRead = miniDatabaseMeterData.stream().anyMatch(miniDatabaseMeterData1 -> miniDatabaseMeterData1.COVER_METER_ID == client.getMeterID() && miniDatabaseMeterData1.FACTORY_CODE == client.FactoryCode && miniDatabaseMeterData1.READING_CODE == filterIndex - 3);
                        return !(matchRead || matchWeb);
                    });
                }

                if (selectedHandasa > 0) {
                    filterdClientsList.removeIf(client -> client.getBrHndsa() != Integer.parseInt(handasat.get(selectedHandasa)));
                }
                if (selectedManteka > 0) {
                    filterdClientsList.removeIf(client -> Integer.parseInt(client.getMntka()) != Integer.parseInt(manatek.get(selectedManteka)));
                }
                if (selectedYomeya > 0) {
                    filterdClientsList.removeIf(client -> Integer.parseInt(client.getDay()) != Integer.parseInt(yawmeyat.get(selectedYomeya)));
                }
                updateFullClientsList();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateFullClientsList() {
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            clientsRecyclerViewAdapter.notifyItemRangeInserted(0, filterdClientsList.size());
            if (!hiddenState) {
                recyclerView.setVisibility((filterdClientsList.size() > 0) ? View.VISIBLE : View.GONE);
                if (fullClientsList.isEmpty()) {
                    txtLoadingData.setVisibility(View.VISIBLE);
                    txtLoadingData.setText(R.string.no_customer_data_please_sync);
                } else {
                    if (filterdClientsList.isEmpty()) {
                        txtLoadingData.setVisibility(View.VISIBLE);
                        txtLoadingData.setText(R.string.no_customer);
                    } else {
                        txtLoadingData.setVisibility(View.GONE);
                    }
                }
            }
        });
    }

    public void updateClientsState() {
        try {
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                clientsRecyclerViewAdapter.notifyItemRangeInserted(0, filterdClientsList.size());
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onItemClick(View view, int position) {
        try {
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).readerFragment.onClientSelected(clientsRecyclerViewAdapter.getItem(position), position);
        } catch (Exception ignore) {
        }
    }

    public Client getClientByIndex(int index) {
        if (index < 0 || filterdClientsList.size() == 0 || index >= filterdClientsList.size())
            return null;
        else return filterdClientsList.get(index);
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        this.hiddenState = hidden;
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).checkTimeAutomatic();
        AppUser appUser = AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
        if (appUser == null || appUser.getUSER_ID() == null || appUser.getUSER_ID().isEmpty() || appUser.getUserName() == null || appUser.getUserName().isEmpty() || appUser.getPassword() == null || appUser.getPassword().isEmpty()) {
            Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent(((MainActivity) MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).finish();
            startActivity(i);
        }
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            if (hidden) recyclerView.setVisibility(View.GONE);
            else {
                recyclerView.setVisibility((fullClientsList.size() > 0) ? View.VISIBLE : View.GONE);
                if (fullClientsList.isEmpty()) {
                    txtLoadingData.setVisibility(View.VISIBLE);
                    txtLoadingData.setText(R.string.no_customer_data_please_sync);
                } else {
                    if (filterdClientsList.isEmpty()) {
                        txtLoadingData.setVisibility(View.VISIBLE);
                        txtLoadingData.setText(R.string.no_customer);
                    } else {
                        txtLoadingData.setVisibility(View.GONE);
                    }
                }
            }
        });
        if (!hidden && handasat.isEmpty() && !fullClientsList.isEmpty()) {
            loadClientsData();
        }
    }

    String replaceNonstandardDigits(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (isNonstandardDigit(ch)) {
                int numericValue = Character.getNumericValue(ch);
                if (numericValue >= 0) {
                    builder.append(numericValue);
                }
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }

    private boolean isNonstandardDigit(char ch) {
        return Character.isDigit(ch) && !(ch >= '0' && ch <= '9');
    }

    private List<String> sortData(List<String> data) {
        List<Integer> convertedData = new ArrayList<>();
        List<String> sortedData = new ArrayList<>();
        String val = "";

        for (String s : data) {
            if (s != null) {
                try {
                    convertedData.add(Integer.parseInt(s));
                } catch (Exception e) {
                    val = s;
                }
            }
        }
        Collections.sort(convertedData);

        for (int b : convertedData) {
            sortedData.add(String.valueOf(b));
        }
        sortedData.add(0, val);
        return sortedData;
    }
}