package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/DlmsData.class */
public class DlmsData {
    public static final String TYPE_U32 = "DoubleLongUnsigned";
    public static final String TYPE_U16 = "LongUnsigned";
    public static final String TYPE_U8 = "Unsigned";
    public static final String TYPE_I8 = "Integer";
    public static final String TYPE_I16 = "Long";
    public static final String TYPE_I32 = "DoubleLong";
    public static final String TYPE_BITSTRING = "BitString";
    public static final String TYPE_VISIBLESTRING = "VisibleString";
    public static final String TYPE_OCTETSTRING = "OctetString";
    public static final String TYPE_BOOL = "Boolean";
    public static final String TYPE_ENUM = "Enum";
    public static final String TYPE_FLOAT32 = "Float32";
    private String dataType;
    private String dataValue;

    public DlmsData(String dataType, String dataValue) {
        this.dataType = dataType;
        this.dataValue = dataValue;
    }

    public String getDataType() {
        return this.dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataValue() {
        return this.dataValue;
    }

    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }
}
