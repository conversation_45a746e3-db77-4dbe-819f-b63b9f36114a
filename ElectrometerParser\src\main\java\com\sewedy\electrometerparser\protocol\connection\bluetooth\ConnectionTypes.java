package com.sewedy.electrometerparser.protocol.connection.bluetooth;

public class ConnectionTypes {

    //BT connections// not working with old FW
    public static String baudTran_300_7 = "426175645472616E2C3330302C452C372C300D0A";
    public static String baudTran_4800_8 = "426175645472616D2C343830302C4E2C382C300D0A";
//    public static String baudTran_4800_8_OldFW = "426175645472616E2C343830302C4E2C382C300D0A";

    //ZPA connections//
    public static String baudTran_300_7_ZPA = "FEFE5A5041313037453130FF";
    //    public static String baudTran_4800_8_ZPA = "FEFE5A50413130384E3034FF";
    // on baudrate 6 --> 14400 it keep receiving data
    //384E3134
    // 8 N 1 4
    public static String baudTran_4800_N_8_ZPA = "FEFE5A50413130384E3134FF";
}
