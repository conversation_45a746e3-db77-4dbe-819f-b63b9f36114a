<component name="libraryTable">
  <library name="Gradle: androidx.test.ext:junit:1.1.3@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ff2a5e81f9b5d6f9a53c31500baf536f/transformed/jetified-junit-1.1.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/43ae14205227a67e15c313a8ea9ded21/transformed/jetified-junit-1.1.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0024b56d9018de6a158d2135884491c2/transformed/jetified-junit-1.1.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ebfa618748b5f1d275d7c10d34eaac8f/transformed/jetified-junit-1.1.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0024b56d9018de6a158d2135884491c2/transformed/jetified-junit-1.1.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/0024b56d9018de6a158d2135884491c2/transformed/jetified-junit-1.1.3/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.ext/junit/1.1.3/f138cf897cc1e024dd714073df04f2425d845104/junit-1.1.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.ext/junit/1.1.3/cdf059d469527681d62d3330131eb875b77911d5/junit-1.1.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>