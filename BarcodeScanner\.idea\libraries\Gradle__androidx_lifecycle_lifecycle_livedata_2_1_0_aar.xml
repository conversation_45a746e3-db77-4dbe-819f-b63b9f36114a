<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-livedata:2.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/174b88ff17d97ec167ee46bb0f74103f/transformed/lifecycle-livedata-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4100b9afab194a4ebf36e299162705b9/transformed/lifecycle-livedata-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5701c4ecbe225e9e6eedb0b2bc94c625/transformed/lifecycle-livedata-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/24ecd1711d56a9636704221dbe460874/transformed/lifecycle-livedata-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e565c4a3c9b348792f3bb3dbbb18e60b/transformed/lifecycle-livedata-2.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/24ecd1711d56a9636704221dbe460874/transformed/lifecycle-livedata-2.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/24ecd1711d56a9636704221dbe460874/transformed/lifecycle-livedata-2.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-livedata/2.1.0/9849dd6833f7e999169e4f9d48949cc92bf58b50/lifecycle-livedata-2.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>