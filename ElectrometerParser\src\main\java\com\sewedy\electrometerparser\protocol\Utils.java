package com.sewedy.electrometerparser.protocol;

import org.apache.commons.lang3.ArrayUtils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.zip.CRC32;
import java.util.zip.Checksum;

public class Utils {
    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    public static String subtractOneMonth(String date) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = new SimpleDateFormat("yyyy-MM-dd").parse(date);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        cal.add(Calendar.MONTH, -1);
        return formatter.format(cal.getTime());
    }

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String bytesToAscii(String hex) {
        if (hex.length() % 2 != 0) {
            return null;
        }

        StringBuilder builder = new StringBuilder();

        for (int i = 0; i < hex.length(); i = i + 2) {
            String s = hex.substring(i, i + 2);
            int n = Integer.valueOf(s, 16);
            builder.append((char) n);
        }

        return builder.toString();
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static byte[] convertBuffArrayListToArr(ArrayList<Byte> arrayList) {
        return ArrayUtils.toPrimitive(arrayList.toArray(new Byte[arrayList.size()]));
    }

    public static short converArrtoShort(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(2);
        bb.order(byteOrder);
        bb.put(arr[index]);
        bb.put(arr[index + 1]);
        short shortVal = bb.getShort(0);
        return shortVal;
    }

    public static short converArrtoShort(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(2);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        bb.put(arr[index]);
        bb.put(arr[index + 1]);
        short shortVal = bb.getShort(0);
        return shortVal;
    }

    public static byte[] shortToByteArr(short i) {
        byte[] ret = new byte[2];
        ret[0] = (byte) (i & 0xff);
        ret[1] = (byte) ((i >> 8) & 0xff);
        return ret;
    }

    public static int converArrtoInt(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        int size = 4;
        if (arr.length < 4)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getInt(0);
    }

    public static int converArrtoInt(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.order(byteOrder);
        int size = 4;
        if (arr.length < 4)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getInt(0);
    }

    public static long converArrtoLong(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(8);
        bb.order(byteOrder);
        int size = 8;
        if (arr.length < 8)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getLong(0);
    }

    public static long converArrtoLong(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(8);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        int size = 8;
        if (arr.length < 8)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getLong(0);
    }

    public static byte[] intToByteArr(int v) {
        return ByteBuffer.allocate(4).putInt(v).array();
    }

    public static int getLastItem(int[] arr) {
        int id = 0;
        for (int i = 0; i < arr.length; i++) {
            if (arr[i] == 0) {
                if (i > 0) {
                    id = arr[--i];
                } else {
                    id = arr[i];
                }
                break;
            } else id = arr[arr.length - 1];
        }
        return id;
    }

    public static byte checkSum(byte[] bytes) {
        byte sum = 0;
        for (int i = 0; i < bytes.length; i++)
            sum += bytes[i];
        return sum;
    }

    public static byte calculateCRC(byte[] arr) {
        byte xOrd = arr[1];
        for (int i = 2; i < arr.length - 1; i++) {
            xOrd ^= arr[i];
        }
        return xOrd;
    }

    public static long getCRC32Checksum(byte[] bytes) {
        Checksum crc32 = new CRC32();
        crc32.update(bytes, 0, bytes.length);
        return crc32.getValue();
    }

    public static void sleep(int millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static boolean isNewControlData() {
        String firmware = MainMeterDataParser.getInstance().getData().meterFirmwareVersion;
        String[] parts = firmware.split("\\.");
        if (parts[0].equalsIgnoreCase("f")
                || parts[0].equalsIgnoreCase("a")
                || parts[0].equalsIgnoreCase("b")
                || parts[0].equalsIgnoreCase("n")
                || parts[0].equalsIgnoreCase("c"))
            return false;
        else {
            switch (parts[0].toLowerCase()) {
                case "d":
                case "2": {
                    int model = Integer.parseInt(parts[1]);
                    return model < 0 || model > 458;
                }
            }
        }
        return false;
    }

    public static int getFrameSize(int length) {
        int frameSize = 0;
        if (length % 11 == 0)
            frameSize = 11;
        else if (length % 19 == 0)
            frameSize = 19;
        else if (length % 23 == 0)
            frameSize = 23;
        else if (length % 31 == 0)
            frameSize = 31;
        return frameSize;
    }
}
