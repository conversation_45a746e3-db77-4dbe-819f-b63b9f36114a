apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    packagingOptions {
        resources {
            excludes += ['META-INF/AL2.0', 'META-INF/LGPL2.1']
        }
    }
    signingConfigs {
        debug {
            storeFile file('Shoaa_Key.jks')
            storePassword '123456789'
            keyPassword '123456789'
            keyAlias 'Shoaa'
        }
        release {
            storeFile file('Shoaa_Key.jks')
            storePassword '123456789'
            keyPassword '123456789'
            keyAlias 'Shoaa'
        }
    }
    compileSdk 34
    buildToolsVersion "33.0.1"

    defaultConfig {
        applicationId "shoaa.opticalsmartreader"
        minSdk 24
        targetSdk 34
        versionCode 774
        versionName "7.7.4"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        project.archivesBaseName = "Optical_Smart_Reader"
    }
    applicationVariants.configureEach { variant ->
        variant.outputs.configureEach {
            outputFileName = "${archivesBaseName}_${variant.flavorName}${defaultConfig.versionName}_${variant.buildType.name}.apk"
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            shrinkResources = false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }

    flavorDimensions = ["client"]
    productFlavors {
        NORTH_CAIRO_USB_SUPPORT_ {
            dimension "client"
            //new ip ************
            // old ip ***************
            buildConfigField "String", "API_BASE_URL", "\"http://************:3000/api/\""
            buildConfigField "String", "arName", "\"شمال القاهرة\""
            buildConfigField "String", "coName", "\"NorthCairo\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "1"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        SOUTH_CAIRO_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://scedc-prepaid.com:3000/api/\""
            buildConfigField "String", "arName", "\"جنوب القاهرة\""
            buildConfigField "String", "coName", "\"SouthCairo\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "2"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.1.2\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        ALBUHAYRAH_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.100.9:3000/api/\""
            buildConfigField "String", "arName", "\"البحيرة\""
            buildConfigField "String", "coName", "\"AlBuhayrah\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "3"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        NORTH_DELTA_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://172.21.0.127:3000/api/\""
            buildConfigField "String", "arName", "\"شمال الدلتا\""
            buildConfigField "String", "coName", "\"NorthDelta\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "4"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        SOUTH_DELTA_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.100.121:3000/api/\""
            buildConfigField "String", "arName", "\"جنوب الدلتا\""
            buildConfigField "String", "coName", "\"SouthDelta\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "5"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        MIDDLE_EGYPT_WE_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://200.1.1.203:5000/api/\""
            buildConfigField "String", "arName", "\"مصر الوسطى\t(WE)\""
            buildConfigField "String", "coName", "\"MiddleEgyptWE\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "6"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        MIDDLE_EGYPT_OR_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://10.180.214.72:5000/api/\""
            buildConfigField "String", "arName", "\"مصر الوسطى\t(Orange)\""
            buildConfigField "String", "coName", "\"MiddleEgyptOR\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "6"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        UPPER_EGYPT_VPN_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://172.21.1.10:3000/api/\""
            buildConfigField "String", "arName", "\"مصر العليا\t(VPN)\""
            buildConfigField "String", "coName", "\"UpperEgyptVPN\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "7"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        UPPER_EGYPT_INTERNET_USB_SUPPORT_ {
            getIsDefault().set(true)
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://196.218.75.29:3000/api/\""
            buildConfigField "String", "arName", "\"مصر العليا\t(Internet)\""
            buildConfigField "String", "coName", "\"UpperEgyptInternet\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "7"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        CANAL_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.40.177:3000/api/\""
            buildConfigField "String", "arName", "\"القناة\""
            buildConfigField "String", "coName", "\"Canal\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "8"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        ALEX_VPN_USB_SUPPORT_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.200.201:3000/api/\""
            buildConfigField "String", "arName", "\"الإسكندرية\t(VPN)\""
            buildConfigField "String", "coName", "\"AlexandriaVPN\""
            buildConfigField "boolean", "enableUSB", "true"
//            buildConfigField "int", "COMPANY_CODE", "9"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }

        NORTH_CAIRO_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://***************:3000/api/\""
            buildConfigField "String", "arName", "\"شمال القاهرة\""
            buildConfigField "String", "coName", "\"NorthCairo\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "1"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        SOUTH_CAIRO_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://scedc-prepaid.com:3000/api/\""
            buildConfigField "String", "arName", "\"جنوب القاهرة\""
            buildConfigField "String", "coName", "\"SouthCairo\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "2"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.1.2\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        ALBUHAYRAH_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.100.9:3000/api/\""
            buildConfigField "String", "arName", "\"البحيرة\""
            buildConfigField "String", "coName", "\"AlBuhayrah\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "3"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        NORTH_DELTA_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://172.21.0.127:3000/api/\""
            buildConfigField "String", "arName", "\"شمال الدلتا\""
            buildConfigField "String", "coName", "\"NorthDelta\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "4"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        SOUTH_DELTA_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.100.121:3000/api/\""
            buildConfigField "String", "arName", "\"جنوب الدلتا\""
            buildConfigField "String", "coName", "\"SouthDelta\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "5"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        MIDDLE_EGYPT_WE_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://200.1.1.203:5000/api/\""
            buildConfigField "String", "arName", "\"مصر الوسطى\t(WE)\""
            buildConfigField "String", "coName", "\"MiddleEgyptWE\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "6"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        MIDDLE_EGYPT_ORANGE_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://10.180.214.72:5000/api/\""
            buildConfigField "String", "arName", "\"مصر الوسطى\t(Orange)\""
            buildConfigField "String", "coName", "\"MiddleEgyptOrange\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "6"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        UPPER_EGYPT_VPN_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://172.21.1.10:3000/api/\""
            buildConfigField "String", "arName", "\"مصر العليا\t(VPN)\""
            buildConfigField "String", "coName", "\"UpperEgyptVPN\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "7"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        UPPER_EGYPT_INTERNET_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://196.218.75.29:3000/api/\""
            buildConfigField "String", "arName", "\"مصر العليا\t(Internet)\""
            buildConfigField "String", "coName", "\"UpperEgyptInternet\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "7"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        CANAL_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.40.177:3000/api/\""
            buildConfigField "String", "arName", "\"القناة\""
            buildConfigField "String", "coName", "\"Canal\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "8"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
        ALEX_VPN_ {
            dimension "client"
            buildConfigField "String", "API_BASE_URL", "\"http://192.168.200.201:3000/api/\""
            buildConfigField "String", "arName", "\"الإسكندرية\t(VPN)\""
            buildConfigField "String", "coName", "\"AlexandriaVPN\""
            buildConfigField "boolean", "enableUSB", "false"
//            buildConfigField "int", "COMPANY_CODE", "9"
            buildConfigField "String[]", "VALID_OLD_VERSIONS",
                    "{" +
                            "\"7.1.3\"," +
                            "\"7.1.1\"," +
                            "\"7.0.36\"," +
                            "\"7.0.35\"," +
                            "\"7.0.26\"," +
                            "\"7.0.22\"," +
                            "\"6.0.9\"," +
                            "\"6.0.8\"," +
                            "\"6.0.7\"," +
                            "\"6.0.6\"," +
                            "\"6.0.3\"," +
                            "\"6.1.2\"," +
                            "\"6.0.0\"," +
                            "\"2.0.8\"," +
                            "}"
            signingConfig signingConfigs.release
        }
    }
    namespace 'shoaa.opticalsmartreader'
}

dependencies {
    implementation project(path: ':ConnectionManager')
    implementation project(path: ':SmartMeterReader')
    implementation project(path: ':BarcodeScanner')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'pub.devrel:easypermissions:3.0.0'
    implementation 'com.yayandroid:LocationManager:2.4.1'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation('com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.14')
    implementation group: 'com.google.guava', name: 'guava', version: '32.1.3-jre'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation platform('com.google.firebase:firebase-bom:32.8.1')
    implementation 'com.google.firebase:firebase-analytics:21.6.2'
    implementation 'com.google.firebase:firebase-crashlytics:18.6.4'
    implementation 'com.google.firebase:firebase-config-ktx:21.6.3'

    implementation 'com.google.guava:guava:32.1.3-jre'


    def room_version = '2.6.1'
    implementation "androidx.room:room-runtime:$room_version"
    annotationProcessor "androidx.room:room-compiler:$room_version"
    // optional - RxJava2 support for Room
    implementation "androidx.room:room-rxjava2:$room_version"
    // optional - RxJava3 support for Room
    implementation "androidx.room:room-rxjava3:$room_version"
    // optional - Guava support for Room, including Optional and ListenableFuture
    implementation "androidx.room:room-guava:$room_version"
    // optional - Paging 3 Integration
    implementation "androidx.room:room-paging:$room_version"

    implementation project(path: ':Common')

//    implementation files('../app/libs/hayka/bcprov-jdk18on-1.71.jar')
//    implementation files('../app/libs/hayka/jdlms-1.7.2-1.7.2.jar')
//    implementation files('../app/libs/hayka/asn1bean-1.13.0.jar')
//    implementation files('../app/libs/hayka/jrxtx-1.0.1.jar')
//    implementation files('../app/libs/hayka/gson-2.8.2.jar')

    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'androidx.recyclerview:recyclerview:1.3.1'

    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    // Optional -- UI testing with Espresso
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    // Optional -- UI testing with UI Automator
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.3.0'
    // Optional -- UI testing with Compose
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4:1.6.6'
    // optional - Test helpers
    testImplementation "androidx.room:room-testing:$room_version"
    testImplementation 'org.bouncycastle:bctls-jdk14:1.78.1'
    testImplementation 'com.google.guava:guava:32.1.3-jre'
    testImplementation 'com.google.code.gson:gson:2.10.1'
    testImplementation 'org.apache.commons:commons-lang3:3.14.0'
    testImplementation 'com.alibaba:fastjson:2.0.49'

    testImplementation files('../app/libs/EsmcProtocol_v2.jar')
    testImplementation files('../app/libs/dlms-maasra.jar')
    testImplementation files('../app/libs/dlms-gpi.jar')
    testImplementation project(path: ':Common')
}

buildscript {
    ext.kotlin_version = '1.9.22'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
    }
    buildDir = "\\build\\"
}
task clean(type: Delete) {
    delete rootProject.buildDir
}