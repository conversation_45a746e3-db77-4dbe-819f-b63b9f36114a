<component name="libraryTable">
  <library name="Gradle: androidx.legacy:legacy-support-v4:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/765bba6daaec554bf05c23f7b2ac9777/transformed/legacy-support-v4-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a99cae7fc5a214f7f10d3ff10b01bdd2/transformed/legacy-support-v4-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b8bd69758fbb061eb5ed839cfdce479a/transformed/legacy-support-v4-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/046744b4b7d989cc040e390b3e260bee/transformed/legacy-support-v4-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/307acce66f6a85e295a5a6ccac83f13a/transformed/legacy-support-v4-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/046744b4b7d989cc040e390b3e260bee/transformed/legacy-support-v4-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/046744b4b7d989cc040e390b3e260bee/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.legacy/legacy-support-v4/1.0.0/5b8f86fea035328fc9e8c660773037a3401ce25f/legacy-support-v4-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>