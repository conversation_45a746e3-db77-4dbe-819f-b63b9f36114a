package com.esmc.protocol.utils;

import com.esmc.protocol.algorithm.AESGcm128;
import com.esmc.protocol.model.ProtocolProfile;

import org.bouncycastle.crypto.InvalidCipherTextException;

import java.util.Arrays;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/utils/AuthenticationMechanismProvider.class */
public class AuthenticationMechanismProvider {
    public static byte[] mechanismGmac(long invocationCounter, ProtocolProfile profile) {
        try {
            byte[] challenge = MyConverter.hexStringToBytes(profile.getServer2Client());
            byte[] iv = ApduProtector.getInitializationVector(invocationCounter, profile.getClientSystemTitle());
            byte[] aad = new byte[17 + challenge.length];
            aad[0] = 16;
            System.arraycopy(MyConverter.hexStringToBytes(profile.getSecurityInfo().getAuthenticationKeyGlobal()), 0, aad, 1, 16);
            System.arraycopy(challenge, 0, aad, 17, challenge.length);
            byte[] data = new byte[0];
            byte[] cipherData = AESGcm128.encrypt(MyConverter.hexStringToBytes(profile.getSecurityInfo().getEncryptionKeyGlobalUnicast()), iv, data, aad);
            byte[] pass3 = new byte[5 + cipherData.length];
            pass3[0] = 16;
            pass3[1] = (byte) ((invocationCounter >> 24) & 255);
            pass3[2] = (byte) ((invocationCounter >> 16) & 255);
            pass3[3] = (byte) ((invocationCounter >> 8) & 255);
            pass3[4] = (byte) (invocationCounter & 255);
            System.arraycopy(cipherData, 0, pass3, 5, cipherData.length);
            return pass3;
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    public static boolean mechanismGmacValidate(byte[] pass4, ProtocolProfile profile) {
        try {
            if (5 > pass4.length || 16 != pass4[0]) {
                return false;
            }
            byte[] challenge = MyConverter.hexStringToBytes(profile.getClient2Server());
            long ic = ((pass4[1] << 24) & (-16777216)) | ((pass4[2] << 16) & 16711680) | ((pass4[3] << 8) & 65280) | (pass4[4] & 255);
            byte[] data = Arrays.copyOfRange(pass4, 5, pass4.length);
            byte[] iv = ApduProtector.getInitializationVector(ic, profile.getServerSystemTitle());
            byte[] aad = new byte[17 + challenge.length];
            aad[0] = 16;
            System.arraycopy(MyConverter.hexStringToBytes(profile.getSecurityInfo().getAuthenticationKeyGlobal()), 0, aad, 1, 16);
            System.arraycopy(challenge, 0, aad, 17, challenge.length);
            byte[] vs = AESGcm128.decrypt(MyConverter.hexStringToBytes(profile.getSecurityInfo().getEncryptionKeyGlobalUnicast()), iv, data, aad);
            return vs != null;
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
            return false;
        }
    }
}
