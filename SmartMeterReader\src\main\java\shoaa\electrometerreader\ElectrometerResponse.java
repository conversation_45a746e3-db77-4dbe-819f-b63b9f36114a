package shoaa.electrometerreader;

import androidx.annotation.NonNull;

import com.sewedy.electrometerparser.protocol.MeterData;

import shoaa.smartmeterreader.ReadingResponse;

/**
 * Created by <PERSON> Darwish
 */

public class ElectrometerResponse extends ReadingResponse {
    private String meterModel = "1";
    private String meterId = "0";
    private String customerId = "0";
    private String cardId = "0";
    private String fwVersion = "0";
    private String activityType = "0";
    private String curentPowerFactor = "0";
    private String lastYearPowerFactor = "0";
    private String installingTechnicanCode = "0";
    private String installingDateAndTime = "0";
    private String meterDateAndTime = "0";
    private String currentTarrifInstalling = "0";
    private String currentTariffActivationDate = "0";
    private String meterStatus = "0";
    private String relayStatus = "0";
    private String batteryStatus = "0";
    private String topCoverStatus = "0";
    private String sideCoverStatus = "0";
    private String technicalCodeEvent1 = "0";
    private String eventType1 = "0";
    private String eventDate1 = "0";
    private String technicalCodeEvent2 = "0";
    private String eventType2 = "0";
    private String eventDate2 = "0";
    private String technicalCodeEvent3 = "0";
    private String eventType3 = "0";
    private String eventDate3 = "0";
    private String rechargeNumber = "0";
    private String rechargeAmount = "0";
    private String lastRechargeDateAndTime = "0";
    private String remainingCreditKw = "0";
    private String remainingCreditMoney = "0";
    private String debts = "0";
    private String totalConsumptionKw = "0";
    private String totalConsumptionMoney = "0";
    private String totalConsumptionKvar = "0";
    private String currentDemand = "0";
    private String maximumDemand = "0";
    private String maximumDemandDate = "0";
    private String instanteneousPahaseAVolt = "0";
    private String instanteneousPahaseBVolt = "0";
    private String instanteneousPahaseCVolt = "0";
    private String instanteneousPahaseACurrent = "0";
    private String instanteneousPahaseBCurrent = "0";
    private String instanteneousPahaseCCurrent = "0";
    private String instanteneousVolt = "0";
    private String instanteneousCurrentPhaseAmpere = "0";
    private String instanteneousCurrentNeutral = "0";
    private String reverseKwh = "0";
    private String unbalanceKwh = "0";
    private String currentMonthConsumptionKW = "0";
    private String currentMonthConsumptionMoney = "0";
    private String month1ConsumptionKWh = "0";
    private String month2ConsumptionKWh = "0";
    private String month3ConsumptionKWh = "0";
    private String month4ConsumptionKWh = "0";
    private String month5ConsumptionKWh = "0";
    private String month6ConsumptionKWh = "0";
    private String month7ConsumptionKWh = "0";
    private String month8ConsumptionKWh = "0";
    private String month9ConsumptionKWh = "0";
    private String month10ConsumptionKWh = "0";
    private String month11ConsumptionKWh = "0";
    private String month12ConsumptionKWh = "0";
    private String month1ConsumptionMoney = "0";
    private String month2ConsumptionMoney = "0";
    private String month3ConsumptionMoney = "0";
    private String month4ConsumptionMoney = "0";
    private String month5ConsumptionMoney = "0";
    private String month6ConsumptionMoney = "0";
    private String month7ConsumptionMoney = "0";
    private String month8ConsumptionMoney = "0";
    private String month9ConsumptionMoney = "0";
    private String month10ConsumptionMoney = "0";
    private String month11ConsumptionMoney = "0";
    private String month12ConsumptionMoney = "0";
    private String maximDemandMonth1 = "0";
    private String maximDemandMonth2 = "0";
    private String maximDemandMonth3 = "0";
    private String maximDemandMonth4 = "0";
    private String maximDemandMonth5 = "0";
    private String maximDemandMonth6 = "0";
    private String maximDemandMonth7 = "0";
    private String maximDemandMonth8 = "0";
    private String maximDemandMonth9 = "0";
    private String maximDemandMonth10 = "0";
    private String maximDemandMonth11 = "0";
    private String maximDemandMonth12 = "0";
    private String maximDemandMonth1Date = "0";
    private String maximDemandMonth2Date = "0";
    private String maximDemandMonth3Date = "0";
    private String maximDemandMonth4Date = "0";
    private String maximDemandMonth5Date = "0";
    private String maximDemandMonth6Date = "0";
    private String maximDemandMonth7Date = "0";
    private String maximDemandMonth8Date = "0";
    private String maximDemandMonth9Date = "0";
    private String maximDemandMonth10Date = "0";
    private String maximDemandMonth11Date = "0";
    private String maximDemandMonth12Date = "0";
    private String month1ConsumptionKvar = "0";
    private String month2ConsumptionKvar = "0";
    private String month3ConsumptionKvar = "0";
    private String month4ConsumptionKvar = "0";
    private String month5ConsumptionKvar = "0";
    private String month6ConsumptionKvar = "0";
    private String month7ConsumptionKvar = "0";
    private String month8ConsumptionKvar = "0";
    private String month9ConsumptionKvar = "0";
    private String month10ConsumptionKvar = "0";
    private String month11ConsumptionKvar = "0";
    private String month12ConsumptionKvar = "0";
    private String meterType = "0";

    private boolean isCustomerId(String text) {
        return text != null && (text.matches("[0-9]+") && text.length() > 2 && text.length() <= 32);
    }

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public ElectrometerResponse fromMeterData(MeterData meterData) {
        if (meterData == null)
            return this;
        if (meterData.getMeterID() != null && !meterData.getMeterID().equalsIgnoreCase("NF"))
            this.meterId = meterData.getMeterID().replace("\t", "_");
        if (meterData.getCustomerID() != null && !meterData.getCustomerID().equalsIgnoreCase("NF") && isCustomerId(meterData.getCustomerID().replace("\t", "_")))
            this.customerId = meterData.getCustomerID().replace("\t", "_");
        if (meterData.getCardID() != null && !meterData.getCardID().equalsIgnoreCase("NF"))
            this.cardId = meterData.getCardID().replace("\t", "_");
        if (meterData.getFirmwareVersion() != null && !meterData.getFirmwareVersion().equalsIgnoreCase("NF"))
            this.fwVersion = meterData.getFirmwareVersion().replace("\t", "_");
        if (meterData.getActivityType() != null && !meterData.getActivityType().equalsIgnoreCase("NF"))
            this.activityType = meterData.getActivityType().replace("\t", "_");
        if (meterData.getPowerFactor() != null && !meterData.getPowerFactor().equalsIgnoreCase("NF"))
            this.curentPowerFactor = meterData.getPowerFactor().replace("\t", "_");
        if (meterData.getLastYearPowerFactor() != null && !meterData.getLastYearPowerFactor().equalsIgnoreCase("NF"))
            this.lastYearPowerFactor = meterData.getLastYearPowerFactor().replace("\t", "_");
        if (meterData.getInstallingTechnicanCode() != null && !meterData.getInstallingTechnicanCode().equalsIgnoreCase("NF"))
            this.installingTechnicanCode = meterData.getInstallingTechnicanCode().replace("\t", "_");
        if (meterData.getInstallingDateAndTime() != null && !meterData.getInstallingDateAndTime().equalsIgnoreCase("NF"))
            this.installingDateAndTime = meterData.getInstallingDateAndTime().replace("\t", "_");
        if (meterData.getMeterDataTime() != null && !meterData.getMeterDataTime().equalsIgnoreCase("NF"))
            this.meterDateAndTime = meterData.getMeterDataTime().replace("\t", "_");
        if (meterData.getCurrentStepTarrif() != null && !meterData.getCurrentStepTarrif().equalsIgnoreCase("NF"))
            this.currentTarrifInstalling = meterData.getCurrentStepTarrif().replace("\t", "_");
        if (meterData.getCurrentTarrifInstallationDate() != null && !meterData.getCurrentTarrifInstallationDate().equalsIgnoreCase("NF"))
            this.currentTariffActivationDate = meterData.getCurrentTarrifInstallationDate().replace("\t", "_");
        if (meterData.getMeterStatus() != null && !meterData.getMeterStatus().equalsIgnoreCase("NF"))
            this.meterStatus = meterData.getMeterStatus().replace("\t", "_");
        if (meterData.getRelayStatus() != null && !meterData.getRelayStatus().equalsIgnoreCase("NF"))
            this.relayStatus = meterData.getRelayStatus().replace("\t", "_");
        if (meterData.getBatteryStatus() != null && !meterData.getBatteryStatus().equalsIgnoreCase("NF"))
            this.batteryStatus = meterData.getBatteryStatus().replace("\t", "_");
        if (meterData.getCoverOpen() != null && !meterData.getCoverOpen().equalsIgnoreCase("NF"))
            this.topCoverStatus = meterData.getCoverOpen().replace("\t", "_");
        if (meterData.getSideCover() != null && !meterData.getSideCover().equalsIgnoreCase("NF"))
            this.sideCoverStatus = meterData.getSideCover().replace("\t", "_");
        if (meterData.getTechnical_code_event_1() != null && !meterData.getTechnical_code_event_1().equalsIgnoreCase("NF"))
            this.technicalCodeEvent1 = meterData.getTechnical_code_event_1().replace("\t", "_");
        if (meterData.getEvent_type_1() != null && !meterData.getEvent_type_1().equalsIgnoreCase("NF"))
            this.eventType1 = meterData.getEvent_type_1().replace("\t", "_");
        if (meterData.getEvent_Date_1() != null && !meterData.getEvent_Date_1().equalsIgnoreCase("NF"))
            this.eventDate1 = meterData.getEvent_Date_1().replace("\t", "_");
        if (meterData.getTechnical_code_event_2() != null && !meterData.getTechnical_code_event_2().equalsIgnoreCase("NF"))
            this.technicalCodeEvent2 = meterData.getTechnical_code_event_2().replace("\t", "_");
        if (meterData.getEvent_type_2() != null && !meterData.getEvent_type_2().equalsIgnoreCase("NF"))
            this.eventType2 = meterData.getEvent_type_2().replace("\t", "_");
        if (meterData.getEvent_Date_2() != null && !meterData.getEvent_Date_2().equalsIgnoreCase("NF"))
            this.eventDate2 = meterData.getEvent_Date_2().replace("\t", "_");
        if (meterData.getTechnical_code_event_3() != null && !meterData.getTechnical_code_event_3().equalsIgnoreCase("NF"))
            this.technicalCodeEvent3 = meterData.getTechnical_code_event_3().replace("\t", "_");
        if (meterData.getEvent_type_3() != null && !meterData.getEvent_type_3().equalsIgnoreCase("NF"))
            this.eventType3 = meterData.getEvent_type_3().replace("\t", "_");
        if (meterData.getEvent_Date_3() != null && !meterData.getEvent_Date_3().equalsIgnoreCase("NF"))
            this.eventDate3 = meterData.getEvent_Date_3().replace("\t", "_");
        if (meterData.getTotalRechargeNumber() != null && !meterData.getTotalRechargeNumber().equalsIgnoreCase("NF"))
            this.rechargeNumber = meterData.getTotalRechargeNumber().replace("\t", "_");
        if (meterData.getTotalRechargeAmount() != null && !meterData.getTotalRechargeAmount().equalsIgnoreCase("NF"))
            this.rechargeAmount = meterData.getTotalRechargeAmount().replace("\t", "_");
        if (meterData.getDateTimeOfLastRechargeTransaction() != null && !meterData.getDateTimeOfLastRechargeTransaction().equalsIgnoreCase("NF"))
            this.lastRechargeDateAndTime = meterData.getDateTimeOfLastRechargeTransaction().replace("\t", "_");
        if (meterData.getTarrifRemainingCreditKw() != null && !meterData.getTarrifRemainingCreditKw().equalsIgnoreCase("NF"))
            this.remainingCreditKw = meterData.getTarrifRemainingCreditKw().replace("\t", "_");
        if (meterData.getTarrifRemainingCredit() != null && !meterData.getTarrifRemainingCredit().equalsIgnoreCase("NF"))
            this.remainingCreditMoney = meterData.getTarrifRemainingCredit().replace("\t", "_");
        if (meterData.getTarrifRemainingDebit() != null && !meterData.getTarrifRemainingDebit().equalsIgnoreCase("NF"))
            this.debts = meterData.getTarrifRemainingDebit().replace("\t", "_");
        if (meterData.getTotalConsumptionKw() != null && !meterData.getTotalConsumptionKw().equalsIgnoreCase("NF"))
            this.totalConsumptionKw = meterData.getTotalConsumptionKw().replace("\t", "_");
        if (meterData.getTotalConsumption() != null && !meterData.getTotalConsumption().equalsIgnoreCase("NF"))
            this.totalConsumptionMoney = meterData.getTotalConsumption().replace("\t", "_");
        if (meterData.getTotalConsumptionKvar() != null && !meterData.getTotalConsumptionKvar().equalsIgnoreCase("NF"))
            this.totalConsumptionKvar = meterData.getTotalConsumptionKvar().replace("\t", "_");
        if (meterData.getCurrentDemand() != null && !meterData.getCurrentDemand().equalsIgnoreCase("NF"))
            this.currentDemand = meterData.getCurrentDemand().replace("\t", "_");
        if (meterData.getLifeMaximumDemandAmpere() != null && !meterData.getLifeMaximumDemandAmpere().equalsIgnoreCase("NF"))
            this.maximumDemand = meterData.getLifeMaximumDemandAmpere().replace("\t", "_");
        if (meterData.getLifeMaximumDemandDateTime() != null && !meterData.getLifeMaximumDemandDateTime().equalsIgnoreCase("NF"))
            this.maximumDemandDate = meterData.getLifeMaximumDemandDateTime().replace("\t", "_");
        if (meterData.getInstanteneousVolt() != null && !meterData.getInstanteneousVolt().equalsIgnoreCase("NF"))
            this.instanteneousVolt = meterData.getInstanteneousVolt().replace("\t", "_");
        if (meterData.getInstanteneousCurrentPhaseAmpere() != null && !meterData.getInstanteneousCurrentPhaseAmpere().equalsIgnoreCase("NF"))
            this.instanteneousCurrentPhaseAmpere = meterData.getInstanteneousCurrentPhaseAmpere().replace("\t", "_");
        if (meterData.getInstanteneous_current_Neutral() != null && !meterData.getInstanteneous_current_Neutral().equalsIgnoreCase("NF"))
            this.instanteneousCurrentNeutral = meterData.getInstanteneous_current_Neutral().replace("\t", "_");
        if (meterData.getTotalReverse() != null && !meterData.getTotalReverse().equalsIgnoreCase("NF"))
            this.reverseKwh = meterData.getTotalReverse().replace("\t", "_");
        if (meterData.getTotalUnbalance() != null && !meterData.getTotalUnbalance().equalsIgnoreCase("NF"))
            this.unbalanceKwh = meterData.getTotalUnbalance().replace("\t", "_");
        if (meterData.getCurrentMonthConsumption() != null && !meterData.getCurrentMonthConsumption().equalsIgnoreCase("NF"))
            this.currentMonthConsumptionKW = meterData.getCurrentMonthConsumption().replace("\t", "_");
        if (meterData.getCurrentMonthConsumptionMoney() != null && !meterData.getCurrentMonthConsumptionMoney().equalsIgnoreCase("NF"))
            this.currentMonthConsumptionMoney = meterData.getCurrentMonthConsumptionMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(11) != null && meterData.getMonthConsumption(11).getConsumption() != null && !meterData.getMonthConsumption(11).getConsumption().equalsIgnoreCase("NF"))
            this.month1ConsumptionKWh = meterData.getMonthConsumption(11).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(11) != null && meterData.getMonthConsumption(11).getMoney() != null && !meterData.getMonthConsumption(11).getMoney().equalsIgnoreCase("NF"))
            this.month1ConsumptionMoney = meterData.getMonthConsumption(11).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(11) != null && meterData.getMonthConsumption(11).getMaxDemand() != null && !meterData.getMonthConsumption(11).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth1 = meterData.getMonthConsumption(11).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(11) != null && meterData.getMonthConsumption(11).getDateMaxDemand() != null && !meterData.getMonthConsumption(11).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth1Date = meterData.getMonthConsumption(11).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(11) != null && meterData.getMonthConsumption(11).getReactiveEnergy() != null && !meterData.getMonthConsumption(11).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month1ConsumptionKvar = meterData.getMonthConsumption(11).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(10) != null && meterData.getMonthConsumption(10).getConsumption() != null && !meterData.getMonthConsumption(10).getConsumption().equalsIgnoreCase("NF"))
            this.month2ConsumptionKWh = meterData.getMonthConsumption(10).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(10) != null && meterData.getMonthConsumption(10).getMoney() != null && !meterData.getMonthConsumption(10).getMoney().equalsIgnoreCase("NF"))
            this.month2ConsumptionMoney = meterData.getMonthConsumption(10).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(10) != null && meterData.getMonthConsumption(10).getMaxDemand() != null && !meterData.getMonthConsumption(10).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth2 = meterData.getMonthConsumption(10).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(10) != null && meterData.getMonthConsumption(10).getDateMaxDemand() != null && !meterData.getMonthConsumption(10).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth2Date = meterData.getMonthConsumption(10).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(10) != null && meterData.getMonthConsumption(10).getReactiveEnergy() != null && !meterData.getMonthConsumption(10).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month2ConsumptionKvar = meterData.getMonthConsumption(10).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(9) != null && meterData.getMonthConsumption(9).getConsumption() != null && !meterData.getMonthConsumption(9).getConsumption().equalsIgnoreCase("NF"))
            this.month3ConsumptionKWh = meterData.getMonthConsumption(9).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(9) != null && meterData.getMonthConsumption(9).getMoney() != null && !meterData.getMonthConsumption(9).getMoney().equalsIgnoreCase("NF"))
            this.month3ConsumptionMoney = meterData.getMonthConsumption(9).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(9) != null && meterData.getMonthConsumption(9).getMaxDemand() != null && !meterData.getMonthConsumption(9).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth3 = meterData.getMonthConsumption(9).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(9) != null && meterData.getMonthConsumption(9).getDateMaxDemand() != null && !meterData.getMonthConsumption(9).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth3Date = meterData.getMonthConsumption(9).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(9) != null && meterData.getMonthConsumption(9).getReactiveEnergy() != null && !meterData.getMonthConsumption(9).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month3ConsumptionKvar = meterData.getMonthConsumption(9).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(8) != null && meterData.getMonthConsumption(8).getConsumption() != null && !meterData.getMonthConsumption(8).getConsumption().equalsIgnoreCase("NF"))
            this.month4ConsumptionKWh = meterData.getMonthConsumption(8).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(8) != null && meterData.getMonthConsumption(8).getMoney() != null && !meterData.getMonthConsumption(8).getMoney().equalsIgnoreCase("NF"))
            this.month4ConsumptionMoney = meterData.getMonthConsumption(8).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(8) != null && meterData.getMonthConsumption(8).getMaxDemand() != null && !meterData.getMonthConsumption(8).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth4 = meterData.getMonthConsumption(8).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(8) != null && meterData.getMonthConsumption(8).getDateMaxDemand() != null && !meterData.getMonthConsumption(8).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth4Date = meterData.getMonthConsumption(8).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(8) != null && meterData.getMonthConsumption(8).getReactiveEnergy() != null && !meterData.getMonthConsumption(8).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month4ConsumptionKvar = meterData.getMonthConsumption(8).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(7) != null && meterData.getMonthConsumption(7).getConsumption() != null && !meterData.getMonthConsumption(7).getConsumption().equalsIgnoreCase("NF"))
            this.month5ConsumptionKWh = meterData.getMonthConsumption(7).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(7) != null && meterData.getMonthConsumption(7).getMoney() != null && !meterData.getMonthConsumption(7).getMoney().equalsIgnoreCase("NF"))
            this.month5ConsumptionMoney = meterData.getMonthConsumption(7).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(7) != null && meterData.getMonthConsumption(7).getMaxDemand() != null && !meterData.getMonthConsumption(7).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth5 = meterData.getMonthConsumption(7).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(7) != null && meterData.getMonthConsumption(7).getDateMaxDemand() != null && !meterData.getMonthConsumption(7).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth5Date = meterData.getMonthConsumption(7).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(7) != null && meterData.getMonthConsumption(7).getReactiveEnergy() != null && !meterData.getMonthConsumption(7).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month5ConsumptionKvar = meterData.getMonthConsumption(7).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(6) != null && meterData.getMonthConsumption(6).getConsumption() != null && !meterData.getMonthConsumption(6).getConsumption().equalsIgnoreCase("NF"))
            this.month6ConsumptionKWh = meterData.getMonthConsumption(6).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(6) != null && meterData.getMonthConsumption(6).getMoney() != null && !meterData.getMonthConsumption(6).getMoney().equalsIgnoreCase("NF"))
            this.month6ConsumptionMoney = meterData.getMonthConsumption(6).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(6) != null && meterData.getMonthConsumption(6).getMaxDemand() != null && !meterData.getMonthConsumption(6).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth6 = meterData.getMonthConsumption(6).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(6) != null && meterData.getMonthConsumption(6).getDateMaxDemand() != null && !meterData.getMonthConsumption(6).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth6Date = meterData.getMonthConsumption(6).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(6) != null && meterData.getMonthConsumption(6).getReactiveEnergy() != null && !meterData.getMonthConsumption(6).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month6ConsumptionKvar = meterData.getMonthConsumption(6).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(5) != null && meterData.getMonthConsumption(5).getConsumption() != null && !meterData.getMonthConsumption(5).getConsumption().equalsIgnoreCase("NF"))
            this.month7ConsumptionKWh = meterData.getMonthConsumption(5).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(5) != null && meterData.getMonthConsumption(5).getMoney() != null && !meterData.getMonthConsumption(5).getMoney().equalsIgnoreCase("NF"))
            this.month7ConsumptionMoney = meterData.getMonthConsumption(5).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(5) != null && meterData.getMonthConsumption(5).getMaxDemand() != null && !meterData.getMonthConsumption(5).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth7 = meterData.getMonthConsumption(5).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(5) != null && meterData.getMonthConsumption(5).getDateMaxDemand() != null && !meterData.getMonthConsumption(5).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth7Date = meterData.getMonthConsumption(5).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(5) != null && meterData.getMonthConsumption(5).getReactiveEnergy() != null && !meterData.getMonthConsumption(5).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month7ConsumptionKvar = meterData.getMonthConsumption(5).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(4) != null && meterData.getMonthConsumption(4).getConsumption() != null && !meterData.getMonthConsumption(4).getConsumption().equalsIgnoreCase("NF"))
            this.month8ConsumptionKWh = meterData.getMonthConsumption(4).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(4) != null && meterData.getMonthConsumption(4).getMoney() != null && !meterData.getMonthConsumption(4).getMoney().equalsIgnoreCase("NF"))
            this.month8ConsumptionMoney = meterData.getMonthConsumption(4).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(4) != null && meterData.getMonthConsumption(4).getMaxDemand() != null && !meterData.getMonthConsumption(4).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth8 = meterData.getMonthConsumption(4).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(4) != null && meterData.getMonthConsumption(4).getDateMaxDemand() != null && !meterData.getMonthConsumption(4).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth8Date = meterData.getMonthConsumption(4).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(4) != null && meterData.getMonthConsumption(4).getReactiveEnergy() != null && !meterData.getMonthConsumption(4).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month8ConsumptionKvar = meterData.getMonthConsumption(4).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(3) != null && meterData.getMonthConsumption(3).getConsumption() != null && !meterData.getMonthConsumption(3).getConsumption().equalsIgnoreCase("NF"))
            this.month9ConsumptionKWh = meterData.getMonthConsumption(3).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(3) != null && meterData.getMonthConsumption(3).getMoney() != null && !meterData.getMonthConsumption(3).getMoney().equalsIgnoreCase("NF"))
            this.month9ConsumptionMoney = meterData.getMonthConsumption(3).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(3) != null && meterData.getMonthConsumption(3).getMaxDemand() != null && !meterData.getMonthConsumption(3).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth9 = meterData.getMonthConsumption(3).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(3) != null && meterData.getMonthConsumption(3).getDateMaxDemand() != null && !meterData.getMonthConsumption(3).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth9Date = meterData.getMonthConsumption(3).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(3) != null && meterData.getMonthConsumption(3).getReactiveEnergy() != null && !meterData.getMonthConsumption(3).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month9ConsumptionKvar = meterData.getMonthConsumption(3).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(2) != null && meterData.getMonthConsumption(2).getConsumption() != null && !meterData.getMonthConsumption(2).getConsumption().equalsIgnoreCase("NF"))
            this.month10ConsumptionKWh = meterData.getMonthConsumption(2).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(2) != null && meterData.getMonthConsumption(2).getMoney() != null && !meterData.getMonthConsumption(2).getMoney().equalsIgnoreCase("NF"))
            this.month10ConsumptionMoney = meterData.getMonthConsumption(2).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(2) != null && meterData.getMonthConsumption(2).getMaxDemand() != null && !meterData.getMonthConsumption(2).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth10 = meterData.getMonthConsumption(2).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(2) != null && meterData.getMonthConsumption(2).getDateMaxDemand() != null && !meterData.getMonthConsumption(2).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth10Date = meterData.getMonthConsumption(2).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(2) != null && meterData.getMonthConsumption(2).getReactiveEnergy() != null && !meterData.getMonthConsumption(2).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month10ConsumptionKvar = meterData.getMonthConsumption(2).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(1) != null && meterData.getMonthConsumption(1).getConsumption() != null && !meterData.getMonthConsumption(1).getConsumption().equalsIgnoreCase("NF"))
            this.month11ConsumptionKWh = meterData.getMonthConsumption(1).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(1) != null && meterData.getMonthConsumption(1).getMoney() != null && !meterData.getMonthConsumption(1).getMoney().equalsIgnoreCase("NF"))
            this.month11ConsumptionMoney = meterData.getMonthConsumption(1).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(1) != null && meterData.getMonthConsumption(1).getMaxDemand() != null && !meterData.getMonthConsumption(1).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth11 = meterData.getMonthConsumption(1).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(1) != null && meterData.getMonthConsumption(1).getDateMaxDemand() != null && !meterData.getMonthConsumption(1).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth11Date = meterData.getMonthConsumption(1).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(1) != null && meterData.getMonthConsumption(1).getReactiveEnergy() != null && !meterData.getMonthConsumption(1).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month11ConsumptionKvar = meterData.getMonthConsumption(1).getReactiveEnergy().replace("\t", "_");
        if (meterData.getMonthConsumption(0) != null && meterData.getMonthConsumption(0).getConsumption() != null && !meterData.getMonthConsumption(0).getConsumption().equalsIgnoreCase("NF"))
            this.month12ConsumptionKWh = meterData.getMonthConsumption(0).getConsumption().replace("\t", "_");
        if (meterData.getMonthConsumption(0) != null && meterData.getMonthConsumption(0).getMoney() != null && !meterData.getMonthConsumption(0).getMoney().equalsIgnoreCase("NF"))
            this.month12ConsumptionMoney = meterData.getMonthConsumption(0).getMoney().replace("\t", "_");
        if (meterData.getMonthConsumption(0) != null && meterData.getMonthConsumption(0).getMaxDemand() != null && !meterData.getMonthConsumption(0).getMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth12 = meterData.getMonthConsumption(0).getMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(0) != null && meterData.getMonthConsumption(0).getDateMaxDemand() != null && !meterData.getMonthConsumption(0).getDateMaxDemand().equalsIgnoreCase("NF"))
            this.maximDemandMonth12Date = meterData.getMonthConsumption(0).getDateMaxDemand().replace("\t", "_");
        if (meterData.getMonthConsumption(0) != null && meterData.getMonthConsumption(0).getReactiveEnergy() != null && !meterData.getMonthConsumption(0).getReactiveEnergy().equalsIgnoreCase("NF"))
            this.month12ConsumptionKvar = meterData.getMonthConsumption(0).getReactiveEnergy().replace("\t", "_");
        return this;
    }


    public String getMeterId() {
        return meterId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public String getCardId() {
        return cardId;
    }

    public String getFwVersion() {
        return fwVersion;
    }

    public String getActivityType() {
        return activityType;
    }

    public String getCurentPowerFactor() {
        return curentPowerFactor;
    }

    public String getLastYearPowerFactor() {
        return lastYearPowerFactor;
    }

    public String getInstallingTechnicanCode() {
        return installingTechnicanCode;
    }

    public String getInstallingDateAndTime() {
        return installingDateAndTime;
    }

    public String getMeterDateAndTime() {
        return meterDateAndTime;
    }

    public String getCurrentTarrifInstalling() {
        return currentTarrifInstalling;
    }

    public String getCurrentTariffActivationDate() {
        return currentTariffActivationDate;
    }

    public String getMeterStatus() {
        return meterStatus;
    }

    public String getRelayStatus() {
        return relayStatus;
    }

    public String getBatteryStatus() {
        return batteryStatus;
    }

    public String getTopCoverStatus() {
        return topCoverStatus;
    }

    public String getSideCoverStatus() {
        return sideCoverStatus;
    }

    public String getTechnicalCodeEvent1() {
        return technicalCodeEvent1;
    }

    public String getEventType1() {
        return eventType1;
    }

    public String getEventDate1() {
        return eventDate1;
    }

    public String getTechnicalCodeEvent2() {
        return technicalCodeEvent2;
    }

    public String getEventType2() {
        return eventType2;
    }

    public String getEventDate2() {
        return eventDate2;
    }

    public String getTechnicalCodeEvent3() {
        return technicalCodeEvent3;
    }

    public String getEventType3() {
        return eventType3;
    }

    public String getEventDate3() {
        return eventDate3;
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public String getRechargeAmount() {
        return rechargeAmount;
    }

    public String getLastRechargeDateAndTime() {
        return lastRechargeDateAndTime;
    }

    public String getRemainingCreditKw() {
        return remainingCreditKw;
    }

    public String getRemainingCreditMoney() {
        return remainingCreditMoney;
    }

    public String getDebts() {
        return debts;
    }

    public String getTotalConsumptionKw() {
        return totalConsumptionKw;
    }

    public String getTotalConsumptionMoney() {
        return totalConsumptionMoney;
    }

    public String getTotalConsumptionKvar() {
        return totalConsumptionKvar;
    }

    public String getCurrentDemand() {
        return currentDemand;
    }

    public String getMaximumDemand() {
        return maximumDemand;
    }

    public String getMaximumDemandDate() {
        return maximumDemandDate;
    }

    public String getInstanteneousPahaseAVolt() {
        return instanteneousPahaseAVolt;
    }

    public String getInstanteneousPahaseBVolt() {
        return instanteneousPahaseBVolt;
    }

    public String getInstanteneousPahaseCVolt() {
        return instanteneousPahaseCVolt;
    }

    public String getInstanteneousPahaseACurrent() {
        return instanteneousPahaseACurrent;
    }

    public String getInstanteneousPahaseBCurrent() {
        return instanteneousPahaseBCurrent;
    }

    public String getInstanteneousPahaseCCurrent() {
        return instanteneousPahaseCCurrent;
    }

    public String getInstanteneousVolt() {
        return instanteneousVolt;
    }

    public String getInstanteneousCurrentPhaseAmpere() {
        return instanteneousCurrentPhaseAmpere;
    }

    public String getInstanteneousCurrentNeutral() {
        return instanteneousCurrentNeutral;
    }

    public String getReverseKwh() {
        return reverseKwh;
    }

    public String getUnbalanceKwh() {
        return unbalanceKwh;
    }

    public String getCurrentMonthConsumptionKW() {
        return currentMonthConsumptionKW;
    }

    public String getCurrentMonthConsumptionMoney() {
        return currentMonthConsumptionMoney;
    }

    public String getMonth1ConsumptionKWh() {
        return month1ConsumptionKWh;
    }

    public String getMonth2ConsumptionKWh() {
        return month2ConsumptionKWh;
    }

    public String getMonth3ConsumptionKWh() {
        return month3ConsumptionKWh;
    }

    public String getMonth4ConsumptionKWh() {
        return month4ConsumptionKWh;
    }

    public String getMonth5ConsumptionKWh() {
        return month5ConsumptionKWh;
    }

    public String getMonth6ConsumptionKWh() {
        return month6ConsumptionKWh;
    }

    public String getMonth7ConsumptionKWh() {
        return month7ConsumptionKWh;
    }

    public String getMonth8ConsumptionKWh() {
        return month8ConsumptionKWh;
    }

    public String getMonth9ConsumptionKWh() {
        return month9ConsumptionKWh;
    }

    public String getMonth10ConsumptionKWh() {
        return month10ConsumptionKWh;
    }

    public String getMonth11ConsumptionKWh() {
        return month11ConsumptionKWh;
    }

    public String getMonth12ConsumptionKWh() {
        return month12ConsumptionKWh;
    }

    public String getMonth1ConsumptionMoney() {
        return month1ConsumptionMoney;
    }

    public String getMonth2ConsumptionMoney() {
        return month2ConsumptionMoney;
    }

    public String getMonth3ConsumptionMoney() {
        return month3ConsumptionMoney;
    }

    public String getMonth4ConsumptionMoney() {
        return month4ConsumptionMoney;
    }

    public String getMonth5ConsumptionMoney() {
        return month5ConsumptionMoney;
    }

    public String getMonth6ConsumptionMoney() {
        return month6ConsumptionMoney;
    }

    public String getMonth7ConsumptionMoney() {
        return month7ConsumptionMoney;
    }

    public String getMonth8ConsumptionMoney() {
        return month8ConsumptionMoney;
    }

    public String getMonth9ConsumptionMoney() {
        return month9ConsumptionMoney;
    }

    public String getMonth10ConsumptionMoney() {
        return month10ConsumptionMoney;
    }

    public String getMonth11ConsumptionMoney() {
        return month11ConsumptionMoney;
    }

    public String getMonth12ConsumptionMoney() {
        return month12ConsumptionMoney;
    }

    public String getMaximDemandMonth1() {
        return maximDemandMonth1;
    }

    public String getMaximDemandMonth2() {
        return maximDemandMonth2;
    }

    public String getMaximDemandMonth3() {
        return maximDemandMonth3;
    }

    public String getMaximDemandMonth4() {
        return maximDemandMonth4;
    }

    public String getMaximDemandMonth5() {
        return maximDemandMonth5;
    }

    public String getMaximDemandMonth6() {
        return maximDemandMonth6;
    }

    public String getMaximDemandMonth7() {
        return maximDemandMonth7;
    }

    public String getMaximDemandMonth8() {
        return maximDemandMonth8;
    }

    public String getMaximDemandMonth9() {
        return maximDemandMonth9;
    }

    public String getMaximDemandMonth10() {
        return maximDemandMonth10;
    }

    public String getMaximDemandMonth11() {
        return maximDemandMonth11;
    }

    public String getMaximDemandMonth12() {
        return maximDemandMonth12;
    }

    public String getMaximDemandMonth1Date() {
        return maximDemandMonth1Date;
    }

    public String getMaximDemandMonth2Date() {
        return maximDemandMonth2Date;
    }

    public String getMaximDemandMonth3Date() {
        return maximDemandMonth3Date;
    }

    public String getMaximDemandMonth4Date() {
        return maximDemandMonth4Date;
    }

    public String getMaximDemandMonth5Date() {
        return maximDemandMonth5Date;
    }

    public String getMaximDemandMonth6Date() {
        return maximDemandMonth6Date;
    }

    public String getMaximDemandMonth7Date() {
        return maximDemandMonth7Date;
    }

    public String getMaximDemandMonth8Date() {
        return maximDemandMonth8Date;
    }

    public String getMaximDemandMonth9Date() {
        return maximDemandMonth9Date;
    }

    public String getMaximDemandMonth10Date() {
        return maximDemandMonth10Date;
    }

    public String getMaximDemandMonth11Date() {
        return maximDemandMonth11Date;
    }

    public String getMaximDemandMonth12Date() {
        return maximDemandMonth12Date;
    }

    public String getMonth1ConsumptionKvar() {
        return month1ConsumptionKvar;
    }

    public String getMonth2ConsumptionKvar() {
        return month2ConsumptionKvar;
    }

    public String getMonth3ConsumptionKvar() {
        return month3ConsumptionKvar;
    }

    public String getMonth4ConsumptionKvar() {
        return month4ConsumptionKvar;
    }

    public String getMonth5ConsumptionKvar() {
        return month5ConsumptionKvar;
    }

    public String getMonth6ConsumptionKvar() {
        return month6ConsumptionKvar;
    }

    public String getMonth7ConsumptionKvar() {
        return month7ConsumptionKvar;
    }

    public String getMonth8ConsumptionKvar() {
        return month8ConsumptionKvar;
    }

    public String getMonth9ConsumptionKvar() {
        return month9ConsumptionKvar;
    }

    public String getMonth10ConsumptionKvar() {
        return month10ConsumptionKvar;
    }

    public String getMonth11ConsumptionKvar() {
        return month11ConsumptionKvar;
    }

    public String getMonth12ConsumptionKvar() {
        return month12ConsumptionKvar;
    }

    public String getMeterModel() {
        return meterModel;
    }

    public void setMeterModel(String meterModel) {
        this.meterModel = meterModel;
    }

    @NonNull
    @Override
    public String toString() {
        return "ElectrometerResponse : " + '\n' +
                "meterType : " + meterType + '\n' +
                "meterId : " + meterId + '\n' +
                "customerId : " + customerId + '\n' +
                "cardId : " + cardId + '\n' +
                "fwVersion : " + fwVersion + '\n' +
                "activityType : " + activityType + '\n' +
                "curentPowerFactor : " + curentPowerFactor + '\n' +
                "lastYearPowerFactor : " + lastYearPowerFactor + '\n' +
                "installingTechnicanCode : " + installingTechnicanCode + '\n' +
                "installingDateAndTime : " + installingDateAndTime + '\n' +
                "meterDateAndTime : " + meterDateAndTime + '\n' +
                "currentTarrifInstalling : " + currentTarrifInstalling + '\n' +
                "currentTariffActivationDate : " + currentTariffActivationDate + '\n' +
                "meterStatus : " + meterStatus + '\n' +
                "relayStatus : " + relayStatus + '\n' +
                "batteryStatus : " + batteryStatus + '\n' +
                "topCoverStatus : " + topCoverStatus + '\n' +
                "sideCoverStatus : " + sideCoverStatus + '\n' +
                "technicalCodeEvent1 : " + technicalCodeEvent1 + '\n' +
                "eventType1 : " + eventType1 + '\n' +
                "eventDate1 : " + eventDate1 + '\n' +
                "technicalCodeEvent2 : " + technicalCodeEvent2 + '\n' +
                "eventType2 : " + eventType2 + '\n' +
                "eventDate2 : " + eventDate2 + '\n' +
                "technicalCodeEvent3 : " + technicalCodeEvent3 + '\n' +
                "eventType3 : " + eventType3 + '\n' +
                "eventDate3 : " + eventDate3 + '\n' +
                "rechargeNumber : " + rechargeNumber + '\n' +
                "rechargeAmount : " + rechargeAmount + '\n' +
                "lastRechargeDateAndTime : " + lastRechargeDateAndTime + '\n' +
                "remainingCreditKw : " + remainingCreditKw + '\n' +
                "remainingCreditMoney : " + remainingCreditMoney + '\n' +
                "debts : " + debts + '\n' +
                "totalConsumptionKw : " + totalConsumptionKw + '\n' +
                "totalConsumptionMoney : " + totalConsumptionMoney + '\n' +
                "totalConsumptionKvar : " + totalConsumptionKvar + '\n' +
                "currentDemand : " + currentDemand + '\n' +
                "maximumDemand : " + maximumDemand + '\n' +
                "maximumDemandDate : " + maximumDemandDate + '\n' +
                "instanteneousPahaseAVolt : " + instanteneousPahaseAVolt + '\n' +
                "instanteneousPahaseBVolt : " + instanteneousPahaseBVolt + '\n' +
                "instanteneousPahaseCVolt : " + instanteneousPahaseCVolt + '\n' +
                "instanteneousPahaseACurrent : " + instanteneousPahaseACurrent + '\n' +
                "instanteneousPahaseBCurrent : " + instanteneousPahaseBCurrent + '\n' +
                "instanteneousPahaseCCurrent : " + instanteneousPahaseCCurrent + '\n' +
                "instanteneousVolt : " + instanteneousVolt + '\n' +
                "instanteneousCurrentPhaseAmpere : " + instanteneousCurrentPhaseAmpere + '\n' +
                "instanteneousCurrentNeutral : " + instanteneousCurrentNeutral + '\n' +
                "reverseKwh : " + reverseKwh + '\n' +
                "unbalanceKwh : " + unbalanceKwh + '\n' +
                "currentMonthConsumptionKW : " + currentMonthConsumptionKW + '\n' +
                "currentMonthConsumptionMoney : " + currentMonthConsumptionMoney + '\n' +
                "month1ConsumptionKWh : " + month1ConsumptionKWh + '\n' +
                "month2ConsumptionKWh : " + month2ConsumptionKWh + '\n' +
                "month3ConsumptionKWh : " + month3ConsumptionKWh + '\n' +
                "month4ConsumptionKWh : " + month4ConsumptionKWh + '\n' +
                "month5ConsumptionKWh : " + month5ConsumptionKWh + '\n' +
                "month6ConsumptionKWh : " + month6ConsumptionKWh + '\n' +
                "month7ConsumptionKWh : " + month7ConsumptionKWh + '\n' +
                "month8ConsumptionKWh : " + month8ConsumptionKWh + '\n' +
                "month9ConsumptionKWh : " + month9ConsumptionKWh + '\n' +
                "month10ConsumptionKWh : " + month10ConsumptionKWh + '\n' +
                "month11ConsumptionKWh : " + month11ConsumptionKWh + '\n' +
                "month12ConsumptionKWh : " + month12ConsumptionKWh + '\n' +
                "month1ConsumptionMoney : " + month1ConsumptionMoney + '\n' +
                "month2ConsumptionMoney : " + month2ConsumptionMoney + '\n' +
                "month3ConsumptionMoney : " + month3ConsumptionMoney + '\n' +
                "month4ConsumptionMoney : " + month4ConsumptionMoney + '\n' +
                "month5ConsumptionMoney : " + month5ConsumptionMoney + '\n' +
                "month6ConsumptionMoney : " + month6ConsumptionMoney + '\n' +
                "month7ConsumptionMoney : " + month7ConsumptionMoney + '\n' +
                "month8ConsumptionMoney : " + month8ConsumptionMoney + '\n' +
                "month9ConsumptionMoney : " + month9ConsumptionMoney + '\n' +
                "month10ConsumptionMoney : " + month10ConsumptionMoney + '\n' +
                "month11ConsumptionMoney : " + month11ConsumptionMoney + '\n' +
                "month12ConsumptionMoney : " + month12ConsumptionMoney + '\n' +
                "maximDemandMonth1 : " + maximDemandMonth1 + '\n' +
                "maximDemandMonth2 : " + maximDemandMonth2 + '\n' +
                "maximDemandMonth3 : " + maximDemandMonth3 + '\n' +
                "maximDemandMonth4 : " + maximDemandMonth4 + '\n' +
                "maximDemandMonth5 : " + maximDemandMonth5 + '\n' +
                "maximDemandMonth6 : " + maximDemandMonth6 + '\n' +
                "maximDemandMonth7 : " + maximDemandMonth7 + '\n' +
                "maximDemandMonth8 : " + maximDemandMonth8 + '\n' +
                "maximDemandMonth9 : " + maximDemandMonth9 + '\n' +
                "maximDemandMonth10 : " + maximDemandMonth10 + '\n' +
                "maximDemandMonth11 : " + maximDemandMonth11 + '\n' +
                "maximDemandMonth12 : " + maximDemandMonth12 + '\n' +
                "maximDemandMonth1Date : " + maximDemandMonth1Date + '\n' +
                "maximDemandMonth2Date : " + maximDemandMonth2Date + '\n' +
                "maximDemandMonth3Date : " + maximDemandMonth3Date + '\n' +
                "maximDemandMonth4Date : " + maximDemandMonth4Date + '\n' +
                "maximDemandMonth5Date : " + maximDemandMonth5Date + '\n' +
                "maximDemandMonth6Date : " + maximDemandMonth6Date + '\n' +
                "maximDemandMonth7Date : " + maximDemandMonth7Date + '\n' +
                "maximDemandMonth8Date : " + maximDemandMonth8Date + '\n' +
                "maximDemandMonth9Date : " + maximDemandMonth9Date + '\n' +
                "maximDemandMonth10Date : " + maximDemandMonth10Date + '\n' +
                "maximDemandMonth11Date : " + maximDemandMonth11Date + '\n' +
                "maximDemandMonth12Date : " + maximDemandMonth12Date + '\n' +
                "month1ConsumptionKvar : " + month1ConsumptionKvar + '\n' +
                "month2ConsumptionKvar : " + month2ConsumptionKvar + '\n' +
                "month3ConsumptionKvar : " + month3ConsumptionKvar + '\n' +
                "month4ConsumptionKvar : " + month4ConsumptionKvar + '\n' +
                "month5ConsumptionKvar : " + month5ConsumptionKvar + '\n' +
                "month6ConsumptionKvar : " + month6ConsumptionKvar + '\n' +
                "month7ConsumptionKvar : " + month7ConsumptionKvar + '\n' +
                "month8ConsumptionKvar : " + month8ConsumptionKvar + '\n' +
                "month9ConsumptionKvar : " + month9ConsumptionKvar + '\n' +
                "month10ConsumptionKvar : " + month10ConsumptionKvar + '\n' +
                "month11ConsumptionKvar : " + month11ConsumptionKvar + '\n' +
                "month12ConsumptionKvar : " + month12ConsumptionKvar + '\n' +
                "readingResult=" + readingResult + '\n' +
                "message : " + message;
    }

    @NonNull
    @Override
    public String toFileFormate() {
        return
                "Meter Type\t" + meterType + "\n" +
                        "ITEM_1_NEW_BASEITEM_Meter_ID\t" + meterId + "\n" +
                        "ITEM_2_NEW_BASEITEM_Customer_ID\t" + customerId + "\n" +
                        "ITEM_3_NEW_BASEITEM_CardID\t" + cardId + "\n" +
                        "ITEM_4_NEW_BASEITEM_fw_version\t" + fwVersion + "\n" +
                        "ITEM_5_NEW_BASEITEM_ActivityType\t" + activityType + "\n" +
                        "ITEM_6_NEW_BASEITEM_curent_Power_factor\t" + curentPowerFactor + "\n" +
                        "ITEM_7_NEW_BASEITEM_last_year_Power_factor\t" + lastYearPowerFactor + "\n" +
                        "ITEM_8_NEW_BASEITEM_installing_technican_code\t" + installingTechnicanCode + "\n" +
                        "ITEM_9_NEW_BASEITEM_installing_Date_and_time\t" + installingDateAndTime + "\n" +
                        "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time\t" + meterDateAndTime + "\n" +
                        "ITEM_11_NEW_BASEITEM_Current_tarrif_installing\t" + currentTarrifInstalling + "\n" +
                        "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date\t" + currentTariffActivationDate + "\n" +
                        "ITEM_13_NEW_BASEITEM_Meter_status\t" + meterStatus + "\n" +
                        "ITEM_14_NEW_BASEITEM_Relay_status\t" + relayStatus + "\n" +
                        "ITEM_15_NEW_BASEITEM_battery_status\t" + batteryStatus + "\n" +
                        "ITEM_16_NEW_BASEITEM_Top_cover_status\t" + topCoverStatus + "\n" +
                        "ITEM_17_NEW_BASEITEM_Side_cover_status\t" + sideCoverStatus + "\n" +
                        "ITEM_18_NEW_BASEITEM_Technical_code_event_1\t" + technicalCodeEvent1 + "\n" +
                        "ITEM_19_NEW_BASEITEM_event_type_1\t" + eventType1 + "\n" +
                        "ITEM_20_NEW_BASEITEM_event_Date_1\t" + eventDate1 + "\n" +
                        "ITEM_21_NEW_BASEITEM_Technical_code_event_2\t" + technicalCodeEvent2 + "\n" +
                        "ITEM_22_NEW_BASEITEM_event_type_2\t" + eventType2 + "\n" +
                        "ITEM_23_NEW_BASEITEM_event_Date_2\t" + eventDate2 + "\n" +
                        "ITEM_24_NEW_BASEITEM_Technical_code_event_3\t" + technicalCodeEvent3 + "\n" +
                        "ITEM_25_NEW_BASEITEM_event_type_3\t" + eventType3 + "\n" +
                        "ITEM_26_NEW_BASEITEM_event_Date_3\t" + eventDate3 + "\n" +
                        "ITEM_27_NEW_BASEITEM_recharge_number\t" + rechargeNumber + "\n" +
                        "ITEM_28_NEW_BASEITEM_Recharge_Amount\t" + rechargeAmount + "\n" +
                        "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time\t" + lastRechargeDateAndTime + "\n" +
                        "ITEM_30_NEW_BASEITEM_remaining_credit_kw\t" + remainingCreditKw + "\n" +
                        "ITEM_31_NEW_BASEITEM_remaining_credit_mony\t" + remainingCreditMoney + "\n" +
                        "ITEM_32_NEW_BASEITEM_Debts\t" + debts + "\n" +
                        "ITEM_33_NEW_BASEITEM_Total_consumption_kw\t" + totalConsumptionKw + "\n" +
                        "ITEM_34_NEW_BASEITEM_Total_consumption_mony\t" + totalConsumptionMoney + "\n" +
                        "ITEM_35_NEW_BASEITEM_Total_consumption_kvar\t" + totalConsumptionKvar + "\n" +
                        "ITEM_36_NEW_BASEITEM_Current_Demand\t" + currentDemand + "\n" +
                        "ITEM_37_NEW_BASEITEM_Maximum_Demand\t" + maximumDemand + "\n" +
                        "ITEM_38_NEW_BASEITEM_Maximum_Demand_date\t" + maximumDemandDate + "\n" +
                        "ITEM_39_NEW_BASEITEM_instanteneous_volt\t" + instanteneousVolt + "\n" +
                        "ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere\t" + instanteneousCurrentPhaseAmpere + "\n" +
                        "ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral\t" + instanteneousCurrentNeutral + "\n" +
                        "ITEM_42_NEW_BASEITEM_reverse_Kwh\t" + reverseKwh + "\n" +
                        "ITEM_43_NEW_BASEITEM_unbalance_Kwh\t" + unbalanceKwh + "\n" +
                        "ITEM_44_NEW_BASEITEM_current_month_consumption_KW\t" + currentMonthConsumptionKW + "\n" +
                        "ITEM_45_NEW_BASEITEM_current_month_consumption_MONY\t" + currentMonthConsumptionMoney + "\n" +
                        "ITEM_46_NEW_BASEITEM_1_month_consumption_kWh\t" + month1ConsumptionKWh + "\n" +
                        "ITEM_47_NEW_BASEITEM_2_month_consumption_kWh\t" + month2ConsumptionKWh + "\n" +
                        "ITEM_48_NEW_BASEITEM_3_month_consumption_kWh\t" + month3ConsumptionKWh + "\n" +
                        "ITEM_49_NEW_BASEITEM_4_month_consumption_kWh\t" + month4ConsumptionKWh + "\n" +
                        "ITEM_50_NEW_BASEITEM_5_month_consumption_kWh\t" + month5ConsumptionKWh + "\n" +
                        "ITEM_51_NEW_BASEITEM_6_month_consumption_kWh\t" + month6ConsumptionKWh + "\n" +
                        "ITEM_52_NEW_BASEITEM_7_month_consumption_kWh\t" + month7ConsumptionKWh + "\n" +
                        "ITEM_53_NEW_BASEITEM_8_month_consumption_kWh\t" + month8ConsumptionKWh + "\n" +
                        "ITEM_54_NEW_BASEITEM_9_month_consumption_kWh\t" + month9ConsumptionKWh + "\n" +
                        "ITEM_55_NEW_BASEITEM_10_month_consumption_kWh\t" + month10ConsumptionKWh + "\n" +
                        "ITEM_56_NEW_BASEITEM_11_month_consumption_kWh\t" + month11ConsumptionKWh + "\n" +
                        "ITEM_57_NEW_BASEITEM_12_month_consumption_kWh\t" + month12ConsumptionKWh + "\n" +
                        "ITEM_58_NEW_BASEITEM_1_month_consumption_Mony\t" + month1ConsumptionMoney + "\n" +
                        "ITEM_59_NEW_BASEITEM_2_month_consumption_Mony\t" + month2ConsumptionMoney + "\n" +
                        "ITEM_60_NEW_BASEITEM_3_month_consumption_Mony\t" + month3ConsumptionMoney + "\n" +
                        "ITEM_61_NEW_BASEITEM_4_month_consumption_Mony\t" + month4ConsumptionMoney + "\n" +
                        "ITEM_62_NEW_BASEITEM_5_month_consumption_Mony\t" + month5ConsumptionMoney + "\n" +
                        "ITEM_63_NEW_BASEITEM_6_month_consumption_Mony\t" + month6ConsumptionMoney + "\n" +
                        "ITEM_64_NEW_BASEITEM_7_month_consumption_Mony\t" + month7ConsumptionMoney + "\n" +
                        "ITEM_65_NEW_BASEITEM_8_month_consumption_Mony\t" + month8ConsumptionMoney + "\n" +
                        "ITEM_66_NEW_BASEITEM_9_month_consumption_Mony\t" + month9ConsumptionMoney + "\n" +
                        "ITEM_67_NEW_BASEITEM_10_month_consumption_Mony\t" + month10ConsumptionMoney + "\n" +
                        "ITEM_68_NEW_BASEITEM_11_month_consumption_Mony\t" + month11ConsumptionMoney + "\n" +
                        "ITEM_69_NEW_BASEITEM_12_month_consumption_Mony\t" + month12ConsumptionMoney + "\n" +
                        "ITEM_70_NEW_BASEITEM_maxim_demand_month_1\t" + maximDemandMonth1 + "\n" +
                        "ITEM_71_NEW_BASEITEM_maxim_demand_month_2\t" + maximDemandMonth2 + "\n" +
                        "ITEM_72_NEW_BASEITEM_maxim_demand_month_3\t" + maximDemandMonth3 + "\n" +
                        "ITEM_73_NEW_BASEITEM_maxim_demand_month_4\t" + maximDemandMonth4 + "\n" +
                        "ITEM_74_NEW_BASEITEM_maxim_demand_month_5\t" + maximDemandMonth5 + "\n" +
                        "ITEM_75_NEW_BASEITEM_maxim_demand_month_6\t" + maximDemandMonth6 + "\n" +
                        "ITEM_76_NEW_BASEITEM_maxim_demand_month_7\t" + maximDemandMonth7 + "\n" +
                        "ITEM_77_NEW_BASEITEM_maxim_demand_month_8\t" + maximDemandMonth8 + "\n" +
                        "ITEM_78_NEW_BASEITEM_maxim_demand_month_9\t" + maximDemandMonth9 + "\n" +
                        "ITEM_79_NEW_BASEITEM_maxim_demand_month_10\t" + maximDemandMonth10 + "\n" +
                        "ITEM_80_NEW_BASEITEM_maxim_demand_month_11\t" + maximDemandMonth11 + "\n" +
                        "ITEM_81_NEW_BASEITEM_maxim_demand_month_12\t" + maximDemandMonth12 + "\n" +
                        "ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1\t" + maximDemandMonth1Date + "\n" +
                        "ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2\t" + maximDemandMonth2Date + "\n" +
                        "ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3\t" + maximDemandMonth3Date + "\n" +
                        "ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4\t" + maximDemandMonth4Date + "\n" +
                        "ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5\t" + maximDemandMonth5Date + "\n" +
                        "ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6\t" + maximDemandMonth6Date + "\n" +
                        "ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7\t" + maximDemandMonth7Date + "\n" +
                        "ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8\t" + maximDemandMonth8Date + "\n" +
                        "ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9\t" + maximDemandMonth9Date + "\n" +
                        "ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10\t" + maximDemandMonth10Date + "\n" +
                        "ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11\t" + maximDemandMonth11Date + "\n" +
                        "ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12\t" + maximDemandMonth12Date + "\n" +
                        "ITEM_94_NEW_BASEITEM_kvar_consumption_month_1\t" + month1ConsumptionKvar + "\n" +
                        "ITEM_95_NEW_BASEITEM_kvar_consumption_month_2\t" + month2ConsumptionKvar + "\n" +
                        "ITEM_96_NEW_BASEITEM_kvar_consumption_month_3\t" + month3ConsumptionKvar + "\n" +
                        "ITEM_97_NEW_BASEITEM_kvar_consumption_month_4\t" + month4ConsumptionKvar + "\n" +
                        "ITEM_98_NEW_BASEITEM_kvar_consumption_month_5\t" + month5ConsumptionKvar + "\n" +
                        "ITEM_99_NEW_BASEITEM_kvar_consumption_month_6\t" + month6ConsumptionKvar + "\n" +
                        "ITEM_100_NEW_BASEITEM_kvar_consumption_month_7\t" + month7ConsumptionKvar + "\n" +
                        "ITEM_101_NEW_BASEITEM_kvar_consumption_month_8\t" + month8ConsumptionKvar + "\n" +
                        "ITEM_102_NEW_BASEITEM_kvar_consumption_month_9\t" + month9ConsumptionKvar + "\n" +
                        "ITEM_103_NEW_BASEITEM_kvar_consumption_month_10\t" + month10ConsumptionKvar + "\n" +
                        "ITEM_104_NEW_BASEITEM_kvar_consumption_month_11\t" + month11ConsumptionKvar + "\n" +
                        "ITEM_105_NEW_BASEITEM_kvar_consumption_month_12\t" + month12ConsumptionKvar + "\n";
    }
}
