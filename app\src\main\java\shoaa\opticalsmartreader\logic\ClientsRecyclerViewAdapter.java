package shoaa.opticalsmartreader.logic;

import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.MiniDatabaseMeterData;
import shoaa.opticalsmartreader.ui.MainActivity;

public class ClientsRecyclerViewAdapter extends RecyclerView.Adapter<ClientsRecyclerViewAdapter.ViewHolder> {

    private final List<Client> mData;
    private final LayoutInflater mInflater;
    private final ItemClickListener mClickListener;
    private final MainActivity mainActivity;

    public static String[] splitMobileAndDate(String data) {
        String mobile = null;
        String date = null;

        if (data != null && !data.isEmpty()) {
            // Check if the string contains an underscore
            if (data.contains("_")) {
                // Split the data by underscore
                String[] parts = data.split("_", -1); // -1 to include trailing empty strings

                if (parts.length > 0 && !parts[0].isEmpty()) {
                    mobile = parts[0];
                }

                if (parts.length > 1 && !parts[1].isEmpty()) {
                    date = parts[1];
                }

            } else {
                // If no underscore, assume the entire string is the mobile number
                mobile = data;
            }
        }

        return new String[]{mobile, date};
    }

    // data is passed into the constructor
    public ClientsRecyclerViewAdapter(MainActivity mainActivity, List<Client> data, ItemClickListener itemClickListener) {
        this.mInflater = LayoutInflater.from(mainActivity);
        this.mData = data;
        this.mainActivity = mainActivity;
        this.mClickListener = itemClickListener;
    }

    // inflates the row layout from xml when needed
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = mInflater.inflate(R.layout.recyclerview_row, parent, false);
        float radius = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 15f, mainActivity.getResources().getDisplayMetrics());
        ((CardView) view).setRadius(radius);
        ((CardView) view).setCardBackgroundColor(mainActivity.getColor(R.color.white));
        return new ViewHolder(view);
    }

    // binds the data to the TextView in each row
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (mData != null && mData.size() > 0 && position <= (mData.size() - 1)) {
            Client client = mData.get(position);
            if (client != null) {
                holder.tvIndex.setText(String.valueOf(position));
                holder.tvName.setText(client.getName());
                holder.tvAddress.setText(client.getAddress());

                String[] mobileAndLastChargeDate = splitMobileAndDate(client.getCustomerMobile());
                String mobile = mobileAndLastChargeDate[0];
                String lastChargeDate = mobileAndLastChargeDate[1];

                Log.d("CUSTOM_MBOBI", "onBindViewHolder: " + client.getCustomerMobile());

                holder.tvRef.setText(client.getReference());
                holder.tvActivity.setText(client.getActivityName());
                holder.tvDesc.setText(client.getPlaceName());
                holder.tvMeterNumber.setText(String.valueOf(client.getMeterID()));
                holder.tvCustomerNumber.setText(client.getCustomerID());

                if (mobile == null || mobile.isEmpty())
                    holder.tvMobile.setText("0");
                else
                    holder.tvMobile.setText(client.getCustomerMobile());

                if (lastChargeDate == null || lastChargeDate.isEmpty())
                    holder.tvLastChargeDate.setText("-");
                else
                    holder.tvLastChargeDate.setText(lastChargeDate);

                if (client.getCustomerID() == null || client.getCustomerID().isEmpty())
                    holder.tvCustomerNumber.setText("0");

                if (client.getReadableKTCodeSHoaa() == null || client.getReadableKTCodeSHoaa().isEmpty())
                    holder.webStateLayout.setVisibility(View.GONE);
                else {
                    holder.webStateLayout.setVisibility(View.VISIBLE);
                    String readingState = "";
                    if (client.getReadingCount() > 0) {
                        switch (Integer.parseInt(client.getReadableKTCodeSHoaa())) {
                            case 0:
                                readingState = "مقروء";
                                break;
                            case 1:
                                readingState = "مغلق";
                                break;
                            case 2:
                                readingState = "معطل";
                                break;
                            case 3:
                                readingState = "كسر زجاج";
                                break;
                            case 4:
                                readingState = "سرقة تيار";
                                break;
                            case 5:
                                readingState = "مرفوع";
                                break;
                            case 6:
                                readingState = "غير موجود على الطبيعة";
                                break;
                            case 7:
                                readingState = "ساقط ليست";
                                break;
                            case 8:
                                readingState = "مصيف";
                                break;
                            case 9:
                                readingState = "فصل تيار - غير مستعمل";
                                break;
                            case 10:
                            case 11:
                                readingState = "ممتنع";
                                break;
                            case 12:
                                readingState = "مخالفة شروط تعاقد";
                                break;
                            case 13:
                                readingState = "هدم وبناء";
                                break;
                        }
                    } else {
                        holder.webStateLayout.setVisibility(View.GONE);
                    }
                    new Thread(() -> {
                        mainActivity.runOnUiThread(() -> {
                            if (client.ReadingCount <= 0) {
                                holder.view.setBackgroundTintList(mainActivity.getResources().getColorStateList(R.color.card_white, mainActivity.getTheme()));
                            } else {
                                if (client.series == 1002) {
                                    holder.view.setBackgroundTintList(mainActivity.getResources().getColorStateList(R.color.card_purple, mainActivity.getTheme()));
                                } else {
                                    holder.view.setBackgroundTintList(mainActivity.getResources().getColorStateList(R.color.card_yellow, mainActivity.getTheme()));
                                }
                            }
                        });
                        MiniDatabaseMeterData databaseMeterData = Utils.appDatabase.metersDataDao().findByMini(client.MeterID, client.FactoryCode);
                        mainActivity.runOnUiThread(() -> {
                            if (databaseMeterData != null) {
                                if (databaseMeterData.READING_DATA_FLAG == 1) {
                                    holder.view.setBackgroundTintList(mainActivity.getResources().getColorStateList(R.color.card_blue, mainActivity.getTheme()));
                                } else {
                                    holder.view.setBackgroundTintList(mainActivity.getResources().getColorStateList(R.color.card_red, mainActivity.getTheme()));
                                }
                            }
                        });
                    }).start();
                    holder.tvReadingCode.setText(readingState);
                }
            }
        }
    }

    // total number of rows
    @Override
    public int getItemCount() {
        return mData.size();
    }

    // convenience method for getting data at click position
    public Client getItem(int index) {
        return mData.get(index);
    }


    // parent activity will implement this method to respond to click events
    public interface ItemClickListener {
        void onItemClick(View view, int position);
    }

    // stores and recycles views as they are scrolled off screen
    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView tvIndex;
        TextView tvName;
        TextView tvAddress;
        TextView tvMobile;
        TextView tvLastChargeDate;
        TextView tvRef;
        TextView tvActivity;
        TextView tvDesc;
        TextView tvMeterNumber;
        TextView tvCustomerNumber;
        TextView tvReadingCode;
        LinearLayout webStateLayout;
        View view;

        ViewHolder(View itemView) {
            super(itemView);
            tvIndex = itemView.findViewById(R.id.tvIndex);
            tvName = itemView.findViewById(R.id.tvName);
            tvAddress = itemView.findViewById(R.id.tvAddress);
            tvMobile = itemView.findViewById(R.id.tvMobile);
            tvLastChargeDate = itemView.findViewById(R.id.tvLastChargeDate);
            tvRef = itemView.findViewById(R.id.tvRef);
            tvActivity = itemView.findViewById(R.id.tvActivity);
            tvDesc = itemView.findViewById(R.id.tvDesc);
            tvMeterNumber = itemView.findViewById(R.id.tvMeterNumber);
            tvCustomerNumber = itemView.findViewById(R.id.tvCustomerNumber);
            tvReadingCode = itemView.findViewById(R.id.tvReadingCode);
            webStateLayout = itemView.findViewById(R.id.numbers3);
            view = itemView;
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View view) {
            if (mClickListener != null)
                mClickListener.onItemClick(view, getBindingAdapterPosition());
        }
    }
}