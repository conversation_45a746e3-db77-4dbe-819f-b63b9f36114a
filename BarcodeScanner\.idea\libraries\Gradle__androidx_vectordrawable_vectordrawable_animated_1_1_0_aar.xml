<component name="libraryTable">
  <library name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c8459c04f87b070913fa66a740784ccf/transformed/vectordrawable-animated-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f3a7b567952cd8d02a3c04db68384f2d/transformed/vectordrawable-animated-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/837273728ee0cc52186b190f3a269ebc/transformed/vectordrawable-animated-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/45d7443a08a341af88e9a582651e0029/transformed/vectordrawable-animated-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/31b4191c62e43a97a65fff508f42f7d3/transformed/vectordrawable-animated-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/45d7443a08a341af88e9a582651e0029/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/45d7443a08a341af88e9a582651e0029/transformed/vectordrawable-animated-1.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.vectordrawable/vectordrawable-animated/1.1.0/871a7705cd03bc246947638c712cdd11378233ff/vectordrawable-animated-1.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>