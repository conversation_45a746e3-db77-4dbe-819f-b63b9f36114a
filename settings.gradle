pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}
rootProject.name = "Optical Smart Reader"
include ':app'
include ':ConnectionManager'
include ':SmartMeterReader'
include ':BarcodeScanner'
include ':ElectrometerParser'
include ':Common'
