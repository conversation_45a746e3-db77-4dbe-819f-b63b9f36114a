pluginManagement {
    repositories {
        gradlePluginPortal()
        google() {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        maven {
            url 'https://jitpack.io'
            content {
                includeGroupByRegex("com\\.github.*")
            }
        }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google() {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        maven {
            url 'https://jitpack.io'
            content {
                includeGroupByRegex("com\\.github.*")
            }
        }
    }
}
rootProject.name = "Optical Smart Reader"
include ':app'
include ':ConnectionManager'
include ':SmartMeterReader'
include ':BarcodeScanner'
include ':ElectrometerParser'
include ':Common'
