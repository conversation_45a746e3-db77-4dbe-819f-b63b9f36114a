#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32784 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=1248, tid=21524
#
# JRE version: Java(TM) SE Runtime Environment (17.0.7+8) (build 17.0.7+8-LTS-224)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\agents\gradle-instrumentation-agent-8.4.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.4

Host: Intel(R) Core(TM) i5-10200H CPU @ 2.40GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
Time: Mon May 13 12:29:45 2024 Egypt Daylight Time elapsed time: 28.340907 seconds (0d 0h 0m 28s)

---------------  T H R E A D  ---------------

Current thread (0x000001ab72b4d4a0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=21524, stack(0x0000006a04f00000,0x0000006a05000000)]


Current CompileTask:
C2:  28341 4634       4       groovy.lang.MetaClassImpl$1MOPIter::methodNameAction (325 bytes)

Stack: [0x0000006a04f00000,0x0000006a05000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x676a4a]
V  [jvm.dll+0x7d74f4]
V  [jvm.dll+0x7d8c9e]
V  [jvm.dll+0x7d9303]
V  [jvm.dll+0x2452c5]
V  [jvm.dll+0xaaa2b]
V  [jvm.dll+0xaafcc]
V  [jvm.dll+0xaab44]
V  [jvm.dll+0x69a17b]
V  [jvm.dll+0x218ff0]
V  [jvm.dll+0x216df9]
V  [jvm.dll+0x1a35e0]
V  [jvm.dll+0x226a6e]
V  [jvm.dll+0x224e15]
V  [jvm.dll+0x78d05c]
V  [jvm.dll+0x78759a]
V  [jvm.dll+0x675875]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa48]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ab735ee9c0, length=42, elements={
0x000001ab4fd1dcf0, 0x000001ab72b2d7d0, 0x000001ab72b2e640, 0x000001ab72b462e0,
0x000001ab72b46e90, 0x000001ab72b49950, 0x000001ab72b4a300, 0x000001ab72b4d4a0,
0x000001ab72b4e350, 0x000001ab73502090, 0x000001ab7359eb30, 0x000001ab736946b0,
0x000001ab757595a0, 0x000001ab750daaa0, 0x000001ab74e991b0, 0x000001ab74e9a690,
0x000001ab7551f930, 0x000001ab74fccd00, 0x000001ab75769210, 0x000001ab75454470,
0x000001ab75454940, 0x000001ab75453fa0, 0x000001ab75454e10, 0x000001ab75453130,
0x000001ab75452790, 0x000001ab75452c60, 0x000001ab754557b0, 0x000001ab75453600,
0x000001ab754522c0, 0x000001ab75453ad0, 0x000001ab75455c80, 0x000001ab759edbf0,
0x000001ab759ed250, 0x000001ab759eef30, 0x000001ab759ef400, 0x000001ab759ee590,
0x000001ab759eea60, 0x000001ab759f2420, 0x000001ab759ef8d0, 0x000001ab759f28f0,
0x000001ab759f0c10, 0x000001ab759efda0
}

Java Threads: ( => current thread )
  0x000001ab4fd1dcf0 JavaThread "main" [_thread_blocked, id=5100, stack(0x0000006a04100000,0x0000006a04200000)]
  0x000001ab72b2d7d0 JavaThread "Reference Handler" daemon [_thread_blocked, id=32500, stack(0x0000006a04900000,0x0000006a04a00000)]
  0x000001ab72b2e640 JavaThread "Finalizer" daemon [_thread_blocked, id=17768, stack(0x0000006a04a00000,0x0000006a04b00000)]
  0x000001ab72b462e0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=32888, stack(0x0000006a04b00000,0x0000006a04c00000)]
  0x000001ab72b46e90 JavaThread "Attach Listener" daemon [_thread_blocked, id=12964, stack(0x0000006a04c00000,0x0000006a04d00000)]
  0x000001ab72b49950 JavaThread "Service Thread" daemon [_thread_blocked, id=17480, stack(0x0000006a04d00000,0x0000006a04e00000)]
  0x000001ab72b4a300 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=25448, stack(0x0000006a04e00000,0x0000006a04f00000)]
=>0x000001ab72b4d4a0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=21524, stack(0x0000006a04f00000,0x0000006a05000000)]
  0x000001ab72b4e350 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=28336, stack(0x0000006a05000000,0x0000006a05100000)]
  0x000001ab73502090 JavaThread "Sweeper thread" daemon [_thread_blocked, id=22024, stack(0x0000006a05100000,0x0000006a05200000)]
  0x000001ab7359eb30 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=34688, stack(0x0000006a05200000,0x0000006a05300000)]
  0x000001ab736946b0 JavaThread "Notification Thread" daemon [_thread_blocked, id=22380, stack(0x0000006a05300000,0x0000006a05400000)]
  0x000001ab757595a0 JavaThread "Daemon health stats" [_thread_blocked, id=35372, stack(0x0000006a05a00000,0x0000006a05b00000)]
  0x000001ab750daaa0 JavaThread "Incoming local TCP Connector on port 59176" [_thread_in_native, id=35556, stack(0x0000006a05c00000,0x0000006a05d00000)]
  0x000001ab74e991b0 JavaThread "Daemon periodic checks" [_thread_blocked, id=35572, stack(0x0000006a05d00000,0x0000006a05e00000)]
  0x000001ab74e9a690 JavaThread "Daemon" [_thread_blocked, id=35612, stack(0x0000006a05e00000,0x0000006a05f00000)]
  0x000001ab7551f930 JavaThread "Handler for socket connection from /127.0.0.1:59176 to /127.0.0.1:59177" [_thread_in_native, id=35616, stack(0x0000006a05f00000,0x0000006a06000000)]
  0x000001ab74fccd00 JavaThread "Cancel handler" [_thread_blocked, id=35632, stack(0x0000006a06000000,0x0000006a06100000)]
  0x000001ab75769210 JavaThread "Daemon worker" [_thread_in_native, id=35636, stack(0x0000006a06100000,0x0000006a06200000)]
  0x000001ab75454470 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:59176 to /127.0.0.1:59177" [_thread_blocked, id=35816, stack(0x0000006a06200000,0x0000006a06300000)]
  0x000001ab75454940 JavaThread "Stdin handler" [_thread_blocked, id=35820, stack(0x0000006a06300000,0x0000006a06400000)]
  0x000001ab75453fa0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=35824, stack(0x0000006a06400000,0x0000006a06500000)]
  0x000001ab75454e10 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=33328, stack(0x0000006a06500000,0x0000006a06600000)]
  0x000001ab75453130 JavaThread "File lock request listener" [_thread_in_native, id=2800, stack(0x0000006a06600000,0x0000006a06700000)]
  0x000001ab75452790 JavaThread "File lock release action executor" [_thread_blocked, id=14296, stack(0x0000006a06700000,0x0000006a06800000)]
  0x000001ab75452c60 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.4\fileHashes)" [_thread_blocked, id=35320, stack(0x0000006a06800000,0x0000006a06900000)]
  0x000001ab754557b0 JavaThread "File watcher server" daemon [_thread_in_native, id=32736, stack(0x0000006a06900000,0x0000006a06a00000)]
  0x000001ab75453600 JavaThread "File watcher consumer" daemon [_thread_blocked, id=8936, stack(0x0000006a06a00000,0x0000006a06b00000)]
  0x000001ab754522c0 JavaThread "jar transforms" [_thread_blocked, id=6612, stack(0x0000006a06c00000,0x0000006a06d00000)]
  0x000001ab75453ad0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=31528, stack(0x0000006a06d00000,0x0000006a06e00000)]
  0x000001ab75455c80 JavaThread "jar transforms Thread 3" [_thread_blocked, id=2720, stack(0x0000006a06e00000,0x0000006a06f00000)]
  0x000001ab759edbf0 JavaThread "jar transforms Thread 4" [_thread_blocked, id=11184, stack(0x0000006a06f00000,0x0000006a07000000)]
  0x000001ab759ed250 JavaThread "jar transforms Thread 5" [_thread_blocked, id=31256, stack(0x0000006a07000000,0x0000006a07100000)]
  0x000001ab759eef30 JavaThread "jar transforms Thread 6" [_thread_blocked, id=20260, stack(0x0000006a07100000,0x0000006a07200000)]
  0x000001ab759ef400 JavaThread "jar transforms Thread 7" [_thread_blocked, id=34028, stack(0x0000006a07200000,0x0000006a07300000)]
  0x000001ab759ee590 JavaThread "jar transforms Thread 8" [_thread_blocked, id=8252, stack(0x0000006a07300000,0x0000006a07400000)]
  0x000001ab759eea60 JavaThread "Cache worker for checksums cache (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\8.4\checksums)" [_thread_blocked, id=19412, stack(0x0000006a07600000,0x0000006a07700000)]
  0x000001ab759f2420 JavaThread "Cache worker for file hash cache (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\8.4\fileHashes)" [_thread_blocked, id=30084, stack(0x0000006a07700000,0x0000006a07800000)]
  0x000001ab759ef8d0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.4\md-supplier)" [_thread_blocked, id=35232, stack(0x0000006a07800000,0x0000006a07900000)]
  0x000001ab759f28f0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.4\fileContent)" [_thread_blocked, id=17308, stack(0x0000006a07900000,0x0000006a07a00000)]
  0x000001ab759f0c10 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.4\md-rule)" [_thread_blocked, id=35408, stack(0x0000006a07a00000,0x0000006a07b00000)]
  0x000001ab759efda0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.4\executionHistory)" [_thread_blocked, id=35420, stack(0x0000006a07b00000,0x0000006a07c00000)]

Other Threads:
  0x000001ab72b25880 VMThread "VM Thread" [stack: 0x0000006a04800000,0x0000006a04900000] [id=31540]
  0x000001ab73694b80 WatcherThread [stack: 0x0000006a05400000,0x0000006a05500000] [id=5248]
  0x000001ab4fd8c740 GCTaskThread "GC Thread#0" [stack: 0x0000006a04300000,0x0000006a04400000] [id=5780]
  0x000001ab7376c680 GCTaskThread "GC Thread#1" [stack: 0x0000006a05500000,0x0000006a05600000] [id=35068]
  0x000001ab7376c930 GCTaskThread "GC Thread#2" [stack: 0x0000006a05600000,0x0000006a05700000] [id=35072]
  0x000001ab7376cbe0 GCTaskThread "GC Thread#3" [stack: 0x0000006a05700000,0x0000006a05800000] [id=35076]
  0x000001ab7376ce90 GCTaskThread "GC Thread#4" [stack: 0x0000006a05800000,0x0000006a05900000] [id=35080]
  0x000001ab7376d140 GCTaskThread "GC Thread#5" [stack: 0x0000006a05900000,0x0000006a05a00000] [id=35084]
  0x000001ab74e07c60 GCTaskThread "GC Thread#6" [stack: 0x0000006a06b00000,0x0000006a06c00000] [id=35588]
  0x000001ab742aa0f0 GCTaskThread "GC Thread#7" [stack: 0x0000006a07400000,0x0000006a07500000] [id=35836]
  0x000001ab4dc0df90 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000006a04400000,0x0000006a04500000] [id=5236]
  0x000001ab4dc0e9a0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000006a04500000,0x0000006a04600000] [id=27584]
  0x000001ab742a43c0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000006a07500000,0x0000006a07600000] [id=35804]
  0x000001ab729e34f0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000006a04600000,0x0000006a04700000] [id=6108]
  0x000001ab76c27780 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000006a07c00000,0x0000006a07d00000] [id=31064]
  0x000001ab729e3f10 ConcurrentGCThread "G1 Service" [stack: 0x0000006a04700000,0x0000006a04800000] [id=30204]

Threads with active compile tasks:
C2 CompilerThread0    28383 4634       4       groovy.lang.MetaClassImpl$1MOPIter::methodNameAction (325 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bd0000-0x0000000800bd0000), size 12386304, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16159M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 137216K, used 100748K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 28 young (57344K), 3 survivors (6144K)
 Metaspace       used 40035K, committed 40576K, reserved 1089536K
  class space    used 5451K, committed 5696K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%|HS|  |TAMS 0x0000000700200000, 0x0000000700000000| Complete 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%|HC|  |TAMS 0x0000000700400000, 0x0000000700200000| Complete 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%| O|  |TAMS 0x0000000700600000, 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700800000, 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700a00000, 0x0000000700c00000|  0%| F|  |TAMS 0x0000000700a00000, 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%|HS|  |TAMS 0x0000000700e00000, 0x0000000700c00000| Complete 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%|HS|  |TAMS 0x0000000701000000, 0x0000000700e00000| Complete 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%|HC|  |TAMS 0x0000000701200000, 0x0000000701000000| Complete 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%|HS|  |TAMS 0x0000000701400000, 0x0000000701200000| Complete 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%|HC|  |TAMS 0x0000000701600000, 0x0000000701400000| Complete 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%|HS|  |TAMS 0x0000000701800000, 0x0000000701600000| Complete 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%|HC|  |TAMS 0x0000000701a00000, 0x0000000701800000| Complete 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702cdd200, 0x0000000702e00000| 43%| O|  |TAMS 0x0000000702cdd200, 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%|HS|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Complete 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%|HC|  |TAMS 0x0000000703000000, 0x0000000703000000| Complete 
|  25|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000, 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| E|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Complete 
|  40|0x0000000705000000, 0x0000000705185f40, 0x0000000705200000| 76%| S|CS|TAMS 0x0000000705000000, 0x0000000705000000| Complete 
|  41|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| S|CS|TAMS 0x0000000705200000, 0x0000000705200000| Complete 
|  42|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| S|CS|TAMS 0x0000000705400000, 0x0000000705400000| Complete 
|  43|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| E|CS|TAMS 0x0000000705600000, 0x0000000705600000| Complete 
|  44|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| E|CS|TAMS 0x0000000705800000, 0x0000000705800000| Complete 
|  45|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| E|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Complete 
|  46|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| E|CS|TAMS 0x0000000705c00000, 0x0000000705c00000| Complete 
|  47|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| E|CS|TAMS 0x0000000705e00000, 0x0000000705e00000| Complete 
|  48|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| E|CS|TAMS 0x0000000706000000, 0x0000000706000000| Complete 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| E|CS|TAMS 0x0000000706200000, 0x0000000706200000| Complete 
|  50|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| E|CS|TAMS 0x0000000706400000, 0x0000000706400000| Complete 
|  51|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| E|CS|TAMS 0x0000000706600000, 0x0000000706600000| Complete 
|  52|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| E|CS|TAMS 0x0000000706800000, 0x0000000706800000| Complete 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| E|CS|TAMS 0x0000000706a00000, 0x0000000706a00000| Complete 
|  54|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| E|CS|TAMS 0x0000000706c00000, 0x0000000706c00000| Complete 
|  55|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| E|CS|TAMS 0x0000000706e00000, 0x0000000706e00000| Complete 
|  56|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| E|CS|TAMS 0x0000000707000000, 0x0000000707000000| Complete 
|  57|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| E|CS|TAMS 0x0000000707200000, 0x0000000707200000| Complete 
|  58|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| E|CS|TAMS 0x0000000707400000, 0x0000000707400000| Complete 
|  59|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| E|CS|TAMS 0x0000000707600000, 0x0000000707600000| Complete 
|  60|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| E|CS|TAMS 0x0000000707800000, 0x0000000707800000| Complete 
|  61|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| E|CS|TAMS 0x0000000707a00000, 0x0000000707a00000| Complete 
|  62|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| E|CS|TAMS 0x0000000707c00000, 0x0000000707c00000| Complete 
|  63|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| E|CS|TAMS 0x0000000707e00000, 0x0000000707e00000| Complete 
|  64|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| E|CS|TAMS 0x0000000708000000, 0x0000000708000000| Complete 
| 125|0x000000070fa00000, 0x000000070fc00000, 0x000000070fc00000|100%| E|CS|TAMS 0x000000070fa00000, 0x000000070fa00000| Complete 
| 126|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| E|CS|TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 

Card table byte_map: [0x000001ab670c0000,0x000001ab678c0000] _byte_map_base: 0x000001ab638c0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001ab4fd8cd50, (CMBitMap*) 0x000001ab4fd8cd90
 Prev Bits: [0x000001ab680c0000, 0x000001ab6c0c0000)
 Next Bits: [0x000001ab6c0c0000, 0x000001ab700c0000)

Polling page: 0x000001ab4dc40000

Metaspace:

Usage:
  Non-class:     33.78 MB used.
      Class:      5.33 MB used.
       Both:     39.11 MB used.

Virtual space:
  Non-class space:       40.00 MB reserved,      34.06 MB ( 85%) committed,  5 nodes.
      Class space:        1.00 GB reserved,       5.56 MB ( <1%) committed,  1 nodes.
             Both:        1.04 GB reserved,      39.62 MB (  4%) committed. 

Chunk freelists:
   Non-Class:  1.05 MB
       Class:  2.33 MB
        Both:  3.38 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.75 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 456.
num_arena_deaths: 0.
num_vsnodes_births: 6.
num_vsnodes_deaths: 0.
num_space_committed: 634.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1949.
num_chunk_merges: 6.
num_chunk_splits: 1305.
num_chunks_enlarged: 904.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2786Kb max_used=2786Kb free=117213Kb
 bounds [0x000001ab5f060000, 0x000001ab5f320000, 0x000001ab66590000]
CodeHeap 'profiled nmethods': size=120000Kb used=9880Kb max_used=9880Kb free=110120Kb
 bounds [0x000001ab57590000, 0x000001ab57f40000, 0x000001ab5eac0000]
CodeHeap 'non-nmethods': size=5760Kb used=2307Kb max_used=2386Kb free=3452Kb
 bounds [0x000001ab5eac0000, 0x000001ab5ed30000, 0x000001ab5f060000]
 total_blobs=5479 nmethods=4660 adapters=730
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 28.313 Thread 0x000001ab72b4e350 nmethod 4646 0x000001ab57f21910 code [0x000001ab57f21b80, 0x000001ab57f22538]
Event: 28.315 Thread 0x000001ab72b4e350 4647       3       groovy.lang.MetaClassImpl::getNewMetaMethods (18 bytes)
Event: 28.315 Thread 0x000001ab72b4e350 nmethod 4647 0x000001ab57f22890 code [0x000001ab57f22a40, 0x000001ab57f22c88]
Event: 28.315 Thread 0x000001ab72b4e350 4648       3       org.codehaus.groovy.reflection.CachedClass$CachedMethodComparatorWithString::compare (37 bytes)
Event: 28.316 Thread 0x000001ab72b4e350 nmethod 4648 0x000001ab57f22d90 code [0x000001ab57f22fa0, 0x000001ab57f23798]
Event: 28.317 Thread 0x000001ab72b4e350 4649       3       java.util.TreeMap::values (25 bytes)
Event: 28.317 Thread 0x000001ab72b4e350 nmethod 4649 0x000001ab57f23a10 code [0x000001ab57f23bc0, 0x000001ab57f23f58]
Event: 28.317 Thread 0x000001ab72b4e350 4650       3       groovy.lang.MetaClassImpl::addFields (41 bytes)
Event: 28.318 Thread 0x000001ab72b4e350 nmethod 4650 0x000001ab57f24090 code [0x000001ab57f24280, 0x000001ab57f249b8]
Event: 28.318 Thread 0x000001ab72b4e350 4651       3       org.codehaus.groovy.reflection.CachedClass::getFields (11 bytes)
Event: 28.318 Thread 0x000001ab72b4e350 nmethod 4651 0x000001ab57f24c90 code [0x000001ab57f24e40, 0x000001ab57f25108]
Event: 28.318 Thread 0x000001ab72b4e350 4652       3       groovy.lang.MetaClassImpl$Index::put (10 bytes)
Event: 28.318 Thread 0x000001ab72b4e350 nmethod 4652 0x000001ab57f25210 code [0x000001ab57f253c0, 0x000001ab57f255e8]
Event: 28.323 Thread 0x000001ab72b4e350 4653       1       org.codehaus.groovy.runtime.callsite.AbstractCallSite::getArray (5 bytes)
Event: 28.323 Thread 0x000001ab72b4e350 nmethod 4653 0x000001ab5f318710 code [0x000001ab5f3188a0, 0x000001ab5f318978]
Event: 28.323 Thread 0x000001ab72b4e350 4654       3       org.codehaus.groovy.util.ManagedConcurrentLinkedQueue::add (20 bytes)
Event: 28.323 Thread 0x000001ab72b4e350 nmethod 4654 0x000001ab57f25710 code [0x000001ab57f258e0, 0x000001ab57f25c78]
Event: 28.334 Thread 0x000001ab72b4e350 4655       3       com.google.common.collect.MapMakerInternalMap::putIfAbsent (29 bytes)
Event: 28.335 Thread 0x000001ab72b4e350 nmethod 4655 0x000001ab57f25e10 code [0x000001ab57f26040, 0x000001ab57f267c8]
Event: 28.335 Thread 0x000001ab72b4e350 4656   !   3       com.google.common.collect.MapMakerInternalMap$Segment::put (298 bytes)

GC Heap History (12 events):
Event: 3.167 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 26624K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 2990K, committed 3136K, reserved 1056768K
  class space    used 381K, committed 448K, reserved 1048576K
}
Event: 3.360 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 10475K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 2990K, committed 3136K, reserved 1056768K
  class space    used 381K, committed 448K, reserved 1048576K
}
Event: 5.914 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 35051K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 2 survivors (4096K)
 Metaspace       used 4030K, committed 4160K, reserved 1056768K
  class space    used 493K, committed 576K, reserved 1048576K
}
Event: 5.921 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 11350K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 4030K, committed 4160K, reserved 1056768K
  class space    used 493K, committed 576K, reserved 1048576K
}
Event: 17.082 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 154710K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 67 young (137216K), 1 survivors (2048K)
 Metaspace       used 21106K, committed 21504K, reserved 1073152K
  class space    used 3121K, committed 3328K, reserved 1048576K
}
Event: 17.210 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 34165K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 21106K, committed 21504K, reserved 1073152K
  class space    used 3121K, committed 3328K, reserved 1048576K
}
Event: 21.271 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 137216K, used 105845K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 40 young (81920K), 7 survivors (14336K)
 Metaspace       used 29677K, committed 30144K, reserved 1081344K
  class space    used 4316K, committed 4544K, reserved 1048576K
}
Event: 21.282 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 137216K, used 44104K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 29677K, committed 30144K, reserved 1081344K
  class space    used 4316K, committed 4544K, reserved 1048576K
}
Event: 21.369 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 137216K, used 44104K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 4 survivors (8192K)
 Metaspace       used 30990K, committed 31424K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 21.373 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 137216K, used 44832K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 30990K, committed 31424K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 23.414 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 137216K, used 91936K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 24 young (49152K), 1 survivors (2048K)
 Metaspace       used 35550K, committed 35968K, reserved 1081344K
  class space    used 4903K, committed 5120K, reserved 1048576K
}
Event: 23.418 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 137216K, used 49548K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 35550K, committed 35968K, reserved 1081344K
  class space    used 4903K, committed 5120K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 26.085 Thread 0x000001ab75769210 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001ab5f1b30f4 relative=0x00000000000002d4
Event: 26.085 Thread 0x000001ab75769210 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001ab5f1b30f4 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 26.085 Thread 0x000001ab75769210 DEOPT PACKING pc=0x000001ab5f1b30f4 sp=0x0000006a061f9570
Event: 26.085 Thread 0x000001ab75769210 DEOPT UNPACKING pc=0x000001ab5eb123a3 sp=0x0000006a061f9558 mode 2
Event: 26.087 Thread 0x000001ab75769210 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001ab5f1b30f4 relative=0x00000000000002d4
Event: 26.087 Thread 0x000001ab75769210 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001ab5f1b30f4 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 26.087 Thread 0x000001ab75769210 DEOPT PACKING pc=0x000001ab5f1b30f4 sp=0x0000006a061f9750
Event: 26.087 Thread 0x000001ab75769210 DEOPT UNPACKING pc=0x000001ab5eb123a3 sp=0x0000006a061f9738 mode 2
Event: 26.087 Thread 0x000001ab75769210 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000001ab5f2c2c54 relative=0x0000000000000814
Event: 26.087 Thread 0x000001ab75769210 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001ab5f2c2c54 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 26.087 Thread 0x000001ab75769210 DEOPT PACKING pc=0x000001ab5f2c2c54 sp=0x0000006a061f9e70
Event: 26.087 Thread 0x000001ab75769210 DEOPT UNPACKING pc=0x000001ab5eb123a3 sp=0x0000006a061f9e28 mode 2
Event: 27.768 Thread 0x000001ab75769210 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001ab5f1caedc relative=0x000000000000453c
Event: 27.768 Thread 0x000001ab75769210 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001ab5f1caedc method=java.lang.StringLatin1.regionMatchesCI([BI[BII)Z @ 72 c2
Event: 27.768 Thread 0x000001ab75769210 DEOPT PACKING pc=0x000001ab5f1caedc sp=0x0000006a061f95f0
Event: 27.768 Thread 0x000001ab75769210 DEOPT UNPACKING pc=0x000001ab5eb123a3 sp=0x0000006a061f93a8 mode 2
Event: 27.768 Thread 0x000001ab75769210 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001ab5f144280 relative=0x00000000000006c0
Event: 27.768 Thread 0x000001ab75769210 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001ab5f144280 method=java.lang.StringLatin1.regionMatchesCI([BI[BII)Z @ 72 c2
Event: 27.768 Thread 0x000001ab75769210 DEOPT PACKING pc=0x000001ab5f144280 sp=0x0000006a061f9470
Event: 27.768 Thread 0x000001ab75769210 DEOPT UNPACKING pc=0x000001ab5eb123a3 sp=0x0000006a061f9338 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 28.049 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000706055a20}: java/lang/StringBeanInfo> (0x0000000706055a20) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.051 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070605dfd0}: java/lang/StringCustomizer> (0x000000070605dfd0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.092 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007060e57e0}: org/gradle/internal/classloader/VisitableURLClassLoader$InstrumentingVisitableURLClassLoaderBeanInfo> (0x00000007060e57e0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.093 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007060fa920}: org/gradle/internal/classloader/VisitableURLClassLoaderBeanInfo> (0x00000007060fa920) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.093 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000706102f28}: java/net/URLClassLoaderBeanInfo> (0x0000000706102f28) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.094 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070610b580}: java/security/SecureClassLoaderBeanInfo> (0x000000070610b580) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.094 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000706113b80}: java/lang/ClassLoaderBeanInfo> (0x0000000706113b80) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.094 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070611c158}: java/lang/ClassLoaderCustomizer> (0x000000070611c158) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.096 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000706131ca0}: java/security/SecureClassLoaderCustomizer> (0x0000000706131ca0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.097 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070613c308}: java/net/URLClassLoaderCustomizer> (0x000000070613c308) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.098 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007061572e0}: org/gradle/internal/classloader/VisitableURLClassLoaderCustomizer> (0x00000007061572e0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.100 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000706173640}: org/gradle/internal/classloader/VisitableURLClassLoader$InstrumentingVisitableURLClassLoaderCustomizer> (0x0000000706173640) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.106 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007061ab0e8}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilder$ForGradle44BeanInfo> (0x00000007061ab0e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.106 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007061b6998}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilder$ForGradle44Customizer> (0x00000007061b6998) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.300 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705613908}: org/jetbrains/plugins/gradle/tooling/builder/WarModelBuilderImplBeanInfo> (0x0000000705613908) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.301 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705620c90}: org/jetbrains/plugins/gradle/tooling/AbstractModelBuilderServiceBeanInfo> (0x0000000705620c90) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.301 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070562c9b0}: org/jetbrains/plugins/gradle/tooling/AbstractModelBuilderServiceCustomizer> (0x000000070562c9b0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.302 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x000000070563afa0}: org/jetbrains/plugins/gradle/tooling/builder/WarModelBuilderImplCustomizer> (0x000000070563afa0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.316 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007056b8660}: org/jetbrains/plugins/gradle/tooling/builder/EarModelBuilderImplBeanInfo> (0x00000007056b8660) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.316 Thread 0x000001ab75769210 Exception <a 'java/lang/ClassNotFoundException'{0x00000007056c3b88}: org/jetbrains/plugins/gradle/tooling/builder/EarModelBuilderImplCustomizer> (0x00000007056c3b88) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 23.188 Executing VM operation: HandshakeAllThreads
Event: 23.189 Executing VM operation: HandshakeAllThreads done
Event: 23.200 Executing VM operation: HandshakeAllThreads
Event: 23.200 Executing VM operation: HandshakeAllThreads done
Event: 23.414 Executing VM operation: CollectForMetadataAllocation
Event: 23.420 Executing VM operation: CollectForMetadataAllocation done
Event: 23.440 Executing VM operation: G1Concurrent
Event: 23.443 Executing VM operation: G1Concurrent done
Event: 23.450 Executing VM operation: G1Concurrent
Event: 23.450 Executing VM operation: G1Concurrent done
Event: 23.618 Executing VM operation: HandshakeAllThreads
Event: 23.618 Executing VM operation: HandshakeAllThreads done
Event: 24.625 Executing VM operation: Cleanup
Event: 24.626 Executing VM operation: Cleanup done
Event: 25.639 Executing VM operation: Cleanup
Event: 25.640 Executing VM operation: Cleanup done
Event: 27.640 Executing VM operation: Cleanup
Event: 27.640 Executing VM operation: Cleanup done
Event: 27.968 Executing VM operation: HandshakeAllThreads
Event: 27.969 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 28.093 loading class java/security/SecureClassLoaderBeanInfo
Event: 28.093 loading class java/security/SecureClassLoaderBeanInfo done
Event: 28.094 loading class java/security/SecureClassLoaderBeanInfo
Event: 28.094 loading class java/security/SecureClassLoaderBeanInfo done
Event: 28.094 loading class java/lang/ClassLoaderBeanInfo
Event: 28.094 loading class java/lang/ClassLoaderBeanInfo done
Event: 28.094 loading class java/lang/ClassLoaderBeanInfo
Event: 28.094 loading class java/lang/ClassLoaderBeanInfo done
Event: 28.094 loading class java/lang/ClassLoaderCustomizer
Event: 28.094 loading class java/lang/ClassLoaderCustomizer done
Event: 28.094 loading class java/lang/ClassLoaderCustomizer
Event: 28.094 loading class java/lang/ClassLoaderCustomizer done
Event: 28.095 loading class java/security/SecureClassLoaderCustomizer
Event: 28.095 loading class java/security/SecureClassLoaderCustomizer done
Event: 28.096 loading class java/security/SecureClassLoaderCustomizer
Event: 28.096 loading class java/security/SecureClassLoaderCustomizer done
Event: 28.097 loading class java/net/URLClassLoaderCustomizer
Event: 28.097 loading class java/net/URLClassLoaderCustomizer done
Event: 28.097 loading class java/net/URLClassLoaderCustomizer
Event: 28.097 loading class java/net/URLClassLoaderCustomizer done


Dynamic libraries:
0x00007ff7693d0000 - 0x00007ff7693e0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff99a770000 - 0x00007ff99a986000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff952e20000 - 0x00007ff952e39000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ff999bb0000 - 0x00007ff999c74000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff997ff0000 - 0x00007ff998397000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff997b00000 - 0x00007ff997c11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff967000000 - 0x00007ff967019000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff9984c0000 - 0x00007ff998572000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff999b00000 - 0x00007ff999ba7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff998630000 - 0x00007ff9986d8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff998470000 - 0x00007ff998498000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff99a330000 - 0x00007ff99a445000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff999550000 - 0x00007ff9996fe000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9983a0000 - 0x00007ff9983c6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff999d00000 - 0x00007ff999d29000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff997c20000 - 0x00007ff997d39000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9983d0000 - 0x00007ff99846a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff97dec0000 - 0x00007ff97dedb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff95b3f0000 - 0x00007ff95b683000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ff98bea0000 - 0x00007ff98beaa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff998980000 - 0x00007ff9989b1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff97ddb0000 - 0x00007ff97ddbc000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff94dc40000 - 0x00007ff94dcce000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff92fdf0000 - 0x00007ff9309cd000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff99a5b0000 - 0x00007ff99a5b8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff980390000 - 0x00007ff980399000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff999320000 - 0x00007ff999391000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff98c7e0000 - 0x00007ff98c814000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff996bc0000 - 0x00007ff996bd8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff97d2a0000 - 0x00007ff97d2aa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff994f40000 - 0x00007ff995173000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff999770000 - 0x00007ff999af8000 	C:\WINDOWS\System32\combase.dll
0x00007ff9988a0000 - 0x00007ff998977000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9891d0000 - 0x00007ff989202000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff997f70000 - 0x00007ff997fe9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff98d2b0000 - 0x00007ff98d2be000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ff95fcc0000 - 0x00007ff95fce5000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ff92fd10000 - 0x00007ff92fde7000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ff998a70000 - 0x00007ff9992cc000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff995a00000 - 0x00007ff9962f9000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9958c0000 - 0x00007ff9959fe000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff99a1b0000 - 0x00007ff99a2a3000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff99a2d0000 - 0x00007ff99a32e000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9979c0000 - 0x00007ff9979e1000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff97acc0000 - 0x00007ff97acd8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ff97c1d0000 - 0x00007ff97c1e9000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ff98df80000 - 0x00007ff98e0b6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff997030000 - 0x00007ff997099000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff97ad20000 - 0x00007ff97ad36000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ff97d290000 - 0x00007ff97d2a0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ff94a3e0000 - 0x00007ff94a407000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ff94e2c0000 - 0x00007ff94e404000 	C:\Users\<USER>\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64\native-platform-file-events.dll
0x00007ff966de0000 - 0x00007ff966dea000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ff966dd0000 - 0x00007ff966ddb000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ff997290000 - 0x00007ff9972ab000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff996b20000 - 0x00007ff996b55000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff997130000 - 0x00007ff997158000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff9972b0000 - 0x00007ff9972bc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff996550000 - 0x00007ff99657d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff998a60000 - 0x00007ff998a69000 	C:\WINDOWS\System32\NSI.dll
0x00007ff98de90000 - 0x00007ff98dea9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff98de70000 - 0x00007ff98de8f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff996580000 - 0x00007ff996679000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff966db0000 - 0x00007ff966dbd000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ff997e00000 - 0x00007ff997f67000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff997430000 - 0x00007ff99745e000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff9973f0000 - 0x00007ff997427000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff98c180000 - 0x00007ff98c188000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\agents\gradle-instrumentation-agent-8.4.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.4
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\gradle-launcher-8.4.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Python310\Scripts\;C:\Python310\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\wamp64\bin\php\php7.3.33;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\MySQL\MySQL Utilities 1.6\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\cygwin64\bin;C:\protoc-24.3-win64\bin;C:\apache-ant-1.10.14\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\src\sdks\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Program Files\JetBrains\PhpStorm 2022.1.4\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;
USERNAME=Mohamed Gamal
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
OS uptime: 5 days 2:41 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xf8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 16159M (291M free)
TotalPageFile size 55003M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 263M, peak: 274M
current process commit charge ("private bytes"): 313M, peak: 415M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224) for windows-amd64 JRE (17.0.7+8-LTS-224), built on Feb 28 2023 23:03:02 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
