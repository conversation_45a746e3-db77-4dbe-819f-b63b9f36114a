<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    android:layoutDirection="rtl"
    android:padding="10dp"
    android:textDirection="rtl">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:text="@string/lost_statistics"
        android:textColor="@color/on_primary"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/statisticsLayout"
        app:layout_constraintEnd_toEndOf="@id/guideline1"
        app:layout_constraintStart_toStartOf="@id/guideline2"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/statisticsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/btn_back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_title">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_dark"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="إجمالي الساقط ليست : "
                android:textColor="@color/on_primary"
                android:textSize="22sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvTotal_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="22sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="مقروء : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvReaded_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="غير قادر علي الوصول : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvCanNotReach_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="غير قادر علي القراءة : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvCanNotRead_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="معطل : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvError_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="كسر : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvBroken_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="سرقة تيار : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvStolen_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="غير متصل : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvNotConnected_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="مخالفة شروط تعاقد : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvViolation_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ممارسات : "
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvMomarsa_lost"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@color/on_primary"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>
    </LinearLayout>

    <Button
        android:id="@+id/btn_back"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="رجوع"
        android:textColor="@color/on_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/guideline1"
        app:layout_constraintStart_toStartOf="@id/guideline2"
        app:layout_constraintTop_toBottomOf="@id/statisticsLayout" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.8" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.2" />

</androidx.constraintlayout.widget.ConstraintLayout>