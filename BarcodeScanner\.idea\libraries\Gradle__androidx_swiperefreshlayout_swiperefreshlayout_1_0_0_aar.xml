<component name="libraryTable">
  <library name="Gradle: androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/fd77f59f969ff6a42bcc8574728346f4/transformed/swiperefreshlayout-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/63c164461d677c305ae9bb4a84f7de11/transformed/swiperefreshlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f62740e854c686450e8c7ff807dc79cc/transformed/swiperefreshlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b0d3372072e4c54fc9e001e7a1fd4aed/transformed/swiperefreshlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/fd77f59f969ff6a42bcc8574728346f4/transformed/swiperefreshlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d402e7ddbb0ee3f921bd508ead2f5d98/transformed/swiperefreshlayout-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/fd77f59f969ff6a42bcc8574728346f4/transformed/swiperefreshlayout-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/fd77f59f969ff6a42bcc8574728346f4/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.swiperefreshlayout/swiperefreshlayout/1.0.0/ab92d86c004eb1d48e45f311b02ca53d6c86b607/swiperefreshlayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>