package com.esmc.protocol.algorithm;

import com.esmc.protocol.utils.HexUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/algorithm/SHA1.class */
public class SHA1 {
    public static final byte[] digest(byte[] content) {
        try {
            MessageDigest msgDigest = MessageDigest.getInstance("SHA-1");
            msgDigest.update(content);
            return msgDigest.digest();
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public static final String digestAsString(byte[] content) {
        byte[] result = digest(content);
        if (null == result) {
            return "";
        }
        return HexUtils.toHex(result);
    }
}
