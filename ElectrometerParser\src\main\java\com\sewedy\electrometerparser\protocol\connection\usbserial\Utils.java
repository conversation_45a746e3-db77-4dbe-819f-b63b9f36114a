package com.sewedy.electrometerparser.protocol.connection.usbserial;

import android.app.Activity;
import android.os.Environment;
import android.text.format.DateFormat;

import org.apache.commons.lang3.ArrayUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;

public class Utils {
    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    public static String bytesToAscii(String hex) {
        if (hex.length() % 2 != 0) {
            return null;
        }

        StringBuilder builder = new StringBuilder();

        for (int i = 0; i < hex.length(); i = i + 2) {
            String s = hex.substring(i, i + 2);
            int n = Integer.valueOf(s, 16);
            if (n == 0) {
                break;
            }
            builder.append((char) n);
        }
        return builder.toString();
    }

    public static byte calculateCRC(byte[] arr) {
        byte xOrd = arr[1];
        for (int i = 2; i < arr.length; i++) {
            xOrd ^= arr[i];
        }
        return xOrd;
    }

    public static long hexToLong(String s) {
        return Long.parseLong(s, 16);
    }

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String bytesToString(byte[] bytes) {
        StringBuilder builder = new StringBuilder();
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            builder.append(v);
        }
        return String.valueOf(builder);
    }

    public static int convertHexToString(String hexString) {
        int dec;
        if (hexString.equals("0.0") || hexString.equals("0")) {
            return 0;
        }
        try {
            dec = Integer.parseInt(hexString, 16);
        } catch (NumberFormatException e) {
            BigInteger temp;
            temp = new BigInteger(hexString, 16);
            BigInteger subtrahend = BigInteger.ONE.shiftLeft(hexString.length() * 4);
            temp = temp.subtract(subtrahend);
            dec = Integer.parseInt(String.valueOf(temp));
        }
        return dec;

    }

    public static String hexToAscii(String hexStr) {
        StringBuilder output = new StringBuilder("");

        for (int i = 0; i < hexStr.length(); i += 2) {
            String str = hexStr.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }

        return output.toString();
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        String st;
        byte[] data = new byte[4];
        if (len % 2 == 0) {
            st = s;
        } else {
            st = "0" + s;
        }
        String[] bytes = new String[st.length() / 2];
        for (int i = 0, j = 0; i < st.length(); i += 2, j++) {
            if (i == st.length())
                break;
            bytes[j] = st.substring(i, i + 2);
        }

        int length = data.length - bytes.length;
        for (int x = 0, y = length; x < data.length; x++, y++) {
            if (x < length)
                continue;
            data[x] = Byte.parseByte(bytes[x - length], 16);

        }
        return data;
    }

    public static byte[] convertBuffArrayListToArr(ArrayList<Byte> arrayList) {
        return ArrayUtils.toPrimitive(arrayList.toArray(new Byte[arrayList.size()]));
    }

    public static short converArrtoShort(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(2);
        bb.order(byteOrder);
        bb.put(arr[index]);
        bb.put(arr[index + 1]);
        short shortVal = bb.getShort(0);
        return shortVal;
    }

    public static short converArrtoShort(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(2);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        bb.put(arr[index]);
        bb.put(arr[index + 1]);
        short shortVal = bb.getShort(0);
        return shortVal;
    }

    public static byte[] shortToByteArr(short i) {
        byte[] ret = new byte[2];
        ret[0] = (byte) (i & 0xff);
        ret[1] = (byte) ((i >> 8) & 0xff);
        return ret;
    }

    public static int converArrtoInt(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        int size = 4;
        if (arr.length < 4)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getInt(0);
    }

    public static int converArrtoInt(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.order(byteOrder);
        int size = 4;
        if (arr.length < 4)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getInt(0);
    }

    public static long converArrtoLong(byte[] arr, int index, ByteOrder byteOrder) {
        ByteBuffer bb = ByteBuffer.allocate(8);
        bb.order(byteOrder);
        int size = 8;
        if (arr.length < 8)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getLong(0);
    }

    public static long converArrtoLong(byte[] arr, int index) {
        ByteBuffer bb = ByteBuffer.allocate(8);
        bb.order(ByteOrder.LITTLE_ENDIAN);
        int size = 8;
        if (arr.length < 8)
            size = arr.length;
        for (int i = index; i < size + index; i++) {
            bb.put(arr[i]);
        }
        return bb.getLong(0);
    }

    public static byte[] intToByteArr(int v) {
        return ByteBuffer.allocate(4).putInt(v).array();
    }

    public static int[] byteArrayToIntArray(byte[] bArray) {
        int[] iArray = new int[bArray.length];
        int i = 0;
        for (byte b : bArray)
            iArray[i++] = b & 0xff;
        return iArray;
    }

    public void saveFile(String value, Activity activity) {
        try {
            String h = DateFormat.format("yyyyy/MM/dd HH:mm:ss", System.currentTimeMillis()).toString();
            String v = value + "," + h + "\n";

            String baseFolder;
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                baseFolder = activity.getExternalFilesDir(null).getAbsolutePath();
            } else {
                baseFolder = activity.getFilesDir().getAbsolutePath();
            }
            File file = new File(baseFolder + "/gasMetro_%s.txt", h);
            FileOutputStream fos = new FileOutputStream(file, true);
            fos.write(v.getBytes());
            fos.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
