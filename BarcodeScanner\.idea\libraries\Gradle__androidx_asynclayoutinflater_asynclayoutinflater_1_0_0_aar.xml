<component name="libraryTable">
  <library name="Gradle: androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6cb87b4c7ebd7740d9b26aa89a9cac1c/transformed/asynclayoutinflater-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/15c394e0c58f44a043d9341e35e6fa86/transformed/asynclayoutinflater-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e0be48be6be478da2c9ea3f27dc17116/transformed/asynclayoutinflater-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b3d8b81b8ac899af3981525d5a18965e/transformed/asynclayoutinflater-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a26bf3cabc186a4fb99e27e5b47812d3/transformed/asynclayoutinflater-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b3d8b81b8ac899af3981525d5a18965e/transformed/asynclayoutinflater-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b3d8b81b8ac899af3981525d5a18965e/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.asynclayoutinflater/asynclayoutinflater/1.0.0/ac4d50701fce5c88dcc514f58e695cd32f05134c/asynclayoutinflater-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>