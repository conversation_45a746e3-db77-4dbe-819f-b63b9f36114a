<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    android:padding="10dp"
    android:textDirection="rtl">


    <TextView
        android:id="@+id/txt_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:text="@string/data_sync"
        android:textColor="@color/on_primary"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/my_custom_background"
        app:layout_constraintBottom_toTopOf="@id/btn_sync"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_title">

        <CheckBox
            android:id="@+id/chk_selectAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="تحديد الكل"
            android:textColor="@color/on_primary"
            android:textSize="20sp"
            app:layout_constraintBottom_toTopOf="@id/list_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:id="@+id/list_item"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chk_selectAll">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <CheckBox
                    android:id="@+id/chk_sync_read"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المقروء"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_closed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <CheckBox
                    android:id="@+id/chk_sync_closed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المغلق"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_can_not_reach"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_read" />

                <CheckBox
                    android:id="@+id/chk_sync_can_not_reach"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة غير قادر علي الوصول"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_can_not_read"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_closed" />

                <CheckBox
                    android:id="@+id/chk_sync_can_not_read"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة غير قادر علي القراءة"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_error"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_can_not_reach" />

                <CheckBox
                    android:id="@+id/chk_sync_error"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المعطل"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_glass"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_can_not_read" />

                <CheckBox
                    android:id="@+id/chk_sync_glass"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة كسر الزجاج"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_stolen"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_error" />

                <CheckBox
                    android:id="@+id/chk_sync_stolen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة سرقة التيار"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_up"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_glass" />

                <CheckBox
                    android:id="@+id/chk_up"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المرفوع"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_Lost"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_stolen" />

                <CheckBox
                    android:id="@+id/chk_sync_Lost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة الساقط ليست"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_sync_notFound"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_up" />

                <CheckBox
                    android:id="@+id/chk_sync_notFound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة الغير موجود علي الطبيعة"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_beach"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_Lost" />

                <CheckBox
                    android:id="@+id/chk_beach"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المصيف"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_notConnected"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_sync_notFound" />

                <CheckBox
                    android:id="@+id/chk_notConnected"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة غير متصل"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_refused"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_beach" />

                <CheckBox
                    android:id="@+id/chk_refused"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة الممتنع"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_violation"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_notConnected" />

                <CheckBox
                    android:id="@+id/chk_violation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة المخالفات"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="@id/chk_destroy"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_refused" />

                <CheckBox
                    android:id="@+id/chk_destroy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مزامنة الهدم و البناء"
                    android:textColor="@color/on_primary"
                    android:textSize="20sp"
                    app:layout_constraintBottom_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/chk_violation" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btn_sync"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="20dp"
        android:text="@string/data_sync"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/btn_send_backup"
        app:layout_constraintEnd_toStartOf="@id/btn_send_backup"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btn_send_backup" />

    <Button
        android:id="@+id/btn_send_backup"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/send_backup_files"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_sync"
        app:layout_constraintTop_toBottomOf="@id/view" />


</androidx.constraintlayout.widget.ConstraintLayout>