package shoaa.opticalsmartreader.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.google.gson.Gson;

import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.Enums;
import shoaa.opticalsmartreader.logic.LostActivityResultObject;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;

public class LostActivity extends AppCompatActivity {
    RadioButton rdHome;
    RadioButton rdShop;
    RadioButton rdPower;
    RadioButton rdSingle;
    RadioButton rd3Phase;
    EditText etArea;
    EditText etDay;
    EditText etMain;
    EditText etSub;
    EditText etNote;
    EditText etCustomerId;
    TextView tvTitle4;
    Button btnSave;
    boolean showClientId;
    boolean showMeterType;
    LinearLayout meterTypeLayout;


    TextWatcher watcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        }

        @Override
        public void afterTextChanged(Editable editable) {

        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_lost);
        rdHome = findViewById(R.id.rdHome);
        rdShop = findViewById(R.id.rdShop);
        rdPower = findViewById(R.id.rdPower);
        rdSingle = findViewById(R.id.rdSingle);
        rd3Phase = findViewById(R.id.rd3Phase);
        etArea = findViewById(R.id.etArea);
        etDay = findViewById(R.id.etDay);
        etMain = findViewById(R.id.etMain);
        etSub = findViewById(R.id.etSub);
        etNote = findViewById(R.id.etNote);
        etCustomerId = findViewById(R.id.etCustomerId);
        tvTitle4 = findViewById(R.id.tvTitle4);
        btnSave = findViewById(R.id.btnSave);
        meterTypeLayout = findViewById(R.id.meterTypeLayout);
        showClientId = getIntent().getBooleanExtra("showClientId", false);
        showMeterType = getIntent().getBooleanExtra("showMeterType", false);
        etCustomerId.setVisibility(showClientId ? View.VISIBLE : View.GONE);
        tvTitle4.setVisibility(showClientId ? View.VISIBLE : View.GONE);
        meterTypeLayout.setVisibility(showMeterType ? View.VISIBLE : View.GONE);


        rdHome.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        rdShop.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        rdPower.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        rdPower.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        rd3Phase.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        rdSingle.setOnCheckedChangeListener((compoundButton, b) -> {
            btnSave.setEnabled((rdSingle.isChecked() || rd3Phase.isChecked() || !showMeterType)
                    && (rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etMain.getText().toString().trim().isEmpty()
                    && !etSub.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && (etCustomerId.getText().toString().length() > 2 || !showClientId));
        });
        etCustomerId.addTextChangedListener(watcher);
        etArea.addTextChangedListener(watcher);
        etDay.addTextChangedListener(watcher);
        etMain.addTextChangedListener(watcher);
        etSub.addTextChangedListener(watcher);
        btnSave.setOnClickListener(view -> {
            if (showClientId && Utils.getAsULong(etCustomerId.getText().toString()) == 0 && etNote.getText().toString().trim().isEmpty()) {
                Toast.makeText(this, "رقم المشترك مكون من اصفار برجاء كتابة السبب في الملاحظات", Toast.LENGTH_LONG).show();
                return;
            }
            new Thread(() -> {
                Intent intent = new Intent();
                LostActivityResultObject lostActivityResultObject = new LostActivityResultObject();
                Client client = new Client();
                if (rdHome.isChecked()) {
                    lostActivityResultObject.activityType = (Enums.ActivityType.HOME);
                    client.ActivityName = Enums.ActivityType.HOME.toString();
                } else if (rdShop.isChecked()) {
                    lostActivityResultObject.activityType = (Enums.ActivityType.SHOP);
                    client.ActivityName = Enums.ActivityType.SHOP.toString();
                } else if (rdPower.isChecked()) {
                    lostActivityResultObject.activityType = (Enums.ActivityType.POWER);
                    client.ActivityName = Enums.ActivityType.POWER.toString();
                }
                AppDatabase db = Room.databaseBuilder(getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                client.BrHndsa = Utils.getAsUInt(AppUser.getInstance(this).getBrHndsa());
                client.Mntka = Utils.formatStringUIntNumber(etArea.getText().toString());
                client.Main = Utils.formatStringUIntNumber(etMain.getText().toString());
                client.Day = Utils.formatStringUIntNumber(etDay.getText().toString());
                client.Fary = Utils.formatStringUIntNumber(etSub.getText().toString());
                client.CycleMonth = db.cycleDateDao().get().CycleMonth;
                client.CycleYear = db.cycleDateDao().get().CycleYear;
                client.setCustomerID(Utils.formatStringUIntNumber(etCustomerId.getText().toString()));
                client.setMeterTypeName(rd3Phase.isChecked() ? "ثلاثي" : "احادي");
                lostActivityResultObject.client = client;
                lostActivityResultObject.note = etNote.getText().toString().trim();
                intent.putExtra("data", new Gson().toJson(lostActivityResultObject));
                setResult(RESULT_OK, intent);
                runOnUiThread(this::finish);
            }).start();
        });
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        Toast.makeText(this, "يجب ادخال بيانات الساقط ليست", Toast.LENGTH_LONG).show();
    }
}