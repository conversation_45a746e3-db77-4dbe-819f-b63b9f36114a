<component name="libraryTable">
  <library name="Gradle: androidx.arch.core:core-runtime:2.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1ccaf6d425ba10d8fdc9bffc419647b0/transformed/core-runtime-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7f104a2c94f34dd34a0944db6d275686/transformed/core-runtime-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c2c39a50114a29edee6f15e3a9dfa66b/transformed/core-runtime-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8eb5a780fb465cf51f0bbb4932c8ada3/transformed/core-runtime-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8fd62b68206981d81eb792a92396fb3a/transformed/core-runtime-2.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8eb5a780fb465cf51f0bbb4932c8ada3/transformed/core-runtime-2.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/8eb5a780fb465cf51f0bbb4932c8ada3/transformed/core-runtime-2.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-runtime/2.1.0/f19886651c9946b39f83d8c184fd0e2ce9f43c16/core-runtime-2.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>