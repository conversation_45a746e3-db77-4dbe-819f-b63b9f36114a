package com.esmc.protocol.algorithm;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.security.Key;
import java.security.Security;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/algorithm/AES128.class */
public class AES128 {
    public static final byte[] encrypt(byte[] encrptKey, byte[] initVector, byte[] plainText) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        Key key = new SecretKeySpec(encrptKey, "AES");
        Cipher in = Cipher.getInstance("AES/ECB/NoPadding");
        in.init(1, key);
        byte[] enc = in.doFinal(plainText);
        return enc;
    }

    public static final byte[] decrypt(byte[] encryptKey, byte[] initVector, byte[] cipherText) throws Exception {
        try {
            SecretKeySpec skeySpec = new SecretKeySpec(encryptKey, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/NoPadding");
            cipher.init(2, skeySpec);
            try {
                byte[] original = cipher.doFinal(cipherText);
                return original;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }
}
