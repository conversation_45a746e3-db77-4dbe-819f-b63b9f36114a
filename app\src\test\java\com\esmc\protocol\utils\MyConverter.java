package com.esmc.protocol.utils;

import com.esmc.protocol.model.DlmsData;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/utils/MyConverter.class */
public class MyConverter {
    public static String bytesToHexString(byte[] data, boolean space) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (data == null || data.length <= 0) {
            return "";
        }
        for (int i = 0; i < data.length; i++) {
            int v = data[i] & 255;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
            if (space && i < data.length - 1) {
                stringBuilder.append(" ");
            }
        }
        return stringBuilder.toString();
    }

    public static byte[] hexStringToBytes(String hexString) {
        byte[] destByte = new byte[hexString.length() / 2];
        int j = 0;
        for (int i = 0; i < destByte.length; i++) {
            byte high = (byte) (Character.digit(hexString.charAt(j), 16) & 255);
            byte low = (byte) (Character.digit(hexString.charAt(j + 1), 16) & 255);
            destByte[i] = (byte) ((high << 4) | low);
            j += 2;
        }
        return destByte;
    }

    public static String hexStringToString(String hexString) {
        byte[] vs = hexStringToBytes(hexString);
        return new String(vs, StandardCharsets.US_ASCII);
    }

    public static byte[] displayObisToBytes(String obis) {
        String[] vs = obis.split("\\.");
        if (vs.length < 6) {
            return new byte[0];
        }
        try {
            byte[] result = new byte[6];
            for (int i = 0; i < 6; i++) {
                result[i] = (byte) Integer.parseInt(vs[i], 10);
            }
            return result;
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    public static byte[] dlmsDataToPdu(DlmsData dlmsData) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            int tag = OtherUtils.getCosemDataTypeTag(dlmsData.getDataType());
            if (tag < 0) {
                return new byte[0];
            }
            outputStream.write(tag);
            if (DlmsData.TYPE_BITSTRING.equals(dlmsData.getDataType()) || DlmsData.TYPE_OCTETSTRING.equals(dlmsData.getDataType()) || DlmsData.TYPE_VISIBLESTRING.equals(dlmsData.getDataType())) {
                outputStream.write(dlmsData.getDataValue().length() / 2);
            }
            outputStream.write(hexStringToBytes(dlmsData.getDataValue()));
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    public static DlmsData pduToDlmsData(ByteArrayInputStream input) {
        try {
            if (input.available() < 2) {
                return null;
            }
            String typeName = OtherUtils.getCosemDataTypeName(input.read() & 255);
            int len = 0;
            char c = 65535;
            switch (typeName.hashCode()) {
                case -1959284702:
                    if (typeName.equals(DlmsData.TYPE_U32)) {
                        c = '\n';
                        break;
                    }
                    break;
                case -1522974195:
                    if (typeName.equals(DlmsData.TYPE_I32)) {
                        c = '\t';
                        break;
                    }
                    break;
                case -672261858:
                    if (typeName.equals(DlmsData.TYPE_I8)) {
                        c = 4;
                        break;
                    }
                    break;
                case 2165025:
                    if (typeName.equals(DlmsData.TYPE_ENUM)) {
                        c = 6;
                        break;
                    }
                    break;
                case 2374300:
                    if (typeName.equals(DlmsData.TYPE_I16)) {
                        c = 7;
                        break;
                    }
                    break;
                case 48679701:
                    if (typeName.equals(DlmsData.TYPE_U8)) {
                        c = 5;
                        break;
                    }
                    break;
                case 576720544:
                    if (typeName.equals(DlmsData.TYPE_OCTETSTRING)) {
                        c = 0;
                        break;
                    }
                    break;
                case 898210203:
                    if (typeName.equals(DlmsData.TYPE_FLOAT32)) {
                        c = 11;
                        break;
                    }
                    break;
                case 1636641630:
                    if (typeName.equals(DlmsData.TYPE_BITSTRING)) {
                        c = 1;
                        break;
                    }
                    break;
                case 1694355203:
                    if (typeName.equals(DlmsData.TYPE_VISIBLESTRING)) {
                        c = 2;
                        break;
                    }
                    break;
                case 1729365000:
                    if (typeName.equals(DlmsData.TYPE_BOOL)) {
                        c = 3;
                        break;
                    }
                    break;
                case 2045028785:
                    if (typeName.equals(DlmsData.TYPE_U16)) {
                        c = '\b';
                        break;
                    }
                    break;
            }
            switch (c) {
                case 0:
                case 1:
                case 2:
                    len = OtherUtils.getVariableLengthValue(input);
                    if (len < 0) {
                        return null;
                    }
                    break;
                case 3:
                case 4:
                case 5:
                case 6:
                    len = 1;
                    if (1 > input.available()) {
                        return null;
                    }
                    break;
                case 7:
                case '\b':
                    len = 2;
                    if (2 > input.available()) {
                        return null;
                    }
                    break;
                case HexUtils.time_type_mmhhdd /* 9 */:
                case HexUtils.time_type_wwddmmyy /* 10 */:
                case HexUtils.time_type_ssmmhh /* 11 */:
                    len = 4;
                    if (4 > input.available()) {
                        return null;
                    }
                    break;
            }
            byte[] buf = new byte[len];
            input.read(buf);
            return new DlmsData(typeName, bytesToHexString(buf, false));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        byte[] vs = displayObisToBytes("0.0.40.0.0.255");
        System.out.println(bytesToHexString(vs, false));
        String normal = hexStringToString("30303041414161616120201F159091A0A13132");
        System.out.println(normal);
    }
}
