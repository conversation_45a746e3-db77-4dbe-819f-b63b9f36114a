package com.esmc.protocol.model.parameter;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/parameter/Security.class */
public class Security {
    private String llsKey;
    private String hlsKey;
    private String encryptionKeyGlobalUnicast;
    private String encryptionKeyGlobalBroadcast;
    private String authenticationKeyGlobal;
    private String dedicatedKeyUnicast;

    public Security(String llsKey, String hlsKey) {
        this.llsKey = llsKey;
        this.hlsKey = hlsKey;
    }

    public Security(String llsKey, String hlsKey, String encryptionKeyGlobalUnicast, String authenticationKeyGlobal) {
        this.llsKey = llsKey;
        this.hlsKey = hlsKey;
        this.encryptionKeyGlobalUnicast = encryptionKeyGlobalUnicast;
        this.authenticationKeyGlobal = authenticationKeyGlobal;
    }

    public String getLlsKey() {
        return this.llsKey;
    }

    public String getHlsKey() {
        return this.hlsKey;
    }

    public String getAuthenticationKeyGlobal() {
        return this.authenticationKeyGlobal;
    }

    public String getEncryptionKeyGlobalUnicast() {
        return this.encryptionKeyGlobalUnicast;
    }

    public String getEncryptionKeyGlobalBroadcast() {
        return this.encryptionKeyGlobalBroadcast;
    }

    public String getDedicatedKeyUnicast() {
        return this.dedicatedKeyUnicast;
    }

    public void setDedicatedKeyUnicast(String dedicatedKeyUnicast) {
        this.dedicatedKeyUnicast = dedicatedKeyUnicast;
    }
}
