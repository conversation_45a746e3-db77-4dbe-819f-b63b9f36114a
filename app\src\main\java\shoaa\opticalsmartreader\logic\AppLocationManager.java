package shoaa.opticalsmartreader.logic;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Typeface;
import android.location.GpsStatus;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.location.LocationProvider;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;

public class AppLocationManager implements LocationListener, GpsStatus.Listener, GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener {
    private static final long UPDATE_INTERVAL = 5000, FASTEST_INTERVAL = 5000;
    private static AppLocationManager instance = null;
    public double latitude = 0;
    public double longitude = 0;
    public GoogleApiClient googleApiClient;
    boolean listening = false;
    Activity activity;
    AlertDialog alertDialog;
    private LocationRequest locationRequest;


    private AppLocationManager(Activity activity) {
        this.activity = activity;
    }

    public static AppLocationManager getInstance(Activity activity) {
        if (instance == null) instance = new AppLocationManager(activity);
        instance.activity = activity;
        if (instance.googleApiClient == null && instance.checkPlayServices()) {
            // we build google api client
            instance.googleApiClient = new GoogleApiClient.Builder(instance.activity).addApi(LocationServices.API).addConnectionCallbacks(instance).addOnConnectionFailedListener(instance).build();

            instance.googleApiClient.connect();
        }
        return instance;
    }

    public static boolean isMockLocationOn(Location location) {
        if (location != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                return location.isMock();
            } else {
                location.isFromMockProvider();
            }
        }
        return false;
    }

    public boolean checkPlayServices() {
        GoogleApiAvailability apiAvailability = GoogleApiAvailability.getInstance();
        int resultCode = apiAvailability.isGooglePlayServicesAvailable(instance.activity);
        return resultCode == ConnectionResult.SUCCESS;
    }

    public void listen() {
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return;
        }
        if (!instance.listening) {
            try {
                instance.listening = true;
                if (((LocationManager) activity.getSystemService(Context.LOCATION_SERVICE)).isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                    activity.runOnUiThread(() -> {
                        instance.onLocationChanged(((LocationManager) activity.getSystemService(Context.LOCATION_SERVICE)).getLastKnownLocation(LocationManager.GPS_PROVIDER));
                        ((LocationManager) activity.getSystemService(Context.LOCATION_SERVICE)).requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, this);
                    });
                } else {
                    if (instance.googleApiClient != null) {
                        if (!instance.googleApiClient.isConnected())
                            instance.googleApiClient.connect();
                    }
                }
            } catch (Exception e) {
                instance.listening = false;
                instance.listen();
            }
        }
    }

    private void startLocationUpdates() {
        locationRequest = new LocationRequest();
        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        locationRequest.setInterval(UPDATE_INTERVAL);
        locationRequest.setFastestInterval(FASTEST_INTERVAL);

        if (ActivityCompat.checkSelfPermission(instance.activity, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(instance.activity, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(instance.activity, "You need to enable permissions to display location !", Toast.LENGTH_SHORT).show();
        }

       try{
           LocationServices.FusedLocationApi.requestLocationUpdates(instance.googleApiClient, instance.locationRequest, this::onLocationChanged);
       }catch (Exception e){

       }
    }

    @Override
    public void onLocationChanged(@NonNull Location location) {
        try {
            instance.latitude = location.getLatitude();
            instance.longitude = location.getLongitude();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (isMockLocationOn(location) && instance.alertDialog == null) {
                    AlertDialog.Builder builder = new AlertDialog.Builder(activity);
                    TextView textView = new TextView(activity);
                    textView.setText("يجب الغاء خاصية الموقع الوهمي");
                    textView.setTextDirection(View.TEXT_DIRECTION_RTL);
                    textView.setTypeface(null, Typeface.BOLD);
                    textView.setTextSize(20);
                    textView.setPadding(50, 20, 50, 5);
                    builder.setCustomTitle(textView);
                    builder.setMessage("يجب خاصية الموقع الوهمي GPS للتمكن من استخدام البرنامج");
                    builder.setCancelable(false);
                    instance.alertDialog = builder.show();
                }
            }
        } catch (Exception ignore) {
        }
    }

    @Override
    public void onProviderDisabled(@NonNull String provider) {
//        LocationListener.super.onProviderDisabled(provider);
        instance.listening = false;
        if (instance.alertDialog == null && activity != null && !activity.isDestroyed() && !activity.isFinishing()) {
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            TextView textView = new TextView(activity);
            textView.setText("يجب تفعيل خاصية تحديد المواقع");
            textView.setTextDirection(View.TEXT_DIRECTION_RTL);
            textView.setTypeface(null, Typeface.BOLD);
            textView.setTextSize(20);
            textView.setPadding(50, 20, 50, 5);
            builder.setCustomTitle(textView);
            builder.setMessage("يجب تفعيل خاصية تحديد المواقع GPS للتمكن من استخدام البرنامج");
            builder.setCancelable(false);
            instance.alertDialog = builder.show();
        }
    }

    @Override
    public void onProviderEnabled(@NonNull String provider) {
        instance.listen();
        if (instance.alertDialog != null) {
            if (instance.alertDialog.isShowing()) instance.alertDialog.dismiss();
            instance.alertDialog = null;
        }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        switch (status) {
            case LocationProvider.OUT_OF_SERVICE:
            case LocationProvider.TEMPORARILY_UNAVAILABLE:
                instance.listening = false;
                break;
            case LocationProvider.AVAILABLE:
                instance.listening = true;
                instance.listen();
                break;
        }

    }

    @NonNull
    @Override
    public String toString() {
        return instance.latitude + "," + instance.longitude;
    }


    @Override
    public void onGpsStatusChanged(int event) {
        instance.listen();
    }

    @Override
    public void onConnected(@Nullable Bundle bundle) {
        if (ActivityCompat.checkSelfPermission(instance.activity, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(instance.activity, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        // Permissions ok, we get last location
        Location location = LocationServices.FusedLocationApi.getLastLocation(googleApiClient);

        onLocationChanged(location);

        startLocationUpdates();
    }

    @Override
    public void onConnectionSuspended(int i) {

    }

    @Override
    public void onConnectionFailed(@NonNull ConnectionResult connectionResult) {
    }
}
