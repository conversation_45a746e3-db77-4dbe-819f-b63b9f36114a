package com.sewedy.electrometerparser.protocol.connection.bluetooth;

import android.bluetooth.BluetoothSocket;
import android.util.Log;

import com.sewedy.electrometerparser.protocol.Utils;

import java.io.IOException;

public class ConnectionHandler {
    public Packet result;
    private BluetoothSocket mmSocket;
    private ConnectedThread mythread;

    public ConnectionHandler(BluetoothSocket socket, DataCallback dataCallback) {
        mmSocket = socket;
        try {
            mythread = new ConnectedThread(mmSocket, dataCallback, 2000);
        } catch (IOException e) {
            e.printStackTrace();
        }
        mythread.start();
    }

    public void closeSocket() {
        try {
            mmSocket.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public synchronized void startRead(boolean isProbPacket, int expectedLength) {
        mythread.setRead(true);
        mythread.setProbPacket(isProbPacket);
        mythread.setWrittenPacket(null);
        mythread.setExpectedLength(expectedLength);
        try {
            Thread.sleep(1500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        mythread.run();
    }

    public synchronized void writing(byte[] data, int expectedLength, boolean isDebuggable) {
        mythread.setRead(false);
        mythread.setProbPacket(false);
        Log.d("Writing_Meter", Utils.bytesToHex(data));
        mythread.setWrittenPacket(data);
        mythread.setDebuggable(isDebuggable);
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        mythread.run();
        startRead(false, expectedLength);
    }

    public synchronized void writingZpa300(String strPdu, int expectedLength) {
        mythread.setRead(false);
        mythread.setProbPacket(true);
        Log.d("Writing_Prob", strPdu);
        mythread.setWrittenPacket(Utils.hexStringToByteArray(strPdu));
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        mythread.run();
    }

    public synchronized void writing(String strPdu, int expectedLength) {
        mythread.setRead(false);
        mythread.setProbPacket(true);
//        Log.d("Writing_Prob", strPdu);
        mythread.setWrittenPacket(Utils.hexStringToByteArray(strPdu));
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        mythread.run();
        startRead(true, expectedLength);
    }

    public synchronized void zpaWritingWith4800(byte[] data, int expectedLength, boolean isDebuggable) {
        mythread.setRead(false);
        mythread.setProbPacket(false);
        Log.d("Writing_Meter", Utils.bytesToHex(data));
        mythread.setWrittenPacket(data);
        mythread.setDebuggable(isDebuggable);
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        mythread.run();
        writing(ConnectionTypes.baudTran_4800_N_8_ZPA, 0);
        startRead(false, expectedLength);
    }
}
