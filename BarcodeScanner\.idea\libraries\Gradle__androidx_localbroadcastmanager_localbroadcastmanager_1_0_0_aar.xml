<component name="libraryTable">
  <library name="Gradle: androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/cddb0d8cfcab21953605eeaafe49aa47/transformed/localbroadcastmanager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5ea2860813d96c9e657bd0bf3453c61d/transformed/localbroadcastmanager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/535ddc3ddf6094e27c6e4bb987e51067/transformed/localbroadcastmanager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1137cf9afbe1a3c7a94756dd5e8805fd/transformed/localbroadcastmanager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/00da903c2f97f0c4f9cca8a96babfb43/transformed/localbroadcastmanager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1137cf9afbe1a3c7a94756dd5e8805fd/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/1137cf9afbe1a3c7a94756dd5e8805fd/transformed/localbroadcastmanager-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.localbroadcastmanager/localbroadcastmanager/1.0.0/3930e99159fd6b7f1d2e7d5fe9af0924ca1faf9/localbroadcastmanager-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>