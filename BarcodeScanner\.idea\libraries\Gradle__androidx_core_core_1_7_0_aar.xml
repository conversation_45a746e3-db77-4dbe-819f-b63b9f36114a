<component name="libraryTable">
  <library name="Gradle: androidx.core:core:1.7.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/143a392c90e90febb6edf416a1262453/transformed/core-1.7.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/143a392c90e90febb6edf416a1262453/transformed/core-1.7.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/143a392c90e90febb6edf416a1262453/transformed/core-1.7.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/143a392c90e90febb6edf416a1262453/transformed/core-1.7.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.core/core/1.7.0/f1c3685231d49092ae301932de1481d9b7a9e025/core-1.7.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>