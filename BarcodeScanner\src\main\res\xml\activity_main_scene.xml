<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/start">
        <Constraint
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <CustomAttribute
                app:attributeName="translationY"
                app:customFloatValue="-120" />
        </Constraint>
        <Constraint
            android:id="@+id/cardMlKit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">
            <CustomAttribute
                app:attributeName="translationX"
                app:customFloatValue="-200" />
        </Constraint>
        <Constraint
            android:id="@+id/cardZxing"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardMlKit">
            <CustomAttribute
                app:attributeName="translationX"
                app:customFloatValue="200" />
        </Constraint>
        <Constraint
            android:id="@+id/tv_developer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="translationY"
                app:customFloatValue="120" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/end">
        <Constraint
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <CustomAttribute
                app:attributeName="translationY"
                app:customFloatValue="0" />
        </Constraint>
        <Constraint
            android:id="@+id/cardMlKit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">
            <CustomAttribute
                app:attributeName="translationX"
                app:customFloatValue="0.0" />
        </Constraint>
        <Constraint
            android:id="@+id/cardZxing"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_16sdp"
            android:layout_marginEnd="@dimen/_16sdp"
            android:layout_marginBottom="@dimen/_16sdp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardMlKit">
            <CustomAttribute
                app:attributeName="translationX"
                app:customFloatValue="0.0" />
        </Constraint>
        <Constraint
            android:id="@+id/tv_developer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">
            <CustomAttribute
                app:attributeName="translationY"
                app:customFloatValue="0.0" />
        </Constraint>
    </ConstraintSet>

    <Transition
        app:autoTransition="animateToEnd"
        app:constraintSetEnd="@id/end"
        app:constraintSetStart="@+id/start"
        app:duration="1000"
        app:motionInterpolator="bounce" />
</MotionScene>
