package shoaa.opticalsmartreader.ui;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

import shoaa.opticalsmartreader.R;

public class NoteDialog extends Dialog {

    DialogInterface dialogInterface;
    String noteText = "";
    String customerIdText = "";
    MainActivity mainActivity;
    LinearLayout customerIdLayout;
    boolean askForCustomerId;

    public NoteDialog(MainActivity mainActivity, boolean askForCustomerId, DialogInterface dialogInterface) {
        super(mainActivity);
        this.mainActivity = mainActivity;
        this.dialogInterface = dialogInterface;
        this.askForCustomerId = askForCustomerId;
    }

    @Override
    public void show() {
        super.show();
        LinearLayout bedLcl = findViewById(R.id.mainLayout);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        mainActivity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int widthLcl = (int) (displayMetrics.widthPixels * 0.8f);
        LinearLayout.LayoutParams paramsLcl = (LinearLayout.LayoutParams)
                bedLcl.getLayoutParams();
        paramsLcl.width = widthLcl;
        paramsLcl.gravity = Gravity.CENTER;
        Window window = getWindow();
        bedLcl.setLayoutParams(paramsLcl);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
    }


    String replaceNonstandardDigits(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (isNonstandardDigit(ch)) {
                int numericValue = Character.getNumericValue(ch);
                if (numericValue >= 0) {
                    builder.append(numericValue);
                }
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }

    private boolean isNonstandardDigit(char ch) {
        return Character.isDigit(ch) && !(ch >= '0' && ch <= '9');
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.note_dialog);
        EditText etCustomerId = findViewById(R.id.etCustomerId);
        EditText etNote = findViewById(R.id.etNote);
        Button btnEnter = findViewById(R.id.btnEnter);
        customerIdLayout = findViewById(R.id.customerIdLayout);
        if (askForCustomerId) {
            customerIdLayout.setVisibility(View.VISIBLE);
            btnEnter.setEnabled(false);
        } else {
            customerIdLayout.setVisibility(View.GONE);
            btnEnter.setEnabled(true);
        }
        etCustomerId.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                customerIdText = replaceNonstandardDigits(charSequence.toString().trim());
                btnEnter.setEnabled(customerIdText.length() > 2);
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        etNote.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                noteText = charSequence.toString().trim();
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        btnEnter.setOnClickListener(view -> {
            if (askForCustomerId && Long.parseLong(replaceNonstandardDigits(etCustomerId.getText().toString())) == 0 && etNote.getText().toString().trim().isEmpty()) {
                Toast.makeText(mainActivity, "رقم المشترك مكون من اصفار برجاء كتابة السبب في الملاحظات", Toast.LENGTH_LONG).show();
                return;
            }
            if (askForCustomerId) {
                if (noteText.isEmpty())
                    noteText = " ";
                dialogInterface.onFinish(noteText + "\t" + customerIdText);
            } else {
                dialogInterface.onFinish(noteText);
            }
            dismiss();
        });
    }
}