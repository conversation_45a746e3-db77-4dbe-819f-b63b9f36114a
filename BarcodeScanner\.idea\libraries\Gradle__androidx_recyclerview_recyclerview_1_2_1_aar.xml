<component name="libraryTable">
  <library name="Gradle: androidx.recyclerview:recyclerview:1.2.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a35c96c015105caabc0d38d996efef3d/transformed/recyclerview-1.2.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a35c96c015105caabc0d38d996efef3d/transformed/recyclerview-1.2.1/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a35c96c015105caabc0d38d996efef3d/transformed/recyclerview-1.2.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a35c96c015105caabc0d38d996efef3d/transformed/recyclerview-1.2.1/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.recyclerview/recyclerview/1.2.1/f0f93e67af3f7417bdd560d5142f6dec4fe629c3/recyclerview-1.2.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>