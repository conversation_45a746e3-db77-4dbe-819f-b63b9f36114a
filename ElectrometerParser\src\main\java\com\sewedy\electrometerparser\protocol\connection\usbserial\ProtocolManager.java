package com.sewedy.electrometerparser.protocol.connection.usbserial;

import java.util.ArrayList;

public interface ProtocolManager {
    interface GetMeterNo {
        void onGetMeterNo(long meterNo);
    }

    interface InitializeConnection {
        void onInitializeConnection(long meterNo);
    }

    interface DataCallback {
        void onGetData(String data);
    }

    interface StartConnectionListner {
        void onStartConnection();
    }

    interface ReadEventsListner {
        void onReadEvents(ArrayList<ArrayList<Byte>> readEventsResults);
    }

    interface ReadEventListner {
        void onRead(ArrayList<ArrayList<Byte>> result);
    }

    interface WriteOneListner {
        void onWriteOne();
    }

    /**
     * Interface definition for a callback to be invoked when a write command executed.
     */
    interface WriteListner {
        void onWrite();
    }

    interface FailListener {
        void onFailed(String reason);
    }

    interface GetOneEventListner {
        void onGetOneEvent(byte[] data, short totalEntriesCount);
    }

    interface ReadOneListner {
        void onReadOne(byte[] data);
    }

    interface LoadProfileListner {
        void onLoadProfile(byte[] data);
    }

    interface EndCommunicationListner {
        void onEndCommunication();
    }

    interface ReadLisnter {
        void OnRead();
    }

    interface Read_LoadProfileListner {
        void onRead_LoadProfile();
    }

    interface Read_EventsListner {
        void onRead_Events();
    }

    interface StartCalibrationListner {
        void onStartCalibration();
    }

    interface LogMessageListner {
        void onGetMessage(String res);
    }
}
