<component name="libraryTable">
  <library name="Gradle: androidx.constraintlayout:constraintlayout:2.1.2@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/948c1e8087242b0d0d8a80d9203b6497/transformed/constraintlayout-2.1.2/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/948c1e8087242b0d0d8a80d9203b6497/transformed/constraintlayout-2.1.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/948c1e8087242b0d0d8a80d9203b6497/transformed/constraintlayout-2.1.2/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout/2.1.2/27c8deba9cd24ac581019ea387278de2ffd396a/constraintlayout-2.1.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>