package shoaa.barcodescanner;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.Settings;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import java.io.Serializable;
import java.util.Map;
import shoaa.barcodescanner.ui.BarcodeScanningActivity;

public class BarcodeScanner implements Serializable {
    private final Activity activity;
    private final ActivityResultLauncher<Intent> getBarcode;
    ActivityResultLauncher<String[]> permissionLauncher;
    private ScanCallback scanCallback = null;
    private boolean zoom = true;
    private boolean scan = true;

    public BarcodeScanner(AppCompatActivity activity) {
        this.activity = activity;
        getBarcode = activity.registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        Intent intent = result.getData();
                        // Handle the Intent
                        if (intent != null && intent.getData() != null) {
                            if (scanCallback != null) {
                                BarcodeResult barcodeResult = new BarcodeResult();
                                barcodeResult.setBarCode(intent.getData().toString());
                                barcodeResult.setImagePath(intent.getStringExtra("path"));
                                scanCallback.onFinish(barcodeResult);
                            }
                        }
                    } else {
                        if (scanCallback != null) {
                            BarcodeResult barcodeResult = new BarcodeResult();
                            barcodeResult.setBarCode("");
                            barcodeResult.setImagePath("");
                            scanCallback.onFinish(barcodeResult);
                        }
                    }
                });

        permissionLauncher = activity.registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(),
                (Map<String, Boolean> isGranted) -> {
                    for (Map.Entry<String, Boolean> x : isGranted.entrySet()) {
                        if (x.getKey().equals(Manifest.permission.CAMERA)) {
                            if (x.getValue()) {
                                openCameraWithScanner();
                            } else {

                                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                                        activity,
                                        Manifest.permission.CAMERA
                                )
                                ) {
                                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                    Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
                                    intent.setData(uri);
                                    getBarcode.launch(intent);
                                }
                            }
                        }
                    }
                });

    }

    public BarcodeScanner(FragmentActivity activity) {
        this.activity = activity;
        getBarcode = activity.registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        Intent intent = result.getData();
                        // Handle the Intent
                        if (intent != null && intent.getData() != null) {
                            if (scanCallback != null) {
                                BarcodeResult barcodeResult = new BarcodeResult();
                                barcodeResult.setBarCode(intent.getData().toString());
                                barcodeResult.setImagePath(intent.getStringExtra("path"));
                                scanCallback.onFinish(barcodeResult);
                            }
                        }
                    } else {
                        if (scanCallback != null) {
                            BarcodeResult barcodeResult = new BarcodeResult();
                            barcodeResult.setBarCode("");
                            barcodeResult.setImagePath("");
                            scanCallback.onFinish(barcodeResult);
                        }
                    }
                });
        permissionLauncher = activity.registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(),
                (Map<String, Boolean> isGranted) -> {
                    for (Map.Entry<String, Boolean> x : isGranted.entrySet()) {
                        if (x.getKey().equals(Manifest.permission.CAMERA)) {
                            if (x.getValue()) {
                                openCameraWithScanner();
                            } else {

                                if (!ActivityCompat.shouldShowRequestPermissionRationale(
                                        activity,
                                        Manifest.permission.CAMERA
                                )
                                ) {
                                    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                    Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
                                    intent.setData(uri);
                                    getBarcode.launch(intent);
                                }
                            }
                        }
                    }
                });

    }

    public void setScanCallback(ScanCallback scanCallback) {
        this.scanCallback = scanCallback;
    }

    public BarcodeResult start(boolean scan, boolean zoom) {
        this.zoom = zoom;
        this.scan = scan;
        BarcodeResult barcodeResult = new BarcodeResult();
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
        ) {
            openCameraWithScanner();
        } else {
            if (permissionLauncher != null)
                permissionLauncher.launch(new String[]{Manifest.permission.CAMERA});
        }
        return barcodeResult;
    }

    private void openCameraWithScanner() {
        Intent intent = new Intent(activity, BarcodeScanningActivity.class);
        intent.putExtra("scan", scan);
        intent.putExtra("zoom", zoom);
        if (getBarcode != null)
            getBarcode.launch(intent);
    }

    public interface ScanCallback {
        void onFinish(BarcodeResult barcodeResult);
    }
}
