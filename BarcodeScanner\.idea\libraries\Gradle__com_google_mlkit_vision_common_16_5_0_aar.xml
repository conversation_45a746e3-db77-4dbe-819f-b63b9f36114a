<component name="libraryTable">
  <library name="Gradle: com.google.mlkit:vision-common:16.5.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee90a97ff706197ee37f534a33b78d28/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6776d38e817b1dad3cd4ab1f81e96594/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2e8174a5adff04be1031ead423fe4000/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/824e05dbdbd42cec955caa4fc49b0f93/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d7b6940141bb742f957f7b17ffe6d11c/transformed/jetified-vision-common-16.5.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/824e05dbdbd42cec955caa4fc49b0f93/transformed/jetified-vision-common-16.5.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/824e05dbdbd42cec955caa4fc49b0f93/transformed/jetified-vision-common-16.5.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.mlkit/vision-common/16.5.0/890e185cbcb78c40e5c6c8eaeff03771f041559c/vision-common-16.5.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>