<component name="libraryTable">
  <library name="Gradle: androidx.viewpager2:viewpager2:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b76946d70958c99283d37b48dfc52a50/transformed/jetified-viewpager2-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b76946d70958c99283d37b48dfc52a50/transformed/jetified-viewpager2-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b76946d70958c99283d37b48dfc52a50/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b76946d70958c99283d37b48dfc52a50/transformed/jetified-viewpager2-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.viewpager2/viewpager2/1.0.0/3c3569044e6969f1ee5c3aa03b08e6717a2d782f/viewpager2-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>