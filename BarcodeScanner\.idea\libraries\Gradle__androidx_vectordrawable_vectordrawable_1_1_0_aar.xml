<component name="libraryTable">
  <library name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d686014c154581ef1fb79f3c074386e9/transformed/vectordrawable-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d6099bc2968b7fe35f54676149931d6c/transformed/vectordrawable-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/42a5cd8d07623a4d64ed310fac89a817/transformed/vectordrawable-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0e2886d32e3cd8ff0484d9fd5379ec94/transformed/vectordrawable-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a7240596ff6f225fa39c3b538520feae/transformed/vectordrawable-1.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0e2886d32e3cd8ff0484d9fd5379ec94/transformed/vectordrawable-1.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/0e2886d32e3cd8ff0484d9fd5379ec94/transformed/vectordrawable-1.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.vectordrawable/vectordrawable/1.1.0/1e0694477eed874c50c54b547cc3e5a62a57a62b/vectordrawable-1.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>