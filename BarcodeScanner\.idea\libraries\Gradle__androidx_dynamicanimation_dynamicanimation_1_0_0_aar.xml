<component name="libraryTable">
  <library name="Gradle: androidx.dynamicanimation:dynamicanimation:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f9f6dc8c43c426e6b0b6730e84a82344/transformed/dynamicanimation-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/59d8867445b7f2e2e4245d0a0feee9c1/transformed/dynamicanimation-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c77a976b3ce079e25b76e7212b1b9f81/transformed/dynamicanimation-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3a6e4a9f91860c69e9cf71e79a580470/transformed/dynamicanimation-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/5407d9003dcfba84df6c7a400a8475a2/transformed/dynamicanimation-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/3a6e4a9f91860c69e9cf71e79a580470/transformed/dynamicanimation-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3a6e4a9f91860c69e9cf71e79a580470/transformed/dynamicanimation-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.dynamicanimation/dynamicanimation/1.0.0/5c050e29af5033ff5e2d58ccf657ae92fdfbda4a/dynamicanimation-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>