package com.esmc.protocol;

import com.esmc.protocol.algorithm.AESGcm128;
import com.esmc.protocol.model.AuthenticationMechanisms;
import com.esmc.protocol.model.Conformance;
import com.esmc.protocol.model.DlmsData;
import com.esmc.protocol.model.DlmsResponseInfo;
import com.esmc.protocol.model.ProtocolProfile;
import com.esmc.protocol.model.SecurityControl;
import com.esmc.protocol.model.parameter.Dlms;
import com.esmc.protocol.utils.ApduProtector;
import com.esmc.protocol.utils.MyConverter;
import com.esmc.protocol.utils.OtherUtils;

import org.bouncycastle.crypto.InvalidCipherTextException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/DlmsHelper.class */
public class DlmsHelper {
    public static final int SERVICE_AARQ = 0;
    public static final int SERVICE_AARE = 1;
    public static final int SERVICE_REQ_GET_NORMAL = 16;
    public static final int SERVICE_RES_GET_NORMAL = 17;
    public static final int SERVICE_REQ_ACTION_NORMAL = 48;
    public static final int SERVICE_RES_ACTION_NORMAL = 49;
    public static final int SERVICE_ERROR = -1;
    private static final int TAG_INITIATE_RESPONSE = 8;
    private static final int TAG_INITIATE_RESPONSE_GLO = 40;
    private static final int TAG_ACTION_REQUEST_GLO = 203;
    private static final int TAG_GET_REQUEST_GLO = 200;
    private static final String TAG_AARE = "61";
    private static final String TAG_GET_RESPONSE_NORMAL = "C401";
    private static final String TAG_GET_RESPONSE_GLO = "CC";
    private static final String TAG_ACTION_RESPONSE_NORMAL = "C701";
    private static final String TAG_ACTION_RESPONSE_GLO = "CF";
    private static final byte[] APP_CONTEXT_PLAIN = {-95, 9, 6, 7, 96, -123, 116, 5, 8, 1, 1};
    private static final byte[] APP_CONTEXT_CIPHER = {-95, 9, 6, 7, 96, -123, 116, 5, 8, 1, 3};
    private static final byte[] SYSTEM_TITLE_CLIENT_PRE = {-90, 10, 4, 8};
    private static final byte[] AA_CTRL_SERVICE = {-118, 2, 7, Byte.MIN_VALUE};
    private static final byte[] CTOS_PRE = {-84, 18, Byte.MIN_VALUE};
    private static final byte[] INITIATE_REQUEST_PRE = {1, 0, 0, 0, 6, 95, 31, 4, 0};

    public static String request(int service, ProtocolProfile profile, Dlms dlms) {
        switch (service) {
            case 0:
                return packAarq(profile, dlms);
            case 16:
                return packGetNormal(profile, dlms);
            case SERVICE_REQ_ACTION_NORMAL /* 48 */:
                return packActionNormal(profile, dlms);
            default:
                return "";
        }
    }

    private static String packCipherPdu(int service, byte[] pdu, ProtocolProfile profile, Dlms dlms) {
        try {
            byte[] ciphered = ApduProtector.cipher(pdu, dlms.getSecurityControl(), profile);
            if (ciphered.length == 0) {
                return "";
            }
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            outputStream.write(service);
            if (ciphered.length < 128) {
                outputStream.write(ciphered.length);
            } else {
                outputStream.write(130);
                outputStream.write((ciphered.length >> 8) & 255);
                outputStream.write(ciphered.length & 255);
            }
            outputStream.write(ciphered);
            return MyConverter.bytesToHexString(outputStream.toByteArray(), false);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String unpackCipherPdu(String pdu, ProtocolProfile profile, Dlms dlms) {
        ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(pdu));
        input.read();
        int len = OtherUtils.getVariableLengthValue(input);
        if (len < 0) {
            return "";
        }
        byte[] ciphered = new byte[len];
        try {
            input.read(ciphered);
            byte[] vs = ApduProtector.decipher(ciphered, profile);
            if (vs.length == 0) {
                return "";
            }
            return MyConverter.bytesToHexString(vs, false);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    public static DlmsResponseInfo response(String pdu, ProtocolProfile profile, Dlms dlms) {
        if (pdu.startsWith(TAG_ACTION_RESPONSE_GLO) || pdu.startsWith(TAG_GET_RESPONSE_GLO)) {
            pdu = unpackCipherPdu(pdu, profile, dlms);
            if (pdu.isEmpty()) {
                return DlmsResponseInfo.serviceError();
            }
        }
        if (pdu.startsWith(TAG_AARE)) {
            DlmsResponseInfo info = new DlmsResponseInfo();
            info.setServiceType(1);
            info.setAssociationResult(unpackAare(pdu, profile, dlms));
            return info;
        } else if (pdu.startsWith(TAG_ACTION_RESPONSE_NORMAL)) {
            return unpackActionNormal(pdu, profile, dlms);
        } else {
            if (pdu.startsWith(TAG_GET_RESPONSE_NORMAL)) {
                return unpackGetNormal(pdu, profile, dlms);
            }
            return DlmsResponseInfo.serviceError();
        }
    }

    private static String packGetNormal(ProtocolProfile profile, Dlms dlms) {
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            output.write(192);
            output.write(1);
            output.write(192 + profile.getInvokeId());
            output.write((profile.getCosemItem().getCosemClass() >> 8) & 255);
            output.write(profile.getCosemItem().getCosemClass() & 255);
            byte[] temp = MyConverter.displayObisToBytes(profile.getCosemItem().getCosemObis());
            if (temp.length == 0) {
                return "";
            }
            output.write(temp);
            output.write(profile.getCosemItem().getServiceIndex());
            output.write(0);
            profile.setInvokeId((profile.getInvokeId() + 1) % 16);
            if (dlms.getSecurityControl().isAuthentication() || dlms.getSecurityControl().isEncryption()) {
                return packCipherPdu(TAG_GET_REQUEST_GLO, output.toByteArray(), profile, dlms);
            }
            return MyConverter.bytesToHexString(output.toByteArray(), false);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static String packActionNormal(ProtocolProfile profile, Dlms dlms) {
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            output.write(195);
            output.write(1);
            output.write(192 + profile.getInvokeId());
            output.write((profile.getCosemItem().getCosemClass() >> 8) & 255);
            output.write(profile.getCosemItem().getCosemClass() & 255);
            byte[] temp = MyConverter.displayObisToBytes(profile.getCosemItem().getCosemObis());
            if (temp.length == 0) {
                return "";
            }
            output.write(temp);
            output.write(profile.getCosemItem().getServiceIndex());
            output.write(1);
            output.write(MyConverter.dlmsDataToPdu(profile.getCosemItem().getData()));
            profile.setInvokeId((profile.getInvokeId() + 1) % 16);
            if (dlms.getSecurityControl().isAuthentication() || dlms.getSecurityControl().isEncryption()) {
                return packCipherPdu(TAG_ACTION_REQUEST_GLO, output.toByteArray(), profile, dlms);
            }
            return MyConverter.bytesToHexString(output.toByteArray(), false);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static byte[] packInitiateRequest(ProtocolProfile profile) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        output.write(INITIATE_REQUEST_PRE);
        byte[] buf = new byte[3];
        buf[0] = 0;
        buf[1] = 0;
        buf[2] = 0;
        EnumSet<Conformance> conformances = profile.getConformanceBlock();
        Iterator it = conformances.iterator();
        while (it.hasNext()) {
            Conformance item = (Conformance) it.next();
            int bit = item.getBitPosition();
            int index = bit / 8;
            int pos = (int) Math.pow(2.0d, 7 - (bit % 8));
            buf[index] = (byte) (buf[index] | pos);
        }
        output.write(buf);
        output.write((profile.getMaxRecvPduSizeClient() >> 8) & 255);
        output.write(profile.getMaxRecvPduSizeClient() & 255);
        return output.toByteArray();
    }

    private static String packAarq(ProtocolProfile profile, Dlms dlms) {
        byte[] ctos;
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            output.write(96);
            output.write(0);
            if (dlms.getSecurityControl().isEncryption() || dlms.getSecurityControl().isAuthentication()) {
                output.write(APP_CONTEXT_CIPHER);
            } else {
                output.write(APP_CONTEXT_PLAIN);
            }
            if (profile.getClientSystemTitle() != null) {
                output.write(SYSTEM_TITLE_CLIENT_PRE);
                output.write(MyConverter.hexStringToBytes(profile.getClientSystemTitle()));
            }
            if (dlms.getAuthenticationMechanisms() != AuthenticationMechanisms.NLS) {
                output.write(AA_CTRL_SERVICE);
                output.write(139);
                output.write(7);
                output.write(dlms.getAuthenticationMechanisms().getMechanismName());
                output.write(CTOS_PRE);
                if (AuthenticationMechanisms.LLS == dlms.getAuthenticationMechanisms()) {
                    ctos = MyConverter.hexStringToBytes(profile.getSecurityInfo().getLlsKey());
                } else {
                    ctos = OtherUtils.getRandomString(16).getBytes();
                }
                if (AuthenticationMechanisms.LLS != dlms.getAuthenticationMechanisms()) {
                    profile.setClient2Server(MyConverter.bytesToHexString(ctos, false));
                }
                output.write(ctos.length);
                output.write(ctos);
            }
            byte[] ir = packInitiateRequest(profile);
            byte[] ui = ir;
            if (dlms.getSecurityControl().isAuthentication() || dlms.getSecurityControl().isEncryption()) {
                byte[] cipheredIr = ApduProtector.cipher(ir, new SecurityControl(false, 0, true, true, 0), profile);
                if (cipheredIr.length == 0) {
                    return "";
                }
                ui = new byte[2 + cipheredIr.length];
                ui[0] = 33;
                ui[1] = (byte) cipheredIr.length;
                System.arraycopy(cipheredIr, 0, ui, 2, cipheredIr.length);
            }
            output.write(190);
            output.write(2 + ui.length);
            output.write(4);
            output.write(ui.length);
            output.write(ui);
            byte[] aarq = output.toByteArray();
            aarq[1] = (byte) (aarq.length - 2);
            return MyConverter.bytesToHexString(aarq, false);
        } catch (IOException | NullPointerException e) {
            e.printStackTrace();
            return "";
        }
    }

    private static DlmsResponseInfo unpackGetNormal(String pdu, ProtocolProfile profile, Dlms dlms) {
        DlmsResponseInfo info = new DlmsResponseInfo();
        info.setServiceType(17);
        ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(pdu));
        if (input.available() < 5) {
            info.setDataAccessResult(-1);
            return info;
        }
        input.read();
        input.read();
        int invokeId = input.read() & 15;
        if ((invokeId + 1) % 16 != profile.getInvokeId()) {
            info.setDataAccessResult(-2);
            return info;
        } else if (input.read() != 0) {
            int ar = input.read();
            if (ar == 0) {
                info.setDataAccessResult(-1);
            } else {
                info.setDataAccessResult(ar);
            }
            return info;
        } else {
            info.setDataAccessResult(0);
            DlmsData dlmsData = MyConverter.pduToDlmsData(input);
            info.setDlmsData(dlmsData);
            return info;
        }
    }

    private static DlmsResponseInfo unpackActionNormal(String pdu, ProtocolProfile profile, Dlms dlms) {
        DlmsResponseInfo info = new DlmsResponseInfo();
        info.setServiceType(49);
        byte[] vs = MyConverter.hexStringToBytes(pdu);
        ByteArrayInputStream input = new ByteArrayInputStream(vs);
        if (input.available() < 5) {
            info.setActionResult(-1);
            return info;
        }
        input.read();
        input.read();
        int invokeId = input.read() & 8;
        if ((invokeId + 1) % 16 != profile.getInvokeId()) {
            info.setActionResult(-2);
            return info;
        }
        info.setActionResult(input.read() & 255);
        if (0 != info.getActionResult() || input.read() == 0) {
            return info;
        }
        if (input.available() < 2) {
            info.setActionResult(-1);
            return info;
        }
        int flag = input.read();
        if (flag == 1) {
            info.setDataAccessResult(input.read());
            return info;
        }
        info.setDlmsData(MyConverter.pduToDlmsData(input));
        return info;
    }

    private static int unpackAare(String pdu, ProtocolProfile profile, Dlms dlms) {
        byte[] vs = MyConverter.hexStringToBytes(pdu);
        if (vs.length < 2) {
            return -1;
        }
        int len = vs[1] & 255;
        if (len + 2 != vs.length) {
            return -1;
        }
        int index = 0;
        while (index < len) {
            int aareTag = vs[2 + index] & 255;
            int bufLen = vs[2 + index + 1] & 255;
            if (index + 1 + bufLen < len) {
                byte[] buf = Arrays.copyOfRange(vs, 2 + index, bufLen + 2 + index + 2);
                index += bufLen + 2;
                switch (aareTag) {
                    case 137:
                        if (validateMechanismName(buf, dlms)) {
                            break;
                        } else {
                            return -1;
                        }
                    case 161:
                        if (validateApplicationContextName(buf, dlms)) {
                            break;
                        } else {
                            return -1;
                        }
                    case 162:
                        int aaRsl = getAssociationResult(buf);
                        if (0 == aaRsl) {
                            break;
                        } else {
                            return aaRsl;
                        }
                    case 164:
                        if (updateServerSystemTitle(buf, profile)) {
                            break;
                        } else {
                            return -1;
                        }
                    case 170:
                        if (updateServerToClient(buf, profile)) {
                            break;
                        } else {
                            return -1;
                        }
                    case 190:
                        if (updateUserInformation(buf, profile)) {
                            break;
                        } else {
                            return -1;
                        }
                }
            } else {
                return -1;
            }
        }
        return 0;
    }

    private static boolean updateInitiateResponse(byte[] buf, ProtocolProfile profile) {
        Conformance c;
        if (14 > buf.length || 8 != buf[0]) {
            return false;
        }
        int index = 1 + 1;
        if (buf[1] != 0) {
            index++;
        }
        int index2 = index + 1;
        if (!(buf[index2] == 95 && buf[index2 + 1] == 31 && buf[index2 + 2] == 4 && buf[index2 + 3] == 0)) {
            return false;
        }
        int index3 = index2 + 4 + 1;
        int index4 = index3 + 1;
        byte b = buf[index3];
        int mask = 128;
        List<Conformance> block = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            if ((b & mask & 255) == mask && Conformance.create(8 + i) != Conformance.NONE) {
                block.add(Conformance.create(8 + i));
            }
            mask = (mask >> 1) & 255;
        }
        int index5 = index4 + 1;
        byte b2 = buf[index4];
        int mask2 = 128;
        for (int i2 = 0; i2 < 8; i2++) {
            if ((b2 & mask2 & 255) == mask2 && (c = Conformance.create(16 + i2)) != Conformance.NONE) {
                block.add(c);
            }
            mask2 = (mask2 >> 1) & 255;
        }
        profile.setConformanceBlock(EnumSet.copyOf(block));
        if (index5 + 2 > buf.length) {
            return false;
        }
        profile.setMaxRecvPduSizeServer(((buf[index5] << 8) & 65280) | (buf[index5 + 1] & 255));
        return true;
    }

    private static boolean updateUserInformation(byte[] buf, ProtocolProfile profile) {
        if (5 > buf.length) {
            return false;
        }
        int len = buf[3] & 255;
        if (len + 4 != buf.length) {
            return false;
        }
        if (8 == buf[4]) {
            return updateInitiateResponse(Arrays.copyOfRange(buf, 4, len + 4), profile);
        }
        if (TAG_INITIATE_RESPONSE_GLO != buf[4]) {
            return false;
        }
        byte b = buf[5];
        if (b + 6 != buf.length) {
            return false;
        }
        byte[] irCiphered = Arrays.copyOfRange(buf, 6, b + 6);
        byte[] ir = ApduProtector.decipher(irCiphered, profile);
        return updateInitiateResponse(ir, profile);
    }

    private static boolean updateServerToClient(byte[] buf, ProtocolProfile profile) {
        if (12 > buf.length) {
            return false;
        }
        int len = buf[3] & 255;
        if (len % 8 != 0 || len + 4 != buf.length) {
            return false;
        }
        byte[] temp = Arrays.copyOfRange(buf, 4, len + 4);
        profile.setServer2Client(MyConverter.bytesToHexString(temp, false));
        return true;
    }

    private static boolean updateServerSystemTitle(byte[] buf, ProtocolProfile profile) {
        if (12 != buf.length || 4 != buf[2] || 8 != buf[3]) {
            return false;
        }
        byte[] temp = Arrays.copyOfRange(buf, 4, 12);
        profile.setServerSystemTitle(MyConverter.bytesToHexString(temp, false));
        return true;
    }

    private static boolean validateMechanismName(byte[] buf, Dlms dlms) {
        if (9 != buf.length) {
            return false;
        }
        return Arrays.equals(Arrays.copyOfRange(buf, 2, 9), dlms.getAuthenticationMechanisms().getMechanismName());
    }

    private static boolean validateApplicationContextName(byte[] buf, Dlms dlms) {
        if (dlms.getSecurityControl().isEncryption() || dlms.getSecurityControl().isAuthentication()) {
            return Arrays.equals(APP_CONTEXT_CIPHER, buf);
        }
        return Arrays.equals(APP_CONTEXT_PLAIN, buf);
    }

    private static int getAssociationResult(byte[] buf) {
        if (5 != buf.length) {
            return -1;
        }
        return buf[4] & 255;
    }

    public static void main(String[] args) {
        try {
            byte[] plain = AESGcm128.decrypt(new byte[]{48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48}, new byte[]{67, 76, 69, 0, 0, 0, 0, 1, 0, 0, 0, 2}, new byte[]{-3, 75, 4, 7, 51, 15, -96, TAG_INITIATE_RESPONSE_GLO, -99, -15, 7, 48}, new byte[]{16, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 99, 115, 49, 120, 74, 103, 75, 68, 115, 97, 48, 78, 49, 90, 82, 54});
            System.out.println(MyConverter.bytesToHexString(plain, true));
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
        }
        byte[] vs = {8, 0, 6, 95, 31, 4, 0, 0, 80, 31, 1, -12, 0, 7};
        byte[] test = Arrays.copyOfRange(vs, vs.length - 5, vs.length);
        System.out.println(MyConverter.bytesToHexString(test, true));
        ProtocolProfile protocolProfile = new ProtocolProfile();
        if (!updateInitiateResponse(vs, protocolProfile)) {
            System.out.println("Failed");
            return;
        }
        Iterator it = protocolProfile.getConformanceBlock().iterator();
        while (it.hasNext()) {
            Conformance c = (Conformance) it.next();
            System.out.println(c.name());
        }
    }
}
