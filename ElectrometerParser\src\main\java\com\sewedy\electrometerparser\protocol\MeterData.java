package com.sewedy.electrometerparser.protocol;

import android.text.TextUtils;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.TreeMap;

public class MeterData {
    private static MeterData meterData;
    private String customerCode;
    private Shared.ControlTemperDT1 controlTemperDT1;
    private Shared.TarrifPaymentData tarrifPaymentData;
    private Shared.MeteringData meteringData;
    private ArrayList<Shared.MoneyTransaction> moneyTransactions;
    private ArrayList<Shared.ProfileRecord> profileRecords;
    private ArrayList<Shared.ConfigureMeter> configureMeters;
    private ArrayList<Shared.Event> meterEvents;
    private Shared.NewControlData newControlData;
    private String result = "true";
    private String message = "";
    private ArrayList<Shared.BillingPeriodHistory> billingPeriodHistories;

    private MeterData() {

    }

    public static MeterData getInstance() {
        if (meterData != null) {
            return meterData;
        }
        meterData = new MeterData();
        return meterData;
    }

    public static void reset() {
        meterData = new MeterData();
    }

    //    private final DecimalFormat decimalFormat = new DecimalFormat("0.000");
    String formatDouble(String value) {
        String sValue = String.format("%.3f", Double.parseDouble(value));
        return String.valueOf(Double.parseDouble(sValue));
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String success) {
        result = success;
    }

    public Shared.NewControlData getNewControlData() {
        return newControlData;
    }

    public void setNewControlData(Shared.NewControlData newControlData) {
        this.newControlData = newControlData;
    }

    public Shared.MeteringData getMeteringData() {
        return meteringData;
    }

    public void setMeteringData(Shared.MeteringData meteringData) {
        this.meteringData = meteringData;
    }

    public ArrayList<Shared.MoneyTransaction> getMoneyTransactions() {
        return moneyTransactions;
    }

    public void setMoneyTransactions(ArrayList<Shared.MoneyTransaction> moneyTransactions) {
        this.moneyTransactions = moneyTransactions;
    }

    public Shared.ControlTemperDT1 getControlTemperDT1() {
        return controlTemperDT1;
    }

    public void setControlTemperDT1(Shared.ControlTemperDT1 controlTemperDT1) {
        this.controlTemperDT1 = controlTemperDT1;
    }

    public Shared.TarrifPaymentData getTarrifPaymentData() {
        return tarrifPaymentData;
    }

    public void setTarrifPaymentData(Shared.TarrifPaymentData tarrifPaymentData) {
        this.tarrifPaymentData = tarrifPaymentData;
    }

    public ArrayList<Shared.ConfigureMeter> getConfigureMeters() {
        return configureMeters;
    }

    public void setConfigureMeters(ArrayList<Shared.ConfigureMeter> configureMeters) {
        this.configureMeters = configureMeters;
    }

    public String getSerialNum() {
        return MainMeterDataParser.getInstance().getData().meterSerialNumberRepresentation;
    }

    public String getFirmWareVersion() {
        if (MainMeterDataParser.getInstance().getData().meterType == Shared.DataNewDataListPacket.MeterType.Direct)
            return "503";
        else if (MainMeterDataParser.getInstance().getData().meterType == Shared.DataNewDataListPacket.MeterType.InDirect)
            return "100";
        else return "500";
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public ArrayList<Shared.BillingPeriodHistory> getBillingPeriodHistories() {
        return billingPeriodHistories;
    }

    public void setBillingPeriodHistories(ArrayList<Shared.BillingPeriodHistory> billingPeriodHistories) {
        this.billingPeriodHistories = billingPeriodHistories;
    }

    public ArrayList<Shared.ProfileRecord> getProfileRecords() {
        return profileRecords;
    }

    public void setProfileRecords(ArrayList<Shared.ProfileRecord> profileRecords) {
        this.profileRecords = profileRecords;
    }

    public String getFirmwareVersion() {
        return getFirmWareVersion();
    }

    public String getCustomerID() {
        return getCustomerCode();
    }

    public String getCardID() {
        try {
            if (controlTemperDT1.RfUniqueId == null)
                return "NF";
            return controlTemperDT1.RfUniqueId;
        } catch (Exception e) {
            return "NF";
        }

    }

    public String getMeterID() {
        return getSerialNum();
    }

    public String getActivityType() {
        if (getNewControlData() == null || TextUtils.isEmpty(getNewControlData().activityType))
            return "NF";
        return getNewControlData().activityType;
    }

    public String getPowerFactor() {
        try {
            for (Shared.DataOne dataOne : getMeteringData().dataOne) {
                if (dataOne.name.equals("Average PF Current Year"))
                    return formatDouble(dataOne.totalValue);
                ;
            }
        } catch (Exception e) {
            return "NF";
        }
        return "NF";
    }

    public String getLastYearPowerFactor() {
        try {
            for (Shared.DataOne dataOne : getMeteringData().dataOne) {
                if (dataOne.name.equals("Average PF Previous Year"))
                    return formatDouble(dataOne.totalValue);
            }
        } catch (Exception e) {
            return "NF";
        }
        return "NF";
    }

    public String getInstallingTechnicanCode() {
        try {
            if (TextUtils.isEmpty(getNewControlData().installingTechnicanCode))
                return "NF";
            return getNewControlData().installingTechnicanCode;
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getInstallingDateAndTime() {
        if (getNewControlData() != null) {
            return getNewControlData().installingDateAndTime;
        } else if (getMoneyTransactions() != null && getMoneyTransactions().size() != 0) {
            return getFirstTransactionDate();
        } else {
            return "NF";
        }
    }

    public String getMeterDataTime() {
        try {
            String date = ((MainMeterDataParser.getInstance().getData().year & 0xFF) + 2000 + "/" + ((MainMeterDataParser.getInstance().getData().month & 0xFF))) + "/" + ((MainMeterDataParser.getInstance().getData().day & 0xFF));
            String hour = ((MainMeterDataParser.getInstance().getData().hour & 0xFF)) + ":" + ((MainMeterDataParser.getInstance().getData().minute & 0xFF) + ":0");
            return Shared.getFormattedDate(date + " " + hour);
        } catch (Exception e) {
            return "NF";
        }
    }

//    public String getCurrentTarrifInstalling() {
//
//        try {
//            for (Shared.RowDatatbl r :
//                    getTarrifPaymentData().rowDatatbls) {
//                if (r.dataElement.contains("Current Tariff Index")) {
//                    return String.valueOf(Integer.parseInt(r.value) + 1);
//                }
//            }
//            return "NF";
//        } catch (Exception e) {
//            return "NF";
//        }
//    }

    public String getCurrentStepTarrif() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current Tariff Index")) {
                    // changed because the current tariff index on the meter screen is less than one of what we display
                    // requested by team wael
                    return String.valueOf((int) Double.parseDouble(r.value) + 1);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentTarrifInstallationDate() {
        try {
            String dateTime = getMoneyTransactions().get(getMoneyTransactions().size() - 1).dateTime;
            return Shared.getFormattedDate(dateTime);
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getMeterStatus() {
        try {
            return getControlTemperDT1().CoverOpen.equals("0") && getControlTemperDT1().TerminalOpen.equals("0")
                    && getControlTemperDT1().BatteryLowAlarm.equals("0") &&
                    getControlTemperDT1().Relay.equals("0") ? "M0" : "M1";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getBatteryStatus() {
        try {
            return !getControlTemperDT1().BatteryLowAlarm.equals("0") ? "B1" : "B0";
        } catch (Exception e) {
            return "NF";
        }
    }


    //
    public String getRelayStatus() {
        try {
//            Log.d("TAG", "getRelayStatus: " + getControlTemperDT1().Relay.equals("0"));
//            Log.d("TAG", "getRelayStatus: " + getControlTemperDT1().Relay);
            return getControlTemperDT1().Relay.equals("0") ? "R0" : "R1";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCoverOpen() {
        try {
            return !getControlTemperDT1().CoverOpen.equals("0") ? "T1_"
                    + getControlTemperDT1().CoverOpen : "T0_0";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getSideCover() {
        try {
            return !getControlTemperDT1().TerminalOpen.equals("0") ? "S1_"
                    + getControlTemperDT1().TerminalOpen : "S0_0";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTechnical_code_event_1() {
        try {
            return getNewControlData().RemovedTampers.isEmpty() ? "NF"
                    : ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(0)).techID;
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_Date_1() {
        try {
            return getNewControlData().RemovedTampers.isEmpty() ? "NF"
                    : Shared.getFormattedDate(getNewControlData().RemovedTampers.get(0).resetEventDateTime);
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_type_1() {
        try {
            return getNewControlData().RemovedTampers.size() >= 2 ?
                    ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(0)).tamperId : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_type_2() {
        try {

            return getNewControlData().RemovedTampers.size() >= 2 ?
                    ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(1)).tamperId : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getInstanteneousVolt() {
        try {
            if (TextUtils.isEmpty(profileRecords.get(profileRecords.size() - 1).VoltageChannelA)) {
                return "NF";
            }
            return profileRecords.get(profileRecords.size() - 1).VoltageChannelA;
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getInstanteneousCurrentPhaseAmpere() {
        try {
            if (TextUtils.isEmpty(profileRecords.get(profileRecords.size() - 1).CurrentChannelA)) {
                return "NF";
            }
            return profileRecords.get(profileRecords.size() - 1).CurrentChannelA;
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getInstanteneous_current_Neutral() {
        return "0";
    }

    public String getTechnical_code_event_2() {
        try {
//            Log.d("TAG", "getTechnical_code_event_2: " + ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(1)).techID);
            return getNewControlData().RemovedTampers.size() >= 2 ?
                    ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(1)).techID : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_Date_2() {
        try {
//            Log.d("TAG", "getEvent_Date_1: " + getNewControlData().RemovedTampers.get(1).resetEventDateTime);
            return getNewControlData().RemovedTampers.size() >= 2 ?
                    Shared.getFormattedDate(getNewControlData().RemovedTampers.get(1).resetEventDateTime) : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_type_3() {
        try {
            return getNewControlData().RemovedTampers.size() >= 3 ? ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(2)).tamperId : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTechnical_code_event_3() {
        try {
            return getNewControlData().RemovedTampers.size() >= 3 ? ((Shared.RemovedTampers) getNewControlData().RemovedTampers.get(2)).techID : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getEvent_Date_3() {
        try {
//            Log.d("TAG", "getEvent_Date_1: " + getNewControlData().RemovedTampers.get(2).resetEventDateTime);
            return getNewControlData().RemovedTampers.size() >= 3 ?
                    Shared.getFormattedDate(getNewControlData().RemovedTampers.get(2).resetEventDateTime) : "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTarrifRemainingCredit() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Money Balance")) {
                    return (Double.parseDouble(r.value) >= 0.0D ? formatDouble(r.value) : "0.0") + "";
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTarrifRemainingCreditKw() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Expected Remaining Active Energy")) {
                    return (Double.parseDouble(r.value) >= 0.0D ? formatDouble(r.value) : "0.0") + "";
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTarrifRemainingDebit() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Money Balance")) {
                    return Double.parseDouble(r.value) >= 0.0D ? "0.0" : formatDouble(r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentMonthConsumption() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current Consumption")) {
                    if (r.value.split(" ").length > 1)
                        return formatDouble(r.value.split(" ")[0]) + " " + r.value.split(" ")[1];
                    else
                        return formatDouble(r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentMonthConsumptionMoney() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current BP Bill")) {
                    if (r.value.split(" ").length > 1)
                        return formatDouble(r.value.split(" ")[0]) + " " + r.value.split(" ")[1];
                    else
                        return formatDouble(r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }


    public String getTotalRechargeNumber() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Charge Number Of RFID Interface")) {
                    return r.value;
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalRechargeAmount() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Total Life Charged Money")) {
                    return r.value;
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getMaximumDemandConsumption() {
        try {
            String consumption = "";
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Previous Active Power MD")) {
                    consumption = consumption + "((" + r.value + ")) ";
                }
                if (r.dataElement.contains("Previous Electrical Current MD")) {
                    consumption = consumption + "((" + r.value + ")) ";
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentDemand() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current Electrical Current MD")) {
                    return r.value;
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getMaximumDemandConsumptionDate() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Previous Active Power MD Time")) {
                    return Shared.getFormattedDate(r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentActivePowerMD() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current Active Power MD")) {
                    return (r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentActivePowerMDAmpere() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Current Electrical Current MD")) {
                    return (r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getCurrentActivePowerMDDate() {
        try {
            for (Shared.RowDatatbl r :
                    getTarrifPaymentData().rowDatatbls) {
                if (r.dataElement.contains("Previous Active Power MD Time")) {
                    return Shared.getFormattedDate(r.value);
                }
            }
            return "NF";
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalConsumption() {
        try {
            double rechargeAmount = Double.parseDouble(this.getTotalRechargeAmount());
            double remainingCredit = Double.parseDouble(this.getTarrifRemainingCredit());
            return formatDouble(String.valueOf(Math.abs(rechargeAmount - remainingCredit)));
        } catch (Exception e) {
            return "NF";
        }
    }

//    public String getDateTimeOfLastRechargeTransaction() {
//        try {
//
//            if (null != getMoneyTransactions() && !getMoneyTransactions().isEmpty() && getMoneyTransactions().size() != 0) {
//                double balance = 0.0D;
//                String dateTime = "";
//
//                for (Shared.MoneyTransaction m :
//                        getMoneyTransactions()) {
//                    double currentBalance = Double.parseDouble((String) m.balance);
//                    if (currentBalance >= balance) {
//                        dateTime = (String) m.dateTime;
//                    }
//                }
////                Log.d("TAG", "getDateTimeOfLastRechargeTransaction: " + dateTime);
//                return dateTime;
//            } else {
//                throw new Exception("parsing error");
//            }
//        } catch (Exception e) {
//            return "NF";
//        }
//    }

    public String getDateTimeOfLastRechargeTransaction() {
        String lastDateFromNewController = "";
        if (newControlData != null)
            lastDateFromNewController = newControlData.lastChargeDateTime;
        if (!lastDateFromNewController.isEmpty()) {
            return lastDateFromNewController;
        } else {
            try {
                if (null != getMoneyTransactions() && !getMoneyTransactions().isEmpty() && getMoneyTransactions().size() != 0) {
                    return getLastTransactionDate();
                } else {
                    throw new Exception("parsing error");
                }
            } catch (Exception e) {
                return "NF";
            }
        }
    }

    public String getDateTimeOfTarrif() {
        try {
            HashMap<Integer, String> hm = new HashMap<Integer, String>();


            if (null != getMoneyTransactions() && !getMoneyTransactions().isEmpty() && getMoneyTransactions().size() != 0) {
                String dateTime = "";

                for (Shared.MoneyTransaction m : getMoneyTransactions()) {
                    hm.put(Integer.parseInt(m.chargeDisNum), m.dateTime);
                }
                TreeMap<Integer, String> treeMap = new TreeMap<>(hm);
                Iterator itr = treeMap.keySet().iterator();
                int key = (int) itr.next();
                return hm.get(key);
            } else {
                throw new Exception("parsing error");
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalConsumptionKw() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Active")) {
                        return d.totalValue;
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalConsumptionKvar() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Reactive")) {
                        return d.totalValue;
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getMaxPowerFactor() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Average PF Current Year")) {
                        return formatDouble(d.totalValue);
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getMaxPowerFactorDateTime() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Average PF Current Year")) {
                        Shared.getFormattedDate(d.meterDate);
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalUnbalance() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Fault Active")) {
                        return d.totalValue;
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getTotalReverse() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                for (Shared.DataOne d :
                        getMeteringData().dataOne) {
                    if (d.name.equals("Reverse Active")) {
                        return d.totalValue;
                    }
                }

                return "NF";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getLifeMaximumDemandAmpere() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                return ((Shared.DataTwo) getMeteringData().dataTwo.get(0)).amperesValue + " A";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getLifeMaximumDemandKW() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                return ((Shared.DataTwo) getMeteringData().dataTwo.get(0)).wattsValue + " KW";
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public String getLifeMaximumDemandDateTime() {
        try {
            if (null == getMeteringData()) {
                throw new Exception("parsing error");
            } else {
                return (getMeteringData().dataTwo.get(0)).wattsTime;
            }
        } catch (Exception e) {
            return "NF";
        }
    }

    public ElectrometerMonthConsumption getMonthConsumption(int month) {
        try {
            ArrayList<ElectrometerMonthConsumption> electrometerMonthConsumptions = new ArrayList<>();
            for (Shared.BillingPeriodHistory billingPeriodHistory :
                    getBillingPeriodHistories()) {
                ElectrometerMonthConsumption electrometerMonthConsumption = new ElectrometerMonthConsumption();
                electrometerMonthConsumption.setConsumption(formatDouble(billingPeriodHistory.activeEnergy));
                electrometerMonthConsumption.setMoney(formatDouble(billingPeriodHistory.consumptionBill));
                electrometerMonthConsumption.setDateMaxDemand(billingPeriodHistory.date);
                electrometerMonthConsumption.setMaxDemand(formatDouble(billingPeriodHistory.mdA));
                electrometerMonthConsumption.setPowerFactor(getMonthPowerFactor(Double.parseDouble(formatDouble(billingPeriodHistory.activeEnergy)), Double.parseDouble(formatDouble(billingPeriodHistory.reactiveEnergy))));
                electrometerMonthConsumption.setReactiveEnergy(formatDouble(billingPeriodHistory.reactiveEnergy));
                electrometerMonthConsumptions.add(electrometerMonthConsumption);
            }
            return electrometerMonthConsumptions.get(month);
        } catch (Exception e) {
            ElectrometerMonthConsumption consumption = new ElectrometerMonthConsumption();
            consumption.consumption = "NF";
            consumption.dateMaxDemand = "NF";
            consumption.money = "NF";
            consumption.maxDemand = "NF";
            consumption.reactiveEnergy = "NF";
            return consumption;
        }
    }

    public String getMonthsConsumption() {
        try {
            ArrayList<ElectrometerMonthConsumption> electrometerMonthConsumptions = new ArrayList<>();
            for (Shared.BillingPeriodHistory billingPeriodHistory :
                    getBillingPeriodHistories()) {
                ElectrometerMonthConsumption electrometerMonthConsumption = new ElectrometerMonthConsumption();
                electrometerMonthConsumption.setConsumption(billingPeriodHistory.consumptionBill);
                electrometerMonthConsumption.setDateMaxDemand(billingPeriodHistory.date);
                electrometerMonthConsumption.setMaxDemand(billingPeriodHistory.mdW);
                electrometerMonthConsumption.setMoney(billingPeriodHistory.moneyBalance);
                electrometerMonthConsumption.setReactiveEnergy(billingPeriodHistory.reactiveEnergy);
                electrometerMonthConsumptions.add(electrometerMonthConsumption);
            }
            return new Gson().toJson(electrometerMonthConsumptions);
        } catch (Exception e) {
            return "NF";
        }
    }

    public ArrayList<Shared.Event> getMeterEvents() {
        return meterEvents;
    }

    public void setMeterEvents(ArrayList<Shared.Event> meterEvents) {
        this.meterEvents = meterEvents;
    }

    private String getMonthPowerFactor(double active, double reactive) {
        if (active == 0.0)
            return "0.0";
        double deg = reactive / active;
        double atan = Math.atan(deg);
        double result = Math.cos(atan);
        return formatDouble(String.valueOf(result));

    }

    private String getFirstTransactionDate() {
        ArrayList<Shared.MoneyTransaction> moneyTransactions = getMoneyTransactions();
        Collections.sort(moneyTransactions);
        return moneyTransactions.get(0).dateTime;
    }

    private String getLastTransactionDate() {
        ArrayList<Shared.MoneyTransaction> moneyTransactions = getMoneyTransactions();
        Collections.sort(moneyTransactions);
        return moneyTransactions.get(moneyTransactions.size() - 1).dateTime;
    }

    public class ElectrometerMonthConsumption {
        String dateMaxDemand;
        String consumption;
        String money;
        String maxDemand;
        String reactiveEnergy;
        String powerFactor;

        public ElectrometerMonthConsumption() {
        }

        public String getConsumption() {
            return this.consumption;
        }

        public void setConsumption(String consumption) {
            this.consumption = consumption;
        }

        public String getMoney() {
            return this.money;
        }

        public void setMoney(String money) {
            this.money = money;
        }

        public String getDateMaxDemand() {
            return Shared.getFormattedDate(this.dateMaxDemand);
        }

        public void setDateMaxDemand(String dateMaxDemand) {
            this.dateMaxDemand = dateMaxDemand;
        }

        public String getMaxDemand() {
            return this.maxDemand;
        }

        public void setMaxDemand(String maxDemand) {
            this.maxDemand = maxDemand;
        }

        public String getReactiveEnergy() {
            return this.reactiveEnergy;
        }

        public void setReactiveEnergy(String reactiveEnergy) {
            this.reactiveEnergy = reactiveEnergy;
        }

        public String getPowerFactor() {
            return powerFactor;
        }

        public void setPowerFactor(String powerFactor) {
            this.powerFactor = powerFactor;
        }
    }
}
