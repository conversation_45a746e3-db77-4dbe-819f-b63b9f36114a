<component name="libraryTable">
  <library name="Gradle: com.google.android.odml:image:1.0.0-beta1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/78dbcb1ab002e8a7054983f16cb1e6aa/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6c4b15a08bb55a9d22d93f97dbef380c/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d62b26476de7422d648d3cf0e11b627b/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/16c3aa8ffea53a52f78349fb5f08fcef/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f1763b3df28791d57ee681134b782d29/transformed/jetified-image-1.0.0-beta1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/16c3aa8ffea53a52f78349fb5f08fcef/transformed/jetified-image-1.0.0-beta1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/16c3aa8ffea53a52f78349fb5f08fcef/transformed/jetified-image-1.0.0-beta1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.odml/image/1.0.0-beta1/aa9d23e3124a588104fe600f42af0e192f732599/image-1.0.0-beta1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>