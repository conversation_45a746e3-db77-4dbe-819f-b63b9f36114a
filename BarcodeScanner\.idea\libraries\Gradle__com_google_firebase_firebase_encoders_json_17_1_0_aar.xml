<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-encoders-json:17.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/70cb3eae0b8bf40adc2e707cb4525b7f/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/46dcbe76188c56b700fbcb0119c47c43/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4bbcab0b9043e7d7b9d15d1404898599/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/49a9b023d79d7bb0a1e53c1d14448d56/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/33ca0b99dd961840547915cdf6d6e206/transformed/jetified-firebase-encoders-json-17.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/49a9b023d79d7bb0a1e53c1d14448d56/transformed/jetified-firebase-encoders-json-17.1.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/49a9b023d79d7bb0a1e53c1d14448d56/transformed/jetified-firebase-encoders-json-17.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/17.1.0/5fbe3622c8be8565ee56aa41117b13f095965904/firebase-encoders-json-17.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-encoders-json/17.1.0/562e15d8b50abbf64e6e8b33c3ccdab488ae15f8/firebase-encoders-json-17.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>