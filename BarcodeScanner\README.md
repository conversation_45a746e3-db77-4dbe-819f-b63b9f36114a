# BarcodeScanner

Barcode scanner sample application using CameraX and Android ML vision API's

With ML Kit's barcode scanning API, you can read data encoded using most standard barcode formats.
Barcode scanning happens on the device, and doesn't require a network connection.

### add these lines in "settings.gradle"

```
#Replace USER_NAME with your github username
#Replace TOKEN with your github access token
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        maven {
            url = uri("https://maven.pkg.github.com/ShoaaCo/BarcodeScanner")
            credentials {
                username = "USER_NAME"
                password = "TOKEN"
            }
        }
    }
}
```

### For bundling the model with your app

```
dependencies {
      // ...
      // Use this dependency to bundle the model with your app
      implementation "shoaa.barcodescanner:barcodescanner:1.0.12"
    }
```

### activity_main.xml

```
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <ImageView
        android:id="@+id/image"
        android:layout_width="300dp"
        android:layout_height="300dp"

        app:layout_constraintBottom_toTopOf="@id/text"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hello World!"
        app:layout_constraintBottom_toTopOf="@id/btn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image" />

    <CheckBox
        android:id="@+id/chk_scan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Scan"
        app:layout_constraintBottom_toTopOf="@id/btn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text" />

    <Button
        android:id="@+id/btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Scan"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chk_scan" />

</androidx.constraintlayout.widget.ConstraintLayout>
```

### JAVA Example

```


import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import shoaa.barcodescanner.BarcodeScanner;

public class MainActivity extends AppCompatActivity {
    BarcodeScanner barcodeScanner = new BarcodeScanner(this);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        Button btn = findViewById(R.id.btn);
        TextView textView = findViewById(R.id.text);
        ImageView imageView = findViewById(R.id.image);
        CheckBox checkBox = findViewById(R.id.chk_scan);
        btn.setOnClickListener(view -> {
            barcodeScanner.setScanCallback(barcodeResult -> {
                textView.setText(barcodeResult.getBarCode());
                imageView.setImageBitmap(BitmapFactory.decodeFile(barcodeResult.getImagePath()));
            });
            barcodeScanner.start(checkBox.isChecked());
        });
    }
}
```
    
