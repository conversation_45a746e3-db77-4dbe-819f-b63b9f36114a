<component name="libraryTable">
  <library name="Gradle: androidx.exifinterface:exifinterface:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c632dcc2ee8d705a787167937074b3e9/transformed/exifinterface-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/01fe1563f58616312e56a7eeb6315e38/transformed/exifinterface-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/fe9bfff7ea4df3fd2b414c256eb796da/transformed/exifinterface-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/adc3f1e665dce3159e14777be10d35e5/transformed/exifinterface-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c632dcc2ee8d705a787167937074b3e9/transformed/exifinterface-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/51e0f128a281c831d32c9b8e2cb77469/transformed/exifinterface-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c632dcc2ee8d705a787167937074b3e9/transformed/exifinterface-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c632dcc2ee8d705a787167937074b3e9/transformed/exifinterface-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.exifinterface/exifinterface/1.0.0/cb592500decea684137bd17587a92eee4e2568e/exifinterface-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>