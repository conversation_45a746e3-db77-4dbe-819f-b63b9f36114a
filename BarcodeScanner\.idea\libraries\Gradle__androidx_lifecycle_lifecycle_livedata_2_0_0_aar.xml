<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-livedata:2.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6c2fdf027ba6f280ad834c1bec807bc6/transformed/lifecycle-livedata-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c0311a51ae3683bf9991d5be2be53683/transformed/lifecycle-livedata-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e539b4d9013e0978f31490965cee4f8b/transformed/lifecycle-livedata-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4fd06172c4a78ba44037bad889d0843d/transformed/lifecycle-livedata-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ac0bbc85ce85434be70ff2924322c44a/transformed/lifecycle-livedata-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4fd06172c4a78ba44037bad889d0843d/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/4fd06172c4a78ba44037bad889d0843d/transformed/lifecycle-livedata-2.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-livedata/2.0.0/740ce61935bd789380c01178bd8ce402402ebd2f/lifecycle-livedata-2.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>