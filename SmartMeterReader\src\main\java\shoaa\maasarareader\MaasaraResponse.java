package shoaa.maasarareader;

import androidx.annotation.NonNull;

import shoaa.smartmeterreader.ReadingResponse;

/**
 * Created by Islam Darwish
 */

public class MaasaraResponse extends ReadingResponse {
    private String meterType = "0";
    private String meter_ID = "0";
    private String customer_ID = "0";
    private String cardID = "0";
    private String fw_version = "0";
    private String activityType = "0";
    private String curent_Power_factor = "0";
    private String last_year_Power_factor = "0";
    private String installing_technican_code = "0";
    private String installing_Date_and_time = "0";
    private String meter_Date = "0";
    private String meter_Time = "0";
    private String current_tarrif_installing_code = "0";
    private String item_13_Current_tarrif_installing = "0";
    private String item_13_Current_tarrif_installing_price = "0";
    private String current_tariff_activation_date = "0";
    private String meter_status = "0";
    private String relay_status = "0";
    private String battery_status = "0";
    private String top_cover_status = "0";
    private String side_cover_status = "0";
    private String covers_status = "0";
    private String technical_events = "0";
    private String recharge_number = "0";
    private String recharge_Amount = "0";
    private String last_recharge_Date = "0";
    private String last_recharge_Time = "0";
    private String remaining_credit_kw = "0";
    private String remaining_credit_mony = "0";
    private String debts = "0";
    private String total_consumption_kw = "0";
    private String total_consumption_mony = "0";
    private String total_consumption_kvar = "0";
    private String current_Demand = "0";
    private String maximum_Demand = "0";
    private String maximum_Demand_date = "0";
    private String instanteneous_volt = "0";
    private String instanteneous_current_Phase_Ampere = "0";
    private String instanteneous_current_Neutral = "0";
    private String reverse_Kwh = "0";
    private String unbalance_Kwh = "0";
    private String current_month_consumption_KW = "0";
    private String current_month_consumption_MONY = "0";
    private String month_1_consumption_kWh = "0";
    private String month_2_consumption_kWh = "0";
    private String month_3_consumption_kWh = "0";
    private String month_4_consumption_kWh = "0";
    private String month_5_consumption_kWh = "0";
    private String month_6_consumption_kWh = "0";
    private String month_7_consumption_kWh = "0";
    private String month_8_consumption_kWh = "0";
    private String month_9_consumption_kWh = "0";
    private String month_10_consumption_kWh = "0";
    private String month_11_consumption_kWh = "0";
    private String month_12_consumption_kWh = "0";
    private String month_1_consumption_Mony = "0";
    private String month_2_consumption_Mony = "0";
    private String month_3_consumption_Mony = "0";
    private String month_4_consumption_Mony = "0";
    private String month_5_consumption_Mony = "0";
    private String month_6_consumption_Mony = "0";
    private String month_7_consumption_Mony = "0";
    private String month_8_consumption_Mony = "0";
    private String month_9_consumption_Mony = "0";
    private String month_10_consumption_Mony = "0";
    private String month_11_consumption_Mony = "0";
    private String month_12_consumption_Mony = "0";
    private String maxim_demand_month_1 = "0";
    private String maxim_demand_month_2 = "0";
    private String maxim_demand_month_3 = "0";
    private String maxim_demand_month_4 = "0";
    private String maxim_demand_month_5 = "0";
    private String maxim_demand_month_6 = "0";
    private String maxim_demand_month_7 = "0";
    private String maxim_demand_month_8 = "0";
    private String maxim_demand_month_9 = "0";
    private String maxim_demand_month_10 = "0";
    private String maxim_demand_month_11 = "0";
    private String maxim_demand_month_12 = "0";
    private String maxim_demand_month_Date_1 = "0";
    private String maxim_demand_month_Date_2 = "0";
    private String maxim_demand_month_Date_3 = "0";
    private String maxim_demand_month_Date_4 = "0";
    private String maxim_demand_month_Date_5 = "0";
    private String maxim_demand_month_Date_6 = "0";
    private String maxim_demand_month_Date_7 = "0";
    private String maxim_demand_month_Date_8 = "0";
    private String maxim_demand_month_Date_9 = "0";
    private String maxim_demand_month_Date_10 = "0";
    private String maxim_demand_month_Date_11 = "0";
    private String maxim_demand_month_Date_12 = "0";
    private String kvar_consumption_month_1 = "0";
    private String kvar_consumption_month_2 = "0";
    private String kvar_consumption_month_3 = "0";
    private String kvar_consumption_month_4 = "0";
    private String kvar_consumption_month_5 = "0";
    private String kvar_consumption_month_6 = "0";
    private String kvar_consumption_month_7 = "0";
    private String kvar_consumption_month_8 = "0";
    private String kvar_consumption_month_9 = "0";
    private String kvar_consumption_month_10 = "0";
    private String kvar_consumption_month_11 = "0";
    private String kvar_consumption_month_12 = "0";

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public String getMeter_ID() {
        return meter_ID;
    }

    public void setMeter_ID(String meter_ID) {
        this.meter_ID = meter_ID;
    }

    public String getCustomer_ID() {
        return customer_ID;
    }

    public void setCustomer_ID(String customer_ID) {
        this.customer_ID = customer_ID;
    }

    public String getCardID() {
        return cardID;
    }

    public void setCardID(String cardID) {
        this.cardID = cardID;
    }

    public String getFw_version() {
        return fw_version;
    }

    public void setFw_version(String fw_version) {
        this.fw_version = fw_version;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getCurent_Power_factor() {
        return curent_Power_factor;
    }

    public void setCurent_Power_factor(String curent_Power_factor) {
        this.curent_Power_factor = curent_Power_factor;
    }

    public String getLast_year_Power_factor() {
        return last_year_Power_factor;
    }

    public void setLast_year_Power_factor(String last_year_Power_factor) {
        this.last_year_Power_factor = last_year_Power_factor;
    }

    public String getInstalling_technican_code() {
        return installing_technican_code;
    }

    public void setInstalling_technican_code(String installing_technican_code) {
        this.installing_technican_code = installing_technican_code;
    }

    public String getInstalling_Date_and_time() {
        return installing_Date_and_time;
    }

    public void setInstalling_Date_and_time(String installing_Date_and_time) {
        this.installing_Date_and_time = installing_Date_and_time;
    }

    public String getItem_13_Current_tarrif_installing() {
        return item_13_Current_tarrif_installing;
    }

    public void setItem_13_Current_tarrif_installing(String item_13_Current_tarrif_installing) {
        this.item_13_Current_tarrif_installing = item_13_Current_tarrif_installing;
    }

    public String getItem_13_Current_tarrif_installing_price() {
        return item_13_Current_tarrif_installing_price;
    }

    public void setItem_13_Current_tarrif_installing_price(String item_13_Current_tarrif_installing_price) {
        this.item_13_Current_tarrif_installing_price = item_13_Current_tarrif_installing_price;
    }

    public String getMeter_Date() {
        return meter_Date;
    }

    public void setMeter_Date(String meter_Date) {
        this.meter_Date = meter_Date;
    }

    public String getMeter_Time() {
        return meter_Time;
    }

    public void setMeter_Time(String meter_Time) {
        this.meter_Time = meter_Time;
    }

    public String getCurrent_tarrif_installing_code() {
        return current_tarrif_installing_code;
    }

    public void setCurrent_tarrif_installing_code(String current_tarrif_installing_code) {
        this.current_tarrif_installing_code = current_tarrif_installing_code;
    }

    public String getCurrent_tariff_activation_date() {
        return current_tariff_activation_date;
    }

    public void setCurrent_tariff_activation_date(String current_tariff_activation_date) {
        this.current_tariff_activation_date = current_tariff_activation_date;
    }

    public String getMeter_status() {
        return meter_status;
    }

    public void setMeter_status(String meter_status) {
        this.meter_status = meter_status;
    }

    public String getRelay_status() {
        return relay_status;
    }

    public void setRelay_status(String relay_status) {
        this.relay_status = relay_status;
    }

    public String getBattery_status() {
        return battery_status;
    }

    public void setBattery_status(String battery_status) {
        this.battery_status = battery_status;
    }

    public String getCovers_status() {
        return covers_status;
    }

    public void setCovers_status(String covers_status) {
        this.covers_status = covers_status;
    }

    public String getTop_cover_status() {
        return top_cover_status;
    }

    public void setTop_cover_status(String top_cover_status) {
        this.top_cover_status = top_cover_status;
    }

    public String getSide_cover_status() {
        return side_cover_status;
    }

    public void setSide_cover_status(String side_cover_status) {
        this.side_cover_status = side_cover_status;
    }

    public String getTechnical_events() {
        return technical_events;
    }

    public void setTechnical_events(String technical_events) {
        this.technical_events = technical_events;
    }

    public String getRecharge_number() {
        return recharge_number;
    }

    public void setRecharge_number(String recharge_number) {
        this.recharge_number = recharge_number;
    }

    public String getRecharge_Amount() {
        return recharge_Amount;
    }

    public void setRecharge_Amount(String recharge_Amount) {
        this.recharge_Amount = recharge_Amount;
    }

    public String getLast_recharge_Date() {
        return last_recharge_Date;
    }

    public void setLast_recharge_Date(String last_recharge_Date) {
        this.last_recharge_Date = last_recharge_Date;
    }

    public String getLast_recharge_Time() {
        return last_recharge_Time;
    }

    public void setLast_recharge_Time(String last_recharge_Time) {
        this.last_recharge_Time = last_recharge_Time;
    }

    public String getRemaining_credit_kw() {
        return remaining_credit_kw;
    }

    public void setRemaining_credit_kw(String remaining_credit_kw) {
        this.remaining_credit_kw = remaining_credit_kw;
    }

    public String getRemaining_credit_mony() {
        return remaining_credit_mony;
    }

    public void setRemaining_credit_mony(String remaining_credit_mony) {
        this.remaining_credit_mony = remaining_credit_mony;
    }

    public String getDebts() {
        return debts;
    }

    public void setDebts(String debts) {
        this.debts = debts;
    }

    public String getTotal_consumption_kw() {
        return total_consumption_kw;
    }

    public void setTotal_consumption_kw(String total_consumption_kw) {
        this.total_consumption_kw = total_consumption_kw;
    }

    public String getTotal_consumption_mony() {
        return total_consumption_mony;
    }

    public void setTotal_consumption_mony(String total_consumption_mony) {
        this.total_consumption_mony = total_consumption_mony;
    }

    public String getTotal_consumption_kvar() {
        return total_consumption_kvar;
    }

    public void setTotal_consumption_kvar(String total_consumption_kvar) {
        this.total_consumption_kvar = total_consumption_kvar;
    }

    public String getCurrent_Demand() {
        return current_Demand;
    }

    public void setCurrent_Demand(String current_Demand) {
        this.current_Demand = current_Demand;
    }

    public String getMaximum_Demand() {
        return maximum_Demand;
    }

    public void setMaximum_Demand(String maximum_Demand) {
        this.maximum_Demand = maximum_Demand;
    }

    public String getMaximum_Demand_date() {
        return maximum_Demand_date;
    }

    public void setMaximum_Demand_date(String maximum_Demand_date) {
        this.maximum_Demand_date = maximum_Demand_date;
    }

    public String getInstanteneous_volt() {
        return instanteneous_volt;
    }

    public void setInstanteneous_volt(String instanteneous_volt) {
        this.instanteneous_volt = instanteneous_volt;
    }

    public String getInstanteneous_current_Phase_Ampere() {
        return instanteneous_current_Phase_Ampere;
    }

    public void setInstanteneous_current_Phase_Ampere(String instanteneous_current_Phase_Ampere) {
        this.instanteneous_current_Phase_Ampere = instanteneous_current_Phase_Ampere;
    }

    public String getInstanteneous_current_Neutral() {
        return instanteneous_current_Neutral;
    }

    public void setInstanteneous_current_Neutral(String instanteneous_current_Neutral) {
        this.instanteneous_current_Neutral = instanteneous_current_Neutral;
    }

    public String getReverse_Kwh() {
        return reverse_Kwh;
    }

    public void setReverse_Kwh(String reverse_Kwh) {
        this.reverse_Kwh = reverse_Kwh;
    }

    public String getUnbalance_Kwh() {
        return unbalance_Kwh;
    }

    public void setUnbalance_Kwh(String unbalance_Kwh) {
        this.unbalance_Kwh = unbalance_Kwh;
    }

    public String getCurrent_month_consumption_KW() {
        return current_month_consumption_KW;
    }

    public void setCurrent_month_consumption_KW(String current_month_consumption_KW) {
        this.current_month_consumption_KW = current_month_consumption_KW;
    }

    public String getCurrent_month_consumption_MONY() {
        return current_month_consumption_MONY;
    }

    public void setCurrent_month_consumption_MONY(String current_month_consumption_MONY) {
        this.current_month_consumption_MONY = current_month_consumption_MONY;
    }

    public String getMonth_1_consumption_kWh() {
        return month_1_consumption_kWh;
    }

    public void setMonth_1_consumption_kWh(String month_1_consumption_kWh) {
        this.month_1_consumption_kWh = month_1_consumption_kWh;
    }

    public String getMonth_2_consumption_kWh() {
        return month_2_consumption_kWh;
    }

    public void setMonth_2_consumption_kWh(String month_2_consumption_kWh) {
        this.month_2_consumption_kWh = month_2_consumption_kWh;
    }

    public String getMonth_3_consumption_kWh() {
        return month_3_consumption_kWh;
    }

    public void setMonth_3_consumption_kWh(String month_3_consumption_kWh) {
        this.month_3_consumption_kWh = month_3_consumption_kWh;
    }

    public String getMonth_4_consumption_kWh() {
        return month_4_consumption_kWh;
    }

    public void setMonth_4_consumption_kWh(String month_4_consumption_kWh) {
        this.month_4_consumption_kWh = month_4_consumption_kWh;
    }

    public String getMonth_5_consumption_kWh() {
        return month_5_consumption_kWh;
    }

    public void setMonth_5_consumption_kWh(String month_5_consumption_kWh) {
        this.month_5_consumption_kWh = month_5_consumption_kWh;
    }

    public String getMonth_6_consumption_kWh() {
        return month_6_consumption_kWh;
    }

    public void setMonth_6_consumption_kWh(String month_6_consumption_kWh) {
        this.month_6_consumption_kWh = month_6_consumption_kWh;
    }

    public String getMonth_7_consumption_kWh() {
        return month_7_consumption_kWh;
    }

    public void setMonth_7_consumption_kWh(String month_7_consumption_kWh) {
        this.month_7_consumption_kWh = month_7_consumption_kWh;
    }

    public String getMonth_8_consumption_kWh() {
        return month_8_consumption_kWh;
    }

    public void setMonth_8_consumption_kWh(String month_8_consumption_kWh) {
        this.month_8_consumption_kWh = month_8_consumption_kWh;
    }

    public String getMonth_9_consumption_kWh() {
        return month_9_consumption_kWh;
    }

    public void setMonth_9_consumption_kWh(String month_9_consumption_kWh) {
        this.month_9_consumption_kWh = month_9_consumption_kWh;
    }

    public String getMonth_10_consumption_kWh() {
        return month_10_consumption_kWh;
    }

    public void setMonth_10_consumption_kWh(String month_10_consumption_kWh) {
        this.month_10_consumption_kWh = month_10_consumption_kWh;
    }

    public String getMonth_11_consumption_kWh() {
        return month_11_consumption_kWh;
    }

    public void setMonth_11_consumption_kWh(String month_11_consumption_kWh) {
        this.month_11_consumption_kWh = month_11_consumption_kWh;
    }

    public String getMonth_12_consumption_kWh() {
        return month_12_consumption_kWh;
    }

    public void setMonth_12_consumption_kWh(String month_12_consumption_kWh) {
        this.month_12_consumption_kWh = month_12_consumption_kWh;
    }

    public String getMonth_1_consumption_Mony() {
        return month_1_consumption_Mony;
    }

    public void setMonth_1_consumption_Mony(String month_1_consumption_Mony) {
        this.month_1_consumption_Mony = month_1_consumption_Mony;
    }

    public String getMonth_2_consumption_Mony() {
        return month_2_consumption_Mony;
    }

    public void setMonth_2_consumption_Mony(String month_2_consumption_Mony) {
        this.month_2_consumption_Mony = month_2_consumption_Mony;
    }

    public String getMonth_3_consumption_Mony() {
        return month_3_consumption_Mony;
    }

    public void setMonth_3_consumption_Mony(String month_3_consumption_Mony) {
        this.month_3_consumption_Mony = month_3_consumption_Mony;
    }

    public String getMonth_4_consumption_Mony() {
        return month_4_consumption_Mony;
    }

    public void setMonth_4_consumption_Mony(String month_4_consumption_Mony) {
        this.month_4_consumption_Mony = month_4_consumption_Mony;
    }

    public String getMonth_5_consumption_Mony() {
        return month_5_consumption_Mony;
    }

    public void setMonth_5_consumption_Mony(String month_5_consumption_Mony) {
        this.month_5_consumption_Mony = month_5_consumption_Mony;
    }

    public String getMonth_6_consumption_Mony() {
        return month_6_consumption_Mony;
    }

    public void setMonth_6_consumption_Mony(String month_6_consumption_Mony) {
        this.month_6_consumption_Mony = month_6_consumption_Mony;
    }

    public String getMonth_7_consumption_Mony() {
        return month_7_consumption_Mony;
    }

    public void setMonth_7_consumption_Mony(String month_7_consumption_Mony) {
        this.month_7_consumption_Mony = month_7_consumption_Mony;
    }

    public String getMonth_8_consumption_Mony() {
        return month_8_consumption_Mony;
    }

    public void setMonth_8_consumption_Mony(String month_8_consumption_Mony) {
        this.month_8_consumption_Mony = month_8_consumption_Mony;
    }

    public String getMonth_9_consumption_Mony() {
        return month_9_consumption_Mony;
    }

    public void setMonth_9_consumption_Mony(String month_9_consumption_Mony) {
        this.month_9_consumption_Mony = month_9_consumption_Mony;
    }

    public String getMonth_10_consumption_Mony() {
        return month_10_consumption_Mony;
    }

    public void setMonth_10_consumption_Mony(String month_10_consumption_Mony) {
        this.month_10_consumption_Mony = month_10_consumption_Mony;
    }

    public String getMonth_11_consumption_Mony() {
        return month_11_consumption_Mony;
    }

    public void setMonth_11_consumption_Mony(String month_11_consumption_Mony) {
        this.month_11_consumption_Mony = month_11_consumption_Mony;
    }

    public String getMonth_12_consumption_Mony() {
        return month_12_consumption_Mony;
    }

    public void setMonth_12_consumption_Mony(String month_12_consumption_Mony) {
        this.month_12_consumption_Mony = month_12_consumption_Mony;
    }

    public String getMaxim_demand_month_1() {
        return maxim_demand_month_1;
    }

    public void setMaxim_demand_month_1(String maxim_demand_month_1) {
        this.maxim_demand_month_1 = maxim_demand_month_1;
    }

    public String getMaxim_demand_month_2() {
        return maxim_demand_month_2;
    }

    public void setMaxim_demand_month_2(String maxim_demand_month_2) {
        this.maxim_demand_month_2 = maxim_demand_month_2;
    }

    public String getMaxim_demand_month_3() {
        return maxim_demand_month_3;
    }

    public void setMaxim_demand_month_3(String maxim_demand_month_3) {
        this.maxim_demand_month_3 = maxim_demand_month_3;
    }

    public String getMaxim_demand_month_4() {
        return maxim_demand_month_4;
    }

    public void setMaxim_demand_month_4(String maxim_demand_month_4) {
        this.maxim_demand_month_4 = maxim_demand_month_4;
    }

    public String getMaxim_demand_month_5() {
        return maxim_demand_month_5;
    }

    public void setMaxim_demand_month_5(String maxim_demand_month_5) {
        this.maxim_demand_month_5 = maxim_demand_month_5;
    }

    public String getMaxim_demand_month_6() {
        return maxim_demand_month_6;
    }

    public void setMaxim_demand_month_6(String maxim_demand_month_6) {
        this.maxim_demand_month_6 = maxim_demand_month_6;
    }

    public String getMaxim_demand_month_7() {
        return maxim_demand_month_7;
    }

    public void setMaxim_demand_month_7(String maxim_demand_month_7) {
        this.maxim_demand_month_7 = maxim_demand_month_7;
    }

    public String getMaxim_demand_month_8() {
        return maxim_demand_month_8;
    }

    public void setMaxim_demand_month_8(String maxim_demand_month_8) {
        this.maxim_demand_month_8 = maxim_demand_month_8;
    }

    public String getMaxim_demand_month_9() {
        return maxim_demand_month_9;
    }

    public void setMaxim_demand_month_9(String maxim_demand_month_9) {
        this.maxim_demand_month_9 = maxim_demand_month_9;
    }

    public String getMaxim_demand_month_10() {
        return maxim_demand_month_10;
    }

    public void setMaxim_demand_month_10(String maxim_demand_month_10) {
        this.maxim_demand_month_10 = maxim_demand_month_10;
    }

    public String getMaxim_demand_month_11() {
        return maxim_demand_month_11;
    }

    public void setMaxim_demand_month_11(String maxim_demand_month_11) {
        this.maxim_demand_month_11 = maxim_demand_month_11;
    }

    public String getMaxim_demand_month_12() {
        return maxim_demand_month_12;
    }

    public void setMaxim_demand_month_12(String maxim_demand_month_12) {
        this.maxim_demand_month_12 = maxim_demand_month_12;
    }

    public String getMaxim_demand_month_Date_1() {
        return maxim_demand_month_Date_1;
    }

    public void setMaxim_demand_month_Date_1(String maxim_demand_month_Date_1) {
        this.maxim_demand_month_Date_1 = maxim_demand_month_Date_1;
    }

    public String getMaxim_demand_month_Date_2() {
        return maxim_demand_month_Date_2;
    }

    public void setMaxim_demand_month_Date_2(String maxim_demand_month_Date_2) {
        this.maxim_demand_month_Date_2 = maxim_demand_month_Date_2;
    }

    public String getMaxim_demand_month_Date_3() {
        return maxim_demand_month_Date_3;
    }

    public void setMaxim_demand_month_Date_3(String maxim_demand_month_Date_3) {
        this.maxim_demand_month_Date_3 = maxim_demand_month_Date_3;
    }

    public String getMaxim_demand_month_Date_4() {
        return maxim_demand_month_Date_4;
    }

    public void setMaxim_demand_month_Date_4(String maxim_demand_month_Date_4) {
        this.maxim_demand_month_Date_4 = maxim_demand_month_Date_4;
    }

    public String getMaxim_demand_month_Date_5() {
        return maxim_demand_month_Date_5;
    }

    public void setMaxim_demand_month_Date_5(String maxim_demand_month_Date_5) {
        this.maxim_demand_month_Date_5 = maxim_demand_month_Date_5;
    }

    public String getMaxim_demand_month_Date_6() {
        return maxim_demand_month_Date_6;
    }

    public void setMaxim_demand_month_Date_6(String maxim_demand_month_Date_6) {
        this.maxim_demand_month_Date_6 = maxim_demand_month_Date_6;
    }

    public String getMaxim_demand_month_Date_7() {
        return maxim_demand_month_Date_7;
    }

    public void setMaxim_demand_month_Date_7(String maxim_demand_month_Date_7) {
        this.maxim_demand_month_Date_7 = maxim_demand_month_Date_7;
    }

    public String getMaxim_demand_month_Date_8() {
        return maxim_demand_month_Date_8;
    }

    public void setMaxim_demand_month_Date_8(String maxim_demand_month_Date_8) {
        this.maxim_demand_month_Date_8 = maxim_demand_month_Date_8;
    }

    public String getMaxim_demand_month_Date_9() {
        return maxim_demand_month_Date_9;
    }

    public void setMaxim_demand_month_Date_9(String maxim_demand_month_Date_9) {
        this.maxim_demand_month_Date_9 = maxim_demand_month_Date_9;
    }

    public String getMaxim_demand_month_Date_10() {
        return maxim_demand_month_Date_10;
    }

    public void setMaxim_demand_month_Date_10(String maxim_demand_month_Date_10) {
        this.maxim_demand_month_Date_10 = maxim_demand_month_Date_10;
    }

    public String getMaxim_demand_month_Date_11() {
        return maxim_demand_month_Date_11;
    }

    public void setMaxim_demand_month_Date_11(String maxim_demand_month_Date_11) {
        this.maxim_demand_month_Date_11 = maxim_demand_month_Date_11;
    }

    public String getMaxim_demand_month_Date_12() {
        return maxim_demand_month_Date_12;
    }

    public void setMaxim_demand_month_Date_12(String maxim_demand_month_Date_12) {
        this.maxim_demand_month_Date_12 = maxim_demand_month_Date_12;
    }

    public String getKvar_consumption_month_1() {
        return kvar_consumption_month_1;
    }

    public void setKvar_consumption_month_1(String kvar_consumption_month_1) {
        this.kvar_consumption_month_1 = kvar_consumption_month_1;
    }

    public String getKvar_consumption_month_2() {
        return kvar_consumption_month_2;
    }

    public void setKvar_consumption_month_2(String kvar_consumption_month_2) {
        this.kvar_consumption_month_2 = kvar_consumption_month_2;
    }

    public String getKvar_consumption_month_3() {
        return kvar_consumption_month_3;
    }

    public void setKvar_consumption_month_3(String kvar_consumption_month_3) {
        this.kvar_consumption_month_3 = kvar_consumption_month_3;
    }

    public String getKvar_consumption_month_4() {
        return kvar_consumption_month_4;
    }

    public void setKvar_consumption_month_4(String kvar_consumption_month_4) {
        this.kvar_consumption_month_4 = kvar_consumption_month_4;
    }

    public String getKvar_consumption_month_5() {
        return kvar_consumption_month_5;
    }

    public void setKvar_consumption_month_5(String kvar_consumption_month_5) {
        this.kvar_consumption_month_5 = kvar_consumption_month_5;
    }

    public String getKvar_consumption_month_6() {
        return kvar_consumption_month_6;
    }

    public void setKvar_consumption_month_6(String kvar_consumption_month_6) {
        this.kvar_consumption_month_6 = kvar_consumption_month_6;
    }

    public String getKvar_consumption_month_7() {
        return kvar_consumption_month_7;
    }

    public void setKvar_consumption_month_7(String kvar_consumption_month_7) {
        this.kvar_consumption_month_7 = kvar_consumption_month_7;
    }

    public String getKvar_consumption_month_8() {
        return kvar_consumption_month_8;
    }

    public void setKvar_consumption_month_8(String kvar_consumption_month_8) {
        this.kvar_consumption_month_8 = kvar_consumption_month_8;
    }

    public String getKvar_consumption_month_9() {
        return kvar_consumption_month_9;
    }

    public void setKvar_consumption_month_9(String kvar_consumption_month_9) {
        this.kvar_consumption_month_9 = kvar_consumption_month_9;
    }

    public String getKvar_consumption_month_10() {
        return kvar_consumption_month_10;
    }

    public void setKvar_consumption_month_10(String kvar_consumption_month_10) {
        this.kvar_consumption_month_10 = kvar_consumption_month_10;
    }

    public String getKvar_consumption_month_11() {
        return kvar_consumption_month_11;
    }

    public void setKvar_consumption_month_11(String kvar_consumption_month_11) {
        this.kvar_consumption_month_11 = kvar_consumption_month_11;
    }

    public String getKvar_consumption_month_12() {
        return kvar_consumption_month_12;
    }

    public void setKvar_consumption_month_12(String kvar_consumption_month_12) {
        this.kvar_consumption_month_12 = kvar_consumption_month_12;
    }

    @Override
    public String toString() {
        return "MaasaraResponse : " + "\n" +
                "meterType : " + meterType + "\n" +
                "meter_ID : " + meter_ID + "\n" +
                "customer_ID : " + customer_ID + "\n" +
                "cardID : " + cardID + "\n" +
                "fw_version : " + fw_version + "\n" +
                "activityType : " + activityType + "\n" +
                "curent_Power_factor : " + curent_Power_factor + "\n" +
                "last_year_Power_factor : " + last_year_Power_factor + "\n" +
                "installing_technican_code : " + installing_technican_code + "\n" +
                "installing_Date_and_time : " + installing_Date_and_time + "\n" +
                "meter_Date : " + meter_Date + "\n" +
                "meter_Time : " + meter_Time + "\n" +
                "current_tarrif_installing_code : " + current_tarrif_installing_code + "\n" +
                "item_13_Current_tarrif_installing : " + item_13_Current_tarrif_installing + "\n" +
                "item_13_Current_tarrif_installing_price : " + item_13_Current_tarrif_installing_price + "\n" +
                "current_tariff_activation_date : " + current_tariff_activation_date + "\n" +
                "meter_status : " + meter_status + "\n" +
                "relay_status : " + relay_status + "\n" +
                "battery_status : " + battery_status + "\n" +
                "top_cover_status : " + top_cover_status + "\n" +
                "side_cover_status : " + side_cover_status + "\n" +
                "covers_status : " + covers_status + "\n" +
                "technical_events : " + technical_events + "\n" +
                "recharge_number : " + recharge_number + "\n" +
                "recharge_Amount : " + recharge_Amount + "\n" +
                "last_recharge_Date : " + last_recharge_Date + "\n" +
                "last_recharge_Time : " + last_recharge_Time + "\n" +
                "remaining_credit_kw : " + remaining_credit_kw + "\n" +
                "remaining_credit_mony : " + remaining_credit_mony + "\n" +
                "debts : " + debts + "\n" +
                "total_consumption_kw : " + total_consumption_kw + "\n" +
                "total_consumption_mony : " + total_consumption_mony + "\n" +
                "total_consumption_kvar : " + total_consumption_kvar + "\n" +
                "current_Demand : " + current_Demand + "\n" +
                "maximum_Demand : " + maximum_Demand + "\n" +
                "maximum_Demand_date : " + maximum_Demand_date + "\n" +
                "instanteneous_volt : " + instanteneous_volt + "\n" +
                "instanteneous_current_Phase_Ampere : " + instanteneous_current_Phase_Ampere + "\n" +
                "instanteneous_current_Neutral : " + instanteneous_current_Neutral + "\n" +
                "reverse_Kwh : " + reverse_Kwh + "\n" +
                "unbalance_Kwh : " + unbalance_Kwh + "\n" +
                "current_month_consumption_KW : " + current_month_consumption_KW + "\n" +
                "current_month_consumption_MONY : " + current_month_consumption_MONY + "\n" +
                "month_1_consumption_kWh : " + month_1_consumption_kWh + "\n" +
                "month_2_consumption_kWh : " + month_2_consumption_kWh + "\n" +
                "month_3_consumption_kWh : " + month_3_consumption_kWh + "\n" +
                "month_4_consumption_kWh : " + month_4_consumption_kWh + "\n" +
                "month_5_consumption_kWh : " + month_5_consumption_kWh + "\n" +
                "month_6_consumption_kWh : " + month_6_consumption_kWh + "\n" +
                "month_7_consumption_kWh : " + month_7_consumption_kWh + "\n" +
                "month_8_consumption_kWh : " + month_8_consumption_kWh + "\n" +
                "month_9_consumption_kWh : " + month_9_consumption_kWh + "\n" +
                "month_10_consumption_kWh : " + month_10_consumption_kWh + "\n" +
                "month_11_consumption_kWh : " + month_11_consumption_kWh + "\n" +
                "month_12_consumption_kWh : " + month_12_consumption_kWh + "\n" +
                "month_1_consumption_Mony : " + month_1_consumption_Mony + "\n" +
                "month_2_consumption_Mony : " + month_2_consumption_Mony + "\n" +
                "month_3_consumption_Mony : " + month_3_consumption_Mony + "\n" +
                "month_4_consumption_Mony : " + month_4_consumption_Mony + "\n" +
                "month_5_consumption_Mony : " + month_5_consumption_Mony + "\n" +
                "month_6_consumption_Mony : " + month_6_consumption_Mony + "\n" +
                "month_7_consumption_Mony : " + month_7_consumption_Mony + "\n" +
                "month_8_consumption_Mony : " + month_8_consumption_Mony + "\n" +
                "month_9_consumption_Mony : " + month_9_consumption_Mony + "\n" +
                "month_10_consumption_Mony : " + month_10_consumption_Mony + "\n" +
                "month_11_consumption_Mony : " + month_11_consumption_Mony + "\n" +
                "month_12_consumption_Mony : " + month_12_consumption_Mony + "\n" +
                "maxim_demand_month_1 : " + maxim_demand_month_1 + "\n" +
                "maxim_demand_month_2 : " + maxim_demand_month_2 + "\n" +
                "maxim_demand_month_3 : " + maxim_demand_month_3 + "\n" +
                "maxim_demand_month_4 : " + maxim_demand_month_4 + "\n" +
                "maxim_demand_month_5 : " + maxim_demand_month_5 + "\n" +
                "maxim_demand_month_6 : " + maxim_demand_month_6 + "\n" +
                "maxim_demand_month_7 : " + maxim_demand_month_7 + "\n" +
                "maxim_demand_month_8 : " + maxim_demand_month_8 + "\n" +
                "maxim_demand_month_9 : " + maxim_demand_month_9 + "\n" +
                "maxim_demand_month_10 : " + maxim_demand_month_10 + "\n" +
                "maxim_demand_month_11 : " + maxim_demand_month_11 + "\n" +
                "maxim_demand_month_12 : " + maxim_demand_month_12 + "\n" +
                "maxim_demand_month_Date_1 : " + maxim_demand_month_Date_1 + "\n" +
                "maxim_demand_month_Date_2 : " + maxim_demand_month_Date_2 + "\n" +
                "maxim_demand_month_Date_3 : " + maxim_demand_month_Date_3 + "\n" +
                "maxim_demand_month_Date_4 : " + maxim_demand_month_Date_4 + "\n" +
                "maxim_demand_month_Date_5 : " + maxim_demand_month_Date_5 + "\n" +
                "maxim_demand_month_Date_6 : " + maxim_demand_month_Date_6 + "\n" +
                "maxim_demand_month_Date_7 : " + maxim_demand_month_Date_7 + "\n" +
                "maxim_demand_month_Date_8 : " + maxim_demand_month_Date_8 + "\n" +
                "maxim_demand_month_Date_9 : " + maxim_demand_month_Date_9 + "\n" +
                "maxim_demand_month_Date_10 : " + maxim_demand_month_Date_10 + "\n" +
                "maxim_demand_month_Date_11 : " + maxim_demand_month_Date_11 + "\n" +
                "maxim_demand_month_Date_12 : " + maxim_demand_month_Date_12 + "\n" +
                "kvar_consumption_month_1 : " + kvar_consumption_month_1 + "\n" +
                "kvar_consumption_month_2 : " + kvar_consumption_month_2 + "\n" +
                "kvar_consumption_month_3 : " + kvar_consumption_month_3 + "\n" +
                "kvar_consumption_month_4 : " + kvar_consumption_month_4 + "\n" +
                "kvar_consumption_month_5 : " + kvar_consumption_month_5 + "\n" +
                "kvar_consumption_month_6 : " + kvar_consumption_month_6 + "\n" +
                "kvar_consumption_month_7 : " + kvar_consumption_month_7 + "\n" +
                "kvar_consumption_month_8 : " + kvar_consumption_month_8 + "\n" +
                "kvar_consumption_month_9 : " + kvar_consumption_month_9 + "\n" +
                "kvar_consumption_month_10 : " + kvar_consumption_month_10 + "\n" +
                "kvar_consumption_month_11 : " + kvar_consumption_month_11 + "\n" +
                "kvar_consumption_month_12 : " + kvar_consumption_month_12;
    }

    @NonNull
    @Override
    public String toFileFormate() {
        return "meterType\t" + meterType + "\n" +
                "meter_ID\t" + meter_ID + "\n" +
                "customer_ID\t" + customer_ID + "\n" +
                "cardID\t" + cardID + "\n" +
                "fw_version\t" + fw_version + "\n" +
                "activityType\t" + activityType + "\n" +
                "curent_Power_factor\t" + curent_Power_factor + "\n" +
                "last_year_Power_factor\t" + last_year_Power_factor + "\n" +
                "installing_technican_code\t" + installing_technican_code + "\n" +
                "installing_Date_and_time\t" + installing_Date_and_time + "\n" +
                "meter_Date\t" + meter_Date + "\n" +
                "meter_Time\t" + meter_Time + "\n" +
                "current_tarrif_installing_code\t" + current_tarrif_installing_code + "\n" +
                "item_13_Current_tarrif_installing\t" + item_13_Current_tarrif_installing + "\n" +
                "item_13_Current_tarrif_installing_price\t" + item_13_Current_tarrif_installing_price + "\n" +
                "current_tariff_activation_date\t" + current_tariff_activation_date + "\n" +
                "meter_status\t" + meter_status + "\n" +
                "relay_status\t" + relay_status + "\n" +
                "battery_status\t" + battery_status + "\n" +
                "top_cover_status\t" + top_cover_status + "\n" +
                "side_cover_status\t" + side_cover_status + "\n" +
                "covers_status\t" + covers_status + "\n" +
                "technical_events\t" + technical_events + "\n" +
                "recharge_number\t" + recharge_number + "\n" +
                "recharge_Amount\t" + recharge_Amount + "\n" +
                "last_recharge_Date\t" + last_recharge_Date + "\n" +
                "last_recharge_Time\t" + last_recharge_Time + "\n" +
                "remaining_credit_kw\t" + remaining_credit_kw + "\n" +
                "remaining_credit_mony\t" + remaining_credit_mony + "\n" +
                "debts\t" + debts + "\n" +
                "total_consumption_kw\t" + total_consumption_kw + "\n" +
                "total_consumption_mony\t" + total_consumption_mony + "\n" +
                "total_consumption_kvar\t" + total_consumption_kvar + "\n" +
                "current_Demand\t" + current_Demand + "\n" +
                "maximum_Demand\t" + maximum_Demand + "\n" +
                "maximum_Demand_date\t" + maximum_Demand_date + "\n" +
                "instanteneous_volt\t" + instanteneous_volt + "\n" +
                "instanteneous_current_Phase_Ampere\t" + instanteneous_current_Phase_Ampere + "\n" +
                "instanteneous_current_Neutral\t" + instanteneous_current_Neutral + "\n" +
                "reverse_Kwh\t" + reverse_Kwh + "\n" +
                "unbalance_Kwh\t" + unbalance_Kwh + "\n" +
                "current_month_consumption_KW\t" + current_month_consumption_KW + "\n" +
                "current_month_consumption_MONY\t" + current_month_consumption_MONY + "\n" +
                "month_1_consumption_kWh\t" + month_1_consumption_kWh + "\n" +
                "month_2_consumption_kWh\t" + month_2_consumption_kWh + "\n" +
                "month_3_consumption_kWh\t" + month_3_consumption_kWh + "\n" +
                "month_4_consumption_kWh\t" + month_4_consumption_kWh + "\n" +
                "month_5_consumption_kWh\t" + month_5_consumption_kWh + "\n" +
                "month_6_consumption_kWh\t" + month_6_consumption_kWh + "\n" +
                "month_7_consumption_kWh\t" + month_7_consumption_kWh + "\n" +
                "month_8_consumption_kWh\t" + month_8_consumption_kWh + "\n" +
                "month_9_consumption_kWh\t" + month_9_consumption_kWh + "\n" +
                "month_10_consumption_kWh\t" + month_10_consumption_kWh + "\n" +
                "month_11_consumption_kWh\t" + month_11_consumption_kWh + "\n" +
                "month_12_consumption_kWh\t" + month_12_consumption_kWh + "\n" +
                "month_1_consumption_Mony\t" + month_1_consumption_Mony + "\n" +
                "month_2_consumption_Mony\t" + month_2_consumption_Mony + "\n" +
                "month_3_consumption_Mony\t" + month_3_consumption_Mony + "\n" +
                "month_4_consumption_Mony\t" + month_4_consumption_Mony + "\n" +
                "month_5_consumption_Mony\t" + month_5_consumption_Mony + "\n" +
                "month_6_consumption_Mony\t" + month_6_consumption_Mony + "\n" +
                "month_7_consumption_Mony\t" + month_7_consumption_Mony + "\n" +
                "month_8_consumption_Mony\t" + month_8_consumption_Mony + "\n" +
                "month_9_consumption_Mony\t" + month_9_consumption_Mony + "\n" +
                "month_10_consumption_Mony\t" + month_10_consumption_Mony + "\n" +
                "month_11_consumption_Mony\t" + month_11_consumption_Mony + "\n" +
                "month_12_consumption_Mony\t" + month_12_consumption_Mony + "\n" +
                "maxim_demand_month_1\t" + maxim_demand_month_1 + "\n" +
                "maxim_demand_month_2\t" + maxim_demand_month_2 + "\n" +
                "maxim_demand_month_3\t" + maxim_demand_month_3 + "\n" +
                "maxim_demand_month_4\t" + maxim_demand_month_4 + "\n" +
                "maxim_demand_month_5\t" + maxim_demand_month_5 + "\n" +
                "maxim_demand_month_6\t" + maxim_demand_month_6 + "\n" +
                "maxim_demand_month_7\t" + maxim_demand_month_7 + "\n" +
                "maxim_demand_month_8\t" + maxim_demand_month_8 + "\n" +
                "maxim_demand_month_9\t" + maxim_demand_month_9 + "\n" +
                "maxim_demand_month_10\t" + maxim_demand_month_10 + "\n" +
                "maxim_demand_month_11\t" + maxim_demand_month_11 + "\n" +
                "maxim_demand_month_12\t" + maxim_demand_month_12 + "\n" +
                "maxim_demand_month_Date_1\t" + maxim_demand_month_Date_1 + "\n" +
                "maxim_demand_month_Date_2\t" + maxim_demand_month_Date_2 + "\n" +
                "maxim_demand_month_Date_3\t" + maxim_demand_month_Date_3 + "\n" +
                "maxim_demand_month_Date_4\t" + maxim_demand_month_Date_4 + "\n" +
                "maxim_demand_month_Date_5\t" + maxim_demand_month_Date_5 + "\n" +
                "maxim_demand_month_Date_6\t" + maxim_demand_month_Date_6 + "\n" +
                "maxim_demand_month_Date_7\t" + maxim_demand_month_Date_7 + "\n" +
                "maxim_demand_month_Date_8\t" + maxim_demand_month_Date_8 + "\n" +
                "maxim_demand_month_Date_9\t" + maxim_demand_month_Date_9 + "\n" +
                "maxim_demand_month_Date_10\t" + maxim_demand_month_Date_10 + "\n" +
                "maxim_demand_month_Date_11\t" + maxim_demand_month_Date_11 + "\n" +
                "maxim_demand_month_Date_12\t" + maxim_demand_month_Date_12 + "\n" +
                "kvar_consumption_month_1\t" + kvar_consumption_month_1 + "\n" +
                "kvar_consumption_month_2\t" + kvar_consumption_month_2 + "\n" +
                "kvar_consumption_month_3\t" + kvar_consumption_month_3 + "\n" +
                "kvar_consumption_month_4\t" + kvar_consumption_month_4 + "\n" +
                "kvar_consumption_month_5\t" + kvar_consumption_month_5 + "\n" +
                "kvar_consumption_month_6\t" + kvar_consumption_month_6 + "\n" +
                "kvar_consumption_month_7\t" + kvar_consumption_month_7 + "\n" +
                "kvar_consumption_month_8\t" + kvar_consumption_month_8 + "\n" +
                "kvar_consumption_month_9\t" + kvar_consumption_month_9 + "\n" +
                "kvar_consumption_month_10\t" + kvar_consumption_month_10 + "\n" +
                "kvar_consumption_month_11\t" + kvar_consumption_month_11 + "\n" +
                "kvar_consumption_month_12\t" + kvar_consumption_month_12;
    }
}
