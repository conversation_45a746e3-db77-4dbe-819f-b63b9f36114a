<component name="libraryTable">
  <library name="Gradle: com.google.android.gms:play-services-tasks:17.2.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b77ac9aff896362cdf3aa2fbfb142fa1/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3d249c73a12a61407750a71e493ff419/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e1b15d597761f36d78a9a215532dab32/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7b7b5418d44f1efb3742162516e62cf7/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/14411fb757823f15d4a62b8588dd283f/transformed/jetified-play-services-tasks-17.2.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7b7b5418d44f1efb3742162516e62cf7/transformed/jetified-play-services-tasks-17.2.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/7b7b5418d44f1efb3742162516e62cf7/transformed/jetified-play-services-tasks-17.2.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.gms/play-services-tasks/17.2.1/ef19d8e16a762897c399d10edef4ef3772200155/play-services-tasks-17.2.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>