<component name="libraryTable">
  <library name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/21055fdc0b6ac8b40ee9a0107f761b38/transformed/lifecycle-viewmodel-2.3.1/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/21055fdc0b6ac8b40ee9a0107f761b38/transformed/lifecycle-viewmodel-2.3.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/21055fdc0b6ac8b40ee9a0107f761b38/transformed/lifecycle-viewmodel-2.3.1/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-viewmodel/2.3.1/55d6fa3541ca02167b0bd62a16fbdaec2a71622/lifecycle-viewmodel-2.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>