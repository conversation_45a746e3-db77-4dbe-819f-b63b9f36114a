package shoaa.connectionmanager.bluetooth

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.NotificationCompat
import java.io.IOException
import java.util.LinkedList
import java.util.Queue

/**
 * Created by Islam Darwish
 */
class SerialService : Service(), SerialListener {
    private var channel: NotificationChannel? = null
    private var mainLooper: Handler? = null
    private var queue1: Queue<QueueItem>? = null
    private var queue2: Queue<QueueItem>? = null
    private var socket: SerialSocket? = null
    private var listener: SerialListener? = null

    ////////////////
    private var connected = false
    private var binder: IBinder? = null

    @SuppressLint("DiscouragedApi")
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        mainLooper = Handler(Looper.getMainLooper())
        binder = SerialBinder()
        queue1 = LinkedList()
        queue2 = LinkedList()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            val channelName = "Bluetooth"
            val channelID = "Optical Reader"
            channel = NotificationChannel(
                channelID,
                channelName, NotificationManager.IMPORTANCE_NONE
            )
            channel!!.description = "bluetooth service"
            manager.createNotificationChannel(channel!!)
            val notificationBuilder = NotificationCompat.Builder(this, channelID)
            var drawableResourceId =
                resources.getIdentifier("ic_launcher_main", "mipmap", this.packageName)
            if (drawableResourceId == 0) {
                drawableResourceId =
                    resources.getIdentifier("ic_launcher", "mipmap", this.packageName)
            }
            val notification = notificationBuilder
                .setOngoing(true)
                .setOnlyAlertOnce(true)
                .setAutoCancel(false)
                .setContentIntent(null)
                .setContentTitle("Optical Smart Reader")
                .setSmallIcon(drawableResourceId)
                .setPriority(NotificationManager.IMPORTANCE_NONE)
                .build()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                startForeground(
                    100,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE
                )
            } else {
                startForeground(100, notification)
            }
        }
        return START_STICKY
    }

    /**
     * Lifecylce
     */
    override fun onDestroy() {
        cancelNotification()
        disconnect()
        super.onDestroy()
    }

    override fun onRebind(intent: Intent) {
        super.onRebind(intent)
    }

    override fun onUnbind(intent: Intent): Boolean {
        return super.onUnbind(intent)
    }

    override fun onBind(intent: Intent): IBinder? {
        return binder
    }

    /**
     * Api
     */
    fun connect(socket: SerialSocket?) {
        socket!!.connect(this)
        this.socket = socket
        connected = true
    }

    fun disconnect() {
        connected = false // ignore data,errors while disconnecting
        cancelNotification()
        if (socket != null) {
            socket!!.disconnect()
            socket = null
        }
    }

    @Throws(IOException::class)
    fun write(data: ByteArray) {
        if (!connected) throw IOException("not connected")
        socket!!.write(data)
    }

    fun attach(listener: SerialListener) {
        require(Looper.getMainLooper().thread === Thread.currentThread()) { "not in main thread" }
        // use synchronized() to prevent new items in queue2
        // new items will not be added to queue1 because mainLooper.post and attach() run in main thread
        synchronized(this) { this.listener = listener }
        for (item in queue1!!) {
            when (item.type) {
                QueueType.Connect -> listener.onSerialConnect()
                QueueType.ConnectError -> listener.onSerialConnectError(item.e)
                QueueType.Read -> listener.onSerialRead(item.data)
                QueueType.IoError -> listener.onSerialIoError(item.e)
            }
        }
        for (item in queue2!!) {
            when (item.type) {
                QueueType.Connect -> listener.onSerialConnect()
                QueueType.ConnectError -> listener.onSerialConnectError(item.e)
                QueueType.Read -> listener.onSerialRead(item.data)
                QueueType.IoError -> listener.onSerialIoError(item.e)
            }
        }
        queue1!!.clear()
        queue2!!.clear()
    }

    fun detach() {
        // items already in event queue (posted before detach() to mainLooper) will end up in queue1
        // items occurring later, will be moved directly to queue2
        // detach() and mainLooper.post run in the main thread, so all items are caught
        listener = null
    }

    private fun cancelNotification() {
        stopForeground(STOP_FOREGROUND_DETACH)
    }

    /**
     * SerialListener
     */
    override fun onSerialConnect() {
        if (connected) {
            synchronized(this) {
                if (listener != null) {
                    mainLooper!!.post {
                        if (listener != null) {
                            listener!!.onSerialConnect()
                        } else {
                            queue1!!.add(QueueItem(QueueType.Connect, null, null))
                        }
                    }
                } else {
                    queue2!!.add(QueueItem(QueueType.Connect, null, null))
                }
            }
        }
    }

    override fun onSerialConnectError(e: Exception?) {
        if (connected) {
            synchronized(this) {
                if (listener != null) {
                    mainLooper!!.post {
                        if (listener != null) {
                            listener!!.onSerialConnectError(e)
                        } else {
                            queue1!!.add(QueueItem(QueueType.ConnectError, null, e))
                            cancelNotification()
                            disconnect()
                        }
                    }
                } else {
                    queue2!!.add(QueueItem(QueueType.ConnectError, null, e))
                    cancelNotification()
                    disconnect()
                }
            }
        }
    }

    override fun onSerialRead(data: ByteArray?) {
        if (connected) {
            synchronized(this) {
                if (listener != null) {
                    mainLooper!!.post {
                        if (listener != null) {
                            listener!!.onSerialRead(data)
                        } else {
                            queue1!!.add(QueueItem(QueueType.Read, data, null))
                        }
                    }
                } else {
                    queue2!!.add(QueueItem(QueueType.Read, data, null))
                }
            }
        }
    }

    override fun onSerialIoError(e: Exception?) {
        if (connected) {
            synchronized(this) {
                if (listener != null) {
                    mainLooper!!.post {
                        if (listener != null) {
                            listener!!.onSerialIoError(e)
                        } else {
                            queue1!!.add(QueueItem(QueueType.IoError, null, e))
                            cancelNotification()
                            disconnect()
                        }
                    }
                } else {
                    queue2!!.add(QueueItem(QueueType.IoError, null, e))
                    cancelNotification()
                    disconnect()
                }
            }
        }
    }

    private enum class QueueType {
        Connect,
        ConnectError,
        Read,
        IoError
    }

    private class QueueItem internal constructor(
        var type: QueueType,
        var data: ByteArray?,
        var e: Exception?
    )

    internal inner class SerialBinder : Binder() {
        val service: SerialService
            get() = this@SerialService
    }
}
