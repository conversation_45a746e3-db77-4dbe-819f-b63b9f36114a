package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/DlmsCosemItem.class */
public class DlmsCosemItem {
    private int cosemClass;
    private String cosemObis;
    private int serviceIndex;
    private String name;
    private DlmsData data;

    public DlmsCosemItem(int cosemClass, String cosemObis, int serviceIndex, String name) {
        this.cosemClass = cosemClass;
        this.cosemObis = cosemObis;
        this.serviceIndex = serviceIndex;
        this.name = name;
    }

    public DlmsData getData() {
        return this.data;
    }

    public void setData(DlmsData data) {
        this.data = data;
    }

    public int getCosemClass() {
        return this.cosemClass;
    }

    public void setCosemClass(int cosemClass) {
        this.cosemClass = cosemClass;
    }

    public int getServiceIndex() {
        return this.serviceIndex;
    }

    public void setServiceIndex(int serviceIndex) {
        this.serviceIndex = serviceIndex;
    }

    public String getCosemObis() {
        return this.cosemObis;
    }

    public void setCosemObis(String cosemObis) {
        this.cosemObis = cosemObis;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DlmsCosemItem other = (DlmsCosemItem) o;
        if (this.cosemClass == other.cosemClass && this.serviceIndex == other.serviceIndex) {
            return true;
        }
        return false;
    }
}
