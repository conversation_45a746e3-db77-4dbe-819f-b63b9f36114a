flowchart LR
    subgraph "sendAsync 标准流程"
        A1[sendAsync调用] --> A2[初始化参数<br/>retryCount, sendTimeOut, readTimeOut]
        A2 --> A3[调用send方法]
        A3 --> A4[发送数据]
        A4 --> A5[等待响应开始<br/>固定sendTimeOut时间]
        A5 --> A6{收到响应?}
        A6 -->|否| A7[重试]
        A6 -->|是| A8[等待响应完成<br/>固定readTimeOut时间]
        A8 --> A9[返回所有接收数据]
        A7 --> A4
    end
    
    subgraph "sendWithLength 增强流程"
        B1[sendWithLengthAsync调用] --> B2[初始化参数<br/>retryCount, sendTimeOut, expectedLength]
        B2 --> B3[动态设置readTimeOut<br/>800ms或2000ms]
        B3 --> B4[外层重试循环]
        B4 --> B5[调用sendWithLength方法]
        B5 --> B6[发送数据]
        B6 --> B7[等待响应开始<br/>sendTimeOut时间]
        B7 --> B8{收到响应?}
        B8 -->|否| B9[内层重试]
        B8 -->|是| B10[智能长度检测]
        
        B10 --> B11{expectedLength类型}
        B11 -->|预设长度| B12[使用预设expectedLength]
        B11 -->|动态计算| B13[a]
        
        B12 --> B14[等待完整数据包]
        B13 --> B14
        B14 --> B15{数据完整性检查}
        B15 -->|不完整| B16[内层重试]
        B15 -->|完整| B17[长度验证]
        B17 --> B18[返回精确长度数据]
        
        B9 --> B5
        B16 --> B5
        B4 --> B19{外层重试条件}
        B19 -->|数据不完整 AND 未超限| B5
        B19 -->|完整或超限| B18
    end
    
    subgraph "关键技术差异"
        C1[sendAsync特点] --> C2[简单超时机制]
        C2 --> C3[固定等待时间]
        C3 --> C4[返回所有接收数据]
        C4 --> C5[适用于不确定长度的响应]
        
        D1[sendWithLength特点] --> D2[智能长度检测]
        D2 --> D3[动态超时调整]
        D3 --> D4[精确数据包边界]
        D4 --> D5[双层重试机制]
        D5 --> D6[适用于固定协议格式]
    end
    
    subgraph "数据包长度计算逻辑"
        E1[动态长度计算] --> E2{packet.size > 1?}
        E2 -->|是| E3[读取packet- 低字节]
        E3 --> E4[读取packet - 高字节]
        E4 --> E5[计算: ]
        E5 --> E6[添加协议头尾: +6字节]
        E6 --> E7[得到完整数据包长度]
        
        E2 -->|否| E8[继续等待更多数据]
        E8 --> E2
    end
    
    subgraph "超时策略对比"
        F1[sendAsync超时] --> F2[sendTimeOut: 发送后等待响应开始]
        F2 --> F3[readTimeOut: 响应开始后等待完成]
        F3 --> F4[固定时间，不考虑数据长度]
        
        F5[sendWithLength超时] --> F6[sendTimeOut: 等待响应开始]
        F6 --> F7[readTimeOut: 根据expectedLength动态调整]
        F7 --> F8[800ms已知长度 或 2000ms未知长度]
        F8 --> F9[基于数据完整性判断]
    end
    
    subgraph "应用场景"
        G1[sendAsync适用场景] --> G2[探测性命令]
        G2 --> G3[响应长度不确定]
        G3 --> G4[简单的请求-响应]
        
        G5[sendWithLength适用场景] --> G6[电表数据读取]
        G6 --> G7[固定协议格式]
        G7 --> G8[需要完整数据包]
        G8 --> G9[对数据完整性要求高]
    end
    
    style A1 fill:#e3f2fd
    style B1 fill:#e8f5e8
    style B10 fill:#fff3e0
    style B13 fill:#fff3e0
    style E5 fill:#fff3e0
    style F8 fill:#fff3e0
    
    classDef standardFlow fill:#e3f2fd,stroke:#1976d2
    classDef enhancedFlow fill:#e8f5e8,stroke:#388e3c
    classDef intelligentFeature fill:#fff3e0,stroke:#f57c00
    classDef comparison fill:#f3e5f5,stroke:#7b1fa2
    classDef scenario fill:#fce4ec,stroke:#e91e63
    
    class A1,A2,A3,A4,A5,A6,A7,A8,A9 standardFlow
    class B1,B2,B3,B4,B5,B6,B7,B8,B9,B14,B15,B16,B17,B18,B19 enhancedFlow
    class B10,B11,B12,B13,E1,E2,E3,E4,E5,E6,E7,E8,F5,F6,F7,F8,F9 intelligentFeature
    class C1,C2,C3,C4,C5,D1,D2,D3,D4,D5,D6,F1,F2,F3,F4 comparison
    class G1,G2,G3,G4,G5,G6,G7,G8,G9 scenario