package shoaa.maasarareader;

import static shoaa.common.Utils.writeStringAsFile;

import android.content.Context;

import com.google.gson.Gson;
import com.sanxing.facade.MaasraHelper;
import com.sanxing.model.Constant;
import com.sanxing.model.Request;
import com.sanxing.model.Response;

import java.util.Locale;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionType;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;

/**
 * Created by Islam Darwish
 */

public class MaasaraReader {
    private static MaasraHelper maasraHelper = new MaasraHelper();
    private static Response response = null;
    private static Request request = null;
    private static String requestString = null;
    private static String responseString = null;
    private static String sendApdu = null;
    private static String res = null;
    private static int sendErrorCount = 0;
    private static String session = "";

    public static ReadingResponse read(Context context, ProgressCallback progressCallback) {
        try {
            session = "";
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                    MaasaraResponse readingResponse = new MaasaraResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
            }
            progressCallback.update(1);
            MaasaraResponse readingResponse = new MaasaraResponse();
            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;

            progressCallback.update(1);
            ConnectionManager.Companion.getInstance().sendAsyncNoRes("42617564547261722C393630302C4E2C382C300D0A");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }            

            if (ConnectionManager.Companion.getInstance().getConnectionType() == ConnectionType.BLUETOOTH ? ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1) : ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_300_7_E_1)) {
                int tryCount = 0;
                while (tryCount < 3) {
                    tryCount++;
                    String handshake1 = maasraHelper.handle("{\"service\":\"handshake\",\"pdu\":null,\"item\":null}");
                    response = new Gson().fromJson(handshake1, Response.class);
                    while (response.getResult().equalsIgnoreCase("continue")) {
                        String resPdu = response.getPdu();
                        res = ConnectionManager.Companion.getInstance().sendAsync(resPdu, 2, Common.TIME_OUT_1200, Common.TIME_OUT_500);
                        if (res.length() == 12 && res.startsWith("06") && res.toUpperCase(Locale.ROOT).endsWith("0D0A")) {
                            res = "063235320D0A";
                            if (ConnectionManager.Companion.getInstance().getConnectionType() != ConnectionType.BLUETOOTH) {
                                ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1);
                            }
                        }
                        if (resPdu.length() == 12 && resPdu.startsWith("06") && resPdu.toUpperCase(Locale.ROOT).endsWith("0D0A")) {
                            res = "063235320D0A";
                            if (ConnectionManager.Companion.getInstance().getConnectionType() != ConnectionType.BLUETOOTH) {
                                ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1);
                            }
                        }
                        String request = "{\"service\":\"handshake\",\"pdu\":\"" + res + "\",\"item\":null}";
                        String handshake2 = maasraHelper.handle(request);
                        response = (new Gson().fromJson(handshake2, Response.class));
                    }
                    if (response.getResult().equalsIgnoreCase("success"))
                        break;
                    if (tryCount < 3) {
                        maasraHelper = new MaasraHelper();
                        Thread.sleep(5000);
                    }
                }
                if (response == null || response.getResult() == null || !response.getResult().equalsIgnoreCase("success")) {
                    throw new Exception("handleShake failed.....");
                }
                response = null;
                request = null;
                requestString = null;
                responseString = null;
                sendApdu = null;
                res = null;
                progressCallback.update(5);
                if (ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1)) {
                    sendErrorCount = 0;
                    progressCallback.update(6);
                    //region item_1_MeterType
//                    String itemResponse = getItem( MAASARA_METER_DATA_ITEMS.item_1_MeterType);
//                    if (itemResponse != null && !itemResponse.isEmpty()) {
//                        readingResponse.setMeterType(Integer.parseInt(itemResponse) == 0 ? "Single" : "3Phase");
//                    }
                    readingResponse.setMeterType("Single");
                    //endregion

                    progressCallback.update(7);
                    //region item_2_Meter_ID
                    String itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_2_Meter_ID);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMeter_ID(itemResponse);
                    }
                    //endregion

                    progressCallback.update(8);
                    //region item_3_Customer_ID
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_3_Customer_ID);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCustomer_ID(itemResponse);
                    }
                    //endregion

                    progressCallback.update(9);
                    //region item_4_CardID
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_4_CardID);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCardID(itemResponse);
                    }
                    //endregion

                    progressCallback.update(10);
//                    //region item_5_fw_version
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_5_fw_version);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setFw_version(itemResponse);
                    }
//                    //endregion
                    progressCallback.update(11);
//                    //region item_6_ActivityType
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_6_ActivityType);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setActivityType(itemResponse);
                    }
//                    //endregion
                    progressCallback.update(12);
                    //region item_7_curent_Power_factor
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_7_curent_Power_factor);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCurent_Power_factor(itemResponse);
                    }
                    //endregion
                    progressCallback.update(13);
                    //region item_8_last_year_Power_factor
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_8_last_year_Power_factor);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setLast_year_Power_factor(itemResponse);
                    }
                    //endregion
                    progressCallback.update(14);
//                    //region item_9_installing_technican_code
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_9_installing_technican_code_date);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        String dateTime = itemResponse.substring(0, itemResponse.lastIndexOf(":"));
                        String code = itemResponse.substring(itemResponse.lastIndexOf(":") + 1);
                        readingResponse.setInstalling_Date_and_time(dateTime);
                        readingResponse.setInstalling_technican_code(code);
                    }
//                    //endregion
                    progressCallback.update(15);
                    //region item_10_Meter_Date
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_10_Meter_Date);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMeter_Date(itemResponse);
                    }
                    //endregion
                    progressCallback.update(17);
                    //region item_11_Meter_Time
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_11_Meter_Time);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMeter_Time(itemResponse);
                    }
                    //endregion
                    progressCallback.update(18);
                    //region item_12_Current_tarrif_installing_code
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_12_Current_tarrif_installing_code);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCurrent_tarrif_installing_code(itemResponse);
                    }
                    //endregion
                    //region item_13_Current_tarrif_installing
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_13_Current_tarrif_installing);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setItem_13_Current_tarrif_installing(itemResponse);
                    }
                    //endregion
                    //region item_14_Current_tarrif_installing_price
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_14_Current_tarrif_installing_price);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setItem_13_Current_tarrif_installing_price(itemResponse);
                    }
                    //endregion
                    progressCallback.update(19);
                    //region item_15_Current_tariff_activation_date
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_15_Current_tariff_activation_date);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCurrent_tariff_activation_date(itemResponse);
                    }
                    //endregion

                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;
                    progressCallback.update(20);
                    //region item_16_Meter_status
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_16_Meter_status);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMeter_status(itemResponse);
                    }
                    //endregion
                    progressCallback.update(21);
                    //region item_17_Relay_status
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_17_Relay_status);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setRelay_status(Integer.parseInt(itemResponse) == 0 ? "1" : "0");
                    }
                    //endregion
                    progressCallback.update(22);
                    //region item_18_battery_status
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_18_battery_status);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setBattery_status(itemResponse);
                    }
                    //endregion
                    progressCallback.update(23);
                    //region item_19_cover_status
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_19_cover_status);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCovers_status(itemResponse);
                    }

                    if (itemResponse != null && itemResponse.length() > 6) {
                        String[] statusList = itemResponse.split(",");
                        for (String s : statusList) {
                            try {
                                int code = Integer.parseInt(s.substring(0, s.indexOf(":")));
                                if (code == 1) {
                                    readingResponse.setTop_cover_status("1");
                                    readingResponse.setMeter_status("1");
                                } else if (code == 2) {
                                    readingResponse.setSide_cover_status("1");
                                    readingResponse.setMeter_status("1");
                                }
                            } catch (Exception ignored) {
                            }
                        }
                    }

                    //endregion
                    progressCallback.update(24);
                    //region item_20_Technical_events
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_20_Technical_events);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setTechnical_events(itemResponse);
                    }
                    //endregion
                    progressCallback.update(25);
                    //region item_21_recharge_number
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_21_recharge_number);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setRecharge_number(itemResponse);
                    }
                    //endregion
                    progressCallback.update(26);
                    //region item_22_Recharge_Amount
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_22_Recharge_Amount);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setRecharge_Amount(itemResponse);
                    }
                    //endregion
                    progressCallback.update(27);
                    //region item_23_Last_recharge_Date
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_23_Last_recharge_Date);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setLast_recharge_Date(itemResponse);
                    }
                    //endregion
                    progressCallback.update(28);
                    //region item_24_Last_recharge_Time
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_24_Last_recharge_Time);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setLast_recharge_Time(itemResponse);
                    }
                    //endregion
                    progressCallback.update(29);
                    //region item_25_remaining_credit_kw
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_25_remaining_credit_kw);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setRemaining_credit_kw(itemResponse);
                    }
                    //endregion
                    progressCallback.update(30);
                    //region item_26_remaining_credit_mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_26_remaining_credit_mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setRemaining_credit_mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(31);
                    //region item_27_Debts
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_27_Debts);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setDebts(itemResponse);
                    }
                    //endregion
                    progressCallback.update(32);
                    //region item_28_Total_consumption_kw
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_28_Total_consumption_kw);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setTotal_consumption_kw(itemResponse);
                    }
                    //endregion
                    progressCallback.update(33);
                    //region item_29_Total_consumption_mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_29_Total_consumption_mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setTotal_consumption_mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(34);
                    //region item_30_Total_consumption_kvar
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_30_Total_consumption_kvar);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setTotal_consumption_kvar(itemResponse);
                    }
                    //endregion
                    progressCallback.update(35);
                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;
                    //region item_31_Current_Demand
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_31_Current_Demand);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCurrent_Demand(itemResponse);
                    }
                    //endregion


                    progressCallback.update(36);
                    //region item_32_Maximum_Demand
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_32_Maximum_Demand);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMaximum_Demand(itemResponse);
                    }
                    //endregion
                    progressCallback.update(37);
                    //region item_33_Maximum_Demand_date
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_33_Maximum_Demand_date);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMaximum_Demand_date(itemResponse);
                    }
                    //endregion
                    progressCallback.update(38);
                    //region item_34_instanteneous_volt
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_34_instanteneous_volt);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setInstanteneous_volt(itemResponse);
                    }
                    //endregion
                    progressCallback.update(39);
                    //region item_35_instanteneous_current_Phase_Ampere
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_35_instanteneous_current_Phase_Ampere);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setInstanteneous_current_Phase_Ampere(itemResponse);
                    }
                    //endregion
                    progressCallback.update(40);
                    //region item_36_instanteneous_current_Neutral
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_36_instanteneous_current_Neutral);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setInstanteneous_current_Neutral(itemResponse);
                    }
                    //endregion
                    progressCallback.update(41);
                    //region item_37_reverse_Kwh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_37_reverse_Kwh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setReverse_Kwh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(42);
                    //region item_38_unbalance_Kwh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_38_unbalance_Kwh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setUnbalance_Kwh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(43);
                    //region item_39_current_month_consumption_KW
                    double con1 = 0;
                    double con = 0;
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_39_current_month_consumption_KW);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        try {
                            con = Double.parseDouble(itemResponse);
                        } catch (Exception e) {
                        }
                    }
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_39_current_month_consumption_KW1);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        try {
                            con1 = Double.parseDouble(itemResponse);
                        } catch (Exception e) {
                        }
                    }
                    readingResponse.setCurrent_month_consumption_KW(String.valueOf(con + con1));
                    //endregion
                    progressCallback.update(44);
                    //region item_40_current_month_consumption_MONY
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_40_current_month_consumption_MONY);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setCurrent_month_consumption_MONY(itemResponse);
                    }
                    //endregion
                    progressCallback.update(45);
                    //region item_41_month_1_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_41_month_1_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_1_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(46);
                    //region item_42_month_2_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_42_month_2_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_2_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(47);
                    //region item_43_month_3_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_43_month_3_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_3_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(48);
                    //region item_44_month_4_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_44_month_4_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_4_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(49);
                    //region item_45_month_5_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_45_month_5_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_5_consumption_kWh(itemResponse);
                    }
                    //endregion
                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;
                    progressCallback.update(50);
                    //region item_46_month_6_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_46_month_6_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_6_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(51);
                    //region item_47_month_7_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_47_month_7_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_7_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(52);
                    //region item_48_month_8_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_48_month_8_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_8_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(53);
                    //region item_49_month_9_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_49_month_9_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_9_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(54);
                    //region item_50_month_10_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_50_month_10_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_10_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(55);
                    //region item_51_month_11_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_51_month_11_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_11_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(56);
                    //region item_52_month_12_consumption_kWh
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_52_month_12_consumption_kWh);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_12_consumption_kWh(itemResponse);
                    }
                    //endregion
                    progressCallback.update(57);
                    //region item_53_month_1_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_53_month_1_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_1_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(58);
                    //region item_54_month_2_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_54_month_2_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_2_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(59);
                    //region item_55_month_3_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_55_month_3_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_3_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(60);
                    //region item_56_month_4_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_56_month_4_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_4_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(61);
                    //region item_57_month_5_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_57_month_5_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_5_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(62);
                    //region item_58_month_6_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_58_month_6_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_6_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(63);
                    //region item_59_month_7_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_59_month_7_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_7_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(64);
                    //region item_60_month_8_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_60_month_8_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_8_consumption_Mony(itemResponse);
                    }
                    //endregion
                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;
                    progressCallback.update(65);
                    //region item_61_month_9_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_61_month_9_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_9_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(66);
                    //region item_62_month_10_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_62_month_10_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_10_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(67);
                    //region item_63_month_11_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_63_month_11_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_11_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(68);
                    //region item_64_month_12_consumption_Mony
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_64_month_12_consumption_Mony);
                    if (itemResponse != null && !itemResponse.isEmpty()) {
                        readingResponse.setMonth_12_consumption_Mony(itemResponse);
                    }
                    //endregion
                    progressCallback.update(69);
                    if (readingResponse.getMeterType().equalsIgnoreCase("3Phase")) {
                        //region item_65_maxim_demand_month_1
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_65_maxim_demand_month_1);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_1(itemResponse);
                        }
                        //endregion
                        progressCallback.update(70);
                        //region item_66_maxim_demand_month_2
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_66_maxim_demand_month_2);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_2(itemResponse);
                        }
                        //endregion
                        progressCallback.update(71);
                        //region item_67_maxim_demand_month_3
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_67_maxim_demand_month_3);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_3(itemResponse);
                        }
                        //endregion
                        progressCallback.update(72);
                        //region item_68_maxim_demand_month_4
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_68_maxim_demand_month_4);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_4(itemResponse);
                        }
                        //endregion
                        progressCallback.update(73);
                        //region item_69_maxim_demand_month_5
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_69_maxim_demand_month_5);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_5(itemResponse);
                        }
                        //endregion
                        progressCallback.update(74);
                        //region item_70_maxim_demand_month_6
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_70_maxim_demand_month_6);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_6(itemResponse);
                        }
                        //endregion
                        progressCallback.update(75);
                        //region item_71_maxim_demand_month_7
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_71_maxim_demand_month_7);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_7(itemResponse);
                        }
                        //endregion
                        progressCallback.update(76);
                        //region item_72_maxim_demand_month_8
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_72_maxim_demand_month_8);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_8(itemResponse);
                        }
                        //endregion
                        progressCallback.update(77);
                        //region item_73_maxim_demand_month_9
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_73_maxim_demand_month_9);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_9(itemResponse);
                        }
                        //endregion
                        progressCallback.update(78);
                        //region item_74_maxim_demand_month_10
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_74_maxim_demand_month_10);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_10(itemResponse);
                        }
                        //endregion
                        progressCallback.update(79);
                        //region item_75_maxim_demand_month_11
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_75_maxim_demand_month_11);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_11(itemResponse);
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(80);
                        //region item_76_maxim_demand_month_12
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_76_maxim_demand_month_12);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_12(itemResponse);
                        }
                        //endregion
                        progressCallback.update(81);
                        //region item_77_maxim_demand_month_Date_1
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_77_maxim_demand_month_Date_1);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_1(itemResponse);
                        }
                        //endregion
                        progressCallback.update(82);
                        //region item_78_maxim_demand_month_Date_2
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_78_maxim_demand_month_Date_2);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_2(itemResponse);
                        }
                        //endregion
                        progressCallback.update(83);
                        //region item_79_maxim_demand_month_Date_3
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_79_maxim_demand_month_Date_3);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_3(itemResponse);
                        }
                        //endregion
                        progressCallback.update(84);
                        //region item_80_maxim_demand_month_Date_4
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_80_maxim_demand_month_Date_4);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_4(itemResponse);
                        }
                        //endregion
                        progressCallback.update(85);
                        //region item_81_maxim_demand_month_Date_5
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_81_maxim_demand_month_Date_5);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_5(itemResponse);
                        }
                        //endregion
                        progressCallback.update(86);
                        //region item_82_maxim_demand_month_Date_6
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_82_maxim_demand_month_Date_6);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_6(itemResponse);
                        }
                        //endregion
                        progressCallback.update(87);
                        //region item_83_maxim_demand_month_Date_7
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_83_maxim_demand_month_Date_7);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_7(itemResponse);
                        }
                        //endregion
                        progressCallback.update(88);
                        //region item_84_maxim_demand_month_Date_8
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_84_maxim_demand_month_Date_8);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_8(itemResponse);
                        }
                        //endregion
                        progressCallback.update(89);
                        //region item_85_maxim_demand_month_Date_9
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_85_maxim_demand_month_Date_9);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_9(itemResponse);
                        }
                        //endregion
                        progressCallback.update(90);
                        //region item_86_maxim_demand_month_Date_10
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_86_maxim_demand_month_Date_10);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_10(itemResponse);
                        }
                        //endregion
                        progressCallback.update(91);
                        //region item_87_maxim_demand_month_Date_11
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_87_maxim_demand_month_Date_11);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_11(itemResponse);
                        }
                        //endregion
                        progressCallback.update(92);
                        //region item_88_maxim_demand_month_Date_12
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_88_maxim_demand_month_Date_12);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setMaxim_demand_month_Date_12(itemResponse);
                        }
                        //endregion
                        progressCallback.update(93);
                        //region item_89_kvar_consumption_month_1
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_89_kvar_consumption_month_1);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_1(itemResponse);
                        }
                        //endregion
                        progressCallback.update(94);
                        //region item_90_kvar_consumption_month_2
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_90_kvar_consumption_month_2);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_2(itemResponse);
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(95);
                        //region item_91_kvar_consumption_month_3
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_91_kvar_consumption_month_3);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_3(itemResponse);
                        }
                        //endregion
                        progressCallback.update(96);
                        //region item_92_kvar_consumption_month_4
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_92_kvar_consumption_month_4);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_4(itemResponse);
                        }
                        //endregion
                        //region item_93_kvar_consumption_month_5
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_93_kvar_consumption_month_5);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_5(itemResponse);
                        }
                        //endregion
                        progressCallback.update(97);
                        //region item_94_kvar_consumption_month_6
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_94_kvar_consumption_month_6);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_6(itemResponse);
                        }
                        //endregion
                        //region item_95_kvar_consumption_month_7
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_95_kvar_consumption_month_7);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_7(itemResponse);
                        }
                        //endregion
                        progressCallback.update(98);
                        //region item_96_kvar_consumption_month_8
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_96_kvar_consumption_month_8);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_8(itemResponse);
                        }
                        //endregion
                        //region item_97_kvar_consumption_month_9
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_97_kvar_consumption_month_9);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_9(itemResponse);
                        }
                        //endregion
                        //region item_98_kvar_consumption_month_10
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_98_kvar_consumption_month_10);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_10(itemResponse);
                        }
                        //endregion
                        progressCallback.update(99);
                        //region item_99_kvar_consumption_month_11
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_99_kvar_consumption_month_11);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_11(itemResponse);
                        }
                        //endregion
                        //region item_100_kvar_consumption_month_12
                        itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_100_kvar_consumption_month_12);
                        if (itemResponse != null && !itemResponse.isEmpty()) {
                            readingResponse.setKvar_consumption_month_12(itemResponse);
                        }
                        //endregion
                    }

                    //region check connection
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_2_Meter_ID);
                    if (itemResponse == null || itemResponse.isEmpty() || !(readingResponse.getMeter_ID().equalsIgnoreCase(itemResponse))) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        writeStringAsFile(context, session, "session.txt");
                        return readingResponse;
                    }
                    itemResponse = getItem(MAASARA_METER_DATA_ITEMS.item_3_Customer_ID);
                    if (itemResponse == null || itemResponse.isEmpty() || !(readingResponse.getCustomer_ID().equalsIgnoreCase(itemResponse))) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        writeStringAsFile(context, session, "session.txt");
                        return readingResponse;
                    }
                    //endregion
                } else {
                    release();
                    writeStringAsFile(context, session, "session.txt");
                    readingResponse.readingResult = ReadingResult.CAN_NOT_SET_BaudRate;
                    return readingResponse;
                }
            } else {
                writeStringAsFile(context, session, "session.txt");
                readingResponse.readingResult = ReadingResult.CAN_NOT_SET_BaudRate;
                return readingResponse;
            }
            //region release
            writeStringAsFile(context, session, "session.txt");
            progressCallback.update(100);
            readingResponse.readingResult = ReadingResult.SUCCESS;
            release();
            //endregion
            return readingResponse;
        } catch (Exception e) {
            release();
            writeStringAsFile(context, session, "session.txt");
            MaasaraResponse readingResponse = new MaasaraResponse();
            readingResponse.readingResult = ReadingResult.OTHER;
            readingResponse.message = e.getMessage();
            return readingResponse;
        }
    }

    private static void release() {
        request = new Request(Constant.RELEASE_SERVICE, "", null);
        requestString = new Gson().toJson(request);
        responseString = maasraHelper.handle(requestString);
        response = new Gson().fromJson(responseString, Response.class);
        sendApdu = response.getPdu();
        ConnectionManager.Companion.getInstance().sendAsync(sendApdu, 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
        maasraHelper = new MaasraHelper();
        response = new Response();
        responseString = "";
    }

    private static String getItem(String itemCode) {
        try {
            if (itemCode.equalsIgnoreCase("0"))
                return null;
            request = new Request(Constant.GET_SERVICE, null, itemCode);
            requestString = new Gson().toJson(request);
            responseString = maasraHelper.handle(requestString);
            response = new Gson().fromJson(responseString, Response.class);
            sendApdu = response.getPdu();
            session += "OBIS : " + itemCode + "\n";
            res = ConnectionManager.Companion.getInstance().sendAsync(sendApdu, 1, Common.TIME_OUT_1000, Common.TIME_OUT_300);
            session += "sendApdu : " + sendApdu + "\n";
            session += "res : " + res + "\n";
            if (res.isEmpty()) {
                sendErrorCount++;
                return null;
            }
            request = new Request(Constant.GET_SERVICE, res, itemCode);
            requestString = new Gson().toJson(request);
            responseString = maasraHelper.handle(requestString);
            response = new Gson().fromJson(responseString, Response.class);
            if (response == null || response.getValue() == null || response.getValue().isEmpty())
                return null;
            if (response.getResult().equalsIgnoreCase("success")) {
                String value = response.getValue().replaceAll(";", "_").replaceAll("\t", "_");
                session += "value : " + value + "\n\n";
                response = null;
                request = null;
                requestString = null;
                responseString = null;
                sendApdu = null;
                res = null;
                return value;
            } else {
                session += "value : error" + "\n\n";
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
}
