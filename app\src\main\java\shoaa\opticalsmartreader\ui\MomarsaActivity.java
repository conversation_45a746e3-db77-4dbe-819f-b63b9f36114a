package shoaa.opticalsmartreader.ui;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.pm.PackageManager;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

import shoaa.barcodescanner.BarcodeScanner;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.AppLocationManager;
import shoaa.opticalsmartreader.logic.CycleDate.CycleDateManager;
import shoaa.opticalsmartreader.logic.Enums;
import shoaa.opticalsmartreader.logic.MeterData.DatabaseMeterData;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;
import shoaa.smartmeterreader.ReadingResponse;

public class MomarsaActivity extends AppCompatActivity {
    public BarcodeScanner barcodeScanner = new BarcodeScanner(this);
    RadioButton rdHome;
    RadioButton rdShop;
    RadioButton rdPower;
    RadioButton rdValid;
    RadioButton rdInvalid;
    EditText etName;
    EditText etAddress;
    EditText etNumber;
    EditText etArea;
    EditText etDay;
    EditText etNote;
    ImageView imageView;
    Button btnCapture;
    Button btnSave;
    String imagePath = "";


    TextWatcher watcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && (rdInvalid.isChecked() || rdValid.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etNumber.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && etName.getText().toString().trim().length() > 3
                    && etAddress.getText().toString().trim().length() > 3
                    && !imagePath.trim().isEmpty()
                    && CycleDateManager.getCycleDate(MomarsaActivity.this) != null);
        }

        @Override
        public void afterTextChanged(Editable editable) {

        }
    };


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_momarsa);
        rdHome = findViewById(R.id.rdHome);
        rdShop = findViewById(R.id.rdShop);
        rdPower = findViewById(R.id.rdPower);
        rdValid = findViewById(R.id.rdValid);
        rdInvalid = findViewById(R.id.rdInvalid);
        etArea = findViewById(R.id.etArea);
        etDay = findViewById(R.id.etDay);
        etNumber = findViewById(R.id.etNumber);
        imageView = findViewById(R.id.image);
        btnCapture = findViewById(R.id.btnCapture);
        etNote = findViewById(R.id.etNote);
        etName = findViewById(R.id.etName);
        etAddress = findViewById(R.id.etAddress);
        btnSave = findViewById(R.id.btnSave);
        barcodeScanner.setScanCallback(barcodeResult -> {
            if (barcodeResult.getImagePath() != null && !barcodeResult.getImagePath().isEmpty()) {
                imagePath = barcodeResult.getImagePath();
                imageView.setImageBitmap(BitmapFactory.decodeFile(barcodeResult.getImagePath()));
            } else {
                imagePath = "";
                final Drawable d = ContextCompat.getDrawable(this, R.drawable.image_place_holder);
                imageView.setImageDrawable(d);
            }
            btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                    && (rdInvalid.isChecked() || rdValid.isChecked())
                    && !etArea.getText().toString().trim().isEmpty()
                    && !etNumber.getText().toString().trim().isEmpty()
                    && !etDay.getText().toString().trim().isEmpty()
                    && etName.getText().toString().trim().length() > 3
                    && etAddress.getText().toString().trim().length() > 3
                    && !imagePath.trim().isEmpty()
                    && CycleDateManager.getCycleDate(MomarsaActivity.this) != null);
        });
        btnCapture.setOnClickListener(v -> barcodeScanner.start(false, false));


        rdHome.setOnCheckedChangeListener((compoundButton, b) ->
                btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                        && (rdInvalid.isChecked() || rdValid.isChecked())
                        && !etArea.getText().toString().trim().isEmpty()
                        && !etNumber.getText().toString().trim().isEmpty()
                        && !etDay.getText().toString().trim().isEmpty()
                        && etName.getText().toString().trim().length() > 3
                        && etAddress.getText().toString().trim().length() > 3
                        && !imagePath.trim().isEmpty()
                        && CycleDateManager.getCycleDate(MomarsaActivity.this) != null));
        rdShop.setOnCheckedChangeListener((compoundButton, b) ->
                btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                        && (rdInvalid.isChecked() || rdValid.isChecked())
                        && !etArea.getText().toString().trim().isEmpty()
                        && !etNumber.getText().toString().trim().isEmpty()
                        && !etDay.getText().toString().trim().isEmpty()
                        && etName.getText().toString().trim().length() > 3
                        && etAddress.getText().toString().trim().length() > 3
                        && !imagePath.trim().isEmpty()
                        && CycleDateManager.getCycleDate(MomarsaActivity.this) != null));
        rdPower.setOnCheckedChangeListener((compoundButton, b) ->
                btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                        && (rdInvalid.isChecked() || rdValid.isChecked())
                        && !etArea.getText().toString().trim().isEmpty()
                        && !etNumber.getText().toString().trim().isEmpty()
                        && !etDay.getText().toString().trim().isEmpty()
                        && etName.getText().toString().trim().length() > 3
                        && etAddress.getText().toString().trim().length() > 3
                        && !imagePath.trim().isEmpty()
                        && CycleDateManager.getCycleDate(MomarsaActivity.this) != null));
        rdPower.setOnCheckedChangeListener((compoundButton, b) ->
                btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                        && (rdInvalid.isChecked() || rdValid.isChecked())
                        && !etArea.getText().toString().trim().isEmpty()
                        && !etNumber.getText().toString().trim().isEmpty()
                        && !etDay.getText().toString().trim().isEmpty()
                        && etName.getText().toString().trim().length() > 3
                        && etAddress.getText().toString().trim().length() > 3
                        && !imagePath.trim().isEmpty()
                        && CycleDateManager.getCycleDate(MomarsaActivity.this) != null));
        etArea.addTextChangedListener(watcher);
        etDay.addTextChangedListener(watcher);
        etNumber.addTextChangedListener(watcher);
        etName.addTextChangedListener(watcher);
        etAddress.addTextChangedListener(watcher);
        imageView.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                && (rdInvalid.isChecked() || rdValid.isChecked())
                && !etArea.getText().toString().trim().isEmpty()
                && !etNumber.getText().toString().trim().isEmpty()
                && !etDay.getText().toString().trim().isEmpty()
                && etName.getText().toString().trim().length() > 3
                && etAddress.getText().toString().trim().length() > 3
                && !imagePath.trim().isEmpty()
                && CycleDateManager.getCycleDate(MomarsaActivity.this) != null));

        btnSave.setOnClickListener(v -> {
            Random r = new Random();
            int low = 10000000;
            int high = 99999999;
            int result = r.nextInt(high - low) + low;
            long mBarcode = 14000000000L + result;
            int mReadingCode = 7;
            int mLostReadingCode = 14;
            final ArrayList<Map<String, String>> listItems = new ArrayList<>();
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        return;
                    }
                }
                for (BluetoothDevice device : bluetoothAdapter.getBondedDevices()) {
                    if (device.getType() != BluetoothDevice.DEVICE_TYPE_LE) {
                        Map<String, String> d = new HashMap<String, String>() {{
                            put("name", device.getName() != null ? device.getName() : "");
                            put("address", device.getAddress() != null ? device.getAddress() : "");
                        }};
                        listItems.add(d);
                    }
                }
            }
            listItems.sort(Comparator.comparing(stringStringMap -> Objects.requireNonNull(stringStringMap.get("name"))));
            String savedBluetooth = shoaa.opticalsmartreader.logic.Utils.sharedPreferences.getString("bluetooth", "");
            int bluetoothSerial = 0;
            String bluetoothName = "";
            if (!savedBluetooth.isEmpty()) {
                for (int i = 0; i < listItems.size(); i++) {
                    Map<String, String> bluetooth = listItems.get(i);
                    if (bluetooth.containsKey("address")) {
                        String address = bluetooth.get("address");
                        if (address != null && address.equalsIgnoreCase(savedBluetooth)) {
                            bluetoothName = bluetooth.get("name");
                        }
                    }
                }
            }
            try {
                if (bluetoothName != null && !bluetoothName.isEmpty())
                    bluetoothSerial = Integer.parseInt(bluetoothName.trim().replaceAll("\\D", ""));
            } catch (Exception ignore) {
            }
            ReadingResponse readingResponse = new ReadingResponse();
            Client selectedClient = new Client();
            if (!AppUser.getInstance(this).getBrHndsa().isEmpty()) {
                selectedClient.BrHndsa = Integer.parseInt(AppUser.getInstance(this).getBrHndsa());
            }
            selectedClient.Mntka = Utils.formatStringUIntNumber(etArea.getText().toString());
            selectedClient.Day = Utils.formatStringUIntNumber(etDay.getText().toString());
            selectedClient.Main = "0";
            selectedClient.Fary = "0";
            Enums.ActivityType activityType = Enums.ActivityType.HOME;
            if (rdPower.isChecked()) {
                activityType = Enums.ActivityType.POWER;
            } else if (rdShop.isChecked()) {
                activityType = Enums.ActivityType.SHOP;
            }
            String note = (etNote.getText().toString().trim().isEmpty() ? "لا توجد ملاحظة" : (etNote.getText().toString().trim())) + "," + etName.getText().toString().trim() + "," + etAddress.getText().toString().trim() + "," + (rdValid.isChecked() ? "ممارسة صحيحة" : "ممارسة منتهية") + "," + etNumber.getText().toString();
            DatabaseMeterData databaseMeterData = new DatabaseMeterData(this,
                    AppUser.getInstance(this),
                    selectedClient,
                    AppLocationManager.getInstance(this),
                    CycleDateManager.getCycleDate(this)
                    , mBarcode,
                    1,
                    mReadingCode,
                    mLostReadingCode,
                    0,
                    0,
                    bluetoothSerial,
                    readingResponse, activityType, note, imagePath);
            if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();
            shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().insert(databaseMeterData);
            finish();
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        btnSave.setEnabled((rdHome.isChecked() || rdShop.isChecked() || rdPower.isChecked())
                && (rdInvalid.isChecked() || rdValid.isChecked())
                && !etArea.getText().toString().trim().isEmpty()
                && !etNumber.getText().toString().trim().isEmpty()
                && !etDay.getText().toString().trim().isEmpty()
                && etName.getText().toString().trim().length() > 3
                && etAddress.getText().toString().trim().length() > 3
                && !imagePath.trim().isEmpty()
                && CycleDateManager.getCycleDate(MomarsaActivity.this) != null);
    }
}