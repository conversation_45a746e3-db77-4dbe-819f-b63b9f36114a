<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    android:padding="10dp">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/customers_data"
        android:textColor="@color/on_primary"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/handasaLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/handasaLayout"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/my_custom_background_2"
        android:orientation="horizontal"
        android:padding="2dp"
        app:layout_constraintBottom_toTopOf="@id/searchView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <Spinner
                android:id="@+id/handasaSpinner"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:theme="@style/ThemeOverlay.AppCompat.Light" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:layout_weight="1"
            android:orientation="vertical">


            <Spinner
                android:id="@+id/mantekaSpinner"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:theme="@style/ThemeOverlay.AppCompat.Light" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <Spinner
                android:id="@+id/yawmeyaSpinner"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:theme="@style/ThemeOverlay.AppCompat.Light" />
        </LinearLayout>


    </LinearLayout>

    <androidx.appcompat.widget.SearchView
        android:id="@+id/searchView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/my_custom_background"
        android:layoutDirection="rtl"
        android:textDirection="rtl"
        app:closeIcon="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/guideline"
        app:layout_constraintTop_toBottomOf="@id/handasaLayout"
        app:queryHint="@string/search"
        app:searchHintIcon="@drawable/ic_search_hint"
        app:searchIcon="@drawable/ic_search" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />


    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/my_custom_background_2"
        android:padding="2dp"
        app:layout_constraintBottom_toBottomOf="@id/searchView"
        app:layout_constraintEnd_toEndOf="@id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/searchView">

        <Spinner
            android:id="@+id/filterSpinner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:theme="@style/ThemeOverlay.AppCompat.Light" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/my_custom_background"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchView">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txtNoDataSync"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_customer_data_please_sync"
            android:textColor="@color/on_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txtNoData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_customer"
            android:textColor="@color/on_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/txtLoadingData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/loading_data_please_wait"
            android:textColor="@color/on_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txtNoResult"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_search_result"
            android:textColor="@color/on_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:visibility="gone" />
    </LinearLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/btn_momarsa"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:background="@drawable/custom_btn"
            android:elevation="8dp"
            android:text="إضافة ممارسة"
            android:textColor="@color/on_primary" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>