<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="FullScreenTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="NoActionBarTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:statusBarColor">@android:color/white</item>
    </style>

    <style name="CardShape" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">cut</item>
        <item name="cornerSize">25dp</item>
    </style>

</resources>