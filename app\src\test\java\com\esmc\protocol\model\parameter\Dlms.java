package com.esmc.protocol.model.parameter;

import com.esmc.protocol.model.AuthenticationMechanisms;
import com.esmc.protocol.model.SecurityControl;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/parameter/Dlms.class */
public class Dlms {
    private AuthenticationMechanisms authenticationMechanisms;
    private SecurityControl securityControl;

    public Dlms(AuthenticationMechanisms auth, SecurityControl sh) {
        this.authenticationMechanisms = auth;
        this.securityControl = sh;
    }

    public SecurityControl getSecurityControl() {
        return this.securityControl;
    }

    public AuthenticationMechanisms getAuthenticationMechanisms() {
        return this.authenticationMechanisms;
    }
}
