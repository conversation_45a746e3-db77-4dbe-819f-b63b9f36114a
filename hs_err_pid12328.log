#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 12582912 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=12328, tid=31776
#
# JRE version: Java(TM) SE Runtime Environment (17.0.7+8) (build 17.0.7+8-LTS-224)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\agents\gradle-instrumentation-agent-8.4.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.4

Host: Intel(R) Core(TM) i5-10200H CPU @ 2.40GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
Time: Mon May 13 12:28:14 2024 Egypt Daylight Time elapsed time: 46.635370 seconds (0d 0h 0m 46s)

---------------  T H R E A D  ---------------

Current thread (0x000001cae050e960):  VMThread "VM Thread" [stack: 0x0000009e7f700000,0x0000009e7f800000] [id=31776]

Stack: [0x0000009e7f700000,0x0000009e7f800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x676a4a]
V  [jvm.dll+0x7d74f4]
V  [jvm.dll+0x7d8c9e]
V  [jvm.dll+0x7d9303]
V  [jvm.dll+0x2452c5]
V  [jvm.dll+0x6738f9]
V  [jvm.dll+0x668232]
V  [jvm.dll+0x302826]
V  [jvm.dll+0x309da6]
V  [jvm.dll+0x35952e]
V  [jvm.dll+0x35975f]
V  [jvm.dll+0x2d9a38]
V  [jvm.dll+0x2dcad6]
V  [jvm.dll+0x2e72d8]
V  [jvm.dll+0x31ad30]
V  [jvm.dll+0x7ddb0b]
V  [jvm.dll+0x7de844]
V  [jvm.dll+0x7ded5d]
V  [jvm.dll+0x7df134]
V  [jvm.dll+0x7df200]
V  [jvm.dll+0x78759a]
V  [jvm.dll+0x675875]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa48]

VM_Operation (0x0000009e7f3ff510): G1Concurrent, mode: safepoint, requested by thread 0x000001cabd78ae90


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001cae9c41a00, length=54, elements={
0x000001cabd70b600, 0x000001cae0517ad0, 0x000001cae051aa20, 0x000001cae1005570,
0x000001cae1006030, 0x000001cae100a2a0, 0x000001cae100e8f0, 0x000001cae1010230,
0x000001cae1011470, 0x000001cae101dd00, 0x000001cae110ec20, 0x000001cae110ddb0,
0x000001cae11112a0, 0x000001cae1110900, 0x000001cae110fa90, 0x000001cae110e750,
0x000001cae110f0f0, 0x000001cae110f5c0, 0x000001cae1110dd0, 0x000001cae110ff60,
0x000001cae1110430, 0x000001cae2cb8f10, 0x000001cae2cb5550, 0x000001cae2cb7230,
0x000001cae2cb2ed0, 0x000001cae2cb6890, 0x000001cae2cb6d60, 0x000001cae2cb2530,
0x000001cae2cb2060, 0x000001cae2cb7700, 0x000001cae2cb5a20, 0x000001cae2cb5ef0,
0x000001cae2cb7bd0, 0x000001cae2cb80a0, 0x000001cae2cb8570, 0x000001cae2cb63c0,
0x000001cae2cb33a0, 0x000001cae2cb2a00, 0x000001cae2cb93e0, 0x000001cae2cb3870,
0x000001cae2cb98b0, 0x000001cae2cb3d40, 0x000001cae48a8160, 0x000001cae48a6480,
0x000001cae48a77c0, 0x000001cae48a6950, 0x000001cae48a8630, 0x000001cae48a7c90,
0x000001cae48a5fb0, 0x000001cae48a4c70, 0x000001cae48a6e20, 0x000001cae48a5ae0,
0x000001cae74ec870, 0x000001cae3c19640
}

Java Threads: ( => current thread )
  0x000001cabd70b600 JavaThread "main" [_thread_blocked, id=27856, stack(0x0000009e7f100000,0x0000009e7f200000)]
  0x000001cae0517ad0 JavaThread "Reference Handler" daemon [_thread_blocked, id=7644, stack(0x0000009e7f800000,0x0000009e7f900000)]
  0x000001cae051aa20 JavaThread "Finalizer" daemon [_thread_blocked, id=9292, stack(0x0000009e7f900000,0x0000009e7fa00000)]
  0x000001cae1005570 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=540, stack(0x0000009e7fa00000,0x0000009e7fb00000)]
  0x000001cae1006030 JavaThread "Attach Listener" daemon [_thread_blocked, id=28648, stack(0x0000009e7fb00000,0x0000009e7fc00000)]
  0x000001cae100a2a0 JavaThread "Service Thread" daemon [_thread_blocked, id=32012, stack(0x0000009e7fc00000,0x0000009e7fd00000)]
  0x000001cae100e8f0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=11504, stack(0x0000009e7fd00000,0x0000009e7fe00000)]
  0x000001cae1010230 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=28940, stack(0x0000009e7fe00000,0x0000009e7ff00000)]
  0x000001cae1011470 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=10824, stack(0x0000009e7ff00000,0x0000009e80000000)]
  0x000001cae101dd00 JavaThread "Sweeper thread" daemon [_thread_blocked, id=18536, stack(0x0000009e00000000,0x0000009e00100000)]
  0x000001cae110ec20 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=23296, stack(0x0000009e00100000,0x0000009e00200000)]
  0x000001cae110ddb0 JavaThread "Notification Thread" daemon [_thread_blocked, id=33552, stack(0x0000009e00200000,0x0000009e00300000)]
  0x000001cae11112a0 JavaThread "Daemon health stats" [_thread_blocked, id=15372, stack(0x0000009e00a00000,0x0000009e00b00000)]
  0x000001cae1110900 JavaThread "Incoming local TCP Connector on port 58812" [_thread_in_native, id=3856, stack(0x0000009e00900000,0x0000009e00a00000)]
  0x000001cae110fa90 JavaThread "Daemon periodic checks" [_thread_blocked, id=32320, stack(0x0000009e00b00000,0x0000009e00c00000)]
  0x000001cae110e750 JavaThread "Daemon" [_thread_blocked, id=27196, stack(0x0000009e00c00000,0x0000009e00d00000)]
  0x000001cae110f0f0 JavaThread "Handler for socket connection from /127.0.0.1:58812 to /127.0.0.1:58813" [_thread_in_native, id=8932, stack(0x0000009e00d00000,0x0000009e00e00000)]
  0x000001cae110f5c0 JavaThread "Cancel handler" [_thread_blocked, id=34268, stack(0x0000009e00e00000,0x0000009e00f00000)]
  0x000001cae1110dd0 JavaThread "Daemon worker" [_thread_blocked, id=8928, stack(0x0000009e00f00000,0x0000009e01000000)]
  0x000001cae110ff60 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:58812 to /127.0.0.1:58813" [_thread_blocked, id=33592, stack(0x0000009e01000000,0x0000009e01100000)]
  0x000001cae1110430 JavaThread "Stdin handler" [_thread_blocked, id=30252, stack(0x0000009e01100000,0x0000009e01200000)]
  0x000001cae2cb8f10 JavaThread "Daemon client event forwarder" [_thread_blocked, id=24140, stack(0x0000009e01200000,0x0000009e01300000)]
  0x000001cae2cb5550 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=29660, stack(0x0000009e01300000,0x0000009e01400000)]
  0x000001cae2cb7230 JavaThread "File lock request listener" [_thread_in_native, id=8680, stack(0x0000009e01400000,0x0000009e01500000)]
  0x000001cae2cb2ed0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.4\fileHashes)" [_thread_blocked, id=21956, stack(0x0000009e01500000,0x0000009e01600000)]
  0x000001cae2cb6890 JavaThread "File lock release action executor" [_thread_blocked, id=25304, stack(0x0000009e01600000,0x0000009e01700000)]
  0x000001cae2cb6d60 JavaThread "File watcher server" daemon [_thread_in_native, id=29108, stack(0x0000009e01700000,0x0000009e01800000)]
  0x000001cae2cb2530 JavaThread "File watcher consumer" daemon [_thread_blocked, id=24896, stack(0x0000009e01800000,0x0000009e01900000)]
  0x000001cae2cb2060 JavaThread "jar transforms" [_thread_blocked, id=14184, stack(0x0000009e01900000,0x0000009e01a00000)]
  0x000001cae2cb7700 JavaThread "jar transforms Thread 2" [_thread_blocked, id=30048, stack(0x0000009e01a00000,0x0000009e01b00000)]
  0x000001cae2cb5a20 JavaThread "jar transforms Thread 3" [_thread_blocked, id=26048, stack(0x0000009e01b00000,0x0000009e01c00000)]
  0x000001cae2cb5ef0 JavaThread "jar transforms Thread 4" [_thread_blocked, id=30868, stack(0x0000009e01c00000,0x0000009e01d00000)]
  0x000001cae2cb7bd0 JavaThread "jar transforms Thread 5" [_thread_blocked, id=25348, stack(0x0000009e01d00000,0x0000009e01e00000)]
  0x000001cae2cb80a0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=20244, stack(0x0000009e01e00000,0x0000009e01f00000)]
  0x000001cae2cb8570 JavaThread "jar transforms Thread 7" [_thread_blocked, id=27612, stack(0x0000009e01f00000,0x0000009e02000000)]
  0x000001cae2cb63c0 JavaThread "jar transforms Thread 8" [_thread_blocked, id=11424, stack(0x0000009e02000000,0x0000009e02100000)]
  0x000001cae2cb33a0 JavaThread "Cache worker for checksums cache (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\8.4\checksums)" [_thread_blocked, id=1140, stack(0x0000009e02400000,0x0000009e02500000)]
  0x000001cae2cb2a00 JavaThread "Cache worker for file hash cache (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\8.4\fileHashes)" [_thread_blocked, id=33716, stack(0x0000009e02500000,0x0000009e02600000)]
  0x000001cae2cb93e0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.4\md-supplier)" [_thread_blocked, id=7264, stack(0x0000009e02600000,0x0000009e02700000)]
  0x000001cae2cb3870 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.4\fileContent)" [_thread_blocked, id=20612, stack(0x0000009e02700000,0x0000009e02800000)]
  0x000001cae2cb98b0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.4\md-rule)" [_thread_blocked, id=29832, stack(0x0000009e02800000,0x0000009e02900000)]
  0x000001cae2cb3d40 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.4\executionHistory)" [_thread_blocked, id=26008, stack(0x0000009e02900000,0x0000009e02a00000)]
  0x000001cae48a8160 JavaThread "Cache worker for dependencies-accessors (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\8.4\dependencies-accessors)" [_thread_blocked, id=3060, stack(0x0000009e02c00000,0x0000009e02d00000)]
  0x000001cae48a6480 JavaThread "Cache worker for Build Output Cleanup Cache (D:\ShoaaProjects\FinalApps\Eslam\OpticalSmartReader\.gradle\buildOutputCleanup)" [_thread_blocked, id=17356, stack(0x0000009e02a00000,0x0000009e02b00000)]
  0x000001cae48a77c0 JavaThread "Unconstrained build operations" [_thread_blocked, id=34412, stack(0x0000009e02e00000,0x0000009e02f00000)]
  0x000001cae48a6950 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=24696, stack(0x0000009e02f00000,0x0000009e03000000)]
  0x000001cae48a8630 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=28744, stack(0x0000009e03000000,0x0000009e03100000)]
  0x000001cae48a7c90 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=24168, stack(0x0000009e03100000,0x0000009e03200000)]
  0x000001cae48a5fb0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=27076, stack(0x0000009e03200000,0x0000009e03300000)]
  0x000001cae48a4c70 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=4876, stack(0x0000009e03300000,0x0000009e03400000)]
  0x000001cae48a6e20 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=20828, stack(0x0000009e03400000,0x0000009e03500000)]
  0x000001cae48a5ae0 JavaThread "Memory manager" [_thread_blocked, id=2248, stack(0x0000009e03800000,0x0000009e03900000)]
  0x000001cae74ec870 JavaThread "build event listener" [_thread_blocked, id=3052, stack(0x0000009e03700000,0x0000009e03800000)]
  0x000001cae3c19640 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=15888, stack(0x0000009e03900000,0x0000009e03a00000)]

Other Threads:
=>0x000001cae050e960 VMThread "VM Thread" [stack: 0x0000009e7f700000,0x0000009e7f800000] [id=31776]
  0x000001cabd7934d0 WatcherThread [stack: 0x0000009e00300000,0x0000009e00400000] [id=20608]
  0x000001cabd779f50 GCTaskThread "GC Thread#0" [stack: 0x0000009e7f200000,0x0000009e7f300000] [id=31940]
  0x000001cae197ada0 GCTaskThread "GC Thread#1" [stack: 0x0000009e00400000,0x0000009e00500000] [id=3360]
  0x000001cae197b050 GCTaskThread "GC Thread#2" [stack: 0x0000009e00500000,0x0000009e00600000] [id=33124]
  0x000001cae197b300 GCTaskThread "GC Thread#3" [stack: 0x0000009e00600000,0x0000009e00700000] [id=23716]
  0x000001cae197b5b0 GCTaskThread "GC Thread#4" [stack: 0x0000009e00700000,0x0000009e00800000] [id=28864]
  0x000001cae1bfdd00 GCTaskThread "GC Thread#5" [stack: 0x0000009e00800000,0x0000009e00900000] [id=32868]
  0x000001cae36b3fa0 GCTaskThread "GC Thread#6" [stack: 0x0000009e02100000,0x0000009e02200000] [id=32160]
  0x000001cae36b5270 GCTaskThread "GC Thread#7" [stack: 0x0000009e02200000,0x0000009e02300000] [id=23728]
  0x000001cabd78ae90 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000009e7f300000,0x0000009e7f400000] [id=19280]
  0x000001cabd78c110 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000009e7f400000,0x0000009e7f500000] [id=22424]
  0x000001cae36b3790 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000009e02300000,0x0000009e02400000] [id=19544]
  0x000001cabd7cdd00 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000009e7f500000,0x0000009e7f600000] [id=19496]
  0x000001cae19edd40 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000009e02b00000,0x0000009e02c00000] [id=28096]
  0x000001cabd7ce720 ConcurrentGCThread "G1 Service" [stack: 0x0000009e7f600000,0x0000009e7f700000] [id=30356]

Threads with active compile tasks:
C2 CompilerThread0    46697 10915       4       java.net.URLClassLoader$1::run (5 bytes)
C2 CompilerThread1    46697 10916   !   4       java.net.URLClassLoader$1::run (81 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001cabd705c50] Threads_lock - owner thread: 0x000001cae050e960
[0x000001cabd707480] Heap_lock - owner thread: 0x000001cabd78ae90

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bd0000-0x0000000800bd0000), size 12386304, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16159M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 243712K, used 150809K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 3 survivors (6144K)
 Metaspace       used 84344K, committed 85184K, reserved 1122304K
  class space    used 11569K, committed 11968K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%|HS|  |TAMS 0x0000000700200000, 0x0000000700000000| Complete 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%|HC|  |TAMS 0x0000000700400000, 0x0000000700200000| Complete 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%| O|  |TAMS 0x0000000700600000, 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700800000, 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%|HS|  |TAMS 0x0000000700c00000, 0x0000000700a00000| Complete 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%|HS|  |TAMS 0x0000000700e00000, 0x0000000700c00000| Complete 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%|HC|  |TAMS 0x0000000701000000, 0x0000000700e00000| Complete 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%|HS|  |TAMS 0x0000000701200000, 0x0000000701000000| Complete 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%|HC|  |TAMS 0x0000000701400000, 0x0000000701200000| Complete 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%|HS|  |TAMS 0x0000000701600000, 0x0000000701400000| Complete 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%|HC|  |TAMS 0x0000000701800000, 0x0000000701600000| Complete 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702200000| Updating 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702400000| Updating 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%|HS|  |TAMS 0x0000000702800000, 0x0000000702600000| Complete 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%|HC|  |TAMS 0x0000000702a00000, 0x0000000702800000| Complete 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000, 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%| O|  |TAMS 0x0000000703000000, 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703000000| Updating 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x0000000703400000, 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703600000| Updating 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703c00000, 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%|HS|  |TAMS 0x0000000704000000, 0x0000000703e00000| Complete 
|  32|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704000000| Updating 
|  33|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704600000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704800000, 0x0000000704800000|100%|HS|  |TAMS 0x0000000704800000, 0x0000000704600000| Complete 
|  36|0x0000000704800000, 0x0000000704a00000, 0x0000000704a00000|100%|HC|  |TAMS 0x0000000704a00000, 0x0000000704800000| Complete 
|  37|0x0000000704a00000, 0x0000000704c00000, 0x0000000704c00000|100%| O|  |TAMS 0x0000000704c00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704e00000, 0x0000000704e00000|100%| O|  |TAMS 0x0000000704e00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000705000000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%|HS|  |TAMS 0x0000000705200000, 0x0000000705000000| Complete 
|  41|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705400000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705600000, 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705800000, 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705a00000, 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705c00000, 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705e00000, 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000706000000, 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%|HS|  |TAMS 0x0000000706200000, 0x0000000706000000| Complete 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%|HC|  |TAMS 0x0000000706400000, 0x0000000706200000| Complete 
|  50|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706600000, 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706800000, 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706a00000, 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706c00000, 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706e00000, 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000707000000, 0x0000000706e00000| Untracked 
|  56|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707200000, 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707400000, 0x0000000707200000| Updating 
|  58|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707600000, 0x0000000707400000| Untracked 
|  59|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707800000, 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707a00000, 0x0000000707800000| Untracked 
|  61|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707c00000, 0x0000000707a00000| Untracked 
|  62|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707e00000, 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000707f53800, 0x0000000707e00000| Updating 
|  64|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708000000, 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708600000, 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x00000007089d7600, 0x0000000708a00000| 92%| O|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000, 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000, 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000, 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000, 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000, 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000, 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000, 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000, 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000, 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000, 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000, 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000, 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000, 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000, 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b76eff0, 0x000000070b800000| 71%| S|CS|TAMS 0x000000070b600000, 0x000000070b600000| Complete 
|  92|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| S|CS|TAMS 0x000000070b800000, 0x000000070b800000| Complete 
|  93|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| S|CS|TAMS 0x000000070ba00000, 0x000000070ba00000| Complete 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
| 126|0x000000070fc00000, 0x000000070fce1dd8, 0x000000070fe00000| 44%| E|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 
|2046|0x00000007ffc00000, 0x00000007ffe00000, 0x00000007ffe00000|100%| O|  |TAMS 0x00000007ffe00000, 0x00000007ffc00000| Untracked 
|2047|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x0000000800000000, 0x00000007ffe00000| Untracked 

Card table byte_map: [0x000001cad4ab0000,0x000001cad52b0000] _byte_map_base: 0x000001cad12b0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001cabd77a560, (CMBitMap*) 0x000001cabd77a5a0
 Prev Bits: [0x000001cad5ab0000, 0x000001cad9ab0000)
 Next Bits: [0x000001cad9ab0000, 0x000001caddab0000)

Polling page: 0x000001cabb640000

Metaspace:

Usage:
  Non-class:     71.07 MB used.
      Class:     11.30 MB used.
       Both:     82.37 MB used.

Virtual space:
  Non-class space:       72.00 MB reserved,      71.50 MB (>99%) committed,  9 nodes.
      Class space:        1.00 GB reserved,      11.69 MB (  1%) committed,  1 nodes.
             Both:        1.07 GB reserved,      83.19 MB (  8%) committed. 

Chunk freelists:
   Non-Class:  320.00 KB
       Class:  207.00 KB
        Both:  527.00 KB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 130.56 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1016.
num_arena_deaths: 0.
num_vsnodes_births: 10.
num_vsnodes_deaths: 0.
num_space_committed: 1331.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 4446.
num_chunk_merges: 6.
num_chunk_splits: 2908.
num_chunks_enlarged: 1863.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5534Kb max_used=5688Kb free=114465Kb
 bounds [0x000001cacca50000, 0x000001caccfe0000, 0x000001cad3f80000]
CodeHeap 'profiled nmethods': size=120000Kb used=17786Kb max_used=19089Kb free=102213Kb
 bounds [0x000001cac4f80000, 0x000001cac6230000, 0x000001cacc4b0000]
CodeHeap 'non-nmethods': size=5760Kb used=2436Kb max_used=2519Kb free=3323Kb
 bounds [0x000001cacc4b0000, 0x000001cacc730000, 0x000001cacca50000]
 total_blobs=10351 nmethods=9380 adapters=882
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 46.445 Thread 0x000001cae1011470 10943       3       org.gradle.util.internal.ConfigureUtil::configure (43 bytes)
Event: 46.446 Thread 0x000001cae1011470 nmethod 10943 0x000001cac50d9490 code [0x000001cac50d9700, 0x000001cac50da3d8]
Event: 46.460 Thread 0x000001cae1011470 10944       3       org.jetbrains.kotlin.gradle.plugin.sources.android.KotlinAndroidSourceSetInfoKt::getAndroidSourceSetInfoOrNull (54 bytes)
Event: 46.461 Thread 0x000001cae1011470 nmethod 10944 0x000001cac50cc310 code [0x000001cac50cc640, 0x000001cac50cda98]
Event: 46.462 Thread 0x000001cae1011470 10945       3       org.gradle.internal.extensibility.ExtensionsStorage::firstHolderWithAssignableType (58 bytes)
Event: 46.462 Thread 0x000001cae1011470 nmethod 10945 0x000001cac5984590 code [0x000001cac59847c0, 0x000001cac5985168]
Event: 46.463 Thread 0x000001cae1011470 10946       3       org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated$$Lambda$1200/0x000000080164f1d8::create (8 bytes)
Event: 46.464 Thread 0x000001cae1011470 nmethod 10946 0x000001cac563a110 code [0x000001cac563a2c0, 0x000001cac563a608]
Event: 46.464 Thread 0x000001cae1011470 10947       3       org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated::getConvention (11 bytes)
Event: 46.464 Thread 0x000001cae1011470 nmethod 10947 0x000001cac59a1b90 code [0x000001cac59a1d40, 0x000001cac59a2028]
Event: 46.464 Thread 0x000001cae1011470 10948       3       org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated::getAsDynamicObject (37 bytes)
Event: 46.467 Thread 0x000001cae1011470 nmethod 10948 0x000001cac5c84a90 code [0x000001cac5c84c80, 0x000001cac5c85248]
Event: 46.467 Thread 0x000001cae1011470 10949       1       org.jetbrains.kotlin.gradle.plugin.sources.android.KotlinAndroidSourceSetInfo$Mutable::getAndroidSourceSetName (5 bytes)
Event: 46.467 Thread 0x000001cae1011470 nmethod 10949 0x000001cacca9b090 code [0x000001cacca9b220, 0x000001cacca9b2f8]
Event: 46.496 Thread 0x000001cae1011470 10950       3       org.gradle.api.internal.artifacts.configurations.DefaultConfiguration$$Lambda$592/0x00000008011237d0::validateMutation (9 bytes)
Event: 46.497 Thread 0x000001cae1011470 nmethod 10950 0x000001cac602b110 code [0x000001cac602b2e0, 0x000001cac602b618]
Event: 46.497 Thread 0x000001cae1011470 10951       3       org.gradle.api.internal.artifacts.configurations.DefaultConfiguration::validateParentMutation (24 bytes)
Event: 46.497 Thread 0x000001cae1011470 nmethod 10951 0x000001cac5674290 code [0x000001cac5674460, 0x000001cac5674758]
Event: 46.500 Thread 0x000001cae1011470 10952       3       org.gradle.api.internal.artifacts.configurations.DefaultConfiguration::preventIllegalParentMutation (104 bytes)
Event: 46.501 Thread 0x000001cae1011470 nmethod 10952 0x000001cac6022d90 code [0x000001cac6023140, 0x000001cac6024ea8]

GC Heap History (20 events):
Event: 35.972 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 163840K, used 127446K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 3 survivors (6144K)
 Metaspace       used 64645K, committed 65280K, reserved 1105920K
  class space    used 8788K, committed 9088K, reserved 1048576K
}
Event: 35.977 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 163840K, used 100239K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 64645K, committed 65280K, reserved 1105920K
  class space    used 8788K, committed 9088K, reserved 1048576K
}
Event: 36.998 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 174080K, used 126863K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 1 survivors (2048K)
 Metaspace       used 66257K, committed 67008K, reserved 1114112K
  class space    used 9032K, committed 9408K, reserved 1048576K
}
Event: 37.000 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 174080K, used 102257K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 66257K, committed 67008K, reserved 1114112K
  class space    used 9032K, committed 9408K, reserved 1048576K
}
Event: 37.635 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 174080K, used 137073K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 19 young (38912K), 2 survivors (4096K)
 Metaspace       used 66452K, committed 67200K, reserved 1114112K
  class space    used 9067K, committed 9408K, reserved 1048576K
}
Event: 37.641 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 174080K, used 105312K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 66452K, committed 67200K, reserved 1114112K
  class space    used 9067K, committed 9408K, reserved 1048576K
}
Event: 38.332 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 182272K, used 119648K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 3 survivors (6144K)
 Metaspace       used 68516K, committed 69248K, reserved 1114112K
  class space    used 9348K, committed 9728K, reserved 1048576K
}
Event: 38.336 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 182272K, used 106266K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 68516K, committed 69248K, reserved 1114112K
  class space    used 9348K, committed 9728K, reserved 1048576K
}
Event: 39.532 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 188416K, used 151322K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 2 survivors (4096K)
 Metaspace       used 72867K, committed 73600K, reserved 1114112K
  class space    used 9925K, committed 10304K, reserved 1048576K
}
Event: 39.536 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 188416K, used 112428K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 72867K, committed 73600K, reserved 1114112K
  class space    used 9925K, committed 10304K, reserved 1048576K
}
Event: 40.469 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 188416K, used 153388K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 2 survivors (4096K)
 Metaspace       used 75348K, committed 76096K, reserved 1122304K
  class space    used 10255K, committed 10624K, reserved 1048576K
}
Event: 40.475 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 188416K, used 115350K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 75348K, committed 76096K, reserved 1122304K
  class space    used 10255K, committed 10624K, reserved 1048576K
}
Event: 41.436 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 202752K, used 154262K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 3 survivors (6144K)
 Metaspace       used 76671K, committed 77440K, reserved 1122304K
  class space    used 10384K, committed 10752K, reserved 1048576K
}
Event: 41.444 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 243712K, used 120403K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 76671K, committed 77440K, reserved 1122304K
  class space    used 10384K, committed 10752K, reserved 1048576K
}
Event: 43.495 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 243712K, used 190035K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 38 young (77824K), 3 survivors (6144K)
 Metaspace       used 79375K, committed 80128K, reserved 1122304K
  class space    used 10740K, committed 11136K, reserved 1048576K
}
Event: 43.505 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 243712K, used 131743K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 79375K, committed 80128K, reserved 1122304K
  class space    used 10740K, committed 11136K, reserved 1048576K
}
Event: 44.807 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 243712K, used 193183K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 35 young (71680K), 5 survivors (10240K)
 Metaspace       used 81741K, committed 82560K, reserved 1122304K
  class space    used 11220K, committed 11648K, reserved 1048576K
}
Event: 44.821 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 243712K, used 144718K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 81741K, committed 82560K, reserved 1122304K
  class space    used 11220K, committed 11648K, reserved 1048576K
}
Event: 46.504 GC heap before
{Heap before GC invocations=40 (full 0):
 garbage-first heap   total 243712K, used 191822K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 28 young (57344K), 5 survivors (10240K)
 Metaspace       used 84331K, committed 85184K, reserved 1122304K
  class space    used 11566K, committed 11968K, reserved 1048576K
}
Event: 46.514 GC heap after
{Heap after GC invocations=41 (full 0):
 garbage-first heap   total 243712K, used 150809K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 84331K, committed 85184K, reserved 1122304K
  class space    used 11566K, committed 11968K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 45.015 Thread 0x000001cae1110dd0 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000001cacccb8f80 relative=0x0000000000001380
Event: 45.015 Thread 0x000001cae1110dd0 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000001cacccb8f80 method=com.google.common.collect.RegularImmutableMap.fromEntryArrayCheckingBucketOverflow(I[Ljava/util/Map$Entry;Z)Lcom/google/common/collect/ImmutableMap; @ 220 c2
Event: 45.015 Thread 0x000001cae1110dd0 DEOPT PACKING pc=0x000001cacccb8f80 sp=0x0000009e00ff8a60
Event: 45.015 Thread 0x000001cae1110dd0 DEOPT UNPACKING pc=0x000001cacc5023a3 sp=0x0000009e00ff8730 mode 2
Event: 45.051 Thread 0x000001cae1110dd0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001cacca6fb20 relative=0x0000000000000aa0
Event: 45.051 Thread 0x000001cae1110dd0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001cacca6fb20 method=com.google.common.collect.ImmutableSet$RegularSetBuilderImpl.build()Lcom/google/common/collect/ImmutableSet; @ 4 c2
Event: 45.051 Thread 0x000001cae1110dd0 DEOPT PACKING pc=0x000001cacca6fb20 sp=0x0000009e00ff8670
Event: 45.051 Thread 0x000001cae1110dd0 DEOPT UNPACKING pc=0x000001cacc5023a3 sp=0x0000009e00ff85e8 mode 2
Event: 45.218 Thread 0x000001cae1110dd0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001caccd2be48 relative=0x00000000000005a8
Event: 45.218 Thread 0x000001cae1110dd0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001caccd2be48 method=com.google.common.collect.ImmutableSet$RegularSetBuilderImpl.build()Lcom/google/common/collect/ImmutableSet; @ 4 c2
Event: 45.218 Thread 0x000001cae1110dd0 DEOPT PACKING pc=0x000001caccd2be48 sp=0x0000009e00ff8480
Event: 45.218 Thread 0x000001cae1110dd0 DEOPT UNPACKING pc=0x000001cacc5023a3 sp=0x0000009e00ff8458 mode 2
Event: 45.664 Thread 0x000001cae1110dd0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001caccf41748 relative=0x0000000000000048
Event: 45.664 Thread 0x000001cae1110dd0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001caccf41748 method=java.lang.StringConcatHelper.stringOf(Ljava/lang/Object;)Ljava/lang/String; @ 1 c2
Event: 45.664 Thread 0x000001cae1110dd0 DEOPT PACKING pc=0x000001caccf41748 sp=0x0000009e00ff8870
Event: 45.664 Thread 0x000001cae1110dd0 DEOPT UNPACKING pc=0x000001cacc5023a3 sp=0x0000009e00ff8800 mode 2
Event: 45.969 Thread 0x000001cae1110dd0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001caccf884d8 relative=0x00000000000003f8
Event: 45.969 Thread 0x000001cae1110dd0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001caccf884d8 method=org.gradle.api.internal.DynamicPropertyNamer.determineName(Ljava/lang/Object;)Ljava/lang/String; @ 4 c2
Event: 45.969 Thread 0x000001cae1110dd0 DEOPT PACKING pc=0x000001caccf884d8 sp=0x0000009e00ff8b20
Event: 45.969 Thread 0x000001cae1110dd0 DEOPT UNPACKING pc=0x000001cacc5023a3 sp=0x0000009e00ff8a40 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 40.717 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070b694340}: com/android/build/gradle/internal/dsl/ProductFlavorBeanInfo> (0x000000070b694340) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 40.719 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070b6a3048}: com/android/build/gradle/internal/dsl/ProductFlavorCustomizer> (0x000000070b6a3048) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 40.724 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070b6e09e8}: com/android/build/gradle/internal/dsl/ProductFlavor$AgpDecoratedCustomizer> (0x000000070b6e09e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 40.735 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070b73a988}: com/android/build/gradle/internal/dsl/ProductFlavor$AgpDecorated_DecoratedCustomizer> (0x000000070b73a988) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 41.257 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070a304880}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070a304880) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 44.961 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070dfd09c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070dfd09c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 44.961 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070dfd56f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070dfd56f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 45.609 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070d3ba240}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, long, int, int, int, int, int, int, java.lang.Object, int, int, int, int, java.lang.Object)'> (0x000000070d3ba240) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 45.662 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070d3fc6e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, long, int, int, int, int, int, int, java.lang.Object, int, int, int, int, java.lang.Object)'> (0x000000070d3fc6e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 45.663 Thread 0x000001cae1110dd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000070d001a80}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, long, int, int, int, int, int, int, java.lang.Object, int, int, int, int, java.lang.Object)'> (0x000000070d001a80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 45.961 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070ca4b610}: com/android/build/gradle/internal/api/ApkVariantOutputImpl_DecoratedBeanInfo> (0x000000070ca4b610) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 45.963 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070ca586e8}: com/android/build/gradle/internal/api/ApkVariantOutputImplBeanInfo> (0x000000070ca586e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 45.965 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070ca657d8}: com/android/build/gradle/internal/api/BaseVariantOutputImplBeanInfo> (0x000000070ca657d8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 45.965 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070ca728a0}: com/android/build/gradle/internal/api/BaseVariantOutputImplCustomizer> (0x000000070ca728a0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 45.967 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070ca927e8}: com/android/build/gradle/internal/api/ApkVariantOutputImplCustomizer> (0x000000070ca927e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 45.967 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070caad7a8}: com/android/build/gradle/internal/api/ApkVariantOutputImpl_DecoratedCustomizer> (0x000000070caad7a8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 46.293 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c1405c0}: com/android/build/gradle/internal/api/ReadOnlyBuildTypeBeanInfo> (0x000000070c1405c0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 46.294 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c14d8e8}: com/android/build/gradle/internal/api/ReadOnlyBaseConfigBeanInfo> (0x000000070c14d8e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 46.294 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c15a990}: com/android/build/gradle/internal/api/ReadOnlyBaseConfigCustomizer> (0x000000070c15a990) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 46.295 Thread 0x000001cae1110dd0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c178548}: com/android/build/gradle/internal/api/ReadOnlyBuildTypeCustomizer> (0x000000070c178548) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 43.619 Executing VM operation: G1Concurrent done
Event: 43.897 Executing VM operation: HandshakeAllThreads
Event: 43.897 Executing VM operation: HandshakeAllThreads done
Event: 44.134 Executing VM operation: HandshakeAllThreads
Event: 44.134 Executing VM operation: HandshakeAllThreads done
Event: 44.157 Executing VM operation: HandshakeAllThreads
Event: 44.157 Executing VM operation: HandshakeAllThreads done
Event: 44.424 Executing VM operation: HandshakeAllThreads
Event: 44.424 Executing VM operation: HandshakeAllThreads done
Event: 44.637 Executing VM operation: ICBufferFull
Event: 44.637 Executing VM operation: ICBufferFull done
Event: 44.807 Executing VM operation: G1CollectForAllocation
Event: 44.821 Executing VM operation: G1CollectForAllocation done
Event: 45.830 Executing VM operation: ICBufferFull
Event: 45.830 Executing VM operation: ICBufferFull done
Event: 45.973 Executing VM operation: HandshakeAllThreads
Event: 45.973 Executing VM operation: HandshakeAllThreads done
Event: 46.503 Executing VM operation: G1CollectForAllocation
Event: 46.514 Executing VM operation: G1CollectForAllocation done
Event: 46.624 Executing VM operation: G1Concurrent

Events (20 events):
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6072490
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a3990
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a5310
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a5790
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a6290
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a6690
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60a7310
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60b2a10
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60d0690
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac60ed010
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6107290
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6130990
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6134390
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6145410
Event: 44.437 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6167010
Event: 44.438 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac6189510
Event: 44.438 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac618c390
Event: 44.438 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac618e310
Event: 44.438 Thread 0x000001cae101dd00 flushing nmethod 0x000001cac61acd90
Event: 45.680 Thread 0x000001cae3c19640 Thread added: 0x000001cae3c19640


Dynamic libraries:
0x00007ff7693d0000 - 0x00007ff7693e0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff99a770000 - 0x00007ff99a986000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff952e20000 - 0x00007ff952e39000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ff999bb0000 - 0x00007ff999c74000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff997ff0000 - 0x00007ff998397000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff997b00000 - 0x00007ff997c11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff967000000 - 0x00007ff967019000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff9984c0000 - 0x00007ff998572000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff999b00000 - 0x00007ff999ba7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff97dec0000 - 0x00007ff97dedb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff998630000 - 0x00007ff9986d8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff998470000 - 0x00007ff998498000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff99a330000 - 0x00007ff99a445000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff999550000 - 0x00007ff9996fe000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9983a0000 - 0x00007ff9983c6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff999d00000 - 0x00007ff999d29000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff997c20000 - 0x00007ff997d39000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9983d0000 - 0x00007ff99846a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff95b3f0000 - 0x00007ff95b683000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ff98bea0000 - 0x00007ff98beaa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff998980000 - 0x00007ff9989b1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff97ddb0000 - 0x00007ff97ddbc000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff94dc40000 - 0x00007ff94dcce000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff92fdf0000 - 0x00007ff9309cd000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff99a5b0000 - 0x00007ff99a5b8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff98c7e0000 - 0x00007ff98c814000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff980390000 - 0x00007ff980399000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff999320000 - 0x00007ff999391000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff996bc0000 - 0x00007ff996bd8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff97d2a0000 - 0x00007ff97d2aa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff994f40000 - 0x00007ff995173000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff999770000 - 0x00007ff999af8000 	C:\WINDOWS\System32\combase.dll
0x00007ff9988a0000 - 0x00007ff998977000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9891d0000 - 0x00007ff989202000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff997f70000 - 0x00007ff997fe9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff98c9e0000 - 0x00007ff98c9ee000 	C:\Program Files\Java\jdk-17\bin\instrument.dll
0x00007ff95fcc0000 - 0x00007ff95fce5000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ff92fd10000 - 0x00007ff92fde7000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ff998a70000 - 0x00007ff9992cc000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff995a00000 - 0x00007ff9962f9000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9958c0000 - 0x00007ff9959fe000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff99a1b0000 - 0x00007ff99a2a3000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff99a2d0000 - 0x00007ff99a32e000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9979c0000 - 0x00007ff9979e1000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff97acc0000 - 0x00007ff97acd8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ff97c1d0000 - 0x00007ff97c1e9000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ff98df80000 - 0x00007ff98e0b6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff997030000 - 0x00007ff997099000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff97ad20000 - 0x00007ff97ad36000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ff97d290000 - 0x00007ff97d2a0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ff94a3e0000 - 0x00007ff94a407000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ff95e010000 - 0x00007ff95e154000 	C:\Users\<USER>\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64\native-platform-file-events.dll
0x00007ff966de0000 - 0x00007ff966dea000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ff966dd0000 - 0x00007ff966ddb000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ff997290000 - 0x00007ff9972ab000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff996b20000 - 0x00007ff996b55000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff997130000 - 0x00007ff997158000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff9972b0000 - 0x00007ff9972bc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff996550000 - 0x00007ff99657d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff998a60000 - 0x00007ff998a69000 	C:\WINDOWS\System32\NSI.dll
0x00007ff98de90000 - 0x00007ff98dea9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff98de70000 - 0x00007ff98de8f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff996580000 - 0x00007ff996679000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff966db0000 - 0x00007ff966dbd000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ff997e00000 - 0x00007ff997f67000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff997430000 - 0x00007ff99745e000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff9973f0000 - 0x00007ff997427000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff98c180000 - 0x00007ff98c188000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff98c200000 - 0x00007ff98c20a000 	C:\Windows\System32\rasadhlp.dll
0x00007ff98c750000 - 0x00007ff98c7d3000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\38dada09dfb8b06ba9b0570ebf7e218e3eb74d4ef43ca46872605cf95ebc2f47\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4096m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\agents\gradle-instrumentation-agent-8.4.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.4
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.4-all\56r6xik2f6skrm47et0ibifug\gradle-8.4\lib\gradle-launcher-8.4.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Python310\Scripts\;C:\Python310\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\wamp64\bin\php\php7.3.33;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\MySQL\MySQL Utilities 1.6\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\cygwin64\bin;C:\protoc-24.3-win64\bin;C:\apache-ant-1.10.14\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\src\sdks\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Program Files\JetBrains\PhpStorm 2022.1.4\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;
USERNAME=Mohamed Gamal
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
OS uptime: 5 days 2:39 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xf8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 16159M (317M free)
TotalPageFile size 55003M (AvailPageFile size 9M)
current process WorkingSet (physical memory assigned to process): 479M, peak: 483M
current process commit charge ("private bytes"): 527M, peak: 542M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224) for windows-amd64 JRE (17.0.7+8-LTS-224), built on Feb 28 2023 23:03:02 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
