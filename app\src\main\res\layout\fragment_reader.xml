<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    android:padding="10dp">

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:text="@string/read_meter"
        android:textColor="@color/on_primary"
        android:textSize="25sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/btn_pick_bluetooth"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_pick_bluetooth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:background="@color/primary_dark"
        android:text="@string/pick_optical"
        android:textColor="@color/on_primary"
        android:textSize="15sp"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBottomOf="@id/bluetoothName"
        app:layout_constraintBottom_toBottomOf="@id/bluetoothName"
        app:layout_constraintEnd_toStartOf="@id/bluetoothName"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/bluetoothName"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/bluetoothName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="5dp"
        android:maxLines="1"
        android:textColor="@color/on_primary"
        android:textSize="15sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@id/divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_pick_bluetooth"
        app:layout_constraintTop_toBottomOf="@id/txt_title" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/on_primary"
        app:layout_constraintBottom_toTopOf="@id/stateSpinner"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_pick_bluetooth" />


    <Spinner
        android:id="@+id/stateSpinner"
        style="@style/Widget.AppCompat.DropDownItem.Spinner"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        app:layout_constraintBottom_toTopOf="@id/divider1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider" />


    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/on_primary"
        app:layout_constraintBottom_toTopOf="@id/frameOptions"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/stateSpinner" />


    <LinearLayout
        android:id="@+id/frameOptions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@id/divider2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider1">

        <Spinner
            android:id="@+id/meterPositionSpinner"
            style="@style/Widget.AppCompat.DropDownItem.Spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"/>
        <View
            android:id="@+id/divider5"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:background="@color/on_primary"/>

        <Spinner
            android:id="@+id/meterTypeSpinner"
            style="@style/Widget.AppCompat.DropDownItem.Spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:visibility="gone" />

        <Spinner
            android:id="@+id/upTypeSpinner"
            style="@style/Widget.AppCompat.DropDownItem.Spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/globalVersionView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <View
                android:id="@+id/divider3"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:background="@color/on_primary"
                app:layout_constraintBottom_toTopOf="@id/frameOptions"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/stateSpinner" />

            <Spinner
                android:id="@+id/globalVersionSpinner"
                style="@style/Widget.AppCompat.DropDownItem.Spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp" />
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:background="@color/on_primary"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/scrollView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/frameOptions" />


    <LinearLayout
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/my_custom_background"
        app:layout_constraintBottom_toTopOf="@id/layout_btn_read"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider2">

        <ListView
            android:id="@+id/stringListView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:stackFromBottom="true"
            android:transcriptMode="alwaysScroll"
            tools:listitem="@layout/device_list_item" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_btn_read"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginHorizontal="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scrollView"
        android:weightSum="2"
        >
        <Button
            android:id="@+id/btn_read"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="@string/save"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:layout_marginHorizontal="5dp"
            android:gravity="center"
            android:textSize="22sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <Button
            android:id="@+id/showMeterImageBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/show"
            android:layout_weight="1"
            android:layout_gravity="center"
        android:layout_marginHorizontal="5dp"
            android:gravity="center"
            android:textSize="22sp"
            android:visibility="gone"
            />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>