package shoaa.opticalsmartreader;

import static org.junit.Assert.assertEquals;

import android.util.Log;

import com.esmc.protocol.model.DlmsData;
import com.esmc.protocol.utils.MyConverter;
import com.esmc.protocol.wrap.CosemDataWrapper;
import com.google.gson.Gson;
import com.sanxing.facade.MaasraHelper;

import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;


import shoaa.common.DateUtil;
import shoaa.connectionmanager.TextUtil;
import shoaa.globalreader.GlobalReader;
import shoaa.globalreader.GlobalVersion;
import shoaa.globalreader.MeterData;
import shoaa.globalreader.Request;
import shoaa.globalreader.Response;
import shoaa.globalreader.ShoaaProtocolHelper;
import shoaa.opticalsmartreader.logic.Utils;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {

    private static String getDateValue(String data) {
        if (null != data && data.length() >= 10) {
            try {
                ByteArrayInputStream input = new ByteArrayInputStream(MyConverter.hexStringToBytes(data));
                byte[] temp = new byte[2];
                if (input.read(temp) != 2) {
                    return null;
                } else {
                    int year = (temp[0] & 255) * 256 + (temp[1] & 255);
                    int month = input.read();
                    int day = input.read();
                    return year != 65535 && month >= 1 && month <= 12 && day >= 1 && day <= 31 ? String.format(Locale.getDefault(), "%02d-%02d-%04d", day, month, year) : null;
                }
            } catch (IOException var8) {
                var8.printStackTrace();
                return "";
            }
        } else {
            return "";
        }
    }

    @Test
    public void test1() {

        String v = "0!!34";
        String r = replaceNonstandardDigits(v);
        Log.d("TAG", "test1: $r");

    }

    private boolean isNonstandardDigit(char ch) {
        return Character.isDigit(ch) && !(ch >= '0' && ch <= '9');
    }

    String replaceNonstandardDigits(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if (isNonstandardDigit(ch)) {
                int numericValue = Character.getNumericValue(ch);
                if (numericValue >= 0) {
                    builder.append(numericValue);
                }
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }

    @Test
    public void addition_isCorrect() {
        String v = (String) new CosemDataWrapper().getCosemDataDisplayValue("4:1.0.15.6.0.255:2", new DlmsData(DlmsData.TYPE_I8, "000000DD"));
        String vv = getDateValue("07E6090104000000FF800000");
        assertEquals(4, 2 + 2);
        try {
//            System.out.println(DateUtil.toApiFormat(DateUtil.parse("2000-0-0 0:0:0")));
            String value = "0.00*kWh";
            System.out.println((((!value.contains(",")) &&
                    (!value.replaceAll("[^0-9.-]", "").matches("\\d+(\\.\\d+)?") ||
                            Double.parseDouble(value.replaceAll("[^0-9.-]", "")) == 0.0))
                    ||
                    ((value.contains(",")) &&
                            (!value.split(",")[0].replaceAll("[^0-9.-]", "").matches("\\d+(\\.\\d+)?") ||
                                    Double.parseDouble(value.split(",")[0].replaceAll("[^0-9.-]", "")) == 0.0)))
            );
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String getDecimalValue(DlmsData data) {
        if ("Integer".equals(data.getDataType())) {
            return Byte.toString((byte) Integer.parseInt(data.getDataValue(), 16));
        } else if ("Long".equals(data.getDataType())) {
            return Short.toString((short) Integer.parseInt(data.getDataValue(), 16));
        } else {
            BigInteger bi;
            if ("DoubleLong".equals(data.getDataType())) {
                bi = new BigInteger(data.getDataValue(), 16);
                return Integer.toString(bi.intValue());
            } else if (!"Unsigned".equals(data.getDataType()) && !"LongUnsigned".equals(data.getDataType()) && !"DoubleLongUnsigned".equals(data.getDataType())) {
                return data.getDataValue();
            } else {
                bi = new BigInteger(data.getDataValue(), 16);
                return Long.toString(bi.longValue());
            }
        }
    }

    @Test
    public void checkDate() {
        try {
            String xx = Utils.formatStringUIntNumber("aas sas 0 10 23 66-/.56*+8");
            Log.d("tag", String.valueOf(xx));
            //"^[a-z]{3}\\s\\d{1,2},\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[a,m,p]{3}$", "MMM dd, yyyy hh:mm:ss a"
            String dateString = "2023/03/13 04:56:44 PM";
//            String regexp = "^[a-z]{3}\\s\\d{1,2},\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}$";
            String regexp = "^[a-z]{3}\\s\\d{1,2},\\s\\d{4}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[a,m,p]{2}$";
            boolean res = dateString.toLowerCase().matches(regexp);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MMM dd, yyyy hh:mm:ss aa", Locale.US);
            simpleDateFormat.setLenient(false); // Don't automatically convert invalid date.
            String r = simpleDateFormat.parse(dateString).toString();
            System.out.println("Hello, World!" + res);
            System.out.println("Hello, World!" + r);

            String date1 = DateUtil.toApiFormat(new Date(0));
            boolean ddd = DateUtil.parse(date1).equals(new Date(0));
            boolean x = Boolean.parseBoolean("True");
            String date = "2022/11/29 16:37:00";
            if (!DateUtil.isValidDate(date) ||
                    !DateUtil.parse(date).after(DateUtil.parse("01-01-2000"))) {
                Log.d("TAG", "checkDate: false");
            }
        } catch (Exception e) {
            Log.d("TAG", "checkDate: false");
        }
        Log.d("TAG", "checkDate: true");
    }

    @Test
    public void checkTarrif() {

        String cons = "1916.57*kWh";
        String data = "100.00,250.00,600.00";
        if (data.split(",").length > 1) {
            String[] doubleValues = data.split(",");
            for (int i = 0; i < doubleValues.length; i++) {
                if (i == 0) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 1) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 2) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 3) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 4) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 5) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 6) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 7) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 8) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                } else if (i == 9) {
                    doubleValues[i].replaceAll("[^0-9.-]", "");
                }
            }
            String tarrif = "0";
            for (int i = 0; i < doubleValues.length; i++) {
                if (Double.parseDouble(cons.replaceAll("[^0-9.-]", "")) < Double.parseDouble(doubleValues[i].replaceAll("[^0-9.-]", ""))) {
                    tarrif = String.valueOf(i + 1);
                    Log.d("TAG", "checkTarrif: " + tarrif);
                    break;
                }
            }
            if (tarrif.equals("0")) {
                tarrif = String.valueOf(doubleValues.length + 1);
                Log.d("TAG", "checkTarrif: " + tarrif);
            }
        }

//            if (((!data.contains(",")) &&
//                    (!data.replaceAll("[^0-9.-]", "").matches("\\d+(\\.\\d+)?") ||
//                            Double.parseDouble(data.replaceAll("[^0-9.-]", "")) == 0.0))
//                    ||
//                    ((data.contains(",")) &&
//                            (!data.split(",")[0].replaceAll("[^0-9.-]", "").matches("\\d+(\\.\\d+)?") ||
//                                    Double.parseDouble(data.split(",")[0].replaceAll("[^0-9.-]", "")) == 0.0))) {
//                Log.d("TAG", "checkDate: false");
//            }
//        } catch (Exception e) {
//            Log.d("TAG", "checkDate: false");
//        }
//        Log.d("TAG", "checkDate: true");
    }

    @Test
    public void checkLength() {
        try {
            byte[] data = TextUtil.INSTANCE.fromHexString("0000020000000001010D0000020000020001010D08E60275642F0516051500000275640D0D16051500000275640D0D16051500000275640F0D160515");
            ArrayList<Byte> packet = new ArrayList<>();
            for (int i = 0; i < data.length; i++)
                packet.add(i, data[i]);
            int length = (packet.get(1) & 0xFF) + ((packet.get(2) & 0xFF) * (256));
            Log.d("TAG", "checkLength: " + (data.length == length));
        } catch (Exception e) {
            Log.d("TAG", "checkDate: false");
        }
        Log.d("TAG", "checkDate: true");
    }

    @Test
    public void getItemTest() {
        try {
            Date date = DateUtil.parse("30/12/2022");
            String item = "3:0.0.96.63.0.112:2";
            String pdu = "7EA00A030002C0017363707E";
            MaasraHelper maasraHelper = new MaasraHelper();
            String request5 = "{\"service\":\"get\",\"pdu\":\"" + pdu + "\",\"item\":\"" + item + "\"}";
            String response6 = maasraHelper.handle(request5);
            com.sanxing.model.Response res = new Gson().fromJson(response6, com.sanxing.model.Response.class);
            Log.d("TAG", "checkLength: ");
        } catch (Exception e) {
            Log.d("TAG", "checkDate: false");
        }
        Log.d("TAG", "checkDate: true");
    }

    @Test
    public void checkGlobalFrame() {
        String frame = "30303030303030303030303030303030303030303030303030303030303030303030303033323030303030303030303030303030303030303031303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030373030353033303030303030303030303030303045353037303431313037313532453030303030304535303730343131303731353331303030303038303332414533303730353034303730453330303145333037303530343037313131393046453330373035303430373045303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303837303130303030303030303030303009064535303730343131303731353331333538303030343030363030303044383031303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303130303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303431464637363032363436363834303346303041424330373030303032433031314530303030434130303036303036354430303338383133303030303130323730303030303330303030303038303030303030304643464646464646464630303030303030303346303031323030303030303030303030304545454545383033464646467C06";
        String service = "service_2016";
        GlobalVersion globalVersion = GlobalVersion.VERSION_2016;
        Request requestData = new Request();
        requestData.pdu = frame;
        requestData.service = service;
        try {
            String json = new Gson().toJson(requestData);
            String frameData = new ShoaaProtocolHelper().handle(json);
            Response response = new Gson().fromJson(frameData, Response.class);
            MeterData meterData = new Gson().fromJson(response.value, MeterData.class);
            boolean isTrusted = GlobalReader.isTrusted(frame, globalVersion, meterData);
            Log.d("TAG", "checkGlobalFrame: ");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}