package shoaa.esmcreader;

import static shoaa.common.Utils.writeStringAsFile;

import android.content.Context;

import com.esmc.protocol.EsmcProtocolHelper;
import com.esmc.protocol.model.ResponseInfo;
import com.google.gson.Gson;

import java.util.concurrent.atomic.AtomicReference;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.BaudRate;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;

/**
 * Created by Islam Darwish
 */

public class EsmcReader {
    static String session = "";
    private static int sendErrorCount = 0;

    public static ReadingResponse read(Context context, ProgressCallback progressCallback) {
        try {
            session = "";
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                    EsmcResponse readingResponse = new EsmcResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
            }
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                EsmcResponse readingResponse = new EsmcResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                readingResponse.message = "";
                return readingResponse;
            }

            progressCallback.update(1);
            ConnectionManager.Companion.getInstance().sendAsyncNoRes("42617564547261722C393630302C4E2C382C300D0A");
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }            

            progressCallback.update(5);
            if (ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1)) {
                int tryCount = 0;
                sendErrorCount = 0;
                AtomicReference<ResponseInfo> responseInfo = null;
                EsmcProtocolHelper esmcProtocolHelper = new EsmcProtocolHelper();
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }   
                progressCallback.update(10);
                while (tryCount < 3) {
                    ConnectionManager.Companion.getInstance().setBaudRateAsync(BaudRate.BAUD_9600_8_N_1);
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    } 
                    tryCount++;
                    String handshake1 = esmcProtocolHelper.handle("{\"service\":\"handshake\",\"pdu\":null,\"item\":null}");
                    responseInfo = new AtomicReference<>(new Gson().fromJson(handshake1, ResponseInfo.class));
                    while (responseInfo.get().getResult().equalsIgnoreCase("continue")) {
                        String res = ConnectionManager.Companion.getInstance().sendAsync(responseInfo.get().getPdu(), 2, Common.TIME_OUT_1500, Common.TIME_OUT_300);
                        String request = "{\"service\":\"handshake\",\"pdu\":\"" + res + "\",\"item\":null}";
                        String handshake2 = esmcProtocolHelper.handle(request);
                        responseInfo.set(new Gson().fromJson(handshake2, ResponseInfo.class));
                        if (res.isEmpty())
                            break;
                    }
                    if (responseInfo.get().getResult().equalsIgnoreCase("success"))
                        break;
                }
                progressCallback.update(12);
                if (responseInfo.get().getResult().equalsIgnoreCase("success")) {
                    progressCallback.update(13);
                    EsmcResponse readingResponse = new EsmcResponse();


                    progressCallback.update(14);
                    //region meterType
                    ResponseInfo itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.meterType);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMeterType(Integer.parseInt(String.valueOf(itemResponse.getValue())) == 0 ? "Single" : "3Phase");
                    }
                    //endregion

                    progressCallback.update(15);
                    //region meterID
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.meterID);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMeterId(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(16);
                    //region customerID
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.customerID);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setCustomerId(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(17);
//                    //region fwVersion
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.fwVersion);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setFwVersion(String.valueOf(itemResponse.getValue()));
//                    }
//                    //endregion


                    progressCallback.update(18);
//                    //region activityType
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.activityType);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setActivityType(String.valueOf(itemResponse.getValue()));
////                        readingResponse.setActivityType("0");
//                    }
//                    //endregion

                    progressCallback.update(19);
                    //region curentPowerFactor
                    if (readingResponse.getMeterType().equalsIgnoreCase("3Phase")) {
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.currentPowerFactor);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setCurentPowerFactor(String.valueOf(itemResponse.getValue()));
                        }
                    }
                    //endregion

                    progressCallback.update(20);
//                    //region installingTechnicianCode
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.installingTechnicianCode);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setInstallingTechnicanCode(String.valueOf(itemResponse.getValue()));
//                    }
//                    //endregion

                    progressCallback.update(21);
//                    //region installingDate
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.installingDate);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setInstallingDateAndTime(String.valueOf(itemResponse.getValue()));
                    }
//                    //endregion


                    progressCallback.update(22);
                    //region meterDateAndTime
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.meterDateAndTime);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMeterDateAndTime(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(23);
                    //region currentTarrifInstalling
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.currentTarrifInstalling);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setCurrentTarrifInstalling(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(24);
                    //region currentTariffActivationDate
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.currentTariffActivationDate);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setCurrentTariffActivationDate(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(25);
                    //region meterStatus
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.meterStatus);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMeterStatus(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(26);
                    //region coversStatus
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.coversStatus);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setTopCoverStatus(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(27);
                    //region rechargeNumber
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.rechargeNumber);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setRechargeNumber(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(28);
                    //region rechargeAmount
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.rechargeAmount);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setRechargeAmount(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion
                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;

                    progressCallback.update(29);
                    //region lastRechargeDateAndTime
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.lastRechargeDate);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setLastRechargeDateAndTime(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(30);
                    //region remainingCreditMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.remainingCreditMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setRemainingCreditMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(31);
                    //region totalConsumptionKw
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.totalConsumptionKw);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setTotalConsumptionKw(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(32);
                    //region totalConsumptionKvar
                    if (readingResponse.getMeterType().equalsIgnoreCase("3Phase")) {
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.totalConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setTotalConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                    }
                    //endregion


                    progressCallback.update(33);
                    //region currentDemand
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.currentDemand);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setCurrentDemand(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(34);
                    //region maximumDemand
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.maximumDemand);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMaximumDemand(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(35);
                    //region maximumDemandDate
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.maximumDemandDate);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMaximumDemandDate(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(36);
//                    //region instantaneousVolt
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.instantaneousVolt);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setInstanteneousVolt(String.valueOf(itemResponse.getValue()));
//                    }
//                    //endregion


                    progressCallback.update(37);
//                    //region instantaneousCurrentPhaseAmpere
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.instantaneousCurrentPhaseAmpere);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setInstanteneousCurrentPhaseAmpere(String.valueOf(itemResponse.getValue()));
//                    }
//                    //endregion


                    progressCallback.update(38);
//                    //region instantaneousCurrentNeutral
//                    itemResponse = getItem( esmcProtocolHelper, ESMC_METER_DATA_ITEMS.instantaneousCurrentNeutral);
//                    if (itemResponse != null && itemResponse.getValue() != null) {
//                        readingResponse.setInstanteneousCurrentNeutral(String.valueOf(itemResponse.getValue()));
//                    }
//                    //endregion

                    progressCallback.update(39);
                    //region currentMonthConsumptionKw
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.currentMonthConsumptionKw);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setCurrentMonthConsumptionKW(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(40);
                    //region month1ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month1ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth1ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(41);
                    //region month2ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month2ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth2ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(42);
                    //region month3ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month3ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth3ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(43);
                    //region month4ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month4ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth4ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion
                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;

                    progressCallback.update(44);
                    //region month5ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month5ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth5ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(45);
                    //region month6ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month6ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth6ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(46);
                    //region month7ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month7ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth7ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(47);
                    //region month8ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month8ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth8ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(48);
                    //region month9ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month9ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth9ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(49);
                    //region month10ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month10ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth10ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(50);
                    //region month11ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month11ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth11ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(51);
                    //region month12ConsumptionKWh
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month12ConsumptionKWh);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth12ConsumptionKWh(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(52);
                    //region month1ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month1ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth1ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(53);
                    //region month2ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month2ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth2ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(54);
                    //region month3ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month3ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth3ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(55);
                    //region month4ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month4ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth4ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(56);
                    //region month5ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month5ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth5ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(57);
                    //region month6ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month6ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth6ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(58);
                    //region month7ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month7ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth7ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    if (sendErrorCount > 10) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    sendErrorCount = 0;
                    progressCallback.update(59);
                    //region month8ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month8ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth8ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    progressCallback.update(60);
                    //region month9ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month9ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth9ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(61);
                    //region month10ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month10ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth10ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(62);
                    //region month11ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month11ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth11ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion


                    progressCallback.update(63);
                    //region month12ConsumptionMoney
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month12ConsumptionMoney);
                    if (itemResponse != null && itemResponse.getValue() != null) {
                        readingResponse.setMonth12ConsumptionMoney(String.valueOf(itemResponse.getValue()));
                    }
                    //endregion

                    if (readingResponse.getMeterType().equalsIgnoreCase("3Phase")) {
                        progressCallback.update(64);
                        //region month1MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month1MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth1(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(65);
                        //region month2MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month2MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth2(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(66);
                        //region month3MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month3MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth3(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(67);
                        //region month4MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month4MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth4(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(68);
                        //region month5MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month5MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth5(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(69);
                        //region month6MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month6MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth6(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(70);
                        //region month7MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month7MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth7(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(71);
                        //region month8MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month8MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth8(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(72);
                        //region month9MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month9MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth9(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(73);
                        //region month10MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month10MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth10(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(74);
                        //region month11MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month11MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth11(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(75);
                        //region month12MaxDemand
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month12MaxDemand);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth12(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(76);
                        //region month1MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month1MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth1Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(77);
                        //region month2MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month2MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth2Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(78);
                        //region month3MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month3MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth3Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(79);
                        //region month4MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month4MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth4Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(80);
                        //region month5MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month5MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth5Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(81);
                        //region month6MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month6MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth6Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(82);
                        //region month7MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month7MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth7Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(83);
                        //region month8MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month8MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth8Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(84);
                        //region month9MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month9MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth9Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(85);
                        //region month10MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month10MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth10Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion

                        progressCallback.update(86);
                        //region month11MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month11MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth11Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(87);
                        //region month12MaxDemandDate
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month12MaxDemandDate);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMaximDemandMonth12Date(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(88);
                        //region month1ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month1ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth1ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        if (sendErrorCount > 10) {
                            readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                            return readingResponse;
                        }
                        sendErrorCount = 0;
                        progressCallback.update(89);
                        //region month2ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month2ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth2ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(90);
                        //region month3ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month3ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth3ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(91);
                        //region month4ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month4ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth4ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(92);
                        //region month5ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month5ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth5ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(93);
                        //region month6ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month6ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth6ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(94);
                        //region month7ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month7ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth7ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(95);
                        //region month8ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month8ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth8ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(96);
                        //region month9ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month9ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth9ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(97);
                        //region month10ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month10ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth10ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(98);
                        //region month11ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month11ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth11ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }
                        //endregion
                        progressCallback.update(99);
                        //region month12ConsumptionKvar
                        itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.month12ConsumptionKvar);
                        if (itemResponse != null && itemResponse.getValue() != null) {
                            readingResponse.setMonth12ConsumptionKvar(String.valueOf(itemResponse.getValue()));
                        }

                    }
                    //endregion

                    //region check connection
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.meterID);
                    if (itemResponse == null || itemResponse.getValue() == null || !(readingResponse.getMeterId().equalsIgnoreCase(String.valueOf(itemResponse.getValue())))) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    itemResponse = getItem(esmcProtocolHelper, ESMC_METER_DATA_ITEMS.customerID);
                    if (itemResponse == null || itemResponse.getValue() == null || !(readingResponse.getCustomerId().equalsIgnoreCase(String.valueOf(itemResponse.getValue())))) {
                        readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                        return readingResponse;
                    }
                    //endregion


                    String response7 = esmcProtocolHelper.handle("{\"service\":\"release\",\"pdu\":null,\"item\":null}");
                    responseInfo.set(new Gson().fromJson(response7, ResponseInfo.class));
                    String res = ConnectionManager.Companion.getInstance().sendAsync(responseInfo.get().getPdu(), 2, Common.TIME_OUT_800, Common.TIME_OUT_200);

                    String request6 = "{\"service\":\"release\",\"pdu\":\"" + res + "\",\"item\":null}";
                    String response8 = esmcProtocolHelper.handle(request6);
                    responseInfo.set(new Gson().fromJson(response8, ResponseInfo.class));
                    readingResponse.readingResult = ReadingResult.SUCCESS;
                    readingResponse.message = "";
                    writeStringAsFile(context, session, "session.txt");
                    progressCallback.update(100);
                    return readingResponse;
                } else {
                    writeStringAsFile(context, session, "session.txt");
                    String response7 = esmcProtocolHelper.handle("{\"service\":\"release\",\"pdu\":null,\"item\":null}");
                    responseInfo.set(new Gson().fromJson(response7, ResponseInfo.class));
                    String res = ConnectionManager.Companion.getInstance().sendAsync(responseInfo.get().getPdu(), 2, Common.TIME_OUT_800, Common.TIME_OUT_200);
                    String request6 = "{\"service\":\"release\",\"pdu\":\"" + res + "\",\"item\":null}";
                    String response8 = esmcProtocolHelper.handle(request6);
                    responseInfo.set(new Gson().fromJson(response8, ResponseInfo.class));
                    EsmcResponse readingResponse = new EsmcResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_GET_DATA;
                    readingResponse.message = "";
                    return readingResponse;
                }
            } else {
                writeStringAsFile(context, session, "session.txt");
                EsmcResponse readingResponse = new EsmcResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_SET_BaudRate;
                readingResponse.message = "";
                return readingResponse;
            }
        } catch (Exception e) {
            writeStringAsFile(context, session, "session.txt");
            EsmcResponse readingResponse = new EsmcResponse();
            readingResponse.readingResult = ReadingResult.OTHER;
            readingResponse.message = e.getMessage();
            return readingResponse;
        }
    }

    private static ResponseInfo getItem(EsmcProtocolHelper esmcProtocolHelper, String item) {
        if (item.equalsIgnoreCase("0"))
            return null;
        String request4 = "{\"service\":\"get\",\"pdu\":null,\"item\":\"" + item + "\"}";
        String response5 = esmcProtocolHelper.handle(request4);
        session += "OBIS : " + item + "\n";
        try {
            ResponseInfo responseInfo = new Gson().fromJson(response5, ResponseInfo.class);
            String res = ConnectionManager.Companion.getInstance().sendAsync(responseInfo.getPdu(), 2, Common.TIME_OUT_1500, Common.TIME_OUT_300);
            session += "sendApdu : " + responseInfo.getPdu() + "\n";
            session += "res : " + res + "\n";
            if (res.isEmpty()) {
                sendErrorCount++;
                return null;
            }
            String request5 = "{\"service\":\"get\",\"pdu\":\"" + res + "\",\"item\":\"" + item + "\"}";
            String response6 = esmcProtocolHelper.handle(request5);
            ResponseInfo responseInfo1 = new Gson().fromJson(response6, ResponseInfo.class);
            if (responseInfo1 == null || responseInfo1.getValue() == null || String.valueOf(responseInfo1.getValue()).isEmpty() || String.valueOf(responseInfo1.getValue()).equalsIgnoreCase("NF"))
                return null;
            else if (responseInfo1.getResult().equalsIgnoreCase("success")) {
                session += "value : " + responseInfo1.getValue() + "\n\n";
                return responseInfo1;
            } else {
                session += "value : error" + "\n\n";
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
