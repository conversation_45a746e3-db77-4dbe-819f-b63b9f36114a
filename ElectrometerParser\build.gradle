apply plugin: 'com.android.library'

android {
    compileSdk 34
    buildToolsVersion "33.0.1"

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 34

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.sewedy.electrometerparser'
}

dependencies {
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'junit:junit:4.13.2'
    implementation project(path: ':ConnectionManager')
    implementation project(path: ':Common')
    testImplementation 'com.google.truth:truth:1.4.2'
    androidTestImplementation 'com.google.truth:truth:1.4.2'

}