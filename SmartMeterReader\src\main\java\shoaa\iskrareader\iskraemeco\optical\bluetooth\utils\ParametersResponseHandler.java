package shoaa.iskrareader.iskraemeco.optical.bluetooth.utils;

public class ParametersResponseHandler {
    public String checkIfResponseNeedFormatting(String meterResponseData, String parameterCode) {
        if (meterResponseData.equalsIgnoreCase("NULL"))
            return "null";
        if (parameterCode.equalsIgnoreCase("C001")) {
            String formatResult = formatDateWithHours(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C004")) {
            String formatResult = formatDateWithoutHours(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("D700")) {
            Integer codeResult = Integer.valueOf(Integer.parseUnsignedInt(meterResponseData, 16));
            String formatResult = codeResult.toString();
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("D053") ||
                parameterCode.equalsIgnoreCase("C13C")) {
            String formatResult = parseMeterStatus(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C1F2")) {
            String formatResult = parseMeterStatus3PhaseAlarm(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C280") ||
                parameterCode.equalsIgnoreCase("C281") ||
                parameterCode.equalsIgnoreCase("C282") ||
                parameterCode.equalsIgnoreCase("C292") ||
                parameterCode.equalsIgnoreCase("C291")) {
            String formatResult = extractTechnicianNumberAndDate(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C0B0") ||
                parameterCode.equalsIgnoreCase("C0B1") ||
                parameterCode.equalsIgnoreCase("C0B2")) {
            String formatResult = extractTechnicianNumberAndDateThreePhase(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C0A0")) {
            String formatResult = prepareDateToBeFormatted(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C090")) {
            String[] dataList = meterResponseData.split(",");
            String formatResult = formatDate(dataList[0]);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("F501") ||
                parameterCode.equalsIgnoreCase("F511") ||
                parameterCode.equalsIgnoreCase("F521") ||
                parameterCode.equalsIgnoreCase("F531") ||
                parameterCode.equalsIgnoreCase("F541") ||
                parameterCode.equalsIgnoreCase("F551") ||
                parameterCode.equalsIgnoreCase("F561") ||
                parameterCode.equalsIgnoreCase("F571") ||
                parameterCode.equalsIgnoreCase("F581") ||
                parameterCode.equalsIgnoreCase("F591") ||
                parameterCode.equalsIgnoreCase("F5A1") ||
                parameterCode.equalsIgnoreCase("F5B1") ||
                parameterCode.equalsIgnoreCase("F5C1")) {
            String formatResult = extractMaxPowerAndDate(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("F500") ||
                parameterCode.equalsIgnoreCase("F510") ||
                parameterCode.equalsIgnoreCase("F520") ||
                parameterCode.equalsIgnoreCase("F530") ||
                parameterCode.equalsIgnoreCase("F540") ||
                parameterCode.equalsIgnoreCase("F550") ||
                parameterCode.equalsIgnoreCase("F560") ||
                parameterCode.equalsIgnoreCase("F570") ||
                parameterCode.equalsIgnoreCase("F580") ||
                parameterCode.equalsIgnoreCase("F590") ||
                parameterCode.equalsIgnoreCase("F5A0") ||
                parameterCode.equalsIgnoreCase("F5B0") ||
                parameterCode.equalsIgnoreCase("F5C0")) {
            String formatResult = extractMaxPowerAndDate3Phase(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("F004") ||
                parameterCode.equalsIgnoreCase("F021") ||
                parameterCode.equalsIgnoreCase("F002") ||
                parameterCode.equalsIgnoreCase("F300") ||
                parameterCode.equalsIgnoreCase("C100") ||
                parameterCode.equalsIgnoreCase("C101") ||
                parameterCode.equalsIgnoreCase("C102") ||
                parameterCode.equalsIgnoreCase("C103") ||
                parameterCode.equalsIgnoreCase("C104") ||
                parameterCode.equalsIgnoreCase("C105") ||
                parameterCode.equalsIgnoreCase("F029") ||
                parameterCode.equalsIgnoreCase("F111") ||
                parameterCode.equalsIgnoreCase("F121") ||
                parameterCode.equalsIgnoreCase("F131") ||
                parameterCode.equalsIgnoreCase("F141") ||
                parameterCode.equalsIgnoreCase("F151") ||
                parameterCode.equalsIgnoreCase("F161") ||
                parameterCode.equalsIgnoreCase("F171") ||
                parameterCode.equalsIgnoreCase("F181") ||
                parameterCode.equalsIgnoreCase("F191") ||
                parameterCode.equalsIgnoreCase("F1A1") ||
                parameterCode.equalsIgnoreCase("F1B1") ||
                parameterCode.equalsIgnoreCase("F1C1") ||
                parameterCode.equalsIgnoreCase("F610") ||
                parameterCode.equalsIgnoreCase("F620") ||
                parameterCode.equalsIgnoreCase("F630") ||
                parameterCode.equalsIgnoreCase("F640") ||
                parameterCode.equalsIgnoreCase("F650") ||
                parameterCode.equalsIgnoreCase("F660") ||
                parameterCode.equalsIgnoreCase("F670") ||
                parameterCode.equalsIgnoreCase("F680") ||
                parameterCode.equalsIgnoreCase("F690") ||
                parameterCode.equalsIgnoreCase("F6A0") ||
                parameterCode.equalsIgnoreCase("F6B0") ||
                parameterCode.equalsIgnoreCase("F6C0") ||
                parameterCode.equalsIgnoreCase("F310") ||
                parameterCode.equalsIgnoreCase("F320") ||
                parameterCode.equalsIgnoreCase("F330") ||
                parameterCode.equalsIgnoreCase("F340") ||
                parameterCode.equalsIgnoreCase("F350") ||
                parameterCode.equalsIgnoreCase("F360") ||
                parameterCode.equalsIgnoreCase("F370") ||
                parameterCode.equalsIgnoreCase("F380") ||
                parameterCode.equalsIgnoreCase("F390") ||
                parameterCode.equalsIgnoreCase("F3A0") ||
                parameterCode.equalsIgnoreCase("F3B0") ||
                parameterCode.equalsIgnoreCase("F3C0")) {
            String formatResult = extractDoubleValues(meterResponseData);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("F110") ||
                parameterCode.equalsIgnoreCase("F120") ||
                parameterCode.equalsIgnoreCase("F130") ||
                parameterCode.equalsIgnoreCase("F140") ||
                parameterCode.equalsIgnoreCase("F150") ||
                parameterCode.equalsIgnoreCase("F160") ||
                parameterCode.equalsIgnoreCase("F170") ||
                parameterCode.equalsIgnoreCase("F180") ||
                parameterCode.equalsIgnoreCase("F190") ||
                parameterCode.equalsIgnoreCase("F1A0") ||
                parameterCode.equalsIgnoreCase("F1B0") ||
                parameterCode.equalsIgnoreCase("F1C0")) {
            String[] dataList = meterResponseData.split(",");
            String formatResult = extractDoubleValues(dataList[2]);
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C360")) {
            String[] dataList = meterResponseData.split(",");
            String formatResult = null;
            if (dataList.length > 2) {
                formatResult = extractDoubleValues(dataList[5]);
            } else {
                formatResult = extractDoubleValues(dataList[2]);
            }
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("C290")) {
            String[] dataList = meterResponseData.split(",");
            String formatResult = null;
            if (dataList.length > 2) {
                formatResult = extractDoubleValues(dataList[5]);
            } else {
                formatResult = extractTechnicianNumberAndDate(meterResponseData);
            }
            return formatResult;
        }
        if (parameterCode.equalsIgnoreCase("D272"))
            return parseTariffStepsCombined(meterResponseData);
        if (parameterCode.equalsIgnoreCase("D040") ||
                parameterCode.equalsIgnoreCase("D041") ||
                parameterCode.equalsIgnoreCase("D042") ||
                parameterCode.equalsIgnoreCase("D043") ||
                parameterCode.equalsIgnoreCase("D044") ||
                parameterCode.equalsIgnoreCase("D045") ||
                parameterCode.equalsIgnoreCase("D046") ||
                parameterCode.equalsIgnoreCase("D047") ||
                parameterCode.equalsIgnoreCase("D048") ||
                parameterCode.equalsIgnoreCase("D049"))
            return meterResponseData.split("\\*")[0];
        return meterResponseData;
    }

    private String formatDateWithHours(String dataToBeFormatted) {
        String dateString, dateParts[] = dataToBeFormatted.split(" ");
        if (dateParts.length > 1) {
            String[] part1 = dateParts[0].split("-");
            dateString = String.valueOf(part1[2]) + "/" + part1[1] + "/" + "20" + part1[0] + " " + dateParts[1];
        } else {
            dateString = String.valueOf(dataToBeFormatted.substring(4, 6)) + "/" + dataToBeFormatted.substring(2, 4) + "/" + "20" +
                    dataToBeFormatted.substring(0, 2) + " " + dataToBeFormatted.substring(8, 10) + ":" +
                    dataToBeFormatted.substring(10, 12) + ":" + dataToBeFormatted.substring(12, 14);
        }
        return dateString;
    }

    private String formatDateWithoutHours(String dataToBeFormatted) {
        String dateString = String.valueOf(dataToBeFormatted.substring(4, 6)) + "/" + dataToBeFormatted.substring(2, 4) + "/" + "20" +
                dataToBeFormatted.substring(0, 2);
        return dateString;
    }

    private String parseMeterStatus(String dataToBeDecoded) {
        String decodeResult = null;
        String binaryResult = Integer.toBinaryString(Integer.parseInt(dataToBeDecoded, 16));
        if (!binaryResult.contains("0")) {
            decodeResult = "M0 R0 B0 T0 S0";
            return decodeResult;
        }
        if (binaryResult.length() < 16)
            binaryResult = "0" + binaryResult;
        decodeResult = "M1";
        if (binaryResult.substring(0, 1).equals("0")) {
            decodeResult = String.valueOf(decodeResult) + " R1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " R0";
        }
        if (binaryResult.substring(11, 12).equals("0")) {
            decodeResult = String.valueOf(decodeResult) + " B1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " B0";
        }
        if (binaryResult.substring(10, 11).equals("0")) {
            decodeResult = String.valueOf(decodeResult) + " T1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " T0";
        }
        if (binaryResult.substring(4, 5).equals("0")) {
            decodeResult = String.valueOf(decodeResult) + " S1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " S0";
        }
        return decodeResult;
    }

    private String parseMeterStatus3PhaseAlarm(String dataToBeDecoded) {
        String decodeResult = null;
        if (!dataToBeDecoded.contains("1")) {
            decodeResult = "M0 R0 B0 T0 S0";
            return decodeResult;
        }
        decodeResult = "M1";
        if (dataToBeDecoded.substring(6, 7).equals("1") || dataToBeDecoded.substring(7, 8).equals("1") ||
                dataToBeDecoded.substring(30, 31).equals("1") || dataToBeDecoded.substring(31, 32).equals("1")) {
            decodeResult = String.valueOf(decodeResult) + " R1";
        } else {
            return "M1 R0 B0 T0 S0";
        }
        if (dataToBeDecoded.substring(11, 12).equals("1")) {
            decodeResult = String.valueOf(decodeResult) + " B1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " B0";
        }
        if (dataToBeDecoded.substring(30, 31).equals("1") || dataToBeDecoded.substring(31, 32).equals("1")) {
            decodeResult = String.valueOf(decodeResult) + " T1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " T0";
        }
        if (dataToBeDecoded.substring(6, 7).equals("1") || dataToBeDecoded.substring(7, 8).equals("1")) {
            decodeResult = String.valueOf(decodeResult) + " S1";
        } else {
            decodeResult = String.valueOf(decodeResult) + " S0";
        }
        return decodeResult;
    }

    private String extractTechnicianNumberAndDate(String dataToBeManipulated) {
        String manipulationResult = null;
        String[] dataList = dataToBeManipulated.split(",");
        manipulationResult = String.valueOf(dataList[1]) + ",1,";
        manipulationResult = String.valueOf(manipulationResult) + formatDate(dataList[2]);
        return manipulationResult;
    }

    private String extractTechnicianNumberAndDateThreePhase(String dataToBeManipulated) {
        String manipulationResult = null;
        String[] dataList = dataToBeManipulated.split(",");
        manipulationResult = String.valueOf(dataList[1]) + ",1,";
        manipulationResult = String.valueOf(manipulationResult) + formatDate(dataList[0]);
        return manipulationResult;
    }

    private String extractMaxPowerAndDate(String dataToBeManipulated) {
        String manipulationResult = null;
        if ((dataToBeManipulated.split(",")).length > 1) {
            String[] dataList = dataToBeManipulated.split(",");
            String dateString = dataList[0].replaceAll("-", "").replaceAll(":", "");
            String maxPowerKWH = dataList[1].substring(0, 4);
            String maxPowerAMP = dataList[3].substring(0, 5);
            manipulationResult = String.valueOf(maxPowerKWH) + "," + maxPowerAMP + "," + formatDate(dateString);
        } else {
            String[] dataList = dataToBeManipulated.split("\\*");
            String dateString = dataList[0].substring(0, 12);
            String maxPowerKWH = dataList[0].substring(12);
            String maxPowerAMP = dataList[1].substring(14, 20);
            manipulationResult = String.valueOf(maxPowerKWH) + "," + maxPowerAMP + "," + formatDate(dateString);
        }
        return manipulationResult;
    }

    private String extractMaxPowerAndDate3Phase(String dataToBeManipulated) {
        String manipulationResult = null;
        String[] dataList = dataToBeManipulated.split(",");
        String dateString = dataList[1];
        String maxPowerKWH = dataList[0].split("\\*")[0];
        Double maxPowerAMP = Double.valueOf(Math.round(Double.parseDouble(maxPowerKWH) * 1000.0D / 220.0D * 1000.0D) / 1000.0D);
        manipulationResult = String.valueOf(maxPowerKWH) + "," + maxPowerAMP.toString() + "," + formatDate(dateString);
        return manipulationResult;
    }

    private String formatDate(String dateToBeFormatted) {
        return String.valueOf(dateToBeFormatted.substring(4, 6)) + "/" + dateToBeFormatted.substring(2, 4) + "/" + "20" +
                dateToBeFormatted.substring(0, 2) + " " + dateToBeFormatted.substring(6, 8) + ":" +
                dateToBeFormatted.substring(8, 10) + ":" + dateToBeFormatted.substring(10, 12);
    }

    private String prepareDateToBeFormatted(String data) {
        String result = null;
        String[] dataList = data.split(",");
        try {
            result = formatDate(dataList[2]);
        } catch (StringIndexOutOfBoundsException ex) {
            result = formatDate(dataList[3]);
        }
        return result;
    }

    private String extractDoubleValues(String data) {
        String[] dataList = data.split("\\*");
        return dataList[0];
    }

    private String parseTariffStepsCombined(String data) {
        if (data.contains(","))
            return parseTariffSteps3Phase(data);
        return parseTariffStepsV3(data);
    }

    private String parseTariffStepsV3(String data) {
        Integer numberOfSteps = Integer.valueOf(Integer.parseInt(data.substring(0, 2)));
        data = data.substring(2);
        String result = "";
        for (int i = 0; i < numberOfSteps.intValue(); i++) {
            if (i != 0)
                result = String.valueOf(result) + ",";
            result = String.valueOf(result) + (Double.parseDouble(data.substring(0, 8)) / 100.0D);
            data = data.substring(8);
        }
        return result;
    }

    private String parseTariffSteps3Phase(String data) {
        String[] dataArray = data.split(",");
        String result = "";
        for (int i = 1; i < Integer.parseInt(dataArray[0]); i++) {
            if (i != 1)
                result = String.valueOf(result) + ",";
            result = String.valueOf(result) + dataArray[i].split("\\*")[0];
        }
        return result;
    }
}
