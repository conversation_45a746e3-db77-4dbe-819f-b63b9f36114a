package shoaa.hay2areader;

import static java.sql.DriverManager.println;

import java.io.IOException;
import java.io.OutputStream;

public class DelayedOutputStream extends OutputStream{

    private OutputStream base ;
    private Long delayed ;

    public DelayedOutputStream(OutputStream base, Long delayed) {
        this.base = base;
        this.delayed = delayed;
    }

    @Override
    public void write(int b) throws IOException {
        base.write(b);
    }

    @Override
    public void close() throws IOException {
        println("DelayedOutputStream close");
        base.close();
    }

    @Override
    public void flush() throws IOException {
        if (delayed > 0) {
            try {
                Thread.sleep(delayed);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        base.flush();
    }

}
