package shoaa.globalreader;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class ParseMeterData {

    static final int SINGLEPHASE = 2;
    static final int THREEPHASE = 1;

    static final int STYLE_TYPE_RC = 10;
    static final int STYLE_TYPE_BT = 18;
    static final int STYLE_TYPE_BTC = 19;

    public ParseMeterData() {

    }

    private int getDecimal6(byte a, byte b, byte c, byte d, byte e, byte f) {
        int sa = a & 0xFF;
        int sb = b & 0xFF;
        int sc = c & 0xFF;
        int sd = d & 0xFF;
        int se = e & 0xFF;
        int sf = f & 0xFF;
        return (sa << 0) + (sb << 8) + (sc << 16) + (sd << 24) + (se << 32) + (sf << 40);
    }

    private int getDecimal(byte a, byte b, byte c, byte d) {
        int sa = a & 0xFF;
        int sb = b & 0xFF;
        int sc = c & 0xFF;
        int sd = d & 0xFF;
        return (sa << 0) + (sb << 8) + (sc << 16) + (sd << 24);
    }

    private byte hex2bcd(byte x) {
        //C++ TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
        //ORIGINAL LINE: unsigned char y;
        byte y;
        y = (byte) ((x / 10) << 4);
        y = (byte) (y | (x % 10));
        return (y);
    }

    private byte hex2bcd(int x) {
        //C++ TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
        //ORIGINAL LINE: unsigned char y;
        byte y;
        y = (byte) ((x / 10) << 4);
        y = (byte) (y | (x % 10));
        return (y);
    }

    private int getBCD(byte a, byte b, byte c, byte d) {
        byte[] V = new byte[4];
        byte[] M = new byte[9];
        V[0] = d;
        V[1] = c;
        V[2] = b;
        V[3] = a;
        bcd2asc(V, M, 4, M);
        String s = new String(M, StandardCharsets.US_ASCII);
        try {
            return Integer.parseInt(s.trim());
        } catch (Exception ex) {
            return 0;
        }
    }

    private void bcd2asc(byte[] hexstr, byte[] ascstr, int length, byte[] res) {
        int h, a;
        byte uc;

        ascstr[0] = 0x0;

        h = length - 1;
        a = length + length - 1;
        ascstr[a + 1] = '\0';
        while (h >= 0) {
            uc = (byte) (hexstr[h] & 0x0f);
            res[a--] = (byte) (uc + ((uc > 9) ? ('A' - 10) : '0'));
            uc = (byte) ((hexstr[h--] & 0xf0) >> 4);
            res[a--] = (byte) (uc + ((uc > 9) ? ('A' - 10) : '0'));
        }

    }

    public int ReadCardData(byte[] RetBuffer, METER_READ_DATA FeedBack) {

        byte[] PlainBuffer = new byte[1024];
        byte[] asciiBuffer = new byte[1024];
        byte FeedBackVersion = 0;
        byte ModelNumber;
        byte MeterPhase = 0;
        byte MeterStyle = 0;
        int Length;
        int CNTR;
        int newBufferLen = 0;
        long LastHalfID = 0;
        long FirstHalfID = 0;
        String sTmp1, sTmp2;
        Length = 0;

        FeedBack.LastChargeDateHour = 0;
        FeedBack.LastChargeDateMinute = 0;
        FeedBack.LastChargeDateSecond = 0;

        for (int Y = 0; Y < (RetBuffer.length - 1); Y++) {
            PlainBuffer[Y] = RetBuffer[Y + 1];
        }

        ModelNumber = 0;

        if ((PlainBuffer[0] == 'R') && (PlainBuffer[1] == '5')) ModelNumber = 1;
        else if ((PlainBuffer[0] == 'R') && (PlainBuffer[1] == '6')) ModelNumber = 2;
        else if ((PlainBuffer[0] == 'R') && (PlainBuffer[1] == '7')) ModelNumber = 3;
        else if ((PlainBuffer[0] == 'R') && (PlainBuffer[1] == '9')) ModelNumber = 4;
        else if ((PlainBuffer[0] == 'L') && (PlainBuffer[1] == '1')) ModelNumber = 5;
        else if ((PlainBuffer[0] == 'L') && (PlainBuffer[1] == '2')) ModelNumber = 6;
        else if ((PlainBuffer[0] == 'L') && (PlainBuffer[1] == '4')) ModelNumber = 7;
        else if ((PlainBuffer[0] == 'L') && (PlainBuffer[1] == '5')) ModelNumber = 8;
        else if ((PlainBuffer[0] == 'R') && (PlainBuffer[1] == 'D'))
            ModelNumber = 0; //ModelNumber=9;
        else if ((PlainBuffer[0] == 'C') && (PlainBuffer[1] == '0')) ModelNumber = 10;
        else if ((PlainBuffer[0] == 'C') && (PlainBuffer[1] == '1')) ModelNumber = 11;
        else if ((PlainBuffer[0] == 'Z') && (PlainBuffer[1] == 10))
            ModelNumber = 12; // Single BT First Record 162 bytes
        else if ((PlainBuffer[0] == 'Z') && (PlainBuffer[1] == 30))
            ModelNumber = 14; // Three  BT First Record 162 bytes
        else if ((PlainBuffer[0] == 'Z') && (PlainBuffer[1] == 50))
            ModelNumber = 15; // Three  BTC First Record CT Meters, 186 byte
        else if ((PlainBuffer[0] == 'Z') && (PlainBuffer[1] == 31))
            ModelNumber = 16; // Three  BT second version
        else if ((PlainBuffer[0] == 'Z') && (PlainBuffer[1] == 51))
            ModelNumber = 17; // Three  BTC second version
        else if ((PlainBuffer[0] == 'S') && (PlainBuffer[1] == 1)) ModelNumber = 100;
        else if ((PlainBuffer[0] == 'S') && (PlainBuffer[1] == 5)) ModelNumber = 100;

        if ((ModelNumber == 100)) {

            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal(PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal(PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal(PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal(PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
            CNTR += 4;
            FeedBack.LastChargeDate = 0;
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) - 2000) << 24);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0)) << 16);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0)) << 8);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0)) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0)) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0)) << 0);

            try {
                FeedBack.LastChargeDateDay = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMonth = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateYear = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);

                FeedBack.LastChargeDateHour = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateSecond = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            } catch (Exception e) {
                FeedBack.LastChargeDateHour = 0;
                FeedBack.LastChargeDateMinute = 0;
                FeedBack.LastChargeDateSecond = 0;
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }

            CNTR += 8;

            FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if (((0x0001 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;

            if (((0x0002 & FeedBack.MeterStat)) > 0)
                FeedBack.DoorOpen = 1;
            else
                FeedBack.DoorOpen = 0;

            if (((0x0004 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else {
                FeedBack.coveropen = 0;
                FeedBack.OpenTrials = 0;
            }

            if (((0x0008 & FeedBack.MeterStat)) > 0)
                FeedBack.overload = 1;
            else
                FeedBack.overload = 0;

            if (((0x0020 & FeedBack.MeterStat)) > 0)
                FeedBack.ReverseOpen = 1;
            else
                FeedBack.ReverseOpen = 0;

            if (((0x0040 & FeedBack.MeterStat)) > 0)
                FeedBack.EarthOpen = 1;
            else
                FeedBack.EarthOpen = 0;

            if (((0x0080 & FeedBack.MeterStat)) > 0)
                FeedBack.RelayOpen = 1;
            else
                FeedBack.RelayOpen = 0;
        }
        if ((ModelNumber == 12) || (ModelNumber == 14) || (ModelNumber == 15) || (ModelNumber == 16) || (ModelNumber == 17)) {

            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.MeterSerialNumber = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            if ((ModelNumber == 12) || (ModelNumber == 14) || (ModelNumber == 15)) {
                FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
            } else {

                CNTR += 4;
            }
            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
            CNTR += 4;
            FeedBack.LastChargeDate = 0;
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) - 2000) << 24);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0)) << 16);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0)) << 8);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0)) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0)) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd(getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0)) << 0);
            try {
                FeedBack.LastChargeDateDay = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMonth = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateYear = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);

                FeedBack.LastChargeDateHour = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateSecond = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            } catch (Exception e) {
                FeedBack.LastChargeDateHour = 0;
                FeedBack.LastChargeDateMinute = 0;
                FeedBack.LastChargeDateSecond = 0;
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }
            CNTR += 8;

            FeedBack.InstallCardID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.MeterInstallYears = (short) (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0));
            FeedBack.MeterInstallMonthes = (byte) (getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallDays = (byte) (getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallHours = (byte) (getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallMinute = (byte) (getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0));
            CNTR += 8;

            FeedBack.ControlCardID1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore1 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter1 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardID2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore2 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter2 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardID3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore3 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter3 = PlainBuffer[CNTR + 1];
            CNTR += 1;

            if (ModelNumber == 16) {
                FeedBack.consm_kvar1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm_kvar12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                FeedBack.MaximumDemand_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

            } else {
                FeedBack.Moneyconsm01 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm02 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm03 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm04 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm05 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm06 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm07 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm08 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm09 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.Moneyconsm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

            }
            if ((ModelNumber == 15) || (ModelNumber == 17)) {
                FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
            } else {
                FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }

            if (ModelNumber == 14 || ModelNumber == 15 || ModelNumber == 16 || ModelNumber == 17) {
                FeedBack.TotalConsumVAR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                FeedBack.ConsumptionKVARPos = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

                FeedBack.PowerFactor = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }

            if (ModelNumber == 15) {
                FeedBack.CT = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }

            if (((0x0001 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;

            if (((0x0002 & FeedBack.MeterStat)) > 0)
                FeedBack.DoorOpen = 1;
            else
                FeedBack.DoorOpen = 0;

            if (((0x0004 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else {
                FeedBack.coveropen = 0;
                FeedBack.OpenTrials = 0;
            }

            if (((0x0008 & FeedBack.MeterStat)) > 0)
                FeedBack.overload = 1;
            else
                FeedBack.overload = 0;

            if (((0x0020 & FeedBack.MeterStat)) > 0)
                FeedBack.ReverseOpen = 1;
            else
                FeedBack.ReverseOpen = 0;

            if (((0x0040 & FeedBack.MeterStat)) > 0)
                FeedBack.EarthOpen = 1;
            else
                FeedBack.EarthOpen = 0;

            if (((0x0080 & FeedBack.MeterStat)) > 0)
                FeedBack.RelayOpen = 1;
            else
                FeedBack.RelayOpen = 0;
        }
        ///////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 11) {

            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            CNTR += 14;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPndPrevious = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstrPrevious = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;

            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            if (((0x0001 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;
            if (((0x2000 & FeedBack.MeterStat) > 0) || ((0x0800 & FeedBack.MeterStat) > 0)) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;

        }

        ////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 10) {

            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.OpenKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            FeedBack.ConsumptionKVARPos = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ConsumptionKVARNeg = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.PowerFactor = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.PowerFactor_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.PowerFactor_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.PowerFactor_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.PowerFactor_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) * 10;
            CNTR += 2;
            FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            //MaximumDemandMonthly_1-MaximumDemandMonthly_12
            CNTR += 24;

            FeedBack.MaximumDemand_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

        }

        ///////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 9) {
            CNTR = 1;

            FeedBack.LastHalfID = 0;
            FeedBack.FirstHalfID = 0;
            FeedBack.MeterState = 1;
            FeedBack.ClientID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], ((byte) (PlainBuffer[CNTR + 4] & 0xF)));
            CNTR += 4;
        }
        ////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 6) {

            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            CNTR += 14;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPndPrevious = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstrPrevious = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if ((getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {

                FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 4;
            } else {
                FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 4;
            }

            FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            if (((0x0001 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;
            if (((0x2000 & FeedBack.MeterStat) > 0) || ((0x0800 & FeedBack.MeterStat) > 0)) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;

        }
        ////////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 5) {
            CNTR = 1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.OpenKiloWatt = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            //TotalKVAR_P
            CNTR += 4;
            //TotalKVAR_N
            CNTR += 4;
            FeedBack.PowerFactor = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if (((0x0001 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;
            if (((0x2000 & FeedBack.MeterStat) > 0) || ((0x0800 & FeedBack.MeterStat) > 0)) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;

        }
        /////////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 4 || ModelNumber == 40) {

            CNTR = 1;

            if (ModelNumber == 40) CNTR = -1;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) 0, (byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3]);
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //Remain in Slice
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //Days
            FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.SumOfAllChargesPND = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10);
            CNTR += 4;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            CNTR += 10;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //ImplementedCommands
            FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0) + 2000;
            CNTR += 4;
            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            if ((65535 & (256 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;
            if ((65535 & (0x200 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;

        }
        /////////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 3) {
            CNTR = 1;

            FeedBack.LastHalfID = 0;
            FeedBack.FirstHalfID = 0;
            FeedBack.ClientID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], ((byte) (PlainBuffer[CNTR + 4] & 0xF)));
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toString(FeedBack.ClientID)).replace(' ', '0');
            FeedBack.CardId = sTmp1;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2; //fraction
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //dayconsumption
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 5;
            FeedBack.DebitsInMeterPstr = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0)) % 100;
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 6;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 4;
            FeedBack.DebitConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 4; //battery

            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0) + 2000;
            CNTR += 4;

            if ((65535 & (256 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 1;
            else
                FeedBack.Battery = 0;
            if ((65535 & (0x200 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;
        }
        if (ModelNumber == 2) {
            CNTR = 1;

            FeedBack.LastHalfID = 0;
            FeedBack.FirstHalfID = 0;
            FeedBack.ClientID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], ((byte) (PlainBuffer[CNTR + 4] & 0xF)));
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toString(FeedBack.ClientID)).replace(' ', '0');
            FeedBack.CardId = sTmp1;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2; //fraction
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //dayconsumption
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.TotalConsumVAR = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2; //fraction
            FeedBack.CurrMonthConsumVAR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //history
            FeedBack.OpenKiloWatt = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            CNTR += 5;
            FeedBack.DebitsInMeterPstr = 0; //getDecimal((byte)PlainBuffer[CNTR+1],(byte)0,(byte)0,(byte)0);
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = 0; //getDecimal((byte)PlainBuffer[CNTR+1],(byte)PlainBuffer[CNTR+2],(byte)0,(byte)0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0)) % 100;
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 4;

            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0) + 2000;
            CNTR += 4;

            if ((65535 & (256 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 0; //1;
            else
                FeedBack.Battery = 0;
            if ((65535 & (0x200 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;
        }
        /////////////////////////////////////////////////////////////////////////////////////////
        if (ModelNumber == 1) {
            CNTR = 1;

            FeedBack.LastHalfID = 0;
            FeedBack.FirstHalfID = 0;
            FeedBack.ClientID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], ((byte) (PlainBuffer[CNTR + 4] & 0xF)));
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toString(FeedBack.ClientID)).replace(' ', '0');
            FeedBack.CardId = sTmp1;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2; //fraction
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; //dayconsumption
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 5;
            FeedBack.DebitsInMeterPstr = 0; //getDecimal((byte)PlainBuffer[CNTR+1],(byte)0,(byte)0,(byte)0);
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = 0; //getDecimal((byte)PlainBuffer[CNTR+1],(byte)PlainBuffer[CNTR+2],(byte)0,(byte)0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0)) % 100;
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 6;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 4;
            FeedBack.DebitConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 4; //battery

            FeedBack.consm1 = 0;
            CNTR += 2;
            FeedBack.consm2 = 0;
            CNTR += 2;
            FeedBack.consm3 = 0;
            CNTR += 2;
            FeedBack.consm4 = 0;
            CNTR += 2;
            FeedBack.consm5 = 0;
            CNTR += 2;
            FeedBack.consm6 = 0;
            CNTR += 2;
            FeedBack.consm7 = 0;
            CNTR += 2;
            FeedBack.consm8 = 0;
            CNTR += 2;
            FeedBack.consm9 = 0;
            CNTR += 2;
            FeedBack.consm10 = 0;
            CNTR += 2;
            FeedBack.consm11 = 0;
            CNTR += 2;
            FeedBack.consm12 = 0;
            CNTR += 2;

            FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0) + 2000;
            CNTR += 4;

            if ((65535 & (256 & FeedBack.MeterStat)) > 0)
                FeedBack.Battery = 0; //1;
            else
                FeedBack.Battery = 0;
            if ((65535 & (0x200 & FeedBack.MeterStat)) > 0) {
                FeedBack.coveropen = 1;
                FeedBack.OpenTrials = 1;
            } else
                FeedBack.coveropen = 0;
        }
        return 0;
    }
    ////////////////////////////////////////////////////////////////

    public int ReadCollectionCardRecord(byte[] RetBuffer, METER_READ_DATA FeedBack) {

        byte[] PlainBuffer = new byte[1024];
        byte[] asciiBuffer = new byte[1024];
        byte FeedBackVersion = 0;
        byte ModelNumber;

        byte MeterStyle = 0;
        int Length;
        int CNTR;
        int newBufferLen = 0;
        long LastHalfID = 0;
        long FirstHalfID = 0;
        String sTmp1, sTmp2;
        Length = 0;

        FeedBack.LastChargeDateHour = 0;
        FeedBack.LastChargeDateMinute = 0;
        FeedBack.LastChargeDateSecond = 0;

        if ((RetBuffer[0] >= '0' && RetBuffer[0] <= '9') || (RetBuffer[0] >= 'A' && RetBuffer[0] <= 'F')) //ascii string
        {
            StringBuilder builder = new StringBuilder();
            for (byte c : RetBuffer) {
                int i = (int) c;
                builder.append(String.format("%02x", i).toUpperCase());
            }
            byte[] y1 = hexStringToByteArray(builder.toString());
            for (int b = 0; b < y1.length; b++) {
                if (y1[b] < '0' || y1[b] > 'Z') y1[b] = 0x30;
            }
            String s = new String(y1, StandardCharsets.UTF_8);

            byte[] y2 = hexStringToByteArray(s);
            for (Length = 0; Length < y2.length; Length++) {
                PlainBuffer[Length] = y2[Length];
            }
            newBufferLen = y2.length;
        } else {
            for (Length = 0; Length < RetBuffer.length; Length++) {
                PlainBuffer[Length] = RetBuffer[Length];
            }
            newBufferLen = RetBuffer.length;
        }

        ModelNumber = 0;

        int MeterTypeVersion = 1;

        if (
                (((byte) PlainBuffer[0] == (byte)
                        'R') && ((byte) PlainBuffer[1] == (byte)
                        'D') && ((byte) PlainBuffer[2] == (byte)
                        '1') && ((byte) PlainBuffer[3] == (byte)
                        '1')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '1') && ((byte) PlainBuffer[3] == (byte)
                                '2')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '3') && ((byte) PlainBuffer[3] == (byte)
                                '2'))
        ) {
            MeterTypeVersion = 2;
        }
        if (
                (((byte) PlainBuffer[0] == (byte)
                        'R') && ((byte) PlainBuffer[1] == (byte)
                        'D') && ((byte) PlainBuffer[2] == (byte)
                        '1') && ((byte) PlainBuffer[3] == (byte)
                        '0')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '1') && ((byte) PlainBuffer[3] == (byte)
                                '1')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '1') && ((byte) PlainBuffer[3] == (byte)
                                '2'))
        ) {
            ModelNumber = 20;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_BT;
        } else if (((byte) PlainBuffer[0] == (byte)
                'R') && ((byte) PlainBuffer[1] == (byte)
                'D') && ((byte) PlainBuffer[2] == (byte)
                '5') && ((byte) PlainBuffer[3] == (byte)
                '0')) {
            ModelNumber = 20;
            FeedBack.MeterPhase = THREEPHASE;
            MeterStyle = STYLE_TYPE_BTC;
        } else if (
                (((byte) PlainBuffer[0] == (byte)
                        'R') && ((byte) PlainBuffer[1] == (byte)
                        'D') && ((byte) PlainBuffer[2] == (byte)
                        '3') && ((byte) PlainBuffer[3] == (byte)
                        '0')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '3') && ((byte) PlainBuffer[3] == (byte)
                                '1')) ||
                        (((byte) PlainBuffer[0] == (byte)
                                'R') && ((byte) PlainBuffer[1] == (byte)
                                'D') && ((byte) PlainBuffer[2] == (byte)
                                '3') && ((byte) PlainBuffer[3] == (byte)
                                '2'))
        ) {
            ModelNumber = 20;
            FeedBack.MeterPhase = THREEPHASE;
            MeterStyle = STYLE_TYPE_BT;

        } else if (newBufferLen == 338 || newBufferLen == 334) // Threephase 2018
        {
            ModelNumber = 24;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_RC;
        } else if (newBufferLen == 166) // single 2015
        {
            ModelNumber = 22;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_RC;
        } else if (newBufferLen == 176) // three 2015
        {
            ModelNumber = 25;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_RC;
        } else if (newBufferLen == 150) // single 2015
        {
            ModelNumber = 23;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_RC;
        } else {
            ModelNumber = 21;
            FeedBack.MeterPhase = SINGLEPHASE;
            MeterStyle = STYLE_TYPE_RC;
        }

        if (ModelNumber == 0) {
            return 0;
        }

        if ((ModelNumber == 20)) {
            CNTR = 3;
            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            FeedBack.MeterSerialNumber = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.MaxLoadKW = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0));
            CNTR += 2;
            FeedBack.MaxLoadYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.MaxLoadMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8; // Time Span
            FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 8; // Time Span
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.RemainInDays = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.RemainCurrSlice = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            // UL_KW  number of kilo consumed in grace period
            CNTR += 2;
            // UL_Days number of days consumed in grace period
            CNTR += 2;
            // DayConsumption
            CNTR += 2;
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.SliceNo = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0) + 1;
            CNTR += 1;
            // T_Tariff
            CNTR += 1;
            // credit id
            CNTR += 2;
            FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
            CNTR += 4;
            FeedBack.LastChargeDate = 0;
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) - 2000)) << 24);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0))) << 16);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0))) << 8);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0))) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0))) << 0);
            FeedBack.LastChargeDate = FeedBack.LastChargeDate + (hex2bcd((byte) (getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0))) << 0);

            try {
                FeedBack.LastChargeDateDay = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMonth = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateYear = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);

                FeedBack.LastChargeDateHour = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.LastChargeDateSecond = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            } catch (Exception e) {
                FeedBack.LastChargeDateHour = 0;
                FeedBack.LastChargeDateMinute = 0;
                FeedBack.LastChargeDateSecond = 0;
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }

            CNTR += 8;

            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPndPrevious = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstrPrevious = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            // Consumption in day in money
            CNTR += 4;
            FeedBack.CurrentMonthMoneyConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            //Signature singlephase: 0x280C     threephase: 0x0835    CT: 0x09C5
            CNTR += 2;
            //CRC  starting from total consumption XOR for each word
            CNTR += 2;
            //statusbyteInstantly
            CNTR += 2;
            FeedBack.MeterState = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; // UsedCardsCommands=Commands implemented on this meter from control cards

            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RMinutes = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RSeconds = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);

            CNTR += 8;
            // DUMMY
            CNTR += 2;

            FeedBack.Challenge1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Challenge2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            FeedBack.DoorOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.DoorOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.DoorOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.DoorOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.DoorOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.DoorOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            FeedBack.CoverOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.CoverOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.coveropen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.OpenTrials = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            FeedBack.EarthOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.EarthOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.EarthOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.EarthOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.EarthOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.EarthOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            FeedBack.ReverseOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.ReverseOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.ReverseOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            FeedBack.Battery = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) * 10000 + (getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0) * 100) + (getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0));
            CNTR += 8;

            if ((FeedBack.MeterState & 0x0001) > 0) {
                FeedBack.Battery = 1;
            } else {
                FeedBack.Battery = 0;
            }

            FeedBack.RelayOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.RelayOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            FeedBack.ConsumptionInEarth = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ConsumptionInReverse = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            CNTR += 1; // OverVolt
            CNTR += 1; // OverCurrent
            FeedBack.ROverLoad = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.PowerFactor = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.All_Fail_Count = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if (FeedBack.MeterPhase == THREEPHASE) {
                FeedBack.Ph1OpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.Ph1OpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph1OpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph1OpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph1OpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph1Open = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;

                FeedBack.Ph2OpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.Ph2OpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph2OpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph2OpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph2OpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph2Open = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;

                FeedBack.Ph3OpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.Ph3OpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph3OpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph3OpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph3OpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ph3Open = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;

                FeedBack.ConsumptionKVARPos = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.ConsumptionKVARNeg = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.CurrMonthConsumVAR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.OpenKiloWatt = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

            }

            FeedBack.ControlCardID1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore1 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter1 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardID2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore2 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter2 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardID3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardStateBefore3 = PlainBuffer[CNTR + 1];
            CNTR += 1;
            FeedBack.ControlCardStateAfter3 = PlainBuffer[CNTR + 1];
            CNTR += 1;

            FeedBack.InstallCardID = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.MeterInstallYears = (short) (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0));
            FeedBack.MeterInstallMonthes = (byte) (getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallDays = (byte) (getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallHours = (byte) (getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallMinute = (byte) (getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0));
            FeedBack.MeterInstallSeconds = (byte) (getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0));
            CNTR += 8;

            //CNTR += 2; // echo of NumberOfChargesOnSystem
            if (FeedBack.MeterPhase == THREEPHASE) {
                CNTR += 2;
            }

            if (MeterStyle == STYLE_TYPE_BTC) {
                FeedBack.CT = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }
            if (FeedBack.MeterPhase == SINGLEPHASE) {
                CNTR += 2;
            }

            if (MeterTypeVersion > 1) {
                CNTR += 36;
            }
            FeedBack.Moneyconsm01 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm02 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm03 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm04 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm05 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm06 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm07 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm08 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm09 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.Moneyconsm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            if (MeterStyle == STYLE_TYPE_BTC) {
                FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
            } else {
                FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }

            if (MeterStyle == STYLE_TYPE_BTC) {
                //DUMMY
                CNTR += 8;
            }

            FeedBack.MaximumDemand_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if (FeedBack.MeterPhase == THREEPHASE) {

                FeedBack.MaximumDemand_5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
            }

            if ((FeedBack.MeterPhase == THREEPHASE) && (MeterStyle != STYLE_TYPE_BTC)) {
                //DUMMY
                CNTR += 8;
            }

            if (FeedBack.MeterPhase == THREEPHASE) {
                if (MeterStyle == STYLE_TYPE_BTC) {
                    //Hist_kVarh         DS        4*12
                    FeedBack.consm_kvar1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;
                    FeedBack.consm_kvar12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                    CNTR += 4;

                } else {
                    FeedBack.consm_kvar1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                    FeedBack.consm_kvar12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    CNTR += 2;
                }

                FeedBack.PowerFactor_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.PowerFactor_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.PowerFactor_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.PowerFactor_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                CNTR += (8 * 2);

            }
            //////////////////////////////////////////

            FeedBack.SettledDepitsPiasters = 0;

            FeedBack.SettledDepitsPiasters = ((FeedBack.DebitsInMeterPndPrevious * 100 + FeedBack.DebitsInMeterPstrPrevious));

        }

        if ((ModelNumber == 21)) // 2016 V2 Single
        {
            FeedBackVersion = 2;

            if (FeedBackVersion == 1) {
                CNTR = -1;

                FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.DebitConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.RemainCurrSlice = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.RemainInDays = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.SumOfAllChargesPND = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.SumOfAllChargesPSTR = ((getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10);
                CNTR += 4;
                FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
                CNTR += 4;
                FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
                CNTR += 4;
                FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
                CNTR += 4;
                FeedBack.LastChargeDate = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                try {
                    FeedBack.LastChargeDateDay = (FeedBack.LastChargeDate & 0xFF000000) >> 24;
                    FeedBack.LastChargeDateMonth = (FeedBack.LastChargeDate & 0xFF0000) >> 16;
                    FeedBack.LastChargeDateYear = (FeedBack.LastChargeDate & 0xFFFF);
                } catch (Exception e) {
                    FeedBack.LastChargeDateDay = 1;
                    FeedBack.LastChargeDateMonth = 1;
                    FeedBack.LastChargeDateYear = 2020;
                }
                CNTR += 4; // LastChargeTimeSpan
                CNTR += 2; // credit id
                FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                CNTR += 2; // UsedCardsCommands=Commands implemented on this meter from control cards
                FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.readcount = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.SliceNo = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0) + 1;
                CNTR += 1;
                FeedBack.ROverLoad = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 1;

                FeedBack.CoverOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
                FeedBack.CoverOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.coveropen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 6;

                FeedBack.RelayOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
                FeedBack.RelayOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 6;

                FeedBack.EarthOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
                FeedBack.EarthOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 6;

                FeedBack.ReverseOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
                FeedBack.ReverseOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 6;

                //here

                FeedBack.Battery = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                if (FeedBack.Battery > 0) {
                    FeedBack.Battery = 1;
                } else {
                    FeedBack.Battery = 0;
                }
        /*if ((65535 & (256 & FeedBack.MeterStat)) > 0)
        	FeedBack.Battery= 1;
        else
        	FeedBack.Battery= 0;*/
                CNTR += 4;

                CNTR += 2; //CurrDayConsumption

                CNTR += 7; //DUMMY
                CNTR += 3; //Sec

                FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0) + 2000;
                CNTR += 4;

                FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                FeedBack.MaximumDemand_1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                FeedBack.ControlCardID1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.ControlCardID2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.ControlCardID3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

                FeedBack.MeterSerialNumber = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

            }
            if (FeedBackVersion == 2) {

                CNTR = -1;

                FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.OpenKiloWatt = 0;
                CNTR += 4;
                FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                // DayConsumption
                CNTR += 2;
                FeedBack.RemainCurrSlice = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.RemainInDays = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.SliceNo = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0) + 1;
                CNTR += 1;
                CNTR += 1;
                FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                CNTR += 2; // UsedCardsCommands=Commands implemented on this meter from control cards

                FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
                FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
                CNTR += 6;
                FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
                CNTR += 4;
                FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
                CNTR += 4;
                FeedBack.LastChargeDate = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                try {
                    FeedBack.LastChargeDateDay = (FeedBack.LastChargeDate & 0xFF000000) >> 24;
                    FeedBack.LastChargeDateMonth = (FeedBack.LastChargeDate & 0xFF0000) >> 16;
                    FeedBack.LastChargeDateYear = (FeedBack.LastChargeDate & 0xFFFF);
                } catch (Exception e) {
                    FeedBack.LastChargeDateDay = 1;
                    FeedBack.LastChargeDateMonth = 1;
                    FeedBack.LastChargeDateYear = 2020;
                }
                CNTR += 7; // LastChargeTimeSpan
                CNTR += 1;
                CNTR += 2; // credit id
                FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
                CNTR += 4;
                FeedBack.DebitsInMeterPndPrevious = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
                FeedBack.DebitsInMeterPstrPrevious = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
                CNTR += 4;

                FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaxLoadYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.MaxLoadMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.MaxLoadDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.MaxLoadHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.MaxLoadMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8; // TimeSpan
                FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) / 10f;
                CNTR += 2;
                CNTR += 8; // TimeSpan
                //statusbyteInstantly
                CNTR += 2;
                FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.DoorOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.DoorOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.DoorOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.DoorOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.DoorOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }
                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.CoverOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.CoverOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.coveropen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.CoverOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.CoverOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.CoverOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.coveropen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.OpenTrials = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }

                if ((0xFFFF & (1 & FeedBack.MeterStat)) > 0) {
                    FeedBack.Battery = 1;
                } else {
                    FeedBack.Battery = 0;
                }

                //Battery
                CNTR += 8;

                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.RelayOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.RelayOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.RelayOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.RelayOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RelayOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }

                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.EarthOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.EarthOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.EarthOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.EarthOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.EarthOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }

                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.ReverseOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.ReverseOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.ReverseOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }

                FeedBack.All_Fail_Count = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                CNTR += 2; // overvoltage,overcurrent

                FeedBack.ROverLoad = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 1;
                CNTR += 1;
                CNTR += 1;
                CNTR += 2;

                if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                    FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                } else {
                    FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                    FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                    FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                    CNTR += 8;
                }

                CNTR += 8;

                FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                FeedBack.MaximumDemand_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;
                FeedBack.MaximumDemand_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                CNTR += 2;

                FeedBack.ControlCardID1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.ControlCardID2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.ControlCardID3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                //ControlCardID4 = getBCD((byte)PlainBuffer[CNTR + 1],(byte)PlainBuffer[CNTR + 2],(byte)PlainBuffer[CNTR + 3],(byte)PlainBuffer[CNTR + 4]);
                CNTR += 4;

                FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;
                sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
                sTmp1 = sTmp1.substring(sTmp1.length() - 8);
                sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
                sTmp2 = sTmp2.substring(sTmp2.length() - 8);
                FeedBack.CardId = sTmp1.substring(2) + sTmp2;

                CNTR += 8;
                FeedBack.MeterSerialNumber = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
                CNTR += 4;

            }
        }
        if (ModelNumber == 22) {
            CNTR = 13;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2;
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.readcount = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 4;
            CNTR += 4;
            CNTR += 4;
            FeedBack.LastChargeDate = getBCD((byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) 0x20);
            try {
                FeedBack.LastChargeDateDay = (FeedBack.LastChargeDate & 0xFF000000) >> 24;
                FeedBack.LastChargeDateMonth = (FeedBack.LastChargeDate & 0xFF0000) >> 16;
                FeedBack.LastChargeDateYear = (FeedBack.LastChargeDate & 0xFFFF);
            } catch (Exception e) {
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }
            CNTR += 4;
            FeedBack.Battery = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            if (FeedBack.Battery > 0) {
                FeedBack.Battery = 1;
            } else {
                FeedBack.Battery = 0;
            }
            CNTR += 4;
            CNTR += 8;

            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            CNTR += 5;
            //end of C1 //////////////////////////

            CNTR += 14;
            CNTR += 8;

            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.CoverOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
            FeedBack.CoverOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.coveropen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.RelayOpenYears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
            FeedBack.RelayOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpenMinute = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RelayOpen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 6;

            FeedBack.ROverLoad = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;

            CNTR += 11;
            ////////////////////////
            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

        }
        if (ModelNumber == 25) {
            CNTR = -1;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadAM = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2;
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.TotalConsumVAR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2;
            CNTR += 4;
            FeedBack.OpenKiloWatt = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2;

            FeedBack.readcount = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2;

            CNTR += 12;
            FeedBack.Battery = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            if (FeedBack.Battery > 0) {
                FeedBack.Battery = 1;
            } else {
                FeedBack.Battery = 0;
            }
            CNTR += 4;

            FeedBack.SumOfAllChargesPND = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) 0);
            CNTR += 3;
            FeedBack.SumOfAllChargesPSTR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;
            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 4;

            CNTR += 2;
            CNTR += 6;
            CNTR += 4;
            CNTR += 30;
            CNTR += 14;

            FeedBack.RHours = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Ryears = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0) + 2000;
            CNTR += 6;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

        }
        if (ModelNumber == 23) {
            CNTR = 13;

            FeedBack.TotalConsum = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 2;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2;
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2;

            FeedBack.coveropen = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenHours = getBCD((byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenDays = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.CoverOpenYears = getBCD((byte) PlainBuffer[CNTR + 5], (byte) 0x20, (byte) 0, (byte) 0);
            CNTR += 5;

            FeedBack.ROverLoad = getBCD((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;

            FeedBack.Battery = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            if (FeedBack.Battery > 0) {
                FeedBack.Battery = 1;
            } else {
                FeedBack.Battery = 0;
            }
            CNTR += 4;
            CNTR += 6;

            FeedBack.readcount = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            CNTR += 1;
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 1;
            FeedBack.RemainPiasters = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 1;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 4;
            CNTR += 4;
            FeedBack.LastChargeDate = getBCD((byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) 0x20);
            try {
                FeedBack.LastChargeDateDay = FeedBack.LastChargeDate % 100;
                FeedBack.LastChargeDateMonth = ((FeedBack.LastChargeDate / 100) % 100);
                FeedBack.LastChargeDateYear = ((FeedBack.LastChargeDate / 10000) % 10000);
            } catch (Exception e) {
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }
            CNTR += 4;

            CNTR += 5;
            //end of C1 //////////////////////////

            CNTR += 14;
            FeedBack.consm1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            CNTR += 16;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;
            ////////////////////////

        }
        if (ModelNumber == 24) // Threephase 2016 V2 - 2017 - 2018
        {

            CNTR = -1;

            FeedBack.TotalConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.OpenKiloWatt = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.DebitConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.CurrConsum = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            // DayConsumption
            CNTR += 2;
            FeedBack.RemainCurrSlice = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.RemainKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.RemainInDays = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.SliceNo = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0) + 1;
            CNTR += 1;
            CNTR += 1;
            FeedBack.chargeCount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.readcount = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            CNTR += 2; // UsedCardsCommands=Commands implemented on this meter from control cards

            FeedBack.SumOfAllChargesPND = getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) / 1000;
            FeedBack.SumOfAllChargesPSTR = ((getDecimal6((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4], (byte) PlainBuffer[CNTR + 5], (byte) PlainBuffer[CNTR + 6]) % 1000) / 10);
            CNTR += 6;
            FeedBack.RemainPound = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.RemainPiasters = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            FeedBack.CrdLastBalance = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 10;
            CNTR += 4;
            FeedBack.LastChargeDate = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            try {
                FeedBack.LastChargeDateDay = (FeedBack.LastChargeDate & 0xFF000000) >> 24;
                FeedBack.LastChargeDateMonth = (FeedBack.LastChargeDate & 0xFF0000) >> 16;
                FeedBack.LastChargeDateYear = (FeedBack.LastChargeDate & 0xFFFF);
            } catch (Exception e) {
                FeedBack.LastChargeDateDay = 1;
                FeedBack.LastChargeDateMonth = 1;
                FeedBack.LastChargeDateYear = 2020;
            }
            CNTR += 7; // LastChargeTimeSpan
            CNTR += 1;
            CNTR += 2; // credit id
            FeedBack.DebitsInMeterPnd = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstr = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;
            FeedBack.DebitsInMeterPndPrevious = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) / 1000;
            FeedBack.DebitsInMeterPstrPrevious = (getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]) % 1000) / 10;
            CNTR += 4;

            FeedBack.MaxLoadKW = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaxLoadYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.MaxLoadMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.MaxLoadMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8; // TimeSpan
            FeedBack.MaxLoadAM = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) / 10f;
            CNTR += 2;
            CNTR += 8; // TimeSpan
            //5*16
            //statusbyteInstantly
            CNTR += 2;
            FeedBack.MeterStat = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                FeedBack.DoorOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.DoorOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            } else {
                FeedBack.DoorOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.DoorOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.DoorOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            }
            if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                FeedBack.CoverOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.CoverOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.coveropen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.OpenTrials = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            } else {
                FeedBack.CoverOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.CoverOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.CoverOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.coveropen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.OpenTrials = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            }

            FeedBack.MeterInstallYears = (short) FeedBack.CoverOpenYears;
            FeedBack.MeterInstallMonthes = (byte) FeedBack.CoverOpenMonthes;
            FeedBack.MeterInstallDays = (byte) FeedBack.CoverOpenDays;
            FeedBack.MeterInstallHours = (byte) FeedBack.CoverOpenHours;
            FeedBack.MeterInstallMinute = (byte) FeedBack.CoverOpenMinute;
            FeedBack.MeterInstallSeconds = 0;

            if ((0xFFFF & (1 & FeedBack.MeterStat)) > 0) {
                FeedBack.Battery = 1;
            } else {
                FeedBack.Battery = 0;
            }

            //Battery
            CNTR += 8;

            if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                FeedBack.RelayOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.RelayOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            } else {
                FeedBack.RelayOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.RelayOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.RelayOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            }

            if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                FeedBack.EarthOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.EarthOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            } else {
                FeedBack.EarthOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.EarthOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.EarthOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            }

            if ((getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) > 2000) && (getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0) < 2030)) {
                FeedBack.ReverseOpenYears = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.ReverseOpenMonthes = getBCD((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenDays = getBCD((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenHours = getBCD((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenMinute = getBCD((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpen = getBCD((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            } else {
                FeedBack.ReverseOpenYears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
                FeedBack.ReverseOpenMonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenDays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpenMinute = getDecimal((byte) PlainBuffer[CNTR + 7], (byte) 0, (byte) 0, (byte) 0);
                FeedBack.ReverseOpen = getDecimal((byte) PlainBuffer[CNTR + 8], (byte) 0, (byte) 0, (byte) 0);
                CNTR += 8;
            }

            CNTR += 8; //ph1
            CNTR += 8; //ph1
            CNTR += 8; //ph1

            FeedBack.All_Fail_Count = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            CNTR += 2; // overvoltage,overcurrent

            //10*16

            FeedBack.ROverLoad = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.TotalConsumVAR = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            CNTR += 4;
            FeedBack.PowerFactor = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            CNTR += 1;

            FeedBack.Ryears = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            FeedBack.Rmonthes = getDecimal((byte) PlainBuffer[CNTR + 3], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.Rdays = getDecimal((byte) PlainBuffer[CNTR + 4], (byte) 0, (byte) 0, (byte) 0);
            FeedBack.RHours = getDecimal((byte) PlainBuffer[CNTR + 6], (byte) 0, (byte) 0, (byte) 0);
            CNTR += 8;

            CNTR += 8;

            CNTR += 16;

            FeedBack.consm1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm5 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm6 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm7 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm8 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm9 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm10 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm11 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.consm12 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.MaximumDemand_1 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_2 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_3 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;
            FeedBack.MaximumDemand_4 = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) 0, (byte) 0);
            CNTR += 2;

            FeedBack.ControlCardID1 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardID2 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.ControlCardID3 = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            //ControlCardID4 = getBCD((byte)PlainBuffer[CNTR + 1],(byte)PlainBuffer[CNTR + 2],(byte)PlainBuffer[CNTR + 3],(byte)PlainBuffer[CNTR + 4]);
            CNTR += 4;

            FeedBack.LastHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;
            FeedBack.FirstHalfID = getDecimal((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

            sTmp1 = String.format("%8s", Long.toHexString(FeedBack.FirstHalfID)).replace(' ', '0');
            sTmp1 = sTmp1.substring(sTmp1.length() - 8);
            sTmp2 = String.format("%8s", Long.toHexString(FeedBack.LastHalfID)).replace(' ', '0');
            sTmp2 = sTmp2.substring(sTmp2.length() - 8);
            FeedBack.CardId = sTmp1.substring(2) + sTmp2;

            CNTR += 8;
            FeedBack.MeterSerialNumber = getBCD((byte) PlainBuffer[CNTR + 1], (byte) PlainBuffer[CNTR + 2], (byte) PlainBuffer[CNTR + 3], (byte) PlainBuffer[CNTR + 4]);
            CNTR += 4;

        }

        if ((FeedBack.MeterSerialNumber < 10000000) || (FeedBack.MeterSerialNumber > 99999999))
            FeedBack.MeterSerialNumber = 0;
        if ((FeedBack.TotalConsum > 1000000))
            FeedBack.TotalConsum = 0;
        if ((FeedBack.DebitsInMeterPnd > 1000000))
            FeedBack.DebitsInMeterPnd = 0;
        if ((FeedBack.DebitsInMeterPnd > 0) && (FeedBack.RemainPound > 0))
            FeedBack.DebitsInMeterPnd = 0;
        if (FeedBack.MaxLoadKW > 10000)
            FeedBack.MaxLoadKW = 0;

        return 0;
    }

    private byte[] hexStringToByteArray(String s) {
        boolean isPadded = false;
        int i;
        int len = s.length();
        if ((len % 2) > 0) {
            s += "0";
            len++;
        }
        byte[] data = new byte[len / 2];
        try {
            for (i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) +
                        Character.digit(s.charAt(i + 1), 16));
            }
        } catch (Exception e) {
            System.out.println("");
        }
        return data;
    }
}