<component name="libraryTable">
  <library name="Gradle: androidx.print:print:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e5512a8d0572de98a9615cc1d892b9f4/transformed/print-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4731a4c51ed82042540357d733741736/transformed/print-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/84091db59829d25ae121acf7e47ccc2c/transformed/print-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4a87fc18919d7430ee2d464f327b16a3/transformed/print-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e5512a8d0572de98a9615cc1d892b9f4/transformed/print-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d8a73ae7abaa2f9b03e0c4d9b67d1423/transformed/print-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/e5512a8d0572de98a9615cc1d892b9f4/transformed/print-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e5512a8d0572de98a9615cc1d892b9f4/transformed/print-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.print/print/1.0.0/71fc2d9acf7cce6b96230c5af263268b1664914a/print-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>