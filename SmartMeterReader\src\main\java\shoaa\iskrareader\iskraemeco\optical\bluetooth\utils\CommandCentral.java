package shoaa.iskrareader.iskraemeco.optical.bluetooth.utils;

import org.apache.commons.lang3.ArrayUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CommandCentral {
    public static final Byte SOH = 1;
    public static final Byte STX = 2;
    public static final Byte ETX = 3;
    public static final Byte ACK = 6;
    public static final Byte CR = 10;
    public static final Byte LF = 13;
    public static final Byte OBKT = 40;
    public static final Byte CBKT = 41;
    public static final Byte RCMD = 82;
    public static final Byte CMDSUFFEX = 49;
    Byte[] CMD_INIT = new Byte[]{(byte) 47, (byte) 63, (byte) 33, LF, CR};
    Byte[] CMD_ACK = new Byte[]{(byte) 6, (byte) 48, (byte) 51, (byte) 49, LF, CR};
    HashMap<String, String> hashMap = new HashMap();

    public CommandCentral() {
        this.hashMap.put("/WSE4DDSD101-IT12-0-1", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT21-0-1", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT12-0-6", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT12-0-5", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT22-0-1", "ISKRA_1012_V1");
        this.hashMap.put("/WSE4DDSD101-IT22-0-2", "ISKRA_1012_V1");
        this.hashMap.put("/WSE4DDSD101-IT22-0-3", "ISKRA_1012_V1");
        this.hashMap.put("/WSE4DDSD101-IT12-0-4", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT12-0-5", "ISKRA_1011_V1");
        this.hashMap.put("/WSE4DDSD101-IT22-0-6", "ISKRA_1012_V1");
        this.hashMap.put("/WSE4DDSD101-IT22-0-7", "ISKRA_1012_V1");
        this.hashMap.put("/WSE3DDSD101-IT23-0-2", "ISKRA_1012_V3");
        this.hashMap.put("/ME514", "ISKRA_1012_V4");
        this.hashMap.put("/ME514-5", "ISKRA_1020_V5");
        this.hashMap.put("/WSE3550000", "ISKRA_3010");
        this.hashMap.put("/ISE4XYZ000", "ISKRA_3012");
    }

    public String getHandShakeCommand() {
        return this.convertCommandToHexStr(this.CMD_INIT);
    }

    public String validateHandShakeCommand(String handShakePDUResponse) {
        return this.getIskraMeterModel(this.checkMeterTypeForHandShake(handShakePDUResponse));
    }

    public String getParameterCommand(String itemCode) {
        return this.getIskraParameterCommand(itemCode);
    }

    public String getacknowledgeCommand() {
        return this.convertCommandToHexStr(this.CMD_ACK);
    }

    public String validateParameterCommand(String meterParameterPDUResponse) {
        return this.getParameterDataFromHexStr(meterParameterPDUResponse);
    }

    private String convertCommandToHexStr(Byte[] iCmd) {
        String loStrCmd = "";
        int i = 0;
        while (i < iCmd.length) {
            loStrCmd = String.valueOf(loStrCmd) + String.format("%02x", iCmd[i]);
            ++i;
        }
        return loStrCmd;
    }

    public Byte[] getReadCmd(String iParam) {
        ArrayList<Byte> loCmd = new ArrayList<Byte>();
        byte[] loParam = iParam.getBytes(StandardCharsets.US_ASCII);
        loCmd.add(SOH);
        loCmd.add(RCMD);
        loCmd.add(CMDSUFFEX);
        loCmd.add(STX);
        int i = 0;
        while (i < loParam.length) {
            loCmd.add(loParam[i]);
            ++i;
        }
        loCmd.add(OBKT);
        loCmd.add(CBKT);
        loCmd.add(ETX);
        loCmd.add(this.calculateBCC(loCmd));
        return (Byte[]) loCmd.stream().toArray(Byte[]::new);
    }

    public String getIskraParameterCommand(String itemCode) {
        Byte[] loCmd = this.getReadCmd(itemCode);
        return this.convertCommandToHexStr(loCmd);
    }

    public byte calculateBCC(List<Byte> iCmdBuffer) {
        int loChk = 0;
        Object[] iCmBufferArray = iCmdBuffer.toArray();
        int i = 1;
        while (i < iCmBufferArray.length) {
            loChk ^= ((Byte) iCmBufferArray[i]).byteValue();
            ++i;
        }
        return (byte) (loChk & 0x7F);
    }

    private String checkMeterTypeForHandShake(String pduResponse) {
        return this.getParameterDataFromHandShakeHexStr(pduResponse);
    }

    public String getIskraMeterModel(String handShakeResponse) {
        String iskraMeterModel;
        String meterModel = handShakeResponse.trim();
        if (handShakeResponse.contains("/ME514")) {
            meterModel = handShakeResponse.contains("/ME514-5") ? "/ME514-5" : "/ME514";
        }
        if (handShakeResponse.contains("/ISE4XYZ000")) {
            meterModel = "/ISE4XYZ000";
        }
        if (handShakeResponse.contains("/WSE3550000")) {
            meterModel = "/WSE3550000";
        }
        if ((iskraMeterModel = this.hashMap.get(meterModel)) == null) return null;
        return iskraMeterModel;
    }

    public String getParameterDataFromHexStr(String iRxData) {
        ArrayList<Byte> loHexData = new ArrayList<Byte>();
        if (iRxData == "015") {
            return null;
        }
        int i = 0;
        while (i < iRxData.length()) {
            String loHexChr = iRxData.substring(i, i + 2);
            loHexData.add((byte) Integer.parseInt(loHexChr, 16));
            i += 2;
        }
        return this.getParameterData((Byte[]) loHexData.stream().toArray(Byte[]::new));
    }

    public String getParameterDataFromHandShakeHexStr(String iRxData) {
        ArrayList<Byte> loHexData = new ArrayList<Byte>();
        int i = 0;
        while (i < iRxData.length()) {
            String loHexChr = iRxData.substring(i, i + 2);
            loHexData.add((byte) Integer.parseInt(loHexChr, 16));
            i += 2;
        }
        byte[] bytes = ArrayUtils.toPrimitive((Byte[]) loHexData.stream().toArray(Byte[]::new));
        return new String(bytes, StandardCharsets.US_ASCII);
    }

    public String getParameterData(Byte[] iRxData) {
        ArrayList<Byte> loData = new ArrayList<Byte>();
        boolean loStartFlag = false;
        String result = "";
        if (iRxData.length <= 0) {
            return "";
        }
        int i = 0;
        while (i < iRxData.length) {
            if (loStartFlag && iRxData[i] != CBKT) {
                loData.add(iRxData[i]);
            } else if (iRxData[i] == CBKT) {
                byte[] bytes = ArrayUtils.toPrimitive((Byte[]) loData.stream().toArray(Byte[]::new));
                return new String(bytes, StandardCharsets.US_ASCII);
            }
            if (iRxData[i] == OBKT) {
                loStartFlag = true;
            }
            ++i;
        }
        return result;
    }
}
