<component name="libraryTable">
  <library name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d1020c102bba0c41084e5755bc9fed8a/transformed/drawerlayout-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/791dd7d6c225108d1924766a72231889/transformed/drawerlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2cefc6b9cc82a64a1c701689072c632a/transformed/drawerlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a67d49f988a3b05be82aafcf4a2f21dd/transformed/drawerlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d1020c102bba0c41084e5755bc9fed8a/transformed/drawerlayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c9a4db058a2908b4a990e23269f8935d/transformed/drawerlayout-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d1020c102bba0c41084e5755bc9fed8a/transformed/drawerlayout-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d1020c102bba0c41084e5755bc9fed8a/transformed/drawerlayout-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.drawerlayout/drawerlayout/1.0.0/9ecd4ecb7da215ba4c5c3e00bf8d290dad6f2bc5/drawerlayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>