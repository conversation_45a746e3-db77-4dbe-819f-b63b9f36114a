package com.esmc.protocol.algorithm;

import com.esmc.protocol.utils.HexUtils;

import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.AESFastEngine;
import org.bouncycastle.crypto.modes.GCMBlockCipher;
import org.bouncycastle.crypto.modes.gcm.Tables64kGCMMultiplier;
import org.bouncycastle.crypto.params.AEADParameters;
import org.bouncycastle.crypto.params.KeyParameter;

import java.nio.ByteBuffer;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/algorithm/AESGcm128.class */
public class AESGcm128 {
    public static final byte[] NULL_KEY = new byte[0];
    public static final byte[] NULL_MAC = HexUtils.toArray("000000000000000000000000");
    public static final byte[] ZERO_KEY = HexUtils.toArray("00000000000000000000000000000000");
    public static int AUTHENTICATION_TAG_LENGTH = 12;

    public static final byte[] encrypt(byte[] encryptKey, byte[] initVector, byte[] plainText, byte[] associatedText) throws InvalidCipherTextException {
        if (null == associatedText) {
            associatedText = NULL_KEY;
        }
        boolean encryptOnly = associatedText.length == 0;
        Tables64kGCMMultiplier tables64kGCMMultiplier = new Tables64kGCMMultiplier();
        AEADParameters parameters = new AEADParameters(new KeyParameter(encryptKey), AUTHENTICATION_TAG_LENGTH * 8, initVector, associatedText);
        GCMBlockCipher cipher = new GCMBlockCipher(new AESFastEngine(), tables64kGCMMultiplier);
        cipher.init(true, parameters);
        int outLen = cipher.getOutputSize(plainText.length);
        byte[] out = new byte[outLen];
        int len = cipher.processBytes(plainText, 0, plainText.length, out, 0);
        int doFinal = len + cipher.doFinal(out, len);
        if (encryptOnly) {
            byte[] encOut = new byte[outLen - AUTHENTICATION_TAG_LENGTH];
            System.arraycopy(out, 0, encOut, 0, encOut.length);
            out = encOut;
        }
        return out;
    }

    public static final byte[] decrypt(byte[] encryptKey, byte[] initVector, byte[] cipherText, byte[] associatedText) throws InvalidCipherTextException {
        boolean decryptOnly = false;
        if (null == associatedText || associatedText.length == 0) {
            decryptOnly = true;
            associatedText = NULL_KEY;
            ByteBuffer buffer = ByteBuffer.allocate(cipherText.length + NULL_MAC.length);
            buffer.put(cipherText).put(NULL_MAC);
            cipherText = buffer.array();
        }
        Tables64kGCMMultiplier tables64kGCMMultiplier = new Tables64kGCMMultiplier();
        AEADParameters parameters = new AEADParameters(new KeyParameter(encryptKey), AUTHENTICATION_TAG_LENGTH * 8, initVector, associatedText);
        GCMBlockCipher cipher = new GCMBlockCipher(new AESFastEngine(), tables64kGCMMultiplier);
        cipher.init(false, parameters);
        byte[] out = new byte[cipher.getOutputSize(cipherText.length)];
        int len = cipher.processBytes(cipherText, 0, cipherText.length, out, 0);
        if (!decryptOnly) {
            cipher.doFinal(out, len);
        }
        return out;
    }
}
