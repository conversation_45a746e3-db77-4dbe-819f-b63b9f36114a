<component name="libraryTable">
  <library name="Gradle: androidx.test.services:storage:1.4.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b152f10667f463a9bd1ba941f6de30d8/transformed/jetified-storage-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/766d5c2d6af39c145de993f98236059f/transformed/jetified-storage-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ef1b50518aa95d7ec1f2f02ce6af4964/transformed/jetified-storage-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0218d3e592d45fea0e07e27b2f81ee4b/transformed/jetified-storage-1.4.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ef1b50518aa95d7ec1f2f02ce6af4964/transformed/jetified-storage-1.4.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ef1b50518aa95d7ec1f2f02ce6af4964/transformed/jetified-storage-1.4.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.services/storage/1.4.0/27088f46bc0ab94523a20ccc56aa27e142662396/storage-1.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>