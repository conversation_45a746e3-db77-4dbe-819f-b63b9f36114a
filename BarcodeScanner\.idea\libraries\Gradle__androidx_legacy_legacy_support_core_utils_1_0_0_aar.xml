<component name="libraryTable">
  <library name="Gradle: androidx.legacy:legacy-support-core-utils:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f27d5ebd38e44dd46627b5890803dece/transformed/legacy-support-core-utils-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/65529a69d0f52557a8f7a589389e6212/transformed/legacy-support-core-utils-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/9cf86f1cdd0c627dc9400ade65bafe55/transformed/legacy-support-core-utils-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/78627855eeeffdab8e633153dabefd37/transformed/legacy-support-core-utils-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c2c20dd865f4b89bb781d6ac8348a2a7/transformed/legacy-support-core-utils-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/78627855eeeffdab8e633153dabefd37/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/78627855eeeffdab8e633153dabefd37/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.legacy/legacy-support-core-utils/1.0.0/46c37f178088153618cfb0afef08ec96c48f93cb/legacy-support-core-utils-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>