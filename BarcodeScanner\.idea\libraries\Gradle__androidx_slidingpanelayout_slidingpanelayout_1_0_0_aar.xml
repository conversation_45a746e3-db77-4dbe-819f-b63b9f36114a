<component name="libraryTable">
  <library name="Gradle: androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/713d9de25a1b15c60bc54ffe5f80f119/transformed/slidingpanelayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/91c23e99a8e2d27c36d971141c600295/transformed/slidingpanelayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8297a5060a8d99d564b0cf7a2a81363b/transformed/slidingpanelayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7b4e61d2f27a7e9dbe57f2148c2af4f5/transformed/slidingpanelayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f98b6bd9470b12f63031cc9dec0176f9/transformed/slidingpanelayout-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7b4e61d2f27a7e9dbe57f2148c2af4f5/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/7b4e61d2f27a7e9dbe57f2148c2af4f5/transformed/slidingpanelayout-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.slidingpanelayout/slidingpanelayout/1.0.0/f3f2e4fded24d5969a86e1974ad7e96975d970a0/slidingpanelayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>