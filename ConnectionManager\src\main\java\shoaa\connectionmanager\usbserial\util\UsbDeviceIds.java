package shoaa.connectionmanager.usbserial.util;

import androidx.annotation.Nullable;

import java.util.ArrayList;

public class UsbDeviceIds {
    public static ArrayList<UsbDeviceIds> allowedDevice;

    static {
        allowedDevice = new ArrayList<>();
//        <!-- 0x0403 / 0x60??: FTDI -->
        allowedDevice.add(new UsbDeviceIds(1027, 24577));
        allowedDevice.add(new UsbDeviceIds(1027, 24592));
        allowedDevice.add(new UsbDeviceIds(1027, 24593));
        allowedDevice.add(new UsbDeviceIds(1027, 24596));
        allowedDevice.add(new UsbDeviceIds(1027, 24597));
//    <!-- 0x10C4 / 0xEA??: Silabs CP210x -->
        allowedDevice.add(new UsbDeviceIds(4292, 60000));
        allowedDevice.add(new UsbDeviceIds(4292, 60016));
        allowedDevice.add(new UsbDeviceIds(4292, 60017));
//    <!-- 0x067B / 0x23?3: Prolific PL2303x -->
        allowedDevice.add(new UsbDeviceIds(1659, 8963));
        allowedDevice.add(new UsbDeviceIds(1659, 9123));
        allowedDevice.add(new UsbDeviceIds(1659, 9139));
        allowedDevice.add(new UsbDeviceIds(1659, 9155));
        allowedDevice.add(new UsbDeviceIds(1659, 9171));
        allowedDevice.add(new UsbDeviceIds(1659, 9187));
        allowedDevice.add(new UsbDeviceIds(1659, 9203));
//    <!-- 0x1a86 / 0x?523: Qinheng CH34x -->
        allowedDevice.add(new UsbDeviceIds(6790, 21795));
        allowedDevice.add(new UsbDeviceIds(6790, 29987));
//    <!-- CDC driver -->
        allowedDevice.add(new UsbDeviceIds(9025, 0));
        allowedDevice.add(new UsbDeviceIds(5824, 1155));
        allowedDevice.add(new UsbDeviceIds(1003, 8260));
        allowedDevice.add(new UsbDeviceIds(7855, 4));
        allowedDevice.add(new UsbDeviceIds(3368, 516));
        allowedDevice.add(new UsbDeviceIds(1155, 22336));
        allowedDevice.add(new UsbDeviceIds(11914, 5));
        allowedDevice.add(new UsbDeviceIds(11914, 10));
        allowedDevice.add(new UsbDeviceIds(6790, 21972));
    }

    int vendorId;
    int productId;

    public UsbDeviceIds(int vendorId, int productId) {
        this.vendorId = vendorId;
        this.productId = productId;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj == null) {
            return false;
        }
        return this.vendorId == (((UsbDeviceIds) obj).vendorId) && this.productId == (((UsbDeviceIds) obj).productId);
    }
}

