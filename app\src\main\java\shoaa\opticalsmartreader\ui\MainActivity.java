package shoaa.opticalsmartreader.ui;

import static android.os.Build.VERSION.SDK_INT;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.ColorStateList;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.Settings;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.navigation.NavigationBarView;
import com.google.firebase.FirebaseApp;
import com.google.gson.Gson;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import pub.devrel.easypermissions.AfterPermissionGranted;
import pub.devrel.easypermissions.EasyPermissions;
import shoaa.barcodescanner.BarcodeScanner;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionNotifier;
import shoaa.connectionmanager.bluetooth.SerialService;
import shoaa.opticalsmartreader.BuildConfig;
import shoaa.opticalsmartreader.LocaleManager;
import shoaa.opticalsmartreader.MainApplication;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.AppLocationManager;
import shoaa.opticalsmartreader.logic.CycleDate.CycleDateManager;
import shoaa.opticalsmartreader.logic.LostActivityResult;
import shoaa.opticalsmartreader.logic.LostActivityResultObject;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.models.CycleDate;
import shoaa.opticalsmartreader.models.MeterCheck;

public class MainActivity extends AppCompatActivity implements EasyPermissions.PermissionCallbacks, NavigationBarView.OnItemSelectedListener {

    public static final int MY_PERMISSIONS = 120;
    private static final int TIME_INTERVAL = 2000;
    public static boolean enableBottomNav = true;
    public BarcodeScanner barcodeScanner = new BarcodeScanner(this);
    public ArrayList<MeterCheck> meterCheckArrayList = new ArrayList<>();
    Fragment currentFragment = null;
    BottomNavigationView bottomNavigationView;
    StatisticsFragment statisticsFragment = new StatisticsFragment();
    ReaderFragment readerFragment = new ReaderFragment();
    CustomersDataFragment customersDataFragment = new CustomersDataFragment();
    SyncDataFragment syncDataFragment = new SyncDataFragment();
    LostActivityResult lostActivityResult;

    ActivityResultLauncher<Intent> lostActivityResultLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK) {
                    new Thread(() -> {
                        Intent data = result.getData();
                        CycleDate cycleDate = CycleDateManager.getCycleDate(MainActivity.this);
                        LostActivityResultObject lostActivityResultObject = new Gson().fromJson(data.getStringExtra("data"), LostActivityResultObject.class);
                        lostActivityResultObject.client.CycleMonth = cycleDate.CycleMonth;
                        lostActivityResultObject.client.CycleYear = cycleDate.CycleYear;
                        lostActivityResult.onFinish(lostActivityResultObject);
                    }).start();
                } else {
                    lostActivityResult.onFinish(null);
                }
            });

    private long mBackPressed;

    public static void copy(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src)) {
            try (OutputStream out = new FileOutputStream(dst)) {
                // Transfer bytes from in to out
                byte[] buf = new byte[1024];
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
            }
        }
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleManager.updateResources(newBase, "en"));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        MainApplication.getInstance().setCurrentActivity(this);
        FirebaseApp.initializeApp(getApplicationContext());
        shoaa.opticalsmartreader.Utils.init();
        bottomNavigationView = findViewById(R.id.bottomNavigationView);

        bottomNavigationView.setOnItemSelectedListener(this);
        bottomNavigationView.setSelectedItemId(R.id.read);
        bottomNavigationView.setItemActiveIndicatorColor(ColorStateList.valueOf(getColor(R.color.ripple_color)));
        bottomNavigationView.setItemRippleColor(ColorStateList.valueOf(getColor(R.color.ripple_color)));
        bottomNavigationView.setBackgroundColor(getColor(R.color.white));
        Utils.sharedPreferences = getSharedPreferences(getPackageName(), Context.MODE_PRIVATE);

// restore backup file
//        File newDBFile = new File(Environment.getExternalStorageDirectory(), "backup.db");
//        try {
//            copy( newDBFile, getDatabasePath("shoaa_database"));
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

        Utils.appDatabase = Room.databaseBuilder(getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();

    }

    public void checkTimeAutomatic() {
        if (Settings.Global.getInt(getApplicationContext().getContentResolver(), Settings.Global.AUTO_TIME, 0) != 1) {
            new AlertDialog.Builder(this)
                    .setTitle("خطأ في اعدادات الوقت")
                    .setMessage("يجب ضبط الوقت علي نظام الضبط الاوتوماتيكي")
                    .setCancelable(false)
                    .setPositiveButton(R.string.ok, (dialogInterface, i) -> System.exit(0))
                    .create()
                    .show();
        }
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        if (!enableBottomNav)
            return false;
        switch (item.getItemId()) {
            case R.id.statistics:
                if (currentFragment == null) {
                    getSupportFragmentManager().beginTransaction().replace(R.id.flFragment, statisticsFragment).commit();
                } else {
                    if (currentFragment == statisticsFragment)
                        return true;
                    if (getSupportFragmentManager().findFragmentById(statisticsFragment.getId()) == null) {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().add(R.id.flFragment, statisticsFragment).commit();
                    } else {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().show(statisticsFragment).commit();
                    }
                }
                currentFragment = statisticsFragment;
                return true;

            case R.id.read:
                if (currentFragment == null) {
                    getSupportFragmentManager().beginTransaction().replace(R.id.flFragment, readerFragment).commit();
                } else {
                    if (currentFragment == readerFragment)
                        return true;
                    if (getSupportFragmentManager().findFragmentById(readerFragment.getId()) == null) {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().add(R.id.flFragment, readerFragment).commit();
                    } else {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().show(readerFragment).commit();
                    }
                }
                currentFragment = readerFragment;
                return true;

            case R.id.customers:
                if (currentFragment == null) {
                    getSupportFragmentManager().beginTransaction().replace(R.id.flFragment, customersDataFragment).commit();
                } else {
                    if (currentFragment == customersDataFragment)
                        return true;
                    if (getSupportFragmentManager().findFragmentById(customersDataFragment.getId()) == null) {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().add(R.id.flFragment, customersDataFragment).commit();
                    } else {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().show(customersDataFragment).commit();
                    }
                }
                currentFragment = customersDataFragment;
                return true;
            case R.id.sync:
                if (currentFragment == null) {
                    getSupportFragmentManager().beginTransaction().replace(R.id.flFragment, syncDataFragment).commit();
                } else {
                    if (currentFragment == syncDataFragment)
                        return true;
                    if (getSupportFragmentManager().findFragmentById(syncDataFragment.getId()) == null) {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().add(R.id.flFragment, syncDataFragment).commit();
                    } else {
                        getSupportFragmentManager().beginTransaction().hide(currentFragment).commit();
                        getSupportFragmentManager().beginTransaction().show(syncDataFragment).commit();
                    }
                }
                currentFragment = syncDataFragment;
                return true;
        }
        return false;
    }

    @Override
    public void onBackPressed() {
        if (getSupportFragmentManager().getBackStackEntryCount() != 0) {
            super.onBackPressed();
        } else if ((mBackPressed + TIME_INTERVAL > System.currentTimeMillis())) {
            finish();
            android.os.Process.killProcess(android.os.Process.myPid());
        } else {
            mBackPressed = System.currentTimeMillis();
            Toast.makeText(getBaseContext(), "اضغط مره اخري للخروج من البرنامج", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (AppLocationManager.getInstance(this).googleApiClient != null && AppLocationManager.getInstance(this).googleApiClient.isConnected()) {
            AppLocationManager.getInstance(this).googleApiClient.disconnect();
        }
    }

    @Override
    public void onDestroy() {
        ConnectionManager.Companion.getInstance().disconnect();
        stopService(new Intent(this, SerialService.class));
        super.onDestroy();
    }

    @Override
    public void onStart() {
        MainApplication.getInstance().setCurrentActivity(this);
        super.onStart();
        checkPermissions();
    }

    @Override
    public void onStop() {
        if (!isChangingConfigurations())
            ConnectionManager.Companion.getInstance().deAttachBluetoothService();
        super.onStop();
    }


    @Override
    public void onResume() {
        MainApplication.getInstance().setCurrentActivity(this);
        super.onResume();
        checkTimeAutomatic();
        if (!getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)) {
            Toast.makeText(this, "بلوتوث غير مدعوم", Toast.LENGTH_SHORT).show();
//            finish();
        }


        if (AppLocationManager.getInstance(this).checkPlayServices() && AppLocationManager.getInstance(this).googleApiClient != null && !AppLocationManager.getInstance(this).googleApiClient.isConnected() && !AppLocationManager.getInstance(this).googleApiClient.isConnecting()) {
            // we build google api client
            AppLocationManager.getInstance(this).googleApiClient = new GoogleApiClient.Builder(this).
                    addApi(LocationServices.API).
                    addConnectionCallbacks(AppLocationManager.getInstance(this)).
                    addOnConnectionFailedListener(AppLocationManager.getInstance(this)).build();
            AppLocationManager.getInstance(this).googleApiClient.connect();
        }

    }


    public void openLostActivityForResult(LostActivityResult lostActivityResult, boolean showClientId, boolean showMeterType) {
        this.lostActivityResult = lostActivityResult;
        Intent intent = new Intent(this, LostActivity.class);
        intent.putExtra("showClientId", showClientId);
        intent.putExtra("showMeterType", showMeterType);
        lostActivityResultLauncher.launch(intent);
    }

    @AfterPermissionGranted(MY_PERMISSIONS)
    private void checkPermissions() {
        ArrayList<String> permsList = new ArrayList<>();
        if (SDK_INT < Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        permsList.add(Manifest.permission.CAMERA);
        permsList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        permsList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        if (SDK_INT >= Build.VERSION_CODES.P) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE);
        }
        if (SDK_INT >= Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.BLUETOOTH_CONNECT);
            permsList.add(Manifest.permission.BLUETOOTH_SCAN);
        } else {
            permsList.add(Manifest.permission.BLUETOOTH);
            permsList.add(Manifest.permission.BLUETOOTH_ADMIN);
        }
        if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permsList.add(Manifest.permission.POST_NOTIFICATIONS);
        }
        if (SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE);
        }
        String[] perms = permsList.toArray(new String[0]);
        if (!EasyPermissions.hasPermissions(this, perms)) {
            EasyPermissions.requestPermissions(this, "يجب الموافقة علي الاذونات",
                    MY_PERMISSIONS, perms);
        } else {
            // Use this check to determine whether Bluetooth classic is supported on the device.
            if (!getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)) {
                Toast.makeText(this, "bluetooth_not_supported", Toast.LENGTH_SHORT).show();
//                finish();
            }
            new Thread(() -> {
                ConnectionManager.Companion.getInstance().init(this, BuildConfig.enableUSB, new ConnectionNotifier() {
                    @Override
                    public void notifyConnection() {
                        runOnUiThread(() -> readerFragment.refresh());
                    }
                });
                AppLocationManager.getInstance(this).listen();
            }).start();
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }


    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {

        ArrayList<String> permsList = new ArrayList<>();
        if (SDK_INT < Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
        permsList.add(Manifest.permission.CAMERA);
        permsList.add(Manifest.permission.ACCESS_FINE_LOCATION);
        permsList.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        if (SDK_INT >= Build.VERSION_CODES.P) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE);
        }
        if (SDK_INT >= Build.VERSION_CODES.S) {
            permsList.add(Manifest.permission.BLUETOOTH_CONNECT);
            permsList.add(Manifest.permission.BLUETOOTH_SCAN);
        } else {
            permsList.add(Manifest.permission.BLUETOOTH);
            permsList.add(Manifest.permission.BLUETOOTH_ADMIN);
        }
        if (SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permsList.add(Manifest.permission.POST_NOTIFICATIONS);
        }
        if (SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permsList.add(Manifest.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE);
        }
        String[] perms1 = permsList.toArray(new String[0]);
        if (!EasyPermissions.hasPermissions(this, perms1)) {
//            System.exit(0);
            ((ActivityManager) getSystemService(ACTIVITY_SERVICE)).clearApplicationUserData();
            checkPermissions();
        }
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
        System.exit(0);
    }

}
