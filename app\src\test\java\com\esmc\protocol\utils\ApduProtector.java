package com.esmc.protocol.utils;

import com.esmc.protocol.algorithm.AESGcm128;
import com.esmc.protocol.model.ProtocolProfile;
import com.esmc.protocol.model.SecurityControl;

import org.bouncycastle.crypto.InvalidCipherTextException;
import org.jetbrains.annotations.NotNull;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InvalidObjectException;
import java.util.Arrays;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/utils/ApduProtector.class */
public class ApduProtector {
    public static byte[] cipher(byte[] plain, SecurityControl securityControl, ProtocolProfile profile) {
        try {
            return authenticationEncryptionGcm(plain, securityControl, profile);
        } catch (IOException | InvalidCipherTextException exception) {
            exception.printStackTrace();
            return new byte[0];
        }
    }

    public static byte[] decipher(byte[] ciphered, ProtocolProfile profile) {
        try {
            return authenticationEncryptionGcmReverse(ciphered, profile);
        } catch (IOException | InvalidCipherTextException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    public static byte[] getInitializationVector(long ic, String systemTitle) {
        byte[] iv = new byte[12];
        System.arraycopy(MyConverter.hexStringToBytes(systemTitle), 0, iv, 0, 8);
        iv[8] = (byte) ((ic >> 24) & 255);
        iv[9] = (byte) ((ic >> 16) & 255);
        iv[10] = (byte) ((ic >> 8) & 255);
        iv[11] = (byte) (ic & 255);
        return iv;
    }

    private static byte[] getAdditionalAuthenticatedData(@NotNull ProtocolProfile profile, byte securityControl, byte[] data) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        stream.write(securityControl);
        stream.write(MyConverter.hexStringToBytes(profile.getSecurityInfo().getAuthenticationKeyGlobal()));
        if (securityControl == 16) {
            if (data == null || data.length == 0) {
                throw new InvalidObjectException("data is null");
            }
            stream.write(data);
        }
        return stream.toByteArray();
    }

    private static byte[] authenticationEncryptionGcm(byte[] plain, @NotNull SecurityControl securityControl, ProtocolProfile profile) throws IOException, InvalidCipherTextException {
        long ic = profile.getInvocationCounterClient();
        byte[] iv = getInitializationVector(ic, profile.getClientSystemTitle());
        byte[] aad = securityControl.isAuthentication() ? getAdditionalAuthenticatedData(profile, securityControl.getByteValue(), plain) : new byte[0];
        byte[] data = securityControl.isEncryption() ? plain : new byte[0];
        byte[] cipherData = AESGcm128.encrypt(MyConverter.hexStringToBytes(profile.getSecurityInfo().getEncryptionKeyGlobalUnicast()), iv, data, aad);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        stream.write(securityControl.getByteValue());
        stream.write((byte) ((ic >> 24) & 255));
        stream.write((byte) ((ic >> 16) & 255));
        stream.write((byte) ((ic >> 8) & 255));
        stream.write((byte) (ic & 255));
        if (!securityControl.isEncryption()) {
            stream.write(plain);
        }
        stream.write(cipherData);
        profile.setInvocationCounterClient(profile.getInvocationCounterClient() + 1);
        return stream.toByteArray();
    }

    private static byte[] authenticationEncryptionGcmReverse(byte[] ciphered, ProtocolProfile profile) throws IOException, InvalidCipherTextException {
        if (5 > ciphered.length) {
            return new byte[0];
        }
        SecurityControl sc = new SecurityControl(ciphered[0]);
        long ic = ((ciphered[1] << 24) & (-16777216)) | ((ciphered[2] << 16) & 16711680) | ((ciphered[3] << 8) & 65280) | (ciphered[4] & 255);
        if (ic < profile.getInvocationCounterServer()) {
            return new byte[0];
        }
        profile.setInvocationCounterServer(ic + 1);
        byte[] data = Arrays.copyOfRange(ciphered, 5, ciphered.length);
        byte[] iv = getInitializationVector(ic, profile.getServerSystemTitle());
        byte[] aad = sc.isAuthentication() ? getAdditionalAuthenticatedData(profile, sc.getByteValue(), data) : new byte[0];
        return AESGcm128.decrypt(MyConverter.hexStringToBytes(profile.getSecurityInfo().getEncryptionKeyGlobalUnicast()), iv, data, aad);
    }
}
