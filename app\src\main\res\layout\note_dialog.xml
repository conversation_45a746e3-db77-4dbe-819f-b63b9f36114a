<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/my_custom_background"
    android:backgroundTint="@color/primary_dark"
    android:orientation="vertical"
    android:padding="15dp">

    <LinearLayout
        android:id="@+id/mainLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/customerIdLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"

            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="15dp"
                android:gravity="center"
                android:text="ادخل كود المشترك"
                android:textColor="@color/on_primary"
                android:textSize="26sp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/etCustomerId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/my_custom_background_2"
                android:digits="0123456789٠١٢٣٤٥٦٧٨٩"
                android:gravity="center"
                android:hint=""
                android:inputType="number"
                android:maxLength="10"
                android:maxLines="1"
                android:padding="10dp"
                android:textColor="@color/on_primary"
                android:textColorHint="@color/light_grey"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>


        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:text="ادخل ملاحظة"
            android:textColor="@color/on_primary"
            android:textSize="26sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/etNote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/my_custom_background_2"
            android:gravity="center"
            android:hint=""
            android:inputType="text"
            android:maxLines="1"
            android:padding="10dp"
            android:textColor="@color/on_primary"
            android:textColorHint="@color/light_grey"
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <androidx.legacy.widget.Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />


            <Button
                android:id="@+id/btnEnter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="ادخال" />

            <androidx.legacy.widget.Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />
        </LinearLayout>
    </LinearLayout>


</androidx.appcompat.widget.LinearLayoutCompat>