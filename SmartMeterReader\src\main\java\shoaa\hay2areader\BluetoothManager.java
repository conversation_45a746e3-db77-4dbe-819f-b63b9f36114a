package shoaa.hay2areader;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Parcelable;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.UUID;

public class BluetoothManager {

    private static final String TAG = "BluetoothManager";

    private final BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
    private BluetoothSocket bluetoothSocket;
    private InputStream inputStream;
    private OutputStream outputStream;
    private Context context;

    public BluetoothManager(Context context) {
        this.context = context;
    }

    // Discover UUIDs from the device
    public void fetchUUIDs(String deviceAddress) {
        BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            return;
        }
        device.fetchUuidsWithSdp();

        BroadcastReceiver uuidReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                Parcelable[] uuidExtra = intent.getParcelableArrayExtra(BluetoothDevice.EXTRA_UUID);
                if (uuidExtra != null) {
                    for (Parcelable parcelable : uuidExtra) {
                        Log.d("UUID", "Discovered UUID: " + parcelable.toString());
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_UUID);
        context.registerReceiver(uuidReceiver, filter);
    }

    public boolean connectWithRetry(String deviceAddress, UUID uuid, int retries, int delayMs) {
        int attempt = 0;
        while (attempt < retries) {
            try {
                BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
                bluetoothSocket = device.createRfcommSocketToServiceRecord(uuid);
                bluetoothAdapter.cancelDiscovery();
                bluetoothSocket.connect();

                inputStream = bluetoothSocket.getInputStream();
                outputStream = bluetoothSocket.getOutputStream();
                Log.d(TAG, "Connected successfully on attempt " + (attempt + 1));
                return true;
            } catch (IOException e) {
                Log.e(TAG, "Connection failed on attempt " + (attempt + 1) + ": " + e.getMessage());
                closeConnection();

                // Wait before retrying
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
            }
            attempt++;
        }
        return false;
    }

    public boolean connectToDevice(String deviceAddress, UUID serviceUUID) {
        try {
            BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceAddress);
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                return false;
            }
            bluetoothSocket = device.createRfcommSocketToServiceRecord(serviceUUID);
            bluetoothSocket.connect();

            inputStream = bluetoothSocket.getInputStream();
            outputStream = bluetoothSocket.getOutputStream();

            Log.d(TAG, "Connected to the Bluetooth device!");
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Error connecting to device: " + e.getMessage());
            closeConnection();
            return false;
        }
    }

    public InputStream getInputStream() {
        return inputStream;
    }
    public OutputStream getOutPutStream() {
        return outputStream;
    }

    public BluetoothSocket getBluetoothSocket() {
        return bluetoothSocket;
    }

    public void sendData(byte[] data) {
        try {
            if (outputStream != null) {
                outputStream.write(data);
                outputStream.flush();
                Log.d(TAG, "Data sent successfully");
            }
        } catch (IOException e) {
            Log.e(TAG, "Error sending data: " + e.getMessage());
        }
    }

    public String readData() {
        StringBuilder result = new StringBuilder();
        byte[] buffer = new byte[1024]; // Adjust buffer size if necessary
        int bytesRead;

        try {
            if (inputStream != null) {
                while ((bytesRead = inputStream.read(buffer)) > 0) {
                    result.append(new String(buffer, 0, bytesRead));
                }
            }
        } catch (IOException e) {
            Log.e(TAG, "Error reading data: " + e.getMessage());
        }

        return result.toString();
    }

    public void closeConnection() {
        try {
            if (inputStream != null) inputStream.close();
            if (outputStream != null) outputStream.close();
            if (bluetoothSocket != null) bluetoothSocket.close();
            Log.d(TAG, "Connection closed");
        } catch (IOException e) {
            Log.e(TAG, "Error closing connection: " + e.getMessage());
        }
    }
}
