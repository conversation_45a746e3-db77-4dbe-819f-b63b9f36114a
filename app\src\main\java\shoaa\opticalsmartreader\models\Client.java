package shoaa.opticalsmartreader.models;

import androidx.room.ColumnInfo;
import androidx.room.Entity;

import java.util.Objects;

import shoaa.opticalsmartreader.logic.Utils;

@Entity(tableName = "clients", primaryKeys = {"MeterID", "BrHndsa", "FactoryCode"})
public class Client implements Comparable<Client> {
    @ColumnInfo(name = "CDID")
    public long CDID;
    @ColumnInfo(name = "CDCreateDate")
    public String CDCreateDate = "";
    @ColumnInfo(name = "CDCreateTime")
    public String CDCreateTime = "";
    @ColumnInfo(name = "BrHndsa")
    public int BrHndsa;
    @ColumnInfo(name = "Mntka")
    public String Mntka = "";
    @ColumnInfo(name = "Day")
    public String Day = "";
    @ColumnInfo(name = "Main")
    public String Main = "";
    @ColumnInfo(name = "Fary")
    public String Fary = "";
    @ColumnInfo(name = "FactoryCode")
    public int FactoryCode;
    @ColumnInfo(name = "Name")
    public String Name = "";
    @ColumnInfo(name = "Address")
    public String Address = "";
    @ColumnInfo(name = "ActivityName")
    public String ActivityName = "";
    @ColumnInfo(name = "PlaceName")
    public String PlaceName = "";
    @ColumnInfo(name = "MeterID")
    public long MeterID;
    @ColumnInfo(name = "series")
    public int series;
    @ColumnInfo(name = "MeterTypeName")
    public String MeterTypeName = "";
    @ColumnInfo(name = "MeterModel")
    public int MeterModel;
    @ColumnInfo(name = "FirmwareVersion")
    public String FirmwareVersion = "";
    @ColumnInfo(name = "CustomerID")
    public String CustomerID = "";
    @ColumnInfo(name = "CardID")
    public String CardID = "";
    @ColumnInfo(name = "CustomerMobile")
    public String CustomerMobile = "";
    @ColumnInfo(name = "CycleYear")
    public int CycleYear;
    @ColumnInfo(name = "CycleMonth")
    public int CycleMonth;
    @ColumnInfo(name = "UsID")
    public long UsID;
    @ColumnInfo(name = "ReadingCount")
    public int ReadingCount;
    @ColumnInfo(name = "KTCodeSHoaa")
    public int KTCodeSHoaa;
    @ColumnInfo(name = "BranchRevision")
    public int BranchRevision;
    @ColumnInfo(name = "BranchRevisionReason")
    public String BranchRevisionReason = "";
    @ColumnInfo(name = "ShoaaRevision")
    public int ShoaaRevision;
    @ColumnInfo(name = "BranchRevisionNotes")
    public String BranchRevisionNotes = "";

    public Client() {
        this.CDID = 0;
        this.CDCreateDate = "0";
        this.CDCreateTime = "0";
        BrHndsa = 0;
        Mntka = "0";
        Day = "0";
        Main = "0";
        Fary = "0";
        FactoryCode = 0;
        Name = "0";
        Address = "0";
        ActivityName = "منزلى";
        PlaceName = "ساقط ليست";
        MeterID = 0;
        this.series = 0;
        MeterTypeName = "احادي";
        MeterModel = 0;
        FirmwareVersion = "0";
        CustomerID = "0";
        CardID = "0";
        CustomerMobile = "0";
        CycleYear = 0;
        CycleMonth = 0;
        UsID = 0;
        ReadingCount = 0;
        this.KTCodeSHoaa = 0;
        BranchRevision = 0;
        BranchRevisionReason = "0";
        ShoaaRevision = 0;
        BranchRevisionNotes = "0";
    }

    public long getCDID() {
        return CDID;
    }

    public String getCDCreateDate() {
        return CDCreateDate;
    }

    public String getCDCreateTime() {
        return CDCreateTime;
    }

    public int getBrHndsa() {
        return BrHndsa;
    }

    public String getMntka() {
        if (Mntka == null)
            return "-1";
        return Utils.formatStringUIntNumber(Mntka);
    }

    public String getDay() {
        if (Day == null)
            return "-1";
        return Utils.formatStringUIntNumber(Day);
    }

    public String getMain() {
        if (Main == null)
            return "-1";
        return Utils.formatStringUIntNumber(Main);
    }

    public String getFary() {
        if (Fary == null)
            return "-1";
        return Utils.formatStringUIntNumber(Fary);
    }

    public int getFactoryCode() {
        return FactoryCode;
    }

    public String getName() {
        if (Name == null)
            return "";
        return Name.trim();
    }

    public String getAddress() {
        if (Address == null)
            return "";
        return Address.trim();
    }

    public String getActivityName() {
        if (ActivityName == null)
            return "";
        return ActivityName.trim();
    }

    public String getPlaceName() {
        if (PlaceName == null)
            return "";
        return PlaceName.trim();
    }

    public void setPlaceName(String placeName) {
        PlaceName = placeName;
    }

    public long getMeterID() {
        return MeterID;
    }

    public int getSeries() {
        return series;
    }

    public String getMeterTypeName() {
        if (MeterTypeName == null)
            return "";
        return MeterTypeName.trim();
    }

    public void setMeterTypeName(String meterTypeName) {
        MeterTypeName = meterTypeName;
    }

    public int getMeterModel() {
        return MeterModel;
    }

    public String getFirmwareVersion() {
        if (FirmwareVersion == null)
            return "";
        return FirmwareVersion.trim();
    }

    public String getCustomerID() {
        if (CustomerID == null)
            return "";
        return CustomerID.trim();
    }

    public void setCustomerID(String customerID) {
        CustomerID = customerID;
    }

    public String getCardID() {
        if (CardID == null)
            return "";
        return CardID.trim();
    }

    public String getCustomerMobile() {
        if (CustomerMobile == null)
            return "";
        return Utils.formatStringUIntNumber(CustomerMobile);
    }

    public int getCycleYear() {
        return CycleYear;
    }

    public int getCycleMonth() {
        return CycleMonth;
    }

    public long getUsID() {
        return UsID;
    }

    public int getReadingCount() {
        return ReadingCount;
    }

    public int getKTCodeSHoaa() {
        return KTCodeSHoaa;
    }

    public String getReadableKTCodeSHoaa() {
        return String.valueOf(KTCodeSHoaa).trim();
    }

    public int getBranchRevision() {
        return BranchRevision;
    }

    public String getBranchRevisionReason() {
        return BranchRevisionReason;
    }

    public int getShoaaRevision() {
        return ShoaaRevision;
    }

    public String getBranchRevisionNotes() {
        return BranchRevisionNotes;
    }

    public String getReference() {
        return getBrHndsa() + "/" + getMntka() + "/" + getDay() + "/" + getMain() + "/" + getFary();
    }

    @Override
    public int compareTo(Client client) {
        int res;
        try {
            res = Integer.compare(getBrHndsa(), client.getBrHndsa());
            if (res != 0)
                return res;
        } catch (Exception e) {
        }
        try {
            res = Integer.compare(Integer.parseInt(getMntka()), Integer.parseInt(client.getMntka()));
            if (res != 0)
                return res;
        } catch (Exception e) {
        }
        try {
            res = Integer.compare(Integer.parseInt(getDay()), Integer.parseInt(client.getDay()));
            if (res != 0)
                return res;
        } catch (Exception e) {
        }
        try {
            res = Integer.compare(Integer.parseInt(getMain()), Integer.parseInt(client.getMain()));
            if (res != 0)
                return res;
        } catch (Exception e) {
        }
        try {
            res = Integer.compare(Integer.parseInt(getFary()), Integer.parseInt(client.getFary()));
            if (res != 0)
                return res;
        } catch (Exception e) {
        }

        try {
            res = getReference().compareTo(client.getReference());
            if (res != 0)
                return res;
        } catch (Exception e) {
        }

        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Client)) return false;
        Client client = (Client) o;
        return BrHndsa == client.BrHndsa && FactoryCode == client.FactoryCode && MeterID == client.MeterID;
    }

    @Override
    public int hashCode() {
        return Objects.hash(BrHndsa, FactoryCode, MeterID);
    }

    @Override
    public String toString() {
        return "Client{" +
                "CDID=" + CDID +
                ", CDCreateDate='" + CDCreateDate + '\'' +
                ", CDCreateTime='" + CDCreateTime + '\'' +
                ", BrHndsa=" + BrHndsa +
                ", Mntka='" + Mntka + '\'' +
                ", Day='" + Day + '\'' +
                ", Main='" + Main + '\'' +
                ", Fary='" + Fary + '\'' +
                ", FactoryCode=" + FactoryCode +
                ", Name='" + Name + '\'' +
                ", Address='" + Address + '\'' +
                ", ActivityName='" + ActivityName + '\'' +
                ", PlaceName='" + PlaceName + '\'' +
                ", MeterID=" + MeterID +
                ", series=" + series +
                ", MeterTypeName='" + MeterTypeName + '\'' +
                ", MeterModel=" + MeterModel +
                ", FirmwareVersion='" + FirmwareVersion + '\'' +
                ", CustomerID='" + CustomerID + '\'' +
                ", CardID='" + CardID + '\'' +
                ", CustomerMobile='" + CustomerMobile + '\'' +
                ", CycleYear=" + CycleYear +
                ", CycleMonth=" + CycleMonth +
                ", UsID=" + UsID +
                ", ReadingCount=" + ReadingCount +
                ", KTCodeSHoaa=" + KTCodeSHoaa +
                ", BranchRevision=" + BranchRevision +
                ", BranchRevisionReason='" + BranchRevisionReason + '\'' +
                ", ShoaaRevision=" + ShoaaRevision +
                ", BranchRevisionNotes='" + BranchRevisionNotes + '\'' +
                '}';
    }
}
