package shoaa.opticalsmartreader.models;

import android.content.Context;

import com.google.gson.Gson;

import java.util.HashMap;
import java.util.Map;

import shoaa.opticalsmartreader.logic.login.LoginResponse;

public class AppUser {
    private static AppUser instance = null;

    private String SESSION_ID = "";
    private String USER_ID = "";
    private String VERSION = "";
    private String OFK = "";
    private String BrSectorCode = "";
    private String BrHndsa = "";
    private String userName = "";
    private String password = "";

    private AppUser() {

    }

    private AppUser(Context context) {
        AppUser appUser = fromSharedInstance(context);
        if (appUser != null) {
            this.SESSION_ID = appUser.SESSION_ID;
            this.USER_ID = appUser.USER_ID;
            this.VERSION = appUser.VERSION;
            this.OFK = appUser.OFK;
            this.BrSectorCode = appUser.BrSectorCode;
            this.BrHndsa = appUser.BrHndsa;
            this.userName = appUser.userName;
            this.password = appUser.password;
        }
    }

    public static AppUser getInstance(Context context) {
        if (instance == null)
            instance = new AppUser(context);
        return instance;
    }

    public String getSESSION_ID() {
        return SESSION_ID;
    }

    public String getUSER_ID() {
        return USER_ID;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }

    public String getVERSION() {
        return VERSION;
    }

    public String getOFK() {
        return OFK;
    }

    public String getBrSectorCode() {
        return BrSectorCode;
    }

    public String getBrHndsa() {
        return BrHndsa;
    }

    private AppUser fromSharedInstance(Context context) {
        String json = context.getSharedPreferences(context.getPackageName(), Context.MODE_PRIVATE).getString("app_user", "");
        if (!json.isEmpty()) {
            return new Gson().fromJson(json, AppUser.class);
        } else
            return null;
    }

    public void setAppUser(String json) {
        instance = new Gson().fromJson(json, AppUser.class);
    }

    public void logut(Context context) {
        context.getSharedPreferences(context.getPackageName(), Context.MODE_PRIVATE).edit().putString("app_user", "").apply();
        instance = null;
    }

    public void setAppUser(Context context, String userName, String password, LoginResponse loginResponse) {
        String[] data = loginResponse.getRESULT().split(";");
        Map<String, String> map = new HashMap<>();
        for (String datum : data) {
            map.put(datum.split("=")[0], datum.split("=")[1]);
        }
        if (map.containsKey("SESSION_ID"))
            instance.SESSION_ID = map.get("SESSION_ID");
        if (map.containsKey("USER_ID"))
            instance.USER_ID = map.get("USER_ID");
        if (map.containsKey("VERSION"))
            instance.VERSION = map.get("VERSION");
        if (map.containsKey("OFK"))
            instance.OFK = map.get("OFK");
        if (map.containsKey("BrSectorCode"))
            instance.BrSectorCode = map.get("BrSectorCode");
        if (map.containsKey("BrHndsa"))
            instance.BrHndsa = map.get("BrHndsa");
        instance.userName = userName;
        instance.password = password;
        String json = new Gson().toJson(this);
        context.getSharedPreferences(context.getPackageName(), Context.MODE_PRIVATE).edit().putString("app_user", json).apply();
    }
}
