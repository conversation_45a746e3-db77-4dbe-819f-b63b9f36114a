package com.sewedy.electrometerparser.protocol;

import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Parser {

    private double CurrencyRatio = 10000;

    public ArrayList<Shared.ConfigureMeter> splitConfigureMeter(byte[] BufferData) {
        ArrayList<Shared.ConfigureMeter> meters = new ArrayList<>();
        int StartIndex = 0;
        for (int i = StartIndex; (i + 10) <= BufferData.length; i = i + 10) {
            short OperatorID = Utils.converArrtoShort(BufferData, i, Shared.DataNewDataListPacket.endianType);
            String Interface = BufferData[i + 2] == 0 ? "Optical" : (BufferData[i + 2] == 1 ? "GPRS" : "RFID");
            short TemplateID = Utils.converArrtoShort(BufferData, i + 3, Shared.DataNewDataListPacket.endianType);

            String dateTime = ((BufferData[i + 9] & 0xFF) + 2000) + "-" + (BufferData[i + 8] & 0xFF) + "-" + (BufferData[i + 7] & 0xFF) + " " + (BufferData[i + 6] & 0xFF) + ":" + (BufferData[i + 5] & 0xFF) + ":0";

            Shared.ConfigureMeter meter = new Shared.ConfigureMeter();
            meter.OperatorID = (OperatorID & 0xffff) + "";
            meter.Interface = Interface;
            meter.ConfMeterDateTime = dateTime;
            meter.TemplateID = (TemplateID & 0xffff) + "";
            meters.add(meter);
        }
        return meters;
    }

    public Shared.MeteringData splitMeteringData(Shared.DataNewDataListPacket NewDataList, byte[] BufferData) {
        ArrayList<Shared.DataOne> dataOnes = new ArrayList<>();
        ArrayList<Shared.DataTwo> dataTwos = new ArrayList<>();
        int index = 4;
        Shared.DataOne dataOne = new Shared.DataOne();
        dataOne.name = "Active";
        if (NewDataList._MTR_TYPE_FETUR == 0) {// todo for three phase
            dataOne.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
            index += 4;//(double)Converter.ToUInt32(BufferData, index) / 1000; index += 4;      // 4 uint32_t
            dataOne.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
            index += 2;     // 2 uint16_t
            dataOne.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
            index += 4;      // 4 uint32_t
            dataOne.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
            index += 2;     // 2 uint16_t
            dataOne.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
            index += 4;      // 4 uint32_t
            dataOne.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
            index += 2;     // 2 uint16_t
        }

        double TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType));
        if (NewDataList._MTR_TYPE_FETUR == 0 && NewDataList.isIndirect)
            TotalValue = TotalValue / 100;
        else TotalValue = TotalValue / 1000;
        index += 4;
        double TotalReset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF;
        index += 2;
        TotalValue = TotalValue + TotalReset * (1000000);

        dataOne.totalValue = TotalValue + " KWH";
        dataOne.totalReset = TotalReset + "";
        dataOne.meterDate = NewDataList.year + 2000 + "/" + NewDataList.month + "/" + NewDataList.day + " " + NewDataList.hour + ":" + NewDataList.minute + ":0";
        dataOnes.add(dataOne);


        if (NewDataList._MTR_REACTIVE_FETUR) {//todo for Three phase
            Shared.DataOne drReActive = new Shared.DataOne();
            drReActive.name = "Reactive";

            if (NewDataList._MTR_TYPE_FETUR == 0) {
                drReActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
            }

            TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType));
            if (NewDataList._MTR_TYPE_FETUR == 0 && NewDataList.isIndirect)
                TotalValue = TotalValue / 100;
            else TotalValue = TotalValue / 1000;
            index += 4;
            TotalReset = Utils.converArrtoShort(BufferData, index) /*/ 1000 + " KWh"*/;
            index += 2;
            //TotalValue = TotalValue + TotalReset * (1000000);
            drReActive.totalValue = TotalValue + " KVarh";   // 4 uint32_t
            drReActive.totalReset = TotalReset + "";


            drReActive.meterDate = dataOnes.get(0).meterDate + "";
            dataOnes.add(drReActive);
        }

        if (NewDataList._MTR_REACTIVE_FETUR) {
            Shared.DataOne drApparent = new Shared.DataOne();
            drApparent.name = "Apparent";
            if (NewDataList._MTR_TYPE_FETUR == 0) {// todo for three phase
                drApparent.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drApparent.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drApparent.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drApparent.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drApparent.ph3val = ((double) Utils.converArrtoInt(BufferData, index)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drApparent.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
            }
            TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000;
            index += 4;
            TotalReset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF;
            index += 2;
//        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }
            drApparent.totalValue = TotalValue + " KWH";
            drApparent.totalReset = TotalReset + "";
            drApparent.meterDate = NewDataList.year + 2000 + "/" + NewDataList.month + "/" + NewDataList.day + " " + NewDataList.hour + ":" + NewDataList.minute + ":0";
            dataOnes.add(drApparent);
        }

        if (NewDataList._MTR_RVS_TMPR_FETUR) {
            Shared.DataOne drReverseActive = new Shared.DataOne();
            drReverseActive.name = "Reverse Active";
            if (NewDataList._MTR_TYPE_FETUR == 0) {// todo for three phase
                drReverseActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReverseActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReverseActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
            }
            TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000;
            index += 4;
            TotalReset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF;
            index += 2;
//        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }
            drReverseActive.totalValue = TotalValue + " KWH";
            drReverseActive.totalReset = TotalReset + "";
            drReverseActive.meterDate = NewDataList.year + 2000 + "/" + NewDataList.month + "/" + NewDataList.day + " " + NewDataList.hour + ":" + NewDataList.minute + ":0";
            dataOnes.add(drReverseActive);
        }

        if (NewDataList._MTR_RVS_TMPR_FETUR && NewDataList._MTR_REACTIVE_FETUR) {//todo ffor three
            Shared.DataOne drReverseReActive = new Shared.DataOne();
            drReverseReActive.name = "Reverse ReActive";

            if (NewDataList._MTR_TYPE_FETUR == 0) // MTR_THREE_PH and MTR_RVS_TMPR and MTR_REACTIVE
            {
                drReverseReActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseReActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReverseReActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseReActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drReverseReActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drReverseReActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
            }

            TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000;
            index += 4;
            TotalReset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) /*/ 1000 + " KWh"*/;
            index += 2;

            drReverseReActive.totalValue = TotalValue + " KVarh";
            drReverseReActive.totalReset = TotalReset + "" /*/ 1000 + " KVah"*/;
            drReverseReActive.meterDate = "";
            dataOnes.add(drReverseReActive);
        }
        if (NewDataList._MTR_ERTH_TMPR_FETUR) {
            Shared.DataOne drFaultActive = new Shared.DataOne();
            drFaultActive.name = "Fault Active";

            TotalValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000;
            index += 4;
            TotalReset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF;
            index += 2;
            //        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }

            drFaultActive.totalValue = TotalValue + " KWH";
            drFaultActive.totalReset = TotalReset + "";
            drFaultActive.meterDate = NewDataList.year + 2000 + "/" + NewDataList.month + "/" + NewDataList.day + " " + NewDataList.hour + ":" + NewDataList.minute + ":0";
            dataOnes.add(drFaultActive);
        }
        /*
         * new addition
         * */
        if (NewDataList.isIndirect) {
            index += 36;
        }

        /*
        Quadrant
         */
        //region Quadrant
        if (NewDataList._MTR_TYPE_FETUR == 0) //Three Phase - Quadrant
        {
            if (NewDataList._MTR_ERTH_TMPR_FETUR) // MTR_ERTH_TMPR
            {
                Shared.DataOne drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Active Quadrant Energy 1";
                TotalValue = 0;
                double QuadrantValue1 = 0;
                double QuadrantValue2 = 0;
                double QuadrantValue3 = 0;

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;

                if (NewDataList.isIndirect) {
                    TotalValue = TotalValue * 10;
                }

                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue = TotalValue * 10;
                /// thanks


                drQuadrantActive.totalValue = TotalValue + " KWh";

                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);
                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Active Quadrant Energy 2";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;

//        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }

                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue = TotalValue * 10;
                /// thanks

                drQuadrantActive.totalValue = TotalValue + " KWh";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);

                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Active Quadrant Energy 3";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
//        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }

                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue = TotalValue * 10;
                /// thanks

                drQuadrantActive.totalValue = TotalValue + " KWh";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);


                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Active Quadrant Energy 4";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
//        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }

                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue = TotalValue * 10;
                /// thanks

                drQuadrantActive.totalValue = TotalValue + " KWh";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);


                if (NewDataList._MTR_REACTIVE_FETUR) // MTR_REACTIVE
                {
                    drQuadrantActive = new Shared.DataOne();
                    drQuadrantActive.name = "ReActive Quadrant Energy 1";

                    drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t

                    TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                    drQuadrantActive.totalValue = TotalValue + " KVar";
                    drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                    dataOnes.add(drQuadrantActive);

                    drQuadrantActive = new Shared.DataOne();
                    drQuadrantActive.name = "ReActive Quadrant Energy 2";

                    drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t

                    TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                    drQuadrantActive.totalValue = TotalValue + " KVar";
                    drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                    dataOnes.add(drQuadrantActive);

                    drQuadrantActive = new Shared.DataOne();
                    drQuadrantActive.name = "ReActive Quadrant Energy 3";

                    drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t

                    TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                    drQuadrantActive.totalValue = TotalValue + " KVar";
                    drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                    dataOnes.add(drQuadrantActive);


                    drQuadrantActive = new Shared.DataOne();
                    drQuadrantActive.name = "ReActive Quadrant Energy 4";

                    drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t
                    drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                    index += 4;      // 4 uint32_t
                    drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                    index += 2;     // 2 uint16_t

                    TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                    drQuadrantActive.totalValue = TotalValue + " KVar";
                    drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                    dataOnes.add(drQuadrantActive);
                }


                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Apparnt Quadrant Energy 1";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                //        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }
                drQuadrantActive.totalValue = TotalValue + " KVah";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);

                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Apparnt Quadrant Energy 2";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                //        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }
                drQuadrantActive.totalValue = TotalValue + " KVah";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);

                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Apparnt Quadrant Energy 3";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t

                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;
                //        if (metertype == indirect) { // todo
//            TotalValue = TotalValue * 10;
//        }

                drQuadrantActive.totalValue = TotalValue + " KVah";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);


                drQuadrantActive = new Shared.DataOne();
                drQuadrantActive.name = "Apparnt Quadrant Energy 4";

                drQuadrantActive.ph1val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph1Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph2val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph2Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                drQuadrantActive.ph3val = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
                index += 4;      // 4 uint32_t
                drQuadrantActive.ph3Reset = Utils.converArrtoShort(BufferData, index, NewDataList.endianType) + "";
                index += 2;     // 2 uint16_t
                TotalValue = QuadrantValue1 + QuadrantValue2 + QuadrantValue3;

                drQuadrantActive.totalValue = TotalValue + " KVah";
                drQuadrantActive.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(drQuadrantActive);


                Shared.DataOne dr1;
                double TotalValue1 = 0;
                double tot_current_year_act_en = 0;
                double tot_current_year_app_en = 0;

                double tot_prv_year_act_en = 0;
                double tot_prv_year_app_en = 0;


                dr1 = new Shared.DataOne();
                dr1.name = "Total Active Energy Current Year";
                dr1.ph1val = "0";
                dr1.ph1Reset = "0";
                dr1.ph2val = "0";
                dr1.ph2Reset = "0";
                dr1.ph3val = "0";
                dr1.ph3Reset = "0";
                TotalValue1 = (double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType) / 1000;
                index += 6;


                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue1 = TotalValue1 * 10;
                /// thanks

                dr1.totalValue = TotalValue1 + " KWh";
                tot_current_year_act_en = TotalValue1;
                dr1.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(dr1);

                dr1 = new Shared.DataOne();
                dr1.name = "Total Apparent Energy Current Year";
                dr1.ph1val = "0";
                dr1.ph1Reset = "0";
                dr1.ph2val = "0";
                dr1.ph2Reset = "0";
                dr1.ph3val = "0";
                dr1.ph3Reset = "0";
                TotalValue1 = (double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType) / 1000;
                index += 6;
                dr1.totalValue = TotalValue1 + " KVAh";
                tot_current_year_app_en = TotalValue1;
                dr1.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(dr1);

                dr1 = new Shared.DataOne();
                dr1.name = "Average PF Current Year";
                dr1.ph1val = "0";
                dr1.ph1Reset = "0";
                dr1.ph2val = "0";
                dr1.ph2Reset = "0";
                dr1.ph3val = "0";
                dr1.ph3Reset = "0";
                if (tot_current_year_app_en != 0) {
                    TotalValue1 = (double) tot_current_year_act_en / tot_current_year_app_en;
                    dr1.totalValue = (TotalValue1 + "");
                } else {
                    dr1.totalValue = "NF";
                }
                dr1.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(dr1);

                dr1 = new Shared.DataOne();
                dr1.name = "Total Active Energy Previous Year";
                dr1.ph1val = "0";
                dr1.ph1Reset = "0";
                dr1.ph2val = "0";
                dr1.ph2Reset = "0";
                dr1.ph3val = "0";
                dr1.ph3Reset = "0";
                TotalValue1 = (double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType) / 1000;
                index += 6;
                ///A.Lasheen 2017 07 12
                ///add TotalValue = TotalValue * 10;
                /// to solbe bug of resultion
                //if (isIndirect) TotalValue1 = TotalValue1 * 10;
                /// thanks
                dr1.totalValue = TotalValue1 + " KWh";
                tot_prv_year_act_en = TotalValue1;
                dr1.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(dr1);

                dr1 = new Shared.DataOne();
                dr1.name = "Total Apparent Energy Previous Year";
                dr1.ph1val = "0";
                dr1.ph1Reset = "0";
                dr1.ph2val = "0";
                dr1.ph2Reset = "0";
                dr1.ph3val = "0";
                dr1.ph3Reset = "0";
                TotalValue1 = (double) Utils.converArrtoInt(BufferData, index, Shared.DataNewDataListPacket.endianType) / 1000;
                index += 6;
                dr1.totalValue = TotalValue1 + " KVAh";
                tot_prv_year_app_en = TotalValue1;
                dr1.meterDate = dataOnes.get(0).meterDate;
                dataOnes.add(dr1);
                try {
                    dr1 = new Shared.DataOne();
                    dr1.name = "Average PF Previous Year";
                    dr1.ph1val = "0";
                    dr1.ph1Reset = "0";
                    dr1.ph2val = "0";
                    dr1.ph2Reset = "0";
                    dr1.ph3val = "0";
                    dr1.ph3Reset = "0";
                    if (tot_prv_year_app_en != 0) {
                        TotalValue1 = (double) tot_prv_year_act_en / tot_prv_year_app_en;
                        dr1.totalValue = (TotalValue1 + "").substring(0, 4);
                    } else {
                        dr1.totalValue = "NF";
                    }
                    dr1.meterDate = dataOnes.get(0).meterDate;
                    dataOnes.add(dr1);
                } catch (Exception e) {

                }
            }
        }

        Shared.DataTwo dr = new Shared.DataTwo();
        dr.wattsValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
        index += 4;
        byte Minute = BufferData[index];
        index += 1; // 1 uint8_t
        byte Hour = BufferData[index];
        index += 1;   // 1 uint8_t
        byte Day = BufferData[index];
        index += 1;    // 1 uint8_t
        byte Month = BufferData[index];
        index += 1;  // 1 uint8_t
        byte Year = BufferData[index];
        index += 1;   // 1 uint8_t
        dr.wattsTime = Shared.getFormattedDate(Year + 2000 + "/" + Month + "/" + Day + " " + Hour + ":" + Minute + ":0");
        dr.wattsPeriod = (Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF) + "";
        index += 2;

        if (NewDataList._MTR_REACTIVE_FETUR) {
            dr.vAsValue = (((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000) + "";
            index += 4;    // 4 uint32_t
            Minute = BufferData[index];
            index += 1;// 1 uint8_t
            Hour = BufferData[index];
            index += 1;  // 1 uint8_t
            Day = BufferData[index];
            index += 1;   // 1 uint8_t
            Month = BufferData[index];
            index += 1; // 1 uint8_t
            Year = BufferData[index];
            index += 1;  // 1 uint8_t

            dr.vAsTime = (Year + 2000) + "/" + (Month == 0 ? 1 : Month) + "/" + (Day == 0 ? 1 : Day) + " " + Hour + ":" + Minute + ":0";

            dr.vAsPeriod = (Utils.converArrtoShort(BufferData, index, NewDataList.endianType) & 0xFFFF) + "";
            index += 2;
        }
        dr.amperesValue = ((double) Utils.converArrtoInt(BufferData, index, NewDataList.endianType)) / 1000 + "";
        index += 4;
        Minute = BufferData[index];
        index += 1;
        Hour = BufferData[index];
        index += 1;   // 1 uint8_t
        Day = BufferData[index];
        index += 1;    // 1 uint8_t
        Month = BufferData[index];
        index += 1;  // 1 uint8_t
        Year = BufferData[index];
        index += 1;   // 1 uint8_t

        dr.amperesTime = Year + 2000 + "/" + Month + "/" + Day + " " + Hour + ":" + Minute + ":0";
        dr.timeStamp = dataOne.meterDate;
        dataTwos.add(dr);


        Shared.MeteringData meteringData = new Shared.MeteringData();
        meteringData.dataOne = dataOnes;
        meteringData.dataTwo = dataTwos;
        for (int i = 0; i < meteringData.dataTwo.size(); i++) {
            Shared.DataTwo dataTwo1 = meteringData.dataTwo.get(i);
            dataTwo1.amperesPeriod = "";
        }
        return meteringData;
    }

    public ArrayList<Shared.Event> splitEventLog(byte[] BufferData) {
        ArrayList<Shared.Event> events = new ArrayList<>();
        int serial = 0;
        int StartIndex = 0;
        for (int i = StartIndex; (i + 14) <= BufferData.length; i = i + 14) {
            byte EventCode = BufferData[i]; // 0
            long MeterStatus = Utils.converArrtoLong(BufferData, i + 1) & 0xFFFFFFFF; // 1 > 8

            String dateTime = ((BufferData[i + 13] & 0xFF) + 2000) + "/" + (BufferData[i + 12] & 0xFF) + "/" + (BufferData[i + 11] & 0xFF) + " " + (BufferData[i + 10] & 0xFF) + ":" + (BufferData[i + 9] & 0xFF) + ":0";

            serial++;

            Shared.Event event = new Shared.Event();
            event.dateTime = dateTime;
            //event.meterStatus = MeterStatus+"";
            event.ser = serial + "";
            //event.event = (EventCode&0xFF)+"";
            Shared.EventsCode code = null;
            for (Shared.EventsCode c : Shared.EventsCode.values()) {
                if (c.getId() == (EventCode & 0xFF)) {
                    code = c;
                    event.event = code.name();
                    break;
                } else {
                    event.event = (EventCode & 0xFF) + "";
                }
            }
            events.add(event);
        }
        return events;
    }

    public ArrayList<Shared.BillingPeriodHistory> SplitBPH(Shared.DataNewDataListPacket obj, byte[] bufferData) {
        int frameSize = Utils.getFrameSize(bufferData.length);
        if (frameSize == 23 || frameSize == 31) {
            return splitBPHNewFirmware(obj, bufferData, frameSize);
        } else if (frameSize == 19 || frameSize == 11) {
            return splitBPHOldFirmware(obj, bufferData, frameSize);
        } else return null;
    }

    private ArrayList<Shared.BillingPeriodHistory> splitBPHNewFirmware(Shared.DataNewDataListPacket obj, byte[] BufferData, int frameSize) {
        try {
            ArrayList<Shared.BillingPeriodHistory> Historytbl = new ArrayList<>();
            int StartIndex = 0;
            double ActiveEnergy, mda, mdw, ReactiveEnergy, consBill, DebitRecalcDifference, CustomerServiceTax, moneyBalance;
            String date = "";
            for (int i = StartIndex; (i + frameSize) <= BufferData.length; i = i + frameSize) {
                try {
                    Shared.BillingPeriodHistory billingPeriodHistory = new Shared.BillingPeriodHistory();
                    if (obj._MTR_TYPE_FETUR != 1) {
                        moneyBalance = 0;
                        ActiveEnergy = ((double) Utils.converArrtoInt(BufferData, i, obj.endianType)) / 1000;
                        consBill = ((double) Utils.converArrtoInt(BufferData, i + 4, obj.endianType)) / 1000;
                        mda = ((double) Utils.converArrtoInt(BufferData, i + 8, obj.endianType)) / 1000;
                        mdw = ((double) Utils.converArrtoInt(BufferData, i + 12, obj.endianType)) / 1000;
                        ReactiveEnergy = ((double) Utils.converArrtoInt(BufferData, i + 16, obj.endianType)) / 1000;
                        date = ((BufferData[i + 22] & 0xFF) + 2000) + "-" + ((BufferData[i + 21])) + "-" + ((BufferData[i + 20]));
                    } else {
                        ActiveEnergy = ((double) Utils.converArrtoInt(BufferData, i, obj.endianType)) / 1000;
                        mda = ((double) Utils.converArrtoInt(BufferData, i + 4, obj.endianType)) / 1000;
                        consBill = ((double) Utils.converArrtoInt(BufferData, i + 8, obj.endianType)) / CurrencyRatio;
                        DebitRecalcDifference = ((double) Utils.converArrtoInt(BufferData, i + 12, obj.endianType)) / CurrencyRatio;
                        CustomerServiceTax = ((double) Utils.converArrtoInt(BufferData, i + 16, obj.endianType)) / CurrencyRatio;
                        moneyBalance = ((double) Utils.converArrtoInt(BufferData, i + 20, obj.endianType)) / CurrencyRatio;
                        mdw = ((double) Utils.converArrtoInt(BufferData, i + 24, obj.endianType)) / 1000;
                        ReactiveEnergy = 0;
                        date = ((BufferData[i + 30] & 0xFF) + 2000) + "-" + ((BufferData[i + 29] & 0xFF)) + "-" + ((BufferData[i + 28] & 0xFF));
                    }

                    billingPeriodHistory.activeEnergy = ActiveEnergy + "";
                    billingPeriodHistory.mdA = mda + "";
                    billingPeriodHistory.mdW = mdw + "";
                    billingPeriodHistory.moneyBalance = moneyBalance + "";
                    billingPeriodHistory.consumptionBill = consBill + "";
                    billingPeriodHistory.reactiveEnergy = ReactiveEnergy + "";
                    billingPeriodHistory.date = date;

                    Historytbl.add(billingPeriodHistory);
                } catch (Exception ignored) {
                }
            }
            return Historytbl;
        } catch (Exception e) {
            return null;
        }
    }

    private ArrayList<Shared.BillingPeriodHistory> splitBPHOldFirmware(Shared.DataNewDataListPacket obj, byte[] BufferData, int frameSize) {
        try {
            ArrayList<Shared.BillingPeriodHistory> Historytbl = new ArrayList<>();
            int StartIndex = 0;
            double ActiveEnergy, mda, mdw = 0, ReactiveEnergy;
            String date = "";
            for (int i = StartIndex; (i + frameSize) <= BufferData.length; i = i + frameSize) {
                try {
                    Shared.BillingPeriodHistory billingPeriodHistory = new Shared.BillingPeriodHistory();
                    if (obj._MTR_TYPE_FETUR != 1) {
                        ActiveEnergy = ((double) Utils.converArrtoInt(BufferData, i, obj.endianType)) / 1000;
                        mda = ((double) Utils.converArrtoInt(BufferData, i + 4, obj.endianType)) / 1000;
                        mdw = ((double) Utils.converArrtoInt(BufferData, i + 8, obj.endianType)) / 1000;
                        ReactiveEnergy = ((double) Utils.converArrtoInt(BufferData, i + 12, obj.endianType)) / 1000;
                        date = ((BufferData[i + 18] & 0xFF) + 2000) + "-" + ((BufferData[i + 17])) + "-" + ((BufferData[i + 16]));
                    } else {
                        ActiveEnergy = ((double) Utils.converArrtoInt(BufferData, i, obj.endianType)) / 1000;
                        mda = ((double) Utils.converArrtoInt(BufferData, i + 4, obj.endianType)) / 1000;
//                        mdw = ((double) Utils.converArrtoInt(BufferData, i + 24, obj.endianType)) / 1000;
                        ReactiveEnergy = 0;
                        date = ((BufferData[i + 10] & 0xFF) + 2000) + "-" + ((BufferData[i + 9] & 0xFF)) + "-" + ((BufferData[i + 8] & 0xFF));
                    }
                    billingPeriodHistory.activeEnergy = ActiveEnergy + "";
                    billingPeriodHistory.mdA = mda + "";
                    billingPeriodHistory.mdW = mdw + "";
                    billingPeriodHistory.reactiveEnergy = ReactiveEnergy + "";
                    billingPeriodHistory.date = date;

                    Historytbl.add(billingPeriodHistory);
                } catch (Exception ignored) {
                }
            }
            return Historytbl;
        } catch (Exception e) {
            return null;
        }
    }


    public ArrayList<Shared.MoneyTransaction> SplitMoneyTransaction(byte[] BufferData) {
        ArrayList<Shared.MoneyTransaction> transactions = new ArrayList<>();
        int StartIndex = 0;
        for (int i = StartIndex; (i + 17) <= BufferData.length; i = i + 17) {
            double MoneyBalance = Utils.converArrtoInt(BufferData, i, Shared.DataNewDataListPacket.endianType) / CurrencyRatio;
            double ChargeMoney = Utils.converArrtoInt(BufferData, i + 4, Shared.DataNewDataListPacket.endianType) / CurrencyRatio;
            if (Shared.DataNewDataListPacket.endianType == ByteOrder.LITTLE_ENDIAN) {
                MoneyBalance *= 10;
                ChargeMoney *= 10;
            }
            short ChargeDischargeMeter = Utils.converArrtoShort(BufferData, i + 8, Shared.DataNewDataListPacket.endianType);
            String Type = BufferData[i + 10] == 0 ? "Charge" : "Discharge";
            String Interface = "";
            if (BufferData[i + 11] == 0)
                Interface = "Optical";
            else if (BufferData[i + 11] == 1)
                Interface = "GPRS";
            else if (BufferData[i + 11] == 2)
                Interface = "RFID";
            else if (BufferData[i + 11] == 3)
                Interface = "Scratch";

            String dateTime = ((BufferData[i + 16] & 0xFF) + 2000) + "/" + (BufferData[i + 15] & 0xFF) + "/" + (BufferData[i + 14] & 0xFF) + " " + (BufferData[i + 13] & 0xFF) + ":" + (BufferData[i + 12] & 0xFF) + ":0";

            //show reccord in table
            Shared.MoneyTransaction transaction = new Shared.MoneyTransaction();
            transaction.balance = MoneyBalance + "";
            transaction.charge = ChargeMoney + "";
            transaction.chargeDisNum = (ChargeDischargeMeter & 0xFFFF) + "";
            transaction.type = Type;
            transaction.interFace = Interface;
            transaction.dateTime = Shared.getFormattedDate(dateTime);
            transactions.add(transaction);
        }
        return transactions;
    }

    public ArrayList<Shared.ProfileRecord> SplitProfileRecord(byte[] BufferData) {
        ArrayList<Shared.ProfileRecord> profileRecords = new ArrayList<>();
        ByteOrder byteOrder = Shared.DataNewDataListPacket.endianType;

        int serial = 0;
        int StartIndex = 0;
        if (byteOrder == ByteOrder.BIG_ENDIAN) {
            for (int i = StartIndex; (i + 10) <= BufferData.length; i = i + 14) {
                double TotalConsumtion = ((double) Utils.converArrtoInt(BufferData, i, byteOrder)) / 1000;
                // there was a problem when dividing by 4
                // divided by 100 instead of 4
                double CurrentChannelA = ((Utils.converArrtoShort(BufferData, i + 4, byteOrder) & 0xffff)) / 100.0;
                int VoltageChannelA = Utils.converArrtoShort(BufferData, i + 6, byteOrder) & 0xffff;

                String PowerFactorChannelA = BufferData[i + 8] == (byte) 127 ? "NF" : (((double) (BufferData[i + 8] & 0xff)) / 100) + "";
                String dateTime = ((BufferData[i + 13] & 0xFF) + 2000) + "-" + (BufferData[i + 12] & 0xFF) + "-" + (BufferData[i + 11] & 0xFF) + " " + (BufferData[i + 10] & 0xFF) + ":" + (BufferData[i + 9] & 0xFF) + ":0";

                serial++;

                Shared.ProfileRecord profileRecord = new Shared.ProfileRecord();
                profileRecord.ser = serial + "";
                profileRecord.CurrentChannelA = CurrentChannelA + "";
                profileRecord.TotalConsumation = TotalConsumtion + "";
                profileRecord.VoltageChannelA = VoltageChannelA + "";
                profileRecord.PowerFactorChannelA = PowerFactorChannelA;
                profileRecord.ProfileDateTime = dateTime;
                profileRecords.add(profileRecord);
            }
        } else {
            for (int i = StartIndex; (i + 32) <= BufferData.length; i = i + 32) {
                double TotalConsumtion = 0;// Convert.ToDecimal(Converter.ToUInt32(BufferData, i)) / 1000;
                double TotalActive = ((double) Utils.converArrtoInt(BufferData, i, byteOrder)) / 1000;
                double TotalReActive = ((double) Utils.converArrtoInt(BufferData, i + 6, byteOrder)) / 1000;

                double CurrentChannelA = (double) ((Utils.converArrtoShort(BufferData, i + 12, byteOrder)) & 0xFFFF) / 100;
                double CurrentChannelB = (double) ((Utils.converArrtoShort(BufferData, i + 14, byteOrder)) & 0xFFFF) / 100;
                double CurrentChannelC = (double) ((Utils.converArrtoShort(BufferData, i + 16, byteOrder)) & 0xFFFF) / 100;
                int VoltageChannelA = Utils.converArrtoShort(BufferData, i + 18, byteOrder) & 0xFFFF;
                int VoltageChannelB = Utils.converArrtoShort(BufferData, i + 20, byteOrder) & 0xFFFF;
                int VoltageChannelC = Utils.converArrtoShort(BufferData, i + 22, byteOrder) & 0xFFFF;
                String PowerFactorChannelA = BufferData[i + 24] == (byte) 127 ? "0" : String.valueOf(((BufferData[i + 24] & 0xFF) / 100));
                String PowerFactorChannelB = BufferData[i + 25] == (byte) 127 ? "0" : String.valueOf(((BufferData[i + 25] & 0xFF) / 100));
                String PowerFactorChannelC = BufferData[i + 26] == (byte) 127 ? "0" : String.valueOf(((BufferData[i + 26] & 0xFF) / 100));

                String ProfileDateTime = ((BufferData[i + 31] & 0xFF) + 2000) + "/" + (BufferData[i + 30] & 0xFF) + "/" + (BufferData[i + 29] & 0xFF) + " " + (BufferData[i + 28] & 0xFF) + ":" + (BufferData[i + 27] & 0xFF) + ":0";


                serial++;

                Shared.ProfileRecord profileRecord = new Shared.ProfileRecord();
                profileRecord.ser = serial + "";
                profileRecord.TotalConsumation = TotalConsumtion + "";
                profileRecord.TotalActive = TotalActive + "";
                profileRecord.TotalReActive = TotalReActive + "";
                profileRecord.CurrentChannelA = "phase1: " + CurrentChannelA + " - " + "phase2: " + CurrentChannelB + " - " + "phase3: " + CurrentChannelC;
                profileRecord.VoltageChannelA = "phase1: " + VoltageChannelA + " - " + "phase2: " + VoltageChannelB + " - " + "phase3: " + VoltageChannelC;
                profileRecord.PowerFactorChannelA = "phase1: " + PowerFactorChannelA + " - " + "phase2: " + PowerFactorChannelB + " - " + "phase3: " + PowerFactorChannelC;
                profileRecord.ProfileDateTime = ProfileDateTime;

                profileRecords.add(profileRecord);
            }
        }
        return profileRecords;
    }

    private String AddDataRowRecord(String DataRowName, int StartIndex, ArrayList<Shared.RowDatatbl> RowDatatbl, byte[] BufferData, int scale, String unit, int type) {
        Shared.RowDatatbl datarow = new Shared.RowDatatbl();
        datarow.dataElement = DataRowName;
        //TODO //if (!isIncludingRegisterResolution)
        {
            if (type == 1)
                datarow.value = (Utils.converArrtoShort(BufferData, StartIndex, Shared.DataNewDataListPacket.endianType)) == 0 ? "0" + unit : (Utils.converArrtoShort(BufferData, StartIndex/* + 60*/, Shared.DataNewDataListPacket.endianType)) * Math.pow(10, scale) + unit + "";
            else if (type == 2)
                datarow.value = (Utils.converArrtoInt(BufferData, StartIndex, Shared.DataNewDataListPacket.endianType)) == 0 ? "0" + unit : (Utils.converArrtoInt(BufferData, StartIndex/* + 60*/, Shared.DataNewDataListPacket.endianType)) * Math.pow(10, scale) + unit + "";
            else if (type == 3)
                datarow.value = (Utils.converArrtoShort(BufferData, StartIndex, Shared.DataNewDataListPacket.endianType)) == 0 ? "0" + unit : (Utils.converArrtoShort(BufferData, StartIndex/* + 60*/, Shared.DataNewDataListPacket.endianType)) * Math.pow(10, scale) + unit + "";
            else if (type == 4)
                datarow.value = (Utils.converArrtoInt(BufferData, StartIndex, Shared.DataNewDataListPacket.endianType)) == 0 ? "0" + unit : (Utils.converArrtoInt(BufferData, StartIndex/* + 60*/, Shared.DataNewDataListPacket.endianType)) * Math.pow(10, scale) + unit + "";
            else if (type == 5)
                datarow.value = BufferData[StartIndex] == 0 ? "0" + unit : (BufferData[StartIndex] & 0xFF) * Math.pow(10, scale) + unit;
        }
        RowDatatbl.add(datarow);
        return datarow.value;
    }

    public Shared.TarrifPaymentData SplitTarifAndPaymentData(byte[] BufferData, Shared.DataNewDataListPacket obj) {
        int MoneyBalance = 0;
        int gprsChargeNo = 0;
        int RFIDChargeNumber = 0;
        int PostPaidMoney = 0;
        try {
            ArrayList<Shared.RowDatatbl> RowDatatbl = new ArrayList<>();
            ArrayList<Shared.Tarifftbl> Tarifftbl = new ArrayList<>();
            ArrayList<Shared.Daystbl> Daystbl = new ArrayList<>();
            SplitRowData(BufferData, obj, RFIDChargeNumber, gprsChargeNo, MoneyBalance, PostPaidMoney, RowDatatbl, Tarifftbl, Daystbl);
            // save in DB
            //SaveRowDataObjects(RowDatatbl, Tarifftbl, Daystbl, MeterID, 0, false);
            Shared.TarrifPaymentData tarrifPaymentData = new Shared.TarrifPaymentData();
            tarrifPaymentData.daystbls = Daystbl;
            tarrifPaymentData.tarifftbls = Tarifftbl;
            tarrifPaymentData.rowDatatbls = RowDatatbl;
            return tarrifPaymentData;
        } catch (Exception e) {
            return null;
        }
    }

    public Shared.ControlTemperDT1 SplitControlAndTamperData(Shared.DataNewDataListPacket NewDataList, byte[] BufferData) {
        boolean MeterStatusOnly = false;
        //ArrayList<Shared.ControlTemperDT1> controlTemperDT1s = new ArrayList<>();
        Shared.ControlTemperDT1 drDataOne = new Shared.ControlTemperDT1();
        try {
            String TimeStamp = NewDataList.year + 2000 + "-" + NewDataList.month + "-" + NewDataList.day + " " + NewDataList.hour + ":" + NewDataList.minute + ":0";
            int index = 4;
            if (!MeterStatusOnly) {
                if (true) {
                    drDataOne.DataCorruption = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (true) {
                    drDataOne.HardwareErrors = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (!NewDataList._CTRL_BTRY_NON_CHG) {
                    drDataOne.BatteryLowAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._PAYMENT_SYS) {
                    drDataOne.LowCreditFirstAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._PYMT_LOW_TWO_LVL) {
                    drDataOne.LowCreditSecondAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._PYMT_FRNDLY) {
                    drDataOne.NoCreditWithFriendlyAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._PAYMENT_SYS) {
                    drDataOne.NoCreditAlarmcount = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._MTR_ENABLE_LMT_FETUR) {
                    drDataOne.OverLoadAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                    drDataOne.OverVoltAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                    drDataOne.UnderVoltAlarm = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._MTR_TYPE_FETUR == 0) {
                    drDataOne.PhaseSequence = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._CTRL_CVR_SW) {
                    drDataOne.CoverOpen = (BufferData[index] & 0xFF) + "";
                    index += 1;     // 1   uint8
                }
                if (NewDataList._CTRL_TRMNL_SW) {
                    drDataOne.TerminalOpen = (BufferData[index] & 0xFF) + "";
                    index += 1; // 1 uint8
                }
                if (NewDataList._MTR_TYPE_FETUR == 1) {
                    index += 1; // 1 uint8
                }
                if (NewDataList._MTR_TYPE_FETUR == 1)//Single phase
                {
                    if (NewDataList._MTR_RVS_TMPR_FETUR) {
                        drDataOne.ReverseCurrent = (BufferData[index] & 0xFF) + "";
                        index += 1; // 1 uint8
                    }
                    if (NewDataList._MTR_ERTH_TMPR_FETUR) {
                        drDataOne.FaultEnergy = (BufferData[index] & 0xFF) + "";
                        index += 1; // 1 uint8
                    }
                } else {
                    if (NewDataList._MTR_RVS_TMPR_FETUR) {
                        drDataOne.ReverseCurrent = (BufferData[index] & 0xFF) + "";
                        index += 1; // 1 uint8
                    }
                    if (NewDataList._MTR_ERTH_TMPR_FETUR) {
                        drDataOne.FaultEnergy = (BufferData[index] & 0xFF) + "";
                        index += 1; // 1 uint8
                    }
//                    if (Shared.DataNewDataListPacket.meterType == Shared.DataNewDataListPacket.MeterType.InDirect) {
//                        index += 36;
//                    }
                    if (NewDataList._MTR_MIS_POT_TMPR_FETUR) {
                        drDataOne.MissingPotential = (BufferData[index] & 0xFF) + "";
                        index += 1; // 1 uint8
                    }
                }
                if (NewDataList._CTRL_GPRS_MODULE_CVR_SW) {
                    drDataOne.GPRSModuleCoverOpenTamperCount = (BufferData[index] & 0xFF) + "";
                    index += 1; // 1 uint8
                }
            }
            //--------------------------------------------------------------------
            index += 6; // 1 uint8

            String hex = Integer.toBinaryString(BufferData[25] & 0xFF);
            String relay = NewDataList._MTR_TYPE_FETUR == 0 ? hex.substring(0, 1) : hex.substring(hex.length() - 1);
            index += 1;     // 1	uint8
            // understand this
            drDataOne.Relay = relay;
            index += 4; // 1 uint8
            byte[] cardIdBytes = new byte[8];
            cardIdBytes = Arrays.copyOfRange(BufferData, index, index + 8);
            String value = Utils.bytesToHex(cardIdBytes);
            drDataOne.RfUniqueId = value;

            drDataOne.TimeStamp = TimeStamp;
            return drDataOne;
        } catch (Exception e) {
            return null;
        }
    }

    public Shared.NewControlData SplitNewControlData(Shared.DataNewDataListPacket NewDataList, byte[] bufferData) {
        Shared.NewControlData readData = new Shared.NewControlData();
        try {

            // 472 473 474 475 476 477 tech codes   {472+32} = 504
            int startIndex = 0;
            String dateTime;
            int minute = 0;
            int hour = 0;
            int day = 0;
            int month = 0;
            int year = 0;

            startIndex++;

            startIndex += 4;
            startIndex++;
            startIndex++;
            startIndex++;
            startIndex++;

            startIndex += 360;
            startIndex += 4;
            startIndex += 4;
            // old way
//            //Last charging dateTime
//            minute = bufferData[startIndex] > 0 ? bufferData[startIndex] : 1;
//            hour = bufferData[startIndex + 1] > 0 ? bufferData[startIndex + 1] : 1;
//            day = bufferData[startIndex + 2] > 0 ? bufferData[startIndex + 2] : 1;
//            month = bufferData[startIndex + 3] > 0 ? bufferData[startIndex + 3] : 1;
//            year = bufferData[startIndex + 4] > 0 ? bufferData[startIndex + 4] : 1;
//            dateTime = (year + 2000 + "-" + month + "-" + day + " " + hour + ":" + minute + ":0");
//            readData.lastChargeDateTime = dateTime;
//
//
            // Here is change -- i need parse from 310 to get last date charge.
            day = bufferData[383] > 0 ? bufferData[383] : 1;
            month = bufferData[383 + 1] > 0 ? bufferData[383 + 1] : 1;
            year = bufferData[383 + 2] > 0 ? bufferData[383 + 2] : 1;
            dateTime = (year + 2000 + "-" + month + "-" + day + " " + hour + ":" + minute + ":0");
            readData.lastChargeDateTime = dateTime;
            startIndex += 5;
            //
            startIndex += 4;
            startIndex += 4;
            startIndex += 4;
            startIndex += 4;
            startIndex += 17;
            startIndex++;
            //Installation date
            minute = bufferData[startIndex] > 0 ? bufferData[startIndex] : 1;
            hour = bufferData[startIndex + 1] > 0 ? bufferData[startIndex + 1] : 1;
            day = bufferData[startIndex + 4] > 0 ? bufferData[startIndex + 4] : 1;
            month = bufferData[startIndex + 1 + 4] > 0 ? bufferData[startIndex + 1 + 4] : 1;
            year = bufferData[startIndex + 2 + 4] > 0 ? bufferData[startIndex + 2 + 4] : 1;
            dateTime = (year + 2000 + "/" + month + "/" + day + " " + hour + ":" + minute + ":0");
            readData.installingDateAndTime = Shared.getFormattedDate(dateTime);
            startIndex += 3;
            startIndex++;
            //Technician Ids
            for (int i = 0; i < 3; i++) {
                startIndex += 2;
            }
            //Skip 16 bytes return data for Firmware not needed
            startIndex += 16;
            //Contactor error counter
            startIndex += 2;
            //Consumption on Contactor error
            startIndex += 4;
            //Skip 10 bytes return data for Firmware not needed
            startIndex += 10;
            //Last 3 technicians for each operation on meters (Set dateTime - Reset all tampers - Reset specific tamper)
            for (int i = 0; i < 9; i++) {
                startIndex += 2;
            }
            int techIndex = 472;
            int[] techs = new int[3];
            // TODO(1) check byte order
            byte[] techArr = new byte[2];
            for (int i = 0; i < 3; i++) {
                if (Shared.DataNewDataListPacket.endianType == ByteOrder.BIG_ENDIAN) {
                    techArr = new byte[]{(byte) (bufferData[techIndex] & 0xFFFF), (byte) (bufferData[techIndex + 1] & 0xFFFF)};
                } else {
                    techArr = new byte[]{(byte) (bufferData[techIndex + 1] & 0xFFFF), (byte) (bufferData[techIndex] & 0xFFFF)};
                }
                String tech = Utils.bytesToHex(techArr);
                int techId = Integer.parseInt(tech, 16);
                techs[i] = techId;
                techIndex += 2;
            }
            int installingTechId = Utils.getLastItem(techs);
            readData.installingTechnicanCode = String.valueOf(installingTechId);

            byte[] activityTypeArr = {bufferData[431 + 4], bufferData[432 + 4]};
            readData.activityType = String.valueOf((Utils.converArrtoShort(activityTypeArr, 0, Shared.DataNewDataListPacket.endianType) & 0xFFFF));
            List<Shared.RemovedTampers> arr = parseRemovedTampers(NewDataList, bufferData, 517 + 4);
            readData.RemovedTampers = arr;
            return readData;
        } catch (Exception ex) {
            throw ex;
        }
    }

    public List<Shared.RemovedTampers> parseRemovedTampers(Shared.DataNewDataListPacket NewDataList, byte[] bufferData, int _start) {
        try {
            int start = _start;
            String[] TamperNames = new String[]{"CoverOpen", "TerminalOpen", "BatteryLowAlarm", "ReverseCurrent", "FaultEnergy", "0", "OverLoadAlarm"};
            List<Shared.RemovedTampers> arr = new ArrayList<>();
            int startIndex, min, hour, day, month, year, currentTamper = 0;
            for (int i = start; i + 15 < bufferData.length; i += 15, currentTamper++) {
                if (currentTamper == 5) {
                    continue;
                }
                if (currentTamper == 7) {
                    break;
                }
                startIndex = i;
                int techId = Utils.converArrtoShort(bufferData, startIndex, Shared.DataNewDataListPacket.endianType);

                startIndex += 2;
                int transactionID = Utils.converArrtoInt(bufferData, startIndex, Shared.DataNewDataListPacket.endianType);
                startIndex += 8;
                min = bufferData[startIndex] >= 0 ? bufferData[startIndex] : 1;
                hour = bufferData[startIndex + 1] >= 0 ? bufferData[startIndex + 1] : 1;
                day = bufferData[startIndex + 2] > 0 ? bufferData[startIndex + 2] : 1;
                month = bufferData[startIndex + 3] > 0 ? bufferData[startIndex + 3] : 1;
                year = bufferData[startIndex + 4] > 0 ? bufferData[startIndex + 4] : 1;

                String resetEventDateTime;
                try {
                    resetEventDateTime = ((year + 2000) + "/" + month + "/" + day + " " + hour + ":" + min + ":0");
                } catch (Exception ex) {
                    continue;
                }
                //show reccord in table
                Shared.RemovedTampers removedTampers = new Shared.RemovedTampers();
                if (year == 0 && month == 0 && day == 0) {
                    removedTampers.tamperId = "NF";
                    removedTampers.techID = "NF";
                    removedTampers.resetEventDateTime = resetEventDateTime;
                    removedTampers.transactionID = "NF";
                } else {
                    removedTampers.tamperId = TamperNames[currentTamper];
                    removedTampers.techID = String.valueOf(techId);
                    removedTampers.resetEventDateTime = resetEventDateTime;
                    removedTampers.transactionID = String.valueOf(transactionID);
                }


                if (techId != 0) {
                    arr.add(removedTampers);
                }
            }
            return arr;
        } catch (Exception ex) {
            return null;
        }
    }

    private void SplitRowData(byte[] BufferData, Shared.DataNewDataListPacket obj, int RFIDChargeNumber, int gprsChargNo, double MoneyBalance, double PostPaidMoney, ArrayList<Shared.RowDatatbl> RowDatatbl, ArrayList<Shared.Tarifftbl> Tarifftbl, ArrayList<Shared.Daystbl> Daystbl) {
        int StartIndex = 4;
        for (int i = 0; i < 7; i++) {
            Shared.Daystbl DR = new Shared.Daystbl();
            if (i == 0)
                DR.day = (i + 1) + " Day ago.";
            else
                DR.day = (i + 1) + " Days ago.";
            Daystbl.add(DR);
        }
        String str = "Tariff No. ";
        for (int i = 0; i < 8/*10*/; i++) {
            Shared.Tarifftbl DR = new Shared.Tarifftbl();
            DR.tariffNo = str + " " + (i + 1);
            Tarifftbl.add(DR);
        }
//        boolean IsLittleEndian = obj.endianType == ByteOrder.LITTLE_ENDIAN ? true : false;
        if (obj._TARIFF_SYS) {
            AddDataRowRecord("Current Consumption", StartIndex, RowDatatbl, BufferData, -3, " KWh", 4);
            AddDataRowRecord("Previous Hour Consumption", StartIndex + 4, RowDatatbl, BufferData, -3, " KWh", 4);
            for (int i = 0; i < 7; i++) {
                Daystbl.get(i).consumptionInKWh = (((double) Utils.converArrtoInt(BufferData, StartIndex + 8 + (6 - i) * 4, Shared.DataNewDataListPacket.endianType)) / 1000) + "";
            }
            AddDataRowRecord("Previous Week Consumption", StartIndex + 36, RowDatatbl, BufferData, -3, " KWh", 4);
            AddDataRowRecord("Previous Billing Period Consumption", StartIndex + 40, RowDatatbl, BufferData, -3, " KWh", 4);
            AddDataRowRecord("Step Remaining", StartIndex + 44, RowDatatbl, BufferData, -3, " KWh", 4);
            if (RowDatatbl.get(RowDatatbl.size() - 1).value.contains("4294967"))
                RowDatatbl.get(RowDatatbl.size() - 1).value = "NF";
            for (int i = 0; i < 8/*10*/; i++) {
                Tarifftbl.get(i).consumptionInKWh = ((double) Utils.converArrtoInt(BufferData, StartIndex + 48 + i * 4, Shared.DataNewDataListPacket.endianType)) / 1000 + "";
            }
//            for (int i = 0; i < 8/*10*/; i++) {
//                Tarifftbl.get(i).consumptionofpreviousInKWh = ((double) Utils.converArrtoInt(BufferData, StartIndex + 93 + i * 4, Shared.DataNewDataListPacket.endianType)) / 1000 + "";
//            }
            //
            int FriendlyConsumptionSize = 0;
            if (Shared.DataNewDataListPacket.endianType == ByteOrder.BIG_ENDIAN)//IsNorthDelta ==
            {
                FriendlyConsumptionSize = 4;
                AddDataRowRecord("Friendly Consumption", StartIndex + 88, RowDatatbl, BufferData, -3, " KWh", 4);
            }
            AddDataRowRecord("Current Tariff Index", StartIndex + 88 + FriendlyConsumptionSize, RowDatatbl, BufferData, 0, "", 5);
            StartIndex += 89 + FriendlyConsumptionSize;
            if (Shared.DataNewDataListPacket.endianType == ByteOrder.BIG_ENDIAN)
                StartIndex += 40;
        }
        // Depends on Existence of PAYMENT_SYS
        if (obj._PAYMENT_SYS) {
            if (Shared.DataNewDataListPacket.endianType == ByteOrder.LITTLE_ENDIAN) {
                AddDataRowRecord("Current BP Bill", StartIndex, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Previous Hour Bill", StartIndex + 4, RowDatatbl, BufferData, -3, "", 4);
                for (int i = 0; i < 7; i++) {
                    Daystbl.get(i).billing = ((double) Utils.converArrtoInt(BufferData, StartIndex + 8 + (6 - i) * 4, Shared.DataNewDataListPacket.endianType)) / 10000 + "";
                    //AddDataRowRecord((i + 1) + " Day(s) ago Billing.", StartIndex + 8 + (6 - i) * 4, ref RowDatatbl, BufferData, typeof(UInt32), -4, "");
                }
                AddDataRowRecord("Previous Week Bill", StartIndex + 36, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Previous Billing Period Bill", StartIndex + 40, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Total Life Charged Money", StartIndex + 44, RowDatatbl, BufferData, -2, "", 4);
                AddDataRowRecord("Current Billing Period Charged Money", StartIndex + 48, RowDatatbl, BufferData, -2, "", 4);
                AddDataRowRecord("Previous Billing Period Charged Money", StartIndex + 52, RowDatatbl, BufferData, -2, "", 4);
                AddDataRowRecord("Money Balance", StartIndex + 56, RowDatatbl, BufferData, -3, "", 2);
            } else {
                AddDataRowRecord("Current BP Bill", StartIndex, RowDatatbl, BufferData, -4, "", 4);
                AddDataRowRecord("Previous Hour Bill", StartIndex + 4, RowDatatbl, BufferData, -4, "", 4);
                for (int i = 0; i < 7; i++) {
                    Daystbl.get(i).billing = ((double) Utils.converArrtoInt(BufferData, StartIndex + 8 + (6 - i) * 4, Shared.DataNewDataListPacket.endianType)) / 10000 + "";
                    //AddDataRowRecord((i + 1) + " Day(s) ago Billing.", StartIndex + 8 + (6 - i) * 4, ref RowDatatbl, BufferData, typeof(UInt32), -4, "");
                }
                AddDataRowRecord("Previous Week Bill", StartIndex + 36, RowDatatbl, BufferData, -4, "", 4);
                AddDataRowRecord("Previous Billing Period Bill", StartIndex + 40, RowDatatbl, BufferData, -4, "", 4);
                AddDataRowRecord("Total Life Charged Money", StartIndex + 44, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Current Billing Period Charged Money", StartIndex + 48, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Previous Billing Period Charged Money", StartIndex + 52, RowDatatbl, BufferData, -3, "", 4);
                AddDataRowRecord("Money Balance", StartIndex + 56, RowDatatbl, BufferData, -4, "", 2);
            }

            MoneyBalance = (double) ((double) Utils.converArrtoInt(BufferData, StartIndex + 56, Shared.DataNewDataListPacket.endianType)) / (double) 10000;
            Shared.RowDatatbl DRExpectedRemainingActive = new Shared.RowDatatbl();
            DRExpectedRemainingActive.dataElement = "Expected Remaining Active Energy";
//            DRExpectedRemainingActive.value = Utils.converArrtoInt(BufferData, StartIndex + 60, Shared.DataNewDataListPacket.endianType) == 0 ? "0 KWh" : ((double) Utils.converArrtoInt(BufferData, StartIndex + 60, ByteOrder.BIG_ENDIAN)) * Math.pow(10, -3) + " KWh";
            DRExpectedRemainingActive.value = AddDataRowRecord("Expected Remaining Consumption", StartIndex + 60, RowDatatbl, BufferData, -3, "", 4);
            if (DRExpectedRemainingActive.value == "4294967.295 KWh" || DRExpectedRemainingActive.value == "999999.999 KWh" || MoneyBalance <= 0)
                DRExpectedRemainingActive.value = "0";
            RowDatatbl.add(DRExpectedRemainingActive);
            Shared.RowDatatbl DRExpectedRemainingDays = new Shared.RowDatatbl();
            DRExpectedRemainingDays.dataElement = "Expected Remaining Days";
            DRExpectedRemainingDays.value = (Utils.converArrtoShort(BufferData, StartIndex + 64, Shared.DataNewDataListPacket.endianType) & 0xFFFF) == 0 ? "0 Day(s)" : (double) (Utils.converArrtoShort(BufferData, StartIndex + 64, ByteOrder.BIG_ENDIAN) & 0xFFFF) + " Day(s)";
            if (DRExpectedRemainingDays.value == "65535 Day(s)" || DRExpectedRemainingActive.value == "365 Day(s)")
                DRExpectedRemainingDays.value = "NF";
            RowDatatbl.add(DRExpectedRemainingDays);
//            AddDataRowRecord("Charge Number Of Optical Interface", StartIndex + 66, RowDatatbl, BufferData, 0, "", 3);
//            AddDataRowRecord("Charge Number Of GPRS Interface", StartIndex + 68, RowDatatbl, BufferData, 0, "", 3);
            AddDataRowRecord("Charge Number Of RFID Interface", StartIndex + 70, RowDatatbl, BufferData, 0, "", 3);
            //Set out Param
            gprsChargNo = Utils.converArrtoShort(BufferData, StartIndex + 68, Shared.DataNewDataListPacket.endianType) & 0xFFFF;
            RFIDChargeNumber = Utils.converArrtoShort(BufferData, StartIndex + 70) & 0xFFFF;
            //AddDataRowRecord("Charge Number Of Scratch Card Interface", StartIndex + 72, ref RowDatatbl, BufferData, typeof(UInt16), 0, "");
//            AddDataRowRecord("Discharge Number Of Optical Interface", StartIndex + 74, RowDatatbl, BufferData, 0, "", 3);
//            AddDataRowRecord("Discharge Number Of GPRS Interface", StartIndex + 76, RowDatatbl, BufferData, 0, "", 3);
            AddDataRowRecord("Discharge Number Of RFID Interface", StartIndex + 78, RowDatatbl, BufferData, 0, "", 3);
            //AddDataRowRecord("Discharge Number Of Scratch Card Interface", StartIndex + 80, ref RowDatatbl, BufferData, typeof(UInt16), 0, "");
            //Int32 PostPaidMoney = Converter.ToInt32(BufferData, StartIndex + 82);
            //if (PostPaidMoney != 0)
            //    this.MoneyBalance = AddDataRowRecord("Post Paid Money", StartIndex + 82, ref RowDatatbl, BufferData, typeof(Int32), -4, "");
            if (Shared.DataNewDataListPacket.endianType == ByteOrder.BIG_ENDIAN)
                StartIndex += 4; //Post Paid Bill
            StartIndex += 82;
        }
        // Depends on Existence of TRF_USE_MD_KW
        if (obj._TRF_USE_MD_KW) {
            AddDataRowRecord("Previous Active Power MD", StartIndex, RowDatatbl, BufferData, -3, " KW", 4);
            //
            AddDateRecord("Previous Active Power MD Time", StartIndex + 4, RowDatatbl, BufferData);
            AddDataRowRecord("Current Active Power MD", StartIndex + 9, RowDatatbl, BufferData, -3, " KW", 4);
            //
            AddDateRecord("Current Active Power MD Time", StartIndex + 13, RowDatatbl, BufferData);
            StartIndex += 58;
        }
        // Depends on Existence of TRF_USE_MD_KVA
        if (obj._TRF_USE_MD_KVA) {
            AddDataRowRecord("Previous Apparent Power MD", StartIndex, RowDatatbl, BufferData, -3, " KVA", 4);
            //
            AddDateRecord("Previous Apparent Power MD Time", StartIndex + 4, RowDatatbl, BufferData);
            AddDataRowRecord("Current Apparent Power MD", StartIndex + 9, RowDatatbl, BufferData, -3, " KVA", 4);
            //
            AddDateRecord("Current Apparent Power MD Time", StartIndex + 13, RowDatatbl, BufferData);
            StartIndex += 58;
        }
        // Depends on Existence of TRF_USE_MD_A
        if (obj._TRF_USE_MD_A) {
            AddDataRowRecord("Previous Electrical Current MD", StartIndex, RowDatatbl, BufferData, -3, " A", 4);
            //
            AddDateRecord("Previous Electrical Current MD Time", StartIndex + 4, RowDatatbl, BufferData);
            AddDataRowRecord("Current Electrical Current MD", StartIndex + 9, RowDatatbl, BufferData, -3, " A", 4);
            AddDateRecord("Current Electrical Current MD Time", StartIndex + 13, RowDatatbl, BufferData);
        }
        if (Shared.DataNewDataListPacket.endianType == ByteOrder.LITTLE_ENDIAN) // Three Phase
        {
            StartIndex = 345;
//            PostPaidMoney = ((double) Utils.converArrtoInt(BufferData, StartIndex + 56, Shared.DataNewDataListPacket.endianType));
//            if (PostPaidMoney != 0)
//                PostPaidMoney = Convert.ToDouble(AddDataRowRecord("Post Paid
//                Money", StartIndex, RowDatatbl, BufferData, typeof(Int32), -4, ""));
            StartIndex += 4; //Post Paid Bill

            AddDataRowRecord("Friendly Consumption", StartIndex, RowDatatbl, BufferData, -3, " KWh", 4);
            StartIndex += 4;
            for (int i = 0; i < 8/*10*/; i++) {
                Tarifftbl.get(i).consumptionofpreviousInKWh = ((double) Utils.converArrtoInt(BufferData, StartIndex + i * 4, Shared.DataNewDataListPacket.endianType)) / 1000 + "";
            }
        }
    }

    private void AddDateRecord(String DataRowName, int MinStartIndex, ArrayList<Shared.RowDatatbl> RowDatatbl, byte[] BufferData) {
        Shared.RowDatatbl datarow = new Shared.RowDatatbl();
        datarow.dataElement = DataRowName;
        datarow.value = (BufferData[MinStartIndex + 2] & 0xFF) + "/" + (BufferData[MinStartIndex + 3] & 0xFF) + "/" + ((BufferData[MinStartIndex + 4] & 0xFF) + 2000) + " " + (BufferData[MinStartIndex + 1] & 0xFF) + ":" + (BufferData[MinStartIndex] & 0xFF);
        RowDatatbl.add(datarow);
    }
}
