package shoaa.connectionmanager.usbserial;


import shoaa.connectionmanager.usbserial.driver.FtdiSerialDriver;
import shoaa.connectionmanager.usbserial.driver.ProbeTable;
import shoaa.connectionmanager.usbserial.driver.UsbSerialProber;

public class CustomProber {

    public static UsbSerialProber getCustomProber() {
        ProbeTable customTable = new ProbeTable();
        customTable.addProduct(0x1234, 0x0001, FtdiSerialDriver.class); // e.g. device with custom VID+PID
        customTable.addProduct(0x1234, 0x0002, FtdiSerialDriver.class); // e.g. device with custom VID+PID
        return new UsbSerialProber(customTable);
    }

}
