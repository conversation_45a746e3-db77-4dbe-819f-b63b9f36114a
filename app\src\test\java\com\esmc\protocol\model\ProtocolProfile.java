package com.esmc.protocol.model;

import com.esmc.protocol.model.parameter.Security;

import java.util.EnumSet;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/ProtocolProfile.class */
public class ProtocolProfile {
    public static final int STATE_NONE = 0;
    public static final int STATE_HDLC = 1;
    public static final int STATE_AA = 2;
    public static final int STATE_DLMS = 4;
    private String client2Server;
    private String server2Client;
    private long invocationCounterServer;
    private DlmsCosemItem cosemItem;
    private String clientSystemTitle = "0000000000000000";
    private String serverSystemTitle = "";
    private int maxRecvPduSizeClient = 1200;
    private int maxRecvPduSizeServer = 256;
    private long invocationCounterClient = 1;
    private int invokeId = 0;
    private int sequenceNumberSend = 0;
    private int sequenceNumberRecv = 0;
    private int maxInfoLengthTransmit = 256;
    private int maxInfoLengthReceive = 256;
    private int connectState = 0;
    private Security securityInfo = new Security("", "", "30303030303030303030303030303030", "30303030303030303030303030303030");
    private EnumSet<Conformance> conformanceBlock = EnumSet.of(Conformance.GET, Conformance.ACTION, Conformance.SET, Conformance.SELECTIVE_ACCESSS);

    public DlmsCosemItem getCosemItem() {
        return this.cosemItem;
    }

    public void setCosemItem(DlmsCosemItem cosemItem) {
        this.cosemItem = cosemItem;
    }

    public int getInvokeId() {
        return this.invokeId;
    }

    public void setInvokeId(int invokeId) {
        this.invokeId = invokeId;
    }

    public Security getSecurityInfo() {
        return this.securityInfo;
    }

    public void setSecurityInfo(Security securityInfo) {
        this.securityInfo = securityInfo;
    }

    public EnumSet<Conformance> getConformanceBlock() {
        return this.conformanceBlock;
    }

    public void setConformanceBlock(EnumSet<Conformance> conformanceBlock) {
        this.conformanceBlock = conformanceBlock;
    }

    public int getConnectState() {
        return this.connectState;
    }

    public void setConnectState(int connectState) {
        this.connectState = connectState;
    }

    public int getMaxInfoLengthReceive() {
        return this.maxInfoLengthReceive;
    }

    public void setMaxInfoLengthReceive(int maxInfoLengthReceive) {
        this.maxInfoLengthReceive = maxInfoLengthReceive;
    }

    public int getMaxInfoLengthTransmit() {
        return this.maxInfoLengthTransmit;
    }

    public void setMaxInfoLengthTransmit(int maxInfoLengthTransmit) {
        this.maxInfoLengthTransmit = maxInfoLengthTransmit;
    }

    public int getSequenceNumberRecv() {
        return this.sequenceNumberRecv;
    }

    public void setSequenceNumberRecv(int sequenceNumberRecv) {
        this.sequenceNumberRecv = sequenceNumberRecv;
    }

    public int getSequenceNumberSend() {
        return this.sequenceNumberSend;
    }

    public void setSequenceNumberSend(int sequenceNumberSend) {
        this.sequenceNumberSend = sequenceNumberSend;
    }

    public long getInvocationCounterServer() {
        return this.invocationCounterServer;
    }

    public void setInvocationCounterServer(long invocationCounterServer) {
        this.invocationCounterServer = invocationCounterServer;
    }

    public long getInvocationCounterClient() {
        return this.invocationCounterClient;
    }

    public void setInvocationCounterClient(long invocationCounterClient) {
        this.invocationCounterClient = invocationCounterClient;
    }

    public String getServer2Client() {
        return this.server2Client;
    }

    public void setServer2Client(String server2Client) {
        this.server2Client = server2Client;
    }

    public String getClient2Server() {
        return this.client2Server;
    }

    public void setClient2Server(String client2Server) {
        this.client2Server = client2Server;
    }

    public int getMaxRecvPduSizeServer() {
        return this.maxRecvPduSizeServer;
    }

    public void setMaxRecvPduSizeServer(int maxRecvPduSizeServer) {
        this.maxRecvPduSizeServer = maxRecvPduSizeServer;
    }

    public int getMaxRecvPduSizeClient() {
        return this.maxRecvPduSizeClient;
    }

    public void setMaxRecvPduSizeClient(int maxRecvPduSizeClient) {
        this.maxRecvPduSizeClient = maxRecvPduSizeClient;
    }

    public String getServerSystemTitle() {
        return this.serverSystemTitle;
    }

    public void setServerSystemTitle(String serverSystemTitle) {
        this.serverSystemTitle = serverSystemTitle;
    }

    public String getClientSystemTitle() {
        return this.clientSystemTitle;
    }

    public void setClientSystemTitle(String clientSystemTitle) {
        this.clientSystemTitle = clientSystemTitle;
    }
}
