<component name="libraryTable">
  <library name="Gradle: com.google.mlkit:common:17.3.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/635a606af01a7b4166997b8ee7cf90b0/transformed/jetified-common-17.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f8deb6f8390415c8de14f7da6533c1a8/transformed/jetified-common-17.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d5a3a22bbe579836100be69cbf047a3d/transformed/jetified-common-17.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8410573ae7867adeff59d255e0b4aaa9/transformed/jetified-common-17.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b8e577fa9413e12af57b8fa9dc9ce4a6/transformed/jetified-common-17.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/8410573ae7867adeff59d255e0b4aaa9/transformed/jetified-common-17.3.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/8410573ae7867adeff59d255e0b4aaa9/transformed/jetified-common-17.3.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.mlkit/common/17.3.0/4145b70d9d45d67877b42953178aa94a1659733f/common-17.3.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>