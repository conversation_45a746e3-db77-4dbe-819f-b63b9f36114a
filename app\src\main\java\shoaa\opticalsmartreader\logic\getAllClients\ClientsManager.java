package shoaa.opticalsmartreader.logic.getAllClients;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import shoaa.opticalsmartreader.api.APIClient;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.logic.login.LoginManager;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;

public class ClientsManager {
    private static Client[] clients;

    public static void loadAllClients(Activity activity, AppUser loginUser, ApiResult apiResult) {
        try {
            Utils.appDatabase.clientDao().deleteAll();
        } catch (Exception e) {
            e.printStackTrace();
        }
        LoginManager.login(activity, loginUser.getUserName(), loginUser.getPassword(), new ApiResult() {
            @Override
            public void onSuccess() {
                getAllClients(activity, loginUser, apiResult);
            }

            @Override
            public void onFailed(int code, String reason) {
                apiResult.onFailed(code, reason);
            }

        });
    }

    private static void getAllClients(Activity activity, AppUser loginUser, ApiResult apiResult) {
        Call<Client[]> allClients = APIClient.getService().getAllClients(loginUser.getSESSION_ID(), "0");
        allClients.enqueue(new Callback<Client[]>() {
            @Override
            public void onResponse(@NonNull Call<Client[]> call, @NonNull Response<Client[]> response) {
                if (response.isSuccessful()) {
                    if (!Utils.appDatabase.isOpen())
                        Utils.appDatabase = Room.databaseBuilder(activity.getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();

                    if (response.body() == null || response.body().length == 0) {
                        new Thread(() -> {
                            Utils.appDatabase.clientDao().deleteAll();
                            apiResult.onSuccess();
                        }).start();
                    } else {
//                        String testData = "[\n" +
//                                "{\n" +
//                                "\"CDID\":233224,\n" +
//                                "\"CDCreateDate\":\"2022-05-31T00:00:00\",\n" +
//                                "\"CDCreateTime\":\"23:45:01\",\n" +
//                                "\"BrHndsa\":510,\n" +
//                                "\"Mntka\":\"3\",\n" +
//                                "\"Day\":\"11\",\n" +
//                                "\"Main\":\"1\",\n" +
//                                "\"Fary\":\"100\",\n" +
//                                "\"FactoryCode\":6,\n" +
//                                "\"Name\":\"محمد الشبراوى حسن السيد\",\n" +
//                                "\"Address\":\"شارع الانتاج / بجوار العباسه\",\n" +
//                                "\"ActivityName\":\"منزلى\",\n" +
//                                "\"PlaceName\":\"شقـــــة\",\n" +
//                                "\"MeterID\":45011793,\n" +
//                                "\"series\":1,\n" +
//                                "\"MeterTypeName\":\"احادي\",\n" +
//                                "\"MeterModel\":2,\n" +
//                                "\"FirmwareVersion\":null,\n" +
//                                "\"CustomerID\":\"5003876\",\n" +
//                                "\"CardID\":\"10680833041667\",\n" +
//                                "\"CustomerMobile\":null,\n" +
//                                "\"CycleYear\":2022,\n" +
//                                "\"CycleMonth\":7,\n" +
//                                "\"UsID\":null,\n" +
//                                "\"ReadingCount\":null,\n" +
//                                "\"KTCodeSHoaa\":null,\n" +
//                                "\"BranchRevision\":0,\n" +
//                                "\"BranchRevisionReason\":null,\n" +
//                                "\"ShoaaRevision\":0,\n" +
//                                "\"BranchRevisionNotes\":null\n" +
//                                "},\n" +
//                                "\n" +
//                                "{\n" +
//                                "\"CDID\":233224,\n" +
//                                "\"CDCreateDate\":\"2022-05-31T00:00:00\",\n" +
//                                "\"CDCreateTime\":\"23:45:01\",\n" +
//                                "\"BrHndsa\":510,\n" +
//                                "\"Mntka\":\"3\",\n" +
//                                "\"Day\":\"11\",\n" +
//                                "\"Main\":\"1\",\n" +
//                                "\"Fary\":\"100\",\n" +
//                                "\"FactoryCode\":6,\n" +
//                                "\"Name\":\"محمد الشبراوى حسن السيد\",\n" +
//                                "\"Address\":\"شارع الانتاج / بجوار العباسه\",\n" +
//                                "\"ActivityName\":\"منزلى\",\n" +
//                                "\"PlaceName\":\"شقـــــة\",\n" +
//                                "\"MeterID\":45011793,\n" +
//                                "\"series\":1,\n" +
//                                "\"MeterTypeName\":\"احادي\",\n" +
//                                "\"MeterModel\":2,\n" +
//                                "\"FirmwareVersion\":null,\n" +
//                                "\"CustomerID\":\"5003876\",\n" +
//                                "\"CardID\":\"10680833041667\",\n" +
//                                "\"CustomerMobile\":null,\n" +
//                                "\"CycleYear\":2022,\n" +
//                                "\"CycleMonth\":7,\n" +
//                                "\"UsID\":null,\n" +
//                                "\"ReadingCount\":null,\n" +
//                                "\"KTCodeSHoaa\":null,\n" +
//                                "\"BranchRevision\":0,\n" +
//                                "\"BranchRevisionReason\":null,\n" +
//                                "\"ShoaaRevision\":0,\n" +
//                                "\"BranchRevisionNotes\":null\n" +
//                                "},\n" +
//                                "\n" +
//                                "{\n" +
//                                "\"CDID\":233224,\n" +
//                                "\"CDCreateDate\":\"2022-05-31T00:00:00\",\n" +
//                                "\"CDCreateTime\":\"23:45:01\",\n" +
//                                "\"BrHndsa\":510,\n" +
//                                "\"Mntka\":\"3\",\n" +
//                                "\"Day\":\"11\",\n" +
//                                "\"Main\":\"1\",\n" +
//                                "\"Fary\":\"100\",\n" +
//                                "\"FactoryCode\":6,\n" +
//                                "\"Name\":\"محمد الشبراوى حسن السيد\",\n" +
//                                "\"Address\":\"شارع الانتاج / بجوار العباسه\",\n" +
//                                "\"ActivityName\":\"منزلى\",\n" +
//                                "\"PlaceName\":\"شقـــــة\",\n" +
//                                "\"MeterID\":45011793,\n" +
//                                "\"series\":1,\n" +
//                                "\"MeterTypeName\":\"احادي\",\n" +
//                                "\"MeterModel\":2,\n" +
//                                "\"FirmwareVersion\":null,\n" +
//                                "\"CustomerID\":\"5003876\",\n" +
//                                "\"CardID\":\"10680833041667\",\n" +
//                                "\"CustomerMobile\":null,\n" +
//                                "\"CycleYear\":2022,\n" +
//                                "\"CycleMonth\":7,\n" +
//                                "\"UsID\":null,\n" +
//                                "\"ReadingCount\":null,\n" +
//                                "\"KTCodeSHoaa\":null,\n" +
//                                "\"BranchRevision\":0,\n" +
//                                "\"BranchRevisionReason\":null,\n" +
//                                "\"ShoaaRevision\":0,\n" +
//                                "\"BranchRevisionNotes\":null\n" +
//                                "}\n" +
//                                "]\n";
//                        clients = new Gson().fromJson(testData, Client[].class);

                        clients = response.body();
                        new Thread(() -> {
                            try {
                                Utils.appDatabase.clientDao().deleteAll();
                                List<Client> clientList = new ArrayList<>();
                                for (Client client : clients) {
                                    if (!clientList.contains(client)) {
                                        clientList.add(client);
                                    }
                                }
//                                Collections.addAll(clientList, clients);
                                clientList.removeIf(client -> {
                                    if (client.getReadingCount() == 0 || client.series == 1002) {
                                        return false;
                                    } else {
                                        return client.getKTCodeSHoaa() != 1 &&
                                                client.getKTCodeSHoaa() != 2 &&
                                                client.getKTCodeSHoaa() != 5 &&
                                                client.getKTCodeSHoaa() != 6 &&
                                                client.getKTCodeSHoaa() != 8 &&
                                                client.getKTCodeSHoaa() != 9 &&
                                                client.getKTCodeSHoaa() != 10 &&
                                                client.getKTCodeSHoaa() != 11;
                                    }
                                });

                                Utils.appDatabase.clientDao().deleteAll();
                                clients = new Client[clientList.size()];
                                clients = clientList.toArray(clients);
                                Utils.appDatabase.clientDao().insertAll(clients);
                                apiResult.onSuccess();
                            } catch (Exception e) {
                                apiResult.onFailed(1, "فشل في تهيئة بيانات المشتركين" + "\n" + e.getMessage());
                            }
                        }).start();
                    }
                } else {
                    apiResult.onFailed(2, "فشل في الاتصال بالسيرفر");
                }
            }

            @Override
            public void onFailure(@NonNull Call<Client[]> call, @NonNull Throwable t) {
                apiResult.onFailed(3, t.getMessage());
            }
        });
    }

    public static List<Client> getLocalClients(Activity activity) {
        return Utils.appDatabase.clientDao().getAll();
    }

    public static List<Client> getLocalClients(Activity activity, int handasa) {
        return Utils.appDatabase.clientDao().getAllHandasa(handasa);
    }

    public static List<Client> getLocalClients(Activity activity, int handasa, String manteka) {
        return Utils.appDatabase.clientDao().getAllHandasaManteka(handasa, manteka);
    }

    public static List<Client> getLocalClients(Activity activity, int handasa, String manteka, String yawmeya) {
        return Utils.appDatabase.clientDao().getAllHandasaMantekaYomeya(handasa, manteka, yawmeya);
    }

    public static Client findClientByBarcode(Activity activity, int barcode, int factoryCode) {
        if (!Utils.appDatabase.isOpen())
            Utils.appDatabase = Room.databaseBuilder(activity.getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();

        Utils.appDatabase.clientDao().find(barcode, factoryCode);
        return null;
    }
}
