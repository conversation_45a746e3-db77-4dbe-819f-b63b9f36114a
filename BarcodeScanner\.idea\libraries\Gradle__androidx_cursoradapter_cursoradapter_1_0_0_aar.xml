<component name="libraryTable">
  <library name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/da176cf0dfd302ed4e348091aa2b4a2a/transformed/cursoradapter-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/e928f23ebcef2914114c38b0cb5e3d0e/transformed/cursoradapter-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b08f4aa18a4024729b73cf7259db73a1/transformed/cursoradapter-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/70b4cade5a590b82e1c90cca845984a2/transformed/cursoradapter-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ee15037bbaf2b36d8f90e3cccaf4c896/transformed/cursoradapter-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/70b4cade5a590b82e1c90cca845984a2/transformed/cursoradapter-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/70b4cade5a590b82e1c90cca845984a2/transformed/cursoradapter-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.cursoradapter/cursoradapter/1.0.0/1e323083b41c31fd4d45510dfce50614963c3c6c/cursoradapter-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>