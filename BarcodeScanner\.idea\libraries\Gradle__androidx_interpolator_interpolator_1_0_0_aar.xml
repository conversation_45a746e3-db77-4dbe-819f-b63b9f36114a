<component name="libraryTable">
  <library name="Gradle: androidx.interpolator:interpolator:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/723361ee1fc6175c4001d79a2bb94645/transformed/interpolator-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/57835f8a1bdbba17cd681bf04e006137/transformed/interpolator-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/43eb96098286c4c2921da02530f28694/transformed/interpolator-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a383a47d990a60b9e9c9bd6b511db92e/transformed/interpolator-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/fdb0acaff5da1172005a1500d1549b4c/transformed/interpolator-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/a383a47d990a60b9e9c9bd6b511db92e/transformed/interpolator-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a383a47d990a60b9e9c9bd6b511db92e/transformed/interpolator-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.interpolator/interpolator/1.0.0/fefd5e3cbc479b6b4a9532d05688a1e659e8d3d2/interpolator-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>