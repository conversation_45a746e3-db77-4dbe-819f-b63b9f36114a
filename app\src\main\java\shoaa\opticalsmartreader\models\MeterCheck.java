package shoaa.opticalsmartreader.models;

import java.util.Date;

public class MeterCheck {
    public CycleDate cycleDate;
    public int factoryCode = 0;
    public long coverMeterId = 0;
    public long originalOpticalMeterId = 0;
    public String opticalCustomerId = "";
    public String cardID = "";
    public int sideCover = 0;
    public int topCover = 0;
    public Date syncDate = new Date(0);

    @Override
    public String toString() {
        return "MeterCheck{" +
                "cycleDate=" + cycleDate +
                ", factoryCode=" + factoryCode +
                ", coverMeterId=" + coverMeterId +
                ", originalOpticalMeterId=" + originalOpticalMeterId +
                ", opticalCustomerId='" + opticalCustomerId + '\'' +
                ", cardID='" + cardID + '\'' +
                ", sideCover=" + sideCover +
                ", topCover=" + topCover +
                ", syncDate=" + syncDate +
                '}';
    }
}
