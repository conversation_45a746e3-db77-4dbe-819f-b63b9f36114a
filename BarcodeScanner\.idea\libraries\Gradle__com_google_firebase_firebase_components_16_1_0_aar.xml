<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-components:16.1.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/537b84a1f8f41e7819ae865311269c9c/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7fae4d9a9de4dfda110eaf38c8fa29b2/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/99d2480f1dfdc97a873f6eebea76f70a/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c305584bc07231fbe4e37546d689f420/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/90ac2d8c0dd8e5f754b70d3573ba1c88/transformed/jetified-firebase-components-16.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/c305584bc07231fbe4e37546d689f420/transformed/jetified-firebase-components-16.1.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c305584bc07231fbe4e37546d689f420/transformed/jetified-firebase-components-16.1.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-components/16.1.0/3a9c1cc3126d1c3fa181be1692de99be7cfe3b83/firebase-components-16.1.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-components/16.1.0/5f2661eee0882325baf3b7ed9626b0dcbd7cbbaf/firebase-components-16.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>