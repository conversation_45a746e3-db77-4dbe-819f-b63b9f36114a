package shoaa.globalreader;


import android.annotation.SuppressLint;
import android.os.Build;

import androidx.annotation.RequiresApi;

import com.google.gson.Gson;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by Islam Darwish
 */

public class ShoaaProtocolHelper {
    private static final Pattern HEXADECIMAL_PATTERN = Pattern.compile("\\p{XDigit}+");

    public ShoaaProtocolHelper() {
    }

    private static Map<String, String> ParseFrame(String frame) {
        HashMap<String, String> table = new HashMap<>();

        try {
            StringBuilder output = new StringBuilder();

            for (int i = 0; i < frame.length(); i += 2) {
                String str = frame.substring(i, i + 2);
                if (!str.startsWith("0")) {
                    output.append((char) Long.parseLong(str, 16));
                }
            }

            String val = output.toString().replace("()", "");
            String result;
            String[] res;
            int i;
            String[] val2;
            if (val.contains("W1T1")) {
                val2 = val.split("W1T1");
                if (val2.length > 0) {
                    result = val2[1];
                    res = result.split("\\)");
                    table = new HashMap<>();

                    for (i = 0; i < res.length - 1; ++i) {
                        table.put(res[i].split("\\(")[0], res[i].split("\\(")[1]);
                    }
                }
            } else {
                val2 = val.split("!D");
                result = val2[0];
                res = result.split("\\)");
                table = new HashMap<>();

                for (i = 0; i < res.length - 1; ++i) {
                    table.put(res[i].split("\\(")[0], res[i].split("\\(")[1]);
                }
            }

            return table;
        } catch (Exception var8) {
            return table;
        }
    }

    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }

        return data;
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private static String GetDate(int d) {
        String HC = String.format("{0:x8}", d);

        String LastChargeDateOnCard;
        try {
            LastChargeDateOnCard = LocalDate.parse("" + (2000 + Long.parseLong(HC.substring(0, 2))) + "/" + HC.substring(2, 2) + "/" + HC.substring(4, 2), DateTimeFormatter.ofPattern("yyyy/MM/dd")).toString();
        } catch (Exception var4) {
            LastChargeDateOnCard = LocalDate.MIN.toString();
        }

        return LastChargeDateOnCard;
    }

    private static Date GetDate(String d) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            String year = d.substring(0, 4);
            String month = d.substring(4, 6);
            String day = d.substring(6);
            if (Integer.parseInt(year) > 2100
                    || (Integer.parseInt(month) > 12 && Integer.parseInt(month) < 1)
                    || (Integer.parseInt(day) > 31 && Integer.parseInt(day) < 1)) {
                year = d.substring(0, 2);
                month = d.substring(2, 4);
                day = d.substring(4, 6);
                year = String.valueOf(Integer.parseInt(year) + 2000);
                if (Integer.parseInt(year) > 2100) {
                    year = "1970";
                    month = "01";
                    day = "01";
                }
            }
            String dtStart = year + "-" + month + "-" + day;
            return format.parse(dtStart);
        } catch (Exception var6) {
            try {
                return format.parse("1970-01-01");
            } catch (ParseException e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    private static boolean isHexadecimal(String input) {
        Matcher matcher = HEXADECIMAL_PATTERN.matcher(input);
        return matcher.matches();
    }

    private static Map<String, String> Parse2015Frame(String frame) {
        byte[] x = hexStringToByteArray(frame);
        String z;
        z = new String(x, StandardCharsets.UTF_8);
        HashMap<String, String> map = new HashMap<>();
        String[] data;
        data = z.split(new String(new byte[]{0x0a}, StandardCharsets.UTF_8));
        try {
            for (String s : data) {
                try {
                    s = s.replace(new String(new byte[]{0x0d}, StandardCharsets.UTF_8), "");
                    if (s.split("[()]").length == 2) {
                        try {
                            map.put(s.split("[()]")[0], s.split("[()]")[1]);
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception ignore) {
                }
            }
        } catch (Exception ignore) {
        }
        return map;
    }

    @SuppressLint("DefaultLocale")
    public String handle(String json) {
        Response responseData = new Response();
        try {
            Request requestData = new Gson().fromJson(json, Request.class);
            String service = requestData.service.split("_")[0];
            String MeterVersion = requestData.service.split("_")[1];
            int CurrDateDay = 1;
            int CurrDateMonth = 1;
            int CurrDateYear = 1970;
            int CurrDateHour = 2;
            int CurrDateMinute = 0;
            int CurrDateSeconds = 0;
            int LastChargeDay = 1;
            int LastChargeMonth = 1;
            int LastChargeYear = 1970;
            String tmpStr = "";

            String OriginalFrame = requestData.pdu.toUpperCase();
            if ((OriginalFrame.contains("024D5F534E28") || OriginalFrame.contains("15024D5F534E28") || OriginalFrame.contains("024D5F494428")) && OriginalFrame.contains("0D0A210D0A0344")) {
                MeterVersion = "2008";
            }

            String frameString;
            ParseMeterData parse;
            METER_READ_DATA meterdata;
            MeterData meterDataObj;
            double remainPiasters;
            double remainLE;
            if (MeterVersion.equals("2019")) {

                if ("handshake".equals(service)) {
                    responseData.service = "handshake";
                } else {
                    frameString = requestData.pdu;
                    frameString = frameString.substring(40);
                    parse = new ParseMeterData();
                    meterdata = new METER_READ_DATA();
                    parse.ReadCollectionCardRecord(hexStringToByteArray(frameString), meterdata);
                    meterDataObj = new MeterData();
                    meterDataObj.meterModel = MeterVersion;
                    meterDataObj.meterPhase = meterdata.MeterPhase;
                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID2 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID2;
                    }

                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID3 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID3;
                    }

                    meterDataObj.meterID = Long.toString(meterdata.MeterSerialNumber);
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.remainKW = meterdata.RemainKW + " kWh";
                    remainPiasters = (double) meterdata.RemainPiasters / 100.0D;
                    remainLE = (double) meterdata.RemainPound + remainPiasters;
                    meterDataObj.remainMoney = remainLE + " LE";
                    meterDataObj.totalConsum = meterdata.TotalConsum + " kWh";
                    if (meterdata.MeterInstallYears > 2000) {
                        meterDataObj.installingDate = String.format("%04d", meterdata.MeterInstallYears) + "-" + String.format("%02d", meterdata.MeterInstallMonthes) + "-" + String.format("%02d", meterdata.MeterInstallDays) + " " + String.format("%02d", meterdata.MeterInstallHours) + ":" + String.format("%02d", meterdata.MeterInstallMinute) + ":" + String.format("%02d", meterdata.MeterInstallSeconds);
                    } else {
                        meterDataObj.installingDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.technicalCode = meterdata.ControlCardID1 + "";
                    if (meterdata.coveropen == 0 && meterdata.Battery == 0 && meterdata.RelayOpen == 0
                            && meterdata.ReverseOpen == 0 && meterdata.EarthOpen == 0) {
                        meterDataObj.meterStatus = " 0";
                    } else {
                        meterDataObj.meterStatus = "1";
                    }

                    meterDataObj.relayStatus = meterdata.RelayOpen;
                    meterDataObj.batteryStatus = meterdata.Battery;
                    meterDataObj.Deon = (float) meterdata.DebitsInMeterPnd;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInReverse;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInEarth;
                    meterDataObj.lastTechnicalCode = meterdata.ControlCardID1 + ";" + meterdata.ControlCardID2 + ";" + meterdata.ControlCardID3;
                    meterDataObj.clearAllTampers = meterDataObj.installingDate;
                    meterDataObj.clearTampers = null;
                    meterDataObj.setTimeAndDate = "0";
                    meterDataObj.controlCardState = "";
                    if (meterdata.ControlCardStateAfter1 != meterdata.ControlCardStateBefore1) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (1)" + meterdata.ControlCardID1 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter2 != meterdata.ControlCardStateBefore2) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (2)" + meterdata.ControlCardID2 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter3 != meterdata.ControlCardStateBefore3) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (3)" + meterdata.ControlCardID3 + " , ";
                    }
                    if (meterDataObj.controlCardState.endsWith(" , ")) {
                        meterDataObj.controlCardState = meterDataObj.controlCardState.substring(0, meterDataObj.controlCardState.length() - 3);
                    }
                    if (meterDataObj.controlCardState.isEmpty()) {
                        meterDataObj.controlCardState = "0";
                    }
                    meterDataObj.fWVersion = "0";
                    meterDataObj.maxDemandPH1 = meterdata.MaxLoadAM / 10.0F;
                    meterDataObj.maxLoadKW = meterdata.MaxLoadKW / 100.0F;
//                    meterDataObj.maximumDemandAndDate1 = String.valueOf(meterdata.MaxLoadKW / 100.0F);
                    if (meterdata.MaxLoadYears > 2000 && meterdata.MaxLoadYears < 2050) {
                        meterDataObj.maximumDemandDate = String.format("%04d", meterdata.MaxLoadYears) + "-" + String.format("%02d", meterdata.MaxLoadMonthes) + "-" + String.format("%02d", meterdata.MaxLoadDays) + " " + String.format("%02d", meterdata.MaxLoadHours) + ":" + String.format("%02d", meterdata.MaxLoadMinute) + ":00";
                    } else {
                        meterDataObj.maximumDemandDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.instanteneousVolt = "0";
                    meterDataObj.powerFactor = (float) meterdata.PowerFactor / 1000.0F;
                    meterDataObj.lastYearPowerFactor = (float) meterdata.PowerFactor_1 / 1000.0F;
                    meterDataObj.totalConsumptionKvh = meterdata.ConsumptionKVARPos;
                    meterDataObj.currentMonthConsumption = meterdata.CurrConsum;
                    meterDataObj.currentMonthConsumptionMoney = meterdata.CurrentMonthMoneyConsum / 1000.0F;
                    meterDataObj.consm_kvar1 = meterdata.consm_kvar1;
                    meterDataObj.consm_kvar2 = meterdata.consm_kvar2;
                    meterDataObj.consm_kvar3 = meterdata.consm_kvar3;
                    meterDataObj.consm_kvar4 = meterdata.consm_kvar4;
                    meterDataObj.consm_kvar5 = meterdata.consm_kvar5;
                    meterDataObj.consm_kvar6 = meterdata.consm_kvar6;
                    meterDataObj.consm_kvar7 = meterdata.consm_kvar7;
                    meterDataObj.consm_kvar8 = meterdata.consm_kvar8;
                    meterDataObj.consm_kvar9 = meterdata.consm_kvar9;
                    meterDataObj.consm_kvar10 = meterdata.consm_kvar10;
                    meterDataObj.consm_kvar11 = meterdata.consm_kvar11;
                    meterDataObj.consm_kvar12 = meterdata.consm_kvar12;

                    try {
                        CurrDateSeconds = meterdata.RSeconds;
                        CurrDateMinute = meterdata.RMinutes;
                        CurrDateHour = meterdata.RHours;
                        CurrDateDay = meterdata.Rdays;
                        CurrDateMonth = meterdata.Rmonthes;
                        CurrDateYear = meterdata.Ryears;
                        if (CurrDateYear < 2000 || CurrDateYear > 2050) {
                            CurrDateDay = 1;
                            CurrDateMonth = 1;
                            CurrDateYear = 1970;
                            CurrDateHour = 2;
                            CurrDateMinute = 0;
                            CurrDateSeconds = 0;
                        }
                        meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                    } catch (Exception var75) {
                        meterDataObj.date = "1970-01-01 02:00:00";
                    }

                    meterDataObj.chargeCount = meterdata.chargeCount;
                    meterDataObj.SumOfAllChargesPND = (long) meterdata.SumOfAllChargesPND;
                    meterDataObj.SumOfAllChargesPSTR = meterdata.SumOfAllChargesPSTR;
                    meterDataObj.TotalBalance = String.valueOf((float) meterdata.SumOfAllChargesPND + (float) meterDataObj.SumOfAllChargesPSTR / 100.0F);

                    try {
                        LastChargeDay = meterdata.LastChargeDateDay;
                        LastChargeMonth = meterdata.LastChargeDateMonth;
                        LastChargeYear = meterdata.LastChargeDateYear;
                        meterDataObj.LastChargeDate = String.format("%04d", LastChargeYear) + "-" + String.format("%02d", LastChargeMonth) + "-" + String.format("%02d", LastChargeDay);
                    } catch (Exception var74) {
                        meterDataObj.LastChargeDate = "1970-01-01";
                    }

                    meterDataObj.ActivityType = "0";
                    meterDataObj.SliceNo = meterdata.SliceNo;
                    meterDataObj.tarrifID = String.valueOf(meterDataObj.SliceNo);
                    if (CurrDateYear != 1970) {
                        if (CurrDateMonth < 7) {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear - 1) + "-07-01" + " 00:00:00";
                        } else {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear) + "-07-01" + " 00:00:00";
                        }
                    } else {
                        meterDataObj.tarrifActivationDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.CurrentMonthlyConsum = meterdata.CurrConsum;
                    meterDataObj.Moneyconsm01 = (float) meterdata.Moneyconsm01 / 1000.0F;
                    meterDataObj.Moneyconsm02 = (float) meterdata.Moneyconsm02 / 1000.0F;
                    meterDataObj.Moneyconsm03 = (float) meterdata.Moneyconsm03 / 1000.0F;
                    meterDataObj.Moneyconsm04 = (float) meterdata.Moneyconsm04 / 1000.0F;
                    meterDataObj.Moneyconsm05 = (float) meterdata.Moneyconsm05 / 1000.0F;
                    meterDataObj.Moneyconsm06 = (float) meterdata.Moneyconsm06 / 1000.0F;
                    meterDataObj.Moneyconsm07 = (float) meterdata.Moneyconsm07 / 1000.0F;
                    meterDataObj.Moneyconsm08 = (float) meterdata.Moneyconsm08 / 1000.0F;
                    meterDataObj.Moneyconsm09 = (float) meterdata.Moneyconsm09 / 1000.0F;
                    meterDataObj.Moneyconsm10 = (float) meterdata.Moneyconsm10 / 1000.0F;
                    meterDataObj.Moneyconsm11 = (float) meterdata.Moneyconsm11 / 1000.0F;
                    meterDataObj.Moneyconsm12 = (float) meterdata.Moneyconsm12 / 1000.0F;
                    meterDataObj.consm1 = meterdata.consm1;
                    meterDataObj.consm2 = meterdata.consm2;
                    meterDataObj.consm3 = meterdata.consm3;
                    meterDataObj.consm4 = meterdata.consm4;
                    meterDataObj.consm5 = meterdata.consm5;
                    meterDataObj.consm6 = meterdata.consm6;
                    meterDataObj.consm7 = meterdata.consm7;
                    meterDataObj.consm8 = meterdata.consm8;
                    meterDataObj.consm9 = meterdata.consm9;
                    meterDataObj.consm10 = meterdata.consm10;
                    meterDataObj.consm11 = meterdata.consm11;
                    meterDataObj.consm12 = meterdata.consm12;
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.clearAllTampers = "1";
                    meterDataObj.clearTampers = "1";

                    try {
                        meterDataObj.clearAllTampers = String.valueOf(meterdata.ControlCardID1);
                        meterDataObj.clearTampers = String.valueOf(meterdata.ControlCardID1);
                    } catch (Exception ignored) {
                    }

                    tmpStr = tmpStr + "M" + meterDataObj.meterStatus;
                    if (meterdata.RelayOpen > 0) {
                        tmpStr = tmpStr + "-R1";
                    } else {
                        tmpStr = tmpStr + "-R0";
                    }

                    if (meterdata.Battery > 0) {
                        tmpStr = tmpStr + "-B1";
                    } else {
                        tmpStr = tmpStr + "-B0";
                    }

                    if (meterdata.coveropen > 0) {
                        tmpStr = tmpStr + "-S1";
                    } else {
                        tmpStr = tmpStr + "-S0";
                    }

                    if (meterdata.EarthOpen > 0) {
                        tmpStr = tmpStr + "-E1";
                    } else {
                        tmpStr = tmpStr + "-E0";
                    }

                    if (meterdata.ReverseOpen > 0) {
                        tmpStr = tmpStr + "-V1";
                    } else {
                        tmpStr = tmpStr + "-V0";
                    }

                    tmpStr = tmpStr + "-T0";
                    meterDataObj.tempresList = tmpStr;
                    meterDataObj.maxim_demand_month_1 = (float) meterdata.MaximumDemand_1 / 100.0F;
                    meterDataObj.maxim_demand_month_2 = (float) meterdata.MaximumDemand_2 / 100.0F;
                    meterDataObj.maxim_demand_month_3 = (float) meterdata.MaximumDemand_3 / 100.0F;
                    meterDataObj.maxim_demand_month_4 = (float) meterdata.MaximumDemand_4 / 100.0F;
                    meterDataObj.maxim_demand_month_5 = (float) meterdata.MaximumDemand_5 / 100.0F;
                    meterDataObj.maxim_demand_month_6 = (float) meterdata.MaximumDemand_6 / 100.0F;
                    meterDataObj.maxim_demand_month_7 = (float) meterdata.MaximumDemand_7 / 100.0F;
                    meterDataObj.maxim_demand_month_8 = (float) meterdata.MaximumDemand_8 / 100.0F;
                    meterDataObj.maxim_demand_month_9 = (float) meterdata.MaximumDemand_9 / 100.0F;
                    meterDataObj.maxim_demand_month_10 = (float) meterdata.MaximumDemand_10 / 100.0F;
                    meterDataObj.maxim_demand_month_11 = (float) meterdata.MaximumDemand_11 / 100.0F;
                    meterDataObj.maxim_demand_month_12 = (float) meterdata.MaximumDemand_12 / 100.0F;
                    int MaxDemYear = CurrDateYear;
                    int MaxDemMonth;
                    if (CurrDateMonth == 1) {
                        MaxDemMonth = 12;
                        MaxDemYear = CurrDateYear - 1;
                    } else {
                        MaxDemMonth = CurrDateMonth - 1;
                    }

                    meterDataObj.maxim_demand_month_date_1 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_2 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_3 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_4 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_5 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_6 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_7 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_8 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_9 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_10 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_11 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    if (MaxDemMonth == 1) {
                        MaxDemMonth = 12;
                        --MaxDemYear;
                    } else {
                        --MaxDemMonth;
                    }

                    meterDataObj.maxim_demand_month_date_12 = String.format("%04d", MaxDemYear) + "-" + String.format("%02d", MaxDemMonth) + "-01 00:00:00";
                    responseData.value = new Gson().toJson(meterDataObj);
                }
                responseData.result = "success";
            } else if (MeterVersion.equals("2016")) {
                if ("handshake".equals(service)) {
                    responseData.service = "handshake";
                } else {
                    responseData.service = "get";
                    frameString = requestData.pdu;
                    parse = new ParseMeterData();
                    meterdata = new METER_READ_DATA();
                    parse.ReadCollectionCardRecord(hexStringToByteArray(frameString), meterdata);
                    meterDataObj = new MeterData();
                    meterDataObj.meterModel = MeterVersion;
                    meterDataObj.meterPhase = meterdata.MeterPhase;
                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID2 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID2;
                    }

                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID3 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID3;
                    }

                    meterDataObj.meterID = Long.toString(meterdata.MeterSerialNumber);
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.remainKW = meterdata.RemainKW + " kWh";
                    remainPiasters = (double) meterdata.RemainPiasters / 100.0D;
                    remainLE = (double) meterdata.RemainPound + remainPiasters;
                    meterDataObj.remainMoney = remainLE + " LE";
                    meterDataObj.totalConsum = meterdata.TotalConsum + " kWh";
                    if (meterdata.MeterInstallYears > 2000) {
                        meterDataObj.installingDate = String.format("%04d", meterdata.MeterInstallYears) + "-" + String.format("%02d", meterdata.MeterInstallMonthes) + "-" + String.format("%02d", meterdata.MeterInstallDays) + " " + String.format("%02d", meterdata.MeterInstallHours) + ":" + String.format("%02d", meterdata.MeterInstallMinute) + ":" + String.format("%02d", meterdata.MeterInstallSeconds);
                    } else {
                        meterDataObj.installingDate = "1970-01-01 02:00:00";
                    }


                    meterDataObj.technicalCode = meterdata.ControlCardID1 + "";
                    if (meterdata.coveropen == 0 && meterdata.Battery == 0 && meterdata.RelayOpen == 0
                            && meterdata.ReverseOpen == 0 && meterdata.EarthOpen == 0) {
                        meterDataObj.meterStatus = " 0";
                    } else {
                        meterDataObj.meterStatus = "1";
                    }

                    meterDataObj.relayStatus = meterdata.RelayOpen;
                    meterDataObj.batteryStatus = meterdata.Battery;
                    meterDataObj.Deon = (float) meterdata.DebitsInMeterPnd + (float) meterdata.DebitsInMeterPstr / 100.0F;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInReverse;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInEarth;
                    meterDataObj.lastTechnicalCode = meterdata.ControlCardID1 + ";" + meterdata.ControlCardID2 + ";" + meterdata.ControlCardID3;
                    meterDataObj.clearAllTampers = meterDataObj.installingDate;
                    meterDataObj.clearTampers = null;
                    meterDataObj.setTimeAndDate = "0";
                    meterDataObj.controlCardState = "";
                    if (meterdata.ControlCardStateAfter1 != meterdata.ControlCardStateBefore1) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (1)" + meterdata.ControlCardID1 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter2 != meterdata.ControlCardStateBefore2) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (2)" + meterdata.ControlCardID2 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter3 != meterdata.ControlCardStateBefore3) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (3)" + meterdata.ControlCardID3 + " , ";
                    }
                    if (meterDataObj.controlCardState.endsWith(" , ")) {
                        meterDataObj.controlCardState = meterDataObj.controlCardState.substring(0, meterDataObj.controlCardState.length() - 3);
                    }
                    if (meterDataObj.controlCardState.isEmpty()) {
                        meterDataObj.controlCardState = "0";
                    }
                    meterDataObj.fWVersion = "0";
                    meterDataObj.instanteneousVolt = "0";

                    meterDataObj.maxDemandPH1 = meterdata.MaxLoadAM / 10.0F;
                    meterDataObj.maxLoadKW = meterdata.MaxLoadKW / 100.0F;
//                    meterDataObj.maximumDemandAndDate1 = String.valueOf(meterdata.MaxLoadKW / 100.0F);
                    if (meterdata.MaxLoadYears > 2000 && meterdata.MaxLoadYears < 2050) {
                        meterDataObj.maximumDemandDate = String.format("%04d", meterdata.MaxLoadYears) + "-" + String.format("%02d", meterdata.MaxLoadMonthes) + "-" + String.format("%02d", meterdata.MaxLoadDays) + " " + String.format("%02d", meterdata.MaxLoadHours) + ":" + String.format("%02d", meterdata.MaxLoadMinute) + ":00";
                    } else {
                        meterDataObj.maximumDemandDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.powerFactor = (float) meterdata.PowerFactor / 1000.0F;
                    meterDataObj.lastYearPowerFactor = (float) meterdata.PowerFactor_1 / 1000.0F;
                    meterDataObj.totalConsumptionKvh = meterdata.ConsumptionKVARPos;
                    meterDataObj.currentMonthConsumption = meterdata.CurrConsum;
                    meterDataObj.currentMonthConsumptionMoney = meterdata.CurrentMonthMoneyConsum / 1000.0F;
                    meterDataObj.consm_kvar1 = meterdata.consm_kvar1;
                    meterDataObj.consm_kvar2 = meterdata.consm_kvar2;
                    meterDataObj.consm_kvar3 = meterdata.consm_kvar3;
                    meterDataObj.consm_kvar4 = meterdata.consm_kvar4;
                    meterDataObj.consm_kvar5 = meterdata.consm_kvar5;
                    meterDataObj.consm_kvar6 = meterdata.consm_kvar6;
                    meterDataObj.consm_kvar7 = meterdata.consm_kvar7;
                    meterDataObj.consm_kvar8 = meterdata.consm_kvar8;
                    meterDataObj.consm_kvar9 = meterdata.consm_kvar9;
                    meterDataObj.consm_kvar10 = meterdata.consm_kvar10;
                    meterDataObj.consm_kvar11 = meterdata.consm_kvar11;
                    meterDataObj.consm_kvar12 = meterdata.consm_kvar12;

                    try {
                        CurrDateSeconds = meterdata.RSeconds;
                        CurrDateMinute = meterdata.RMinutes;
                        CurrDateHour = meterdata.RHours;
                        CurrDateDay = meterdata.Rdays;
                        CurrDateMonth = meterdata.Rmonthes;
                        CurrDateYear = meterdata.Ryears;
                        if (CurrDateYear < 2000 || CurrDateYear > 2050) {
                            CurrDateDay = 1;
                            CurrDateMonth = 1;
                            CurrDateYear = 1970;
                            CurrDateHour = 2;
                            CurrDateMinute = 0;
                            CurrDateSeconds = 0;
                        }

                        meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                    } catch (Exception var76) {
                        meterDataObj.date = "1970-01-01 02:00:00";
                    }

                    meterDataObj.chargeCount = meterdata.chargeCount;
                    meterDataObj.SumOfAllChargesPND = (long) meterdata.SumOfAllChargesPND;
                    meterDataObj.SumOfAllChargesPSTR = meterdata.SumOfAllChargesPSTR;
                    meterDataObj.TotalBalance = String.valueOf((float) meterdata.SumOfAllChargesPND + (float) meterDataObj.SumOfAllChargesPSTR / 100.0F);

                    try {
                        LastChargeDay = meterdata.LastChargeDateDay;
                        LastChargeMonth = meterdata.LastChargeDateMonth;
                        LastChargeYear = meterdata.LastChargeDateYear;
                        meterDataObj.LastChargeDate = String.format("%04d", LastChargeYear) + "-" + String.format("%02d", LastChargeMonth) + "-" + String.format("%02d", LastChargeDay);
                    } catch (Exception var72) {
                        meterDataObj.LastChargeDate = "1970-01-01";
                    }
                    meterDataObj.ActivityType = "0";
                    meterDataObj.SliceNo = meterdata.SliceNo;
                    meterDataObj.tarrifID = String.valueOf(meterDataObj.SliceNo);
                    if (CurrDateYear != 1970) {
                        if (CurrDateMonth < 7) {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear - 1) + "-07-01" + " 00:00:00";
                        } else {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear) + "-07-01" + " 00:00:00";
                        }
                    } else {
                        meterDataObj.tarrifActivationDate = "1970-01-01 02:00:00";

                    }

                    meterDataObj.CurrentMonthlyConsum = meterdata.CurrConsum;
                    meterDataObj.Moneyconsm01 = (float) meterdata.Moneyconsm01;
                    meterDataObj.Moneyconsm02 = (float) meterdata.Moneyconsm02;
                    meterDataObj.Moneyconsm03 = (float) meterdata.Moneyconsm03;
                    meterDataObj.Moneyconsm04 = (float) meterdata.Moneyconsm04;
                    meterDataObj.Moneyconsm05 = (float) meterdata.Moneyconsm05;
                    meterDataObj.Moneyconsm06 = (float) meterdata.Moneyconsm06;
                    meterDataObj.Moneyconsm07 = (float) meterdata.Moneyconsm07;
                    meterDataObj.Moneyconsm08 = (float) meterdata.Moneyconsm08;
                    meterDataObj.Moneyconsm09 = (float) meterdata.Moneyconsm09;
                    meterDataObj.Moneyconsm10 = (float) meterdata.Moneyconsm10;
                    meterDataObj.Moneyconsm11 = (float) meterdata.Moneyconsm11;
                    meterDataObj.Moneyconsm12 = (float) meterdata.Moneyconsm12;
                    meterDataObj.consm1 = meterdata.consm1;
                    meterDataObj.consm2 = meterdata.consm2;
                    meterDataObj.consm3 = meterdata.consm3;
                    meterDataObj.consm4 = meterdata.consm4;
                    meterDataObj.consm5 = meterdata.consm5;
                    meterDataObj.consm6 = meterdata.consm6;
                    meterDataObj.consm7 = meterdata.consm7;
                    meterDataObj.consm8 = meterdata.consm8;
                    meterDataObj.consm9 = meterdata.consm9;
                    meterDataObj.consm10 = meterdata.consm10;
                    meterDataObj.consm11 = meterdata.consm11;
                    meterDataObj.consm12 = meterdata.consm12;
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.clearAllTampers = "1";
                    meterDataObj.clearTampers = "1";

                    try {
                        meterDataObj.clearAllTampers = String.valueOf(meterdata.ControlCardID1);
                        meterDataObj.clearTampers = String.valueOf(meterdata.ControlCardID1);
                    } catch (Exception ignored) {
                    }

                    tmpStr = tmpStr + "M" + meterDataObj.meterStatus;
                    if (meterdata.RelayOpen > 0) {
                        tmpStr = tmpStr + "-R1";
                    } else {
                        tmpStr = tmpStr + "-R0";
                    }

                    if (meterdata.Battery > 0) {
                        tmpStr = tmpStr + "-B1";
                    } else {
                        tmpStr = tmpStr + "-B0";
                    }

                    if (meterdata.coveropen > 0) {
                        tmpStr = tmpStr + "-S1";
                    } else {
                        tmpStr = tmpStr + "-S0";
                    }

                    if (meterdata.EarthOpen > 0) {
                        tmpStr = tmpStr + "-E1";
                    } else {
                        tmpStr = tmpStr + "-E0";
                    }

                    if (meterdata.ReverseOpen > 0) {
                        tmpStr = tmpStr + "-V1";
                    } else {
                        tmpStr = tmpStr + "-V0";
                    }

                    tmpStr = tmpStr + "-T0";
                    meterDataObj.tempresList = tmpStr;
                    responseData.value = new Gson().toJson(meterDataObj);
                }
                responseData.result = "success";
            } else if (MeterVersion.equals("2013")) {
                if ("handshake".equals(service)) {
                    responseData.service = "handshake";
                } else {
                    responseData.service = "get";
                    frameString = requestData.pdu;
                    parse = new ParseMeterData();
                    meterdata = new METER_READ_DATA();
                    parse.ReadCollectionCardRecord(hexStringToByteArray(frameString), meterdata);
                    meterDataObj = new MeterData();
                    meterDataObj.meterModel = MeterVersion;
                    meterDataObj.meterPhase = meterdata.MeterPhase;
                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID2 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID2;
                    }

                    if (meterdata.ControlCardID1 == 0 && meterdata.ControlCardID3 > 0) {
                        meterdata.ControlCardID1 = meterdata.ControlCardID3;
                    }

                    meterDataObj.meterID = Long.toString(meterdata.MeterSerialNumber);
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.remainKW = meterdata.RemainKW + " kWh";
                    remainPiasters = (double) meterdata.RemainPiasters / 100.0D;
                    remainLE = (double) meterdata.RemainPound + remainPiasters;
                    meterDataObj.remainMoney = remainLE + " LE";
                    meterDataObj.totalConsum = meterdata.TotalConsum + " kWh";
                    if (meterdata.MeterInstallYears > 2000) {
                        meterDataObj.installingDate = String.format("%04d", meterdata.MeterInstallYears) + "-" + String.format("%02d", meterdata.MeterInstallMonthes) + "-" + String.format("%02d", meterdata.MeterInstallDays) + " " + String.format("%02d", meterdata.MeterInstallHours) + ":" + String.format("%02d", meterdata.MeterInstallMinute) + ":" + String.format("%02d", meterdata.MeterInstallSeconds);
                    } else {
                        meterDataObj.installingDate = "1970-01-01 02:00:00";
                    }


                    meterDataObj.technicalCode = meterdata.ControlCardID1 + "";
                    if (meterdata.coveropen == 0 && meterdata.Battery == 0 && meterdata.RelayOpen == 0
                            && meterdata.ReverseOpen == 0 && meterdata.EarthOpen == 0) {
                        meterDataObj.meterStatus = " 0";
                    } else {
                        meterDataObj.meterStatus = "1";
                    }

                    meterDataObj.relayStatus = meterdata.RelayOpen;
                    meterDataObj.batteryStatus = meterdata.Battery;
                    meterDataObj.Deon = (float) meterdata.DebitsInMeterPnd;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInReverse;
                    meterDataObj.unblanceInKW = meterdata.ConsumptionInEarth;
                    meterDataObj.lastTechnicalCode = meterdata.ControlCardID1 + ";" + meterdata.ControlCardID2 + ";" + meterdata.ControlCardID3;
                    meterDataObj.clearAllTampers = meterDataObj.installingDate;
                    meterDataObj.clearTampers = null;
                    meterDataObj.setTimeAndDate = "0";
                    meterDataObj.controlCardState = "";
                    if (meterdata.ControlCardStateAfter1 != meterdata.ControlCardStateBefore1) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (1)" + meterdata.ControlCardID1 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter2 != meterdata.ControlCardStateBefore2) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (2)" + meterdata.ControlCardID2 + " , ";
                    }

                    if (meterdata.ControlCardStateAfter3 != meterdata.ControlCardStateBefore3) {
                        meterDataObj.controlCardState += "تلاعب بالكارت رقم (3)" + meterdata.ControlCardID3 + " , ";
                    }
                    if (meterDataObj.controlCardState.endsWith(" , ")) {
                        meterDataObj.controlCardState = meterDataObj.controlCardState.substring(0, meterDataObj.controlCardState.length() - 3);
                    }

                    if (meterDataObj.controlCardState.isEmpty()) {
                        meterDataObj.controlCardState = "0";
                    }
                    meterDataObj.fWVersion = "0";
                    meterDataObj.instanteneousVolt = "0";

                    meterDataObj.maxDemandPH1 = meterdata.MaxLoadAM / 10.0F;
                    meterDataObj.maxLoadKW = meterdata.MaxLoadKW / 100.0F;
//                    meterDataObj.maximumDemandAndDate1 = String.valueOf(meterdata.MaxLoadKW / 100.0F);
                    if (meterdata.MaxLoadYears > 2000 && meterdata.MaxLoadYears < 2050) {
                        meterDataObj.maximumDemandDate = String.format("%04d", meterdata.MaxLoadYears) + "-" + String.format("%02d", meterdata.MaxLoadMonthes) + "-" + String.format("%02d", meterdata.MaxLoadDays) + " " + String.format("%02d", meterdata.MaxLoadHours) + ":" + String.format("%02d", meterdata.MaxLoadMinute) + ":00";
                    } else {
                        meterDataObj.maximumDemandDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.powerFactor = (float) meterdata.PowerFactor / 1000.0F;
                    meterDataObj.lastYearPowerFactor = (float) meterdata.PowerFactor_1 / 1000.0F;
                    meterDataObj.totalConsumptionKvh = meterdata.ConsumptionKVARPos;
                    meterDataObj.currentMonthConsumption = meterdata.CurrConsum;
                    meterDataObj.currentMonthConsumptionMoney = meterdata.CurrentMonthMoneyConsum / 1000.0F;
                    meterDataObj.consm_kvar1 = meterdata.consm_kvar1;
                    meterDataObj.consm_kvar2 = meterdata.consm_kvar2;
                    meterDataObj.consm_kvar3 = meterdata.consm_kvar3;
                    meterDataObj.consm_kvar4 = meterdata.consm_kvar4;
                    meterDataObj.consm_kvar5 = meterdata.consm_kvar5;
                    meterDataObj.consm_kvar6 = meterdata.consm_kvar6;
                    meterDataObj.consm_kvar7 = meterdata.consm_kvar7;
                    meterDataObj.consm_kvar8 = meterdata.consm_kvar8;
                    meterDataObj.consm_kvar9 = meterdata.consm_kvar9;
                    meterDataObj.consm_kvar10 = meterdata.consm_kvar10;
                    meterDataObj.consm_kvar11 = meterdata.consm_kvar11;
                    meterDataObj.consm_kvar12 = meterdata.consm_kvar12;

                    try {
                        CurrDateSeconds = meterdata.RSeconds;
                        CurrDateMinute = meterdata.RMinutes;
                        CurrDateHour = meterdata.RHours;
                        CurrDateDay = meterdata.Rdays;
                        CurrDateMonth = meterdata.Rmonthes;
                        CurrDateYear = meterdata.Ryears;
                        if (CurrDateYear < 2000 || CurrDateYear > 2050) {
                            CurrDateDay = 1;
                            CurrDateMonth = 1;
                            CurrDateYear = 1970;
                            CurrDateHour = 2;
                            CurrDateMinute = 0;
                            CurrDateSeconds = 0;
                        }

                        meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                    } catch (Exception var77) {
                        meterDataObj.date = "1970-01-01 02:00:00";
                    }

                    meterDataObj.chargeCount = meterdata.chargeCount;
                    meterDataObj.SumOfAllChargesPND = (long) meterdata.SumOfAllChargesPND;
                    meterDataObj.SumOfAllChargesPSTR = meterdata.SumOfAllChargesPSTR;
                    meterDataObj.TotalBalance = String.valueOf((float) meterdata.SumOfAllChargesPND + (float) meterDataObj.SumOfAllChargesPSTR / 100.0F);

                    try {
                        LastChargeDay = meterdata.LastChargeDateDay;
                        LastChargeMonth = meterdata.LastChargeDateMonth;
                        LastChargeYear = meterdata.LastChargeDateYear;
                        meterDataObj.LastChargeDate = String.format("%04d", LastChargeYear) + "-" + String.format("%02d", LastChargeMonth) + "-" + String.format("%02d", LastChargeDay);
                    } catch (Exception var72) {
                        meterDataObj.LastChargeDate = "1970-01-01";
                    }

                    meterDataObj.ActivityType = "0";
                    meterDataObj.SliceNo = meterdata.SliceNo;
                    meterDataObj.tarrifID = String.valueOf(meterDataObj.SliceNo);
                    if (CurrDateYear != 1970) {
                        if (CurrDateMonth < 7) {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear - 1) + "-07-01" + " 00:00:00";
                        } else {
                            meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear) + "-07-01" + " 00:00:00";
                        }
                    } else {
                        meterDataObj.tarrifActivationDate = "1970-01-01 02:00:00";
                    }

                    meterDataObj.CurrentMonthlyConsum = meterdata.CurrConsum;
                    meterDataObj.Moneyconsm01 = (float) meterdata.Moneyconsm01;
                    meterDataObj.Moneyconsm02 = (float) meterdata.Moneyconsm02;
                    meterDataObj.Moneyconsm03 = (float) meterdata.Moneyconsm03;
                    meterDataObj.Moneyconsm04 = (float) meterdata.Moneyconsm04;
                    meterDataObj.Moneyconsm05 = (float) meterdata.Moneyconsm05;
                    meterDataObj.Moneyconsm06 = (float) meterdata.Moneyconsm06;
                    meterDataObj.Moneyconsm07 = (float) meterdata.Moneyconsm07;
                    meterDataObj.Moneyconsm08 = (float) meterdata.Moneyconsm08;
                    meterDataObj.Moneyconsm09 = (float) meterdata.Moneyconsm09;
                    meterDataObj.Moneyconsm10 = (float) meterdata.Moneyconsm10;
                    meterDataObj.Moneyconsm11 = (float) meterdata.Moneyconsm11;
                    meterDataObj.Moneyconsm12 = (float) meterdata.Moneyconsm12;
                    meterDataObj.consm1 = meterdata.consm1;
                    meterDataObj.consm2 = meterdata.consm2;
                    meterDataObj.consm3 = meterdata.consm3;
                    meterDataObj.consm4 = meterdata.consm4;
                    meterDataObj.consm5 = meterdata.consm5;
                    meterDataObj.consm6 = meterdata.consm6;
                    meterDataObj.consm7 = meterdata.consm7;
                    meterDataObj.consm8 = meterdata.consm8;
                    meterDataObj.consm9 = meterdata.consm9;
                    meterDataObj.consm10 = meterdata.consm10;
                    meterDataObj.consm11 = meterdata.consm11;
                    meterDataObj.consm12 = meterdata.consm12;
                    meterDataObj.customerID = meterdata.CardId;
                    meterDataObj.clearAllTampers = "1";
                    meterDataObj.clearTampers = "1";

                    try {
                        meterDataObj.clearAllTampers = String.valueOf(meterdata.ControlCardID1);
                        meterDataObj.clearTampers = String.valueOf(meterdata.ControlCardID1);
                    } catch (Exception ignored) {
                    }

                    tmpStr = tmpStr + "M" + meterDataObj.meterStatus;
                    if (meterdata.RelayOpen > 0) {
                        tmpStr = tmpStr + "-R1";
                    } else {
                        tmpStr = tmpStr + "-R0";
                    }

                    if (meterdata.Battery > 0) {
                        tmpStr = tmpStr + "-B1";
                    } else {
                        tmpStr = tmpStr + "-B0";
                    }

                    if (meterdata.coveropen > 0) {
                        tmpStr = tmpStr + "-S1";
                    } else {
                        tmpStr = tmpStr + "-S0";
                    }

                    if (meterdata.EarthOpen > 0) {
                        tmpStr = tmpStr + "-E1";
                    } else {
                        tmpStr = tmpStr + "-E0";
                    }

                    if (meterdata.ReverseOpen > 0) {
                        tmpStr = tmpStr + "-V1";
                    } else {
                        tmpStr = tmpStr + "-V0";
                    }

                    tmpStr = tmpStr + "-T0";
                    meterDataObj.tempresList = tmpStr;
                    responseData.value = new Gson().toJson(meterDataObj);
                }
                responseData.result = "success";
            } else if ("handshake".equals(service)) {
                responseData.service = "handshake";
                responseData.result = "success";
            } else {
                responseData.service = "get";
                frameString = requestData.pdu;
                Map<String, String> map = Parse2015Frame(frameString);
                meterDataObj = new MeterData();
                meterDataObj.meterModel = MeterVersion;
                meterDataObj.tarrifID = "0";
                meterDataObj.SliceNo = 0;
                meterDataObj.meterID = "0";
                for (String key : map.keySet()) {
                    String value = map.get(key);
                    if (key.contains("M_SN") && meterDataObj.meterID.equals("0")) {
                        meterDataObj.meterID = String.valueOf(value);
                    }
                }
                if (meterDataObj.meterID.endsWith("Jk"))
                    meterDataObj.meterID = "0";
//                map.forEach((key, value) -> {
//                    if (key.contains("M_SN") && meterDataObj.meterID.equals("0")) {
//                        meterDataObj.meterID = String.valueOf(value);
//                    }
//                });
                if (map.containsKey("M_ID"))
                    meterDataObj.customerID = map.get("M_ID");

                if (map.containsKey("R_kWh")) {
                    String RemainWithKW = map.get("R_kWh").split(" ")[0];
                    int RemainInKW = 0;
                    try {
                        RemainInKW = Integer.parseInt(RemainWithKW);
                    } catch (Exception ignore) {
                    }
                    meterDataObj.remainKW = String.valueOf(RemainInKW);
                }
                meterDataObj.installingDate = "0";
                meterDataObj.technicalCode = "0";
                meterDataObj.setTimeAndDate = "0";

                String Tarif;
                try {
                    Tarif = map.get("Date");
                    if (Tarif != null)
                        Tarif = map.get("Date").split(" ")[0];
                    if (Tarif.length() != 11) {
                        throw new Exception();
                    }
                    try {
                        CurrDateHour = Integer.parseInt(Tarif.substring(0, 2));
                    } catch (Exception e) {
                    }
                    try {
                        CurrDateDay = Integer.parseInt(Tarif.substring(3, 5));
                    } catch (Exception e) {
                    }
                    try {
                        CurrDateMonth = Integer.parseInt(Tarif.substring(6, 8));
                    } catch (Exception e) {
                    }
                    try {
                        CurrDateYear = Integer.parseInt(Tarif.substring(9, 11)) + 2000;
                    } catch (Exception e) {
                    }
                    meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                } catch (Exception var59) {
                    meterDataObj.date = "1970-01-01 02:00:00";
                }

                try {
                    Tarif = map.get("Time") != null ? map.get("Time").split(" ")[0].replace(":", "") : "000000";
                    if (Tarif.length() != 6) {
                        throw new Exception();
                    }
                    try {
                        CurrDateHour = Integer.parseInt(Tarif.substring(0, 2));
                    } catch (Exception ignored) {
                    }
                    try {
                        CurrDateMinute = Integer.parseInt(Tarif.substring(2, 4));
                    } catch (Exception ignored) {
                    }
                    try {
                        CurrDateSeconds = Integer.parseInt(Tarif.substring(4, 6));
                    } catch (Exception ignored) {
                    }
                    meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                } catch (Exception var58) {
                    try {
                        if (map.get("Time").split(":").length == 3) {
                            try {
                                CurrDateHour = Integer.parseInt(map.get("Time").split(":")[0]);
                            } catch (Exception ignored) {
                            }
                            try {
                                CurrDateMinute = Integer.parseInt(map.get("Time").split(":")[1]);
                            } catch (Exception ignored) {
                            }
                            try {
                                CurrDateSeconds = Integer.parseInt(map.get("Time").split(":")[2]);
                            } catch (Exception ignored) {
                            }
                        }
                    } catch (Exception ignore) {
                        CurrDateHour = 2;
                        CurrDateMinute = 0;
                        CurrDateSeconds = 0;
                    }
                    meterDataObj.date = String.format("%04d", CurrDateYear) + "-" + String.format("%02d", CurrDateMonth) + "-" + String.format("%02d", CurrDateDay) + " " + String.format("%02d", CurrDateHour) + ":" + String.format("%02d", CurrDateMinute) + ":" + String.format("%02d", CurrDateSeconds);
                }

                if (map.containsKey("R_LE")) {
                    String RemainWithLE = map.get("R_LE").split(" ")[0];
                    int RemainInLE = 0;
                    try {
                        RemainInLE = Integer.parseInt(RemainWithLE);
                    } catch (Exception ignore) {
                    }
                    meterDataObj.remainMoney = String.valueOf(RemainInLE);
                }
                if (map.containsKey("A_kWh") || map.containsKey("T_kWh")) {
                    String totalConsum = map.get("A_kWh") != null ? map.get("A_kWh").split(" ")[0] : map.get("T_kWh").split(" ")[0];
                    Long totalConsumParseToDecimal = 0L;

                    try {
                        totalConsumParseToDecimal = Long.parseLong(totalConsum);
                    } catch (NumberFormatException var68) {
                        try {
                            totalConsumParseToDecimal = Long.parseLong(totalConsum, 16);
                        } catch (Exception ignored) {
                        }
                    }

                    meterDataObj.totalConsum = String.valueOf(totalConsumParseToDecimal);
                }
                try {
                    Tarif = map.get("T_Crdt").split(" ")[0];
                    long lTotalb;
                    if (Tarif.matches("-?[0-9a-fA-F]+")) {
                        lTotalb = Long.parseLong(Tarif, 16);
                        Tarif = String.valueOf((float) lTotalb / 1000.0F);
                    }

                    meterDataObj.TotalBalance = Tarif;
                } catch (Exception var66) {
                    meterDataObj.TotalBalance = "0";
                }

                try {
                    Tarif = map.get("Ch_Cnt") != null ? map.get("Ch_Cnt") : "";
                    boolean valid = false;
                    for (int i = 0; i < Tarif.length(); i++) {
                        if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                            valid = true;
                        }
                    }
                    if (valid)
                        meterDataObj.chargeCount = Integer.parseInt(Tarif);
                    else
                        meterDataObj.chargeCount = 0;
                } catch (Exception var65) {
                    meterDataObj.chargeCount = 0;
                }

                int currentMonthConsumptionVal;
                try {
                    Tarif = map.get("M_kWh") != null ? map.get("M_kWh").split(" ")[0] : "";
                    boolean valid = false;
                    for (int i = 0; i < Tarif.length(); i++) {
                        if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                            valid = true;
                        }
                    }
                    if (valid) {
                        currentMonthConsumptionVal = Integer.parseInt(Tarif, 16);
                    } else {
                        currentMonthConsumptionVal = 0;
                    }
                    meterDataObj.CurrentMonthlyConsum = currentMonthConsumptionVal;
                    meterDataObj.currentMonthConsumption = currentMonthConsumptionVal;
                } catch (Exception var64) {
                    meterDataObj.CurrentMonthlyConsum = 0;
                    meterDataObj.currentMonthConsumption = 0;
                }

                try {
                    Tarif = map.get("M_kWh") != null ? map.get("M_kWh").split(" ")[0] : "";
                    boolean valid = false;
                    for (int i = 0; i < Tarif.length(); i++) {
                        if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                            valid = true;
                        }
                    }
                    if (valid)
                        currentMonthConsumptionVal = Integer.parseInt(Tarif, 16);
                    else
                        currentMonthConsumptionVal = 0;
                    meterDataObj.CurrentMonthlyConsum = currentMonthConsumptionVal;
                    meterDataObj.currentMonthConsumption = currentMonthConsumptionVal;
                } catch (Exception var63) {
                    meterDataObj.CurrentMonthlyConsum = 0;
                    meterDataObj.currentMonthConsumption = 0;
                }

                try {
                    SimpleDateFormat format = new SimpleDateFormat("yyMMdd");
                    if (Integer.parseInt(map.get("L_Ch_D").substring(0, 6)) == 0) {
                        throw new Exception();
                    }
                    Date date = format.parse(map.get("L_Ch_D").substring(0, 6));
                    if (date != null) {
                        format = new SimpleDateFormat("yyyy-MM-dd");
                        meterDataObj.LastChargeDate = format.format(date);
                    } else {
                        meterDataObj.LastChargeDate = "1970-01-01";
                    }
                } catch (Exception var63) {
                    meterDataObj.LastChargeDate = "1970-01-01";
                }

                if (CurrDateYear != 1970) {
                    if (CurrDateMonth < 7) {
                        meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear - 1) + "-07-01" + " 00:00:00";
                    } else {
                        meterDataObj.tarrifActivationDate = String.format("%04d", CurrDateYear) + "-07-01" + " 00:00:00";
                    }
                } else {
                    meterDataObj.tarrifActivationDate = meterDataObj.LastChargeDate;
                }
                try {
                    int readCardCount = Integer.parseInt(map.get("Rd_Cnt"));
                } catch (Exception ignored) {
                }

                try {
                    if (map.get("T_Crdt").split(" ")[0].split("\\.").length > 1) {
                        Tarif = map.get("T_Crdt") != null ? map.get("T_Crdt").split(" ")[0] : "";
                        boolean valid = false;
                        for (int i = 0; i < Tarif.length(); i++) {
                            if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                                valid = true;
                            }
                        }
                        if (valid) {
                            meterDataObj.SumOfAllChargesPND = Long.parseLong(Tarif.split("\\.")[0]) / 1000L;
                            meterDataObj.SumOfAllChargesPSTR = Integer.parseInt(Tarif.split("\\.")[1]);
                        } else {
                            meterDataObj.SumOfAllChargesPND = 0L;
                            meterDataObj.SumOfAllChargesPSTR = 0;
                        }
                    } else {
                        Tarif = map.get("T_Crdt") != null ? map.get("T_Crdt").split(" ")[0] : "";
                        boolean valid = false;
                        for (int i = 0; i < Tarif.length(); i++) {
                            if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                                valid = true;
                            }
                        }
                        if (valid)
                            meterDataObj.SumOfAllChargesPND = Long.parseLong(Tarif, 16) / 1000L;
                        else
                            meterDataObj.SumOfAllChargesPND = 0L;
                    }
                } catch (Exception var61) {
                    meterDataObj.SumOfAllChargesPND = 0L;
                }

                try {
                    Tarif = map.get("Deon") != null ? map.get("Deon").split(" ")[0] : "";
                    boolean valid = false;
                    for (int i = 0; i < Tarif.length(); i++) {
                        if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                            valid = true;
                        }
                    }

                    if (valid)
                        meterDataObj.Deon = (float) Long.parseLong(Tarif, 16);
                    else
                        meterDataObj.Deon = 0.0F;
                } catch (Exception var60) {
                    meterDataObj.Deon = 0.0F;
                }

                try {
                    Tarif = map.get("Tarif") != null ? map.get("Tarif") : null;
                    boolean valid = false;
                    for (int i = 0; i < Tarif.length(); i++) {
                        if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                            valid = true;
                        }
                    }
                    if (valid) {
                        Integer tarfinInt = Integer.parseInt(Tarif, 16);
                        meterDataObj.tarrifID = String.valueOf(tarfinInt);
                    } else {
                        meterDataObj.tarrifID = "1";
                    }
                } catch (Exception var57) {
                    try {
                        Tarif = map.get("Tarif") != null ? map.get("Tarif") : null;
                        boolean valid = false;
                        for (int i = 0; i < Tarif.length(); i++) {
                            if (Tarif.charAt(i) != '0' && Tarif.charAt(i) != 'F') {
                                valid = true;
                            }
                        }
                        if (valid) {
                            Integer tarfinInt = Integer.parseInt(Tarif.split(",")[0].split("\\.")[0], 16);
                            meterDataObj.tarrifID = String.valueOf(tarfinInt);
                        } else {
                            meterDataObj.tarrifID = "1";
                        }
                    } catch (Exception e) {
                        meterDataObj.tarrifID = "1";
                    }
                }

                try {
                    meterDataObj.SliceNo = Integer.parseInt(meterDataObj.tarrifID);
                } catch (Exception ignore) {
                }
                boolean isCoverOpened = false;
                boolean isBatteryFault = false;
                boolean isRelayOpened = false;
                boolean isEarthManipulation = false;
                boolean isReverseManipulation = false;
//                if (map.containsKey("Cover")) {
//                    String Cover = map.get("Cover") != null ? map.get("Cover").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < Cover.length(); i++) {
//                        if (Cover.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            isCoverOpened = true;
//                            break;
//                        }
//                    }
//                }
//
//                if (map.containsKey("Earth")) {
//                    String Earth = map.get("Earth") != null ? map.get("Earth").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < Earth.length(); i++) {
//                        if (Earth.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            isEarthManipulation = true;
//                            break;
//                        }
//                    }
//                }
//                if (map.containsKey("Relay")) {
//                    String Relay = map.get("Relay") != null ? map.get("Relay").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < Relay.length(); i++) {
//                        if (Relay.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            isRelayOpened = true;
//                            break;
//                        }
//                    }
//                }
//
//                if (map.containsKey("L_Batt")) {
//                    String BatteryFault = map.get("L_Batt") != null ? map.get("L_Batt").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < BatteryFault.length(); i++) {
//                        if (BatteryFault.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            isBatteryFault = true;
//                            break;
//                        }
//                    }
//                }
//
//                if (map.containsKey("O_Load")) {
//                    String O_Load = map.get("O_Load") != null ? map.get("O_Load").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < O_Load.length(); i++) {
//                        if (O_Load.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            break;
//                        }
//                    }
//                }
//                if (map.containsKey("Rever")) {
//                    String O_Rever = map.get("Rever") != null ? map.get("Rever").split("-")[0].replaceAll("[^0-9]", "") : "";
//
//                    for (int i = 0; i < O_Rever.length(); i++) {
//                        if (O_Rever.charAt(i) != '0') {
//                            meterDataObj.meterStatus = "1";
//                            isReverseManipulation = true;
//                            break;
//                        }
//                    }
//                }
                meterDataObj.instanteneousVolt = "0";
                if (meterDataObj.meterStatus == null || meterDataObj.meterStatus.trim().isEmpty()) {
                    meterDataObj.meterStatus = "0";
                }

                try {
                    String Kvarh = map.get("Kvarh") != null ? map.get("Kvarh").split(" ")[0] : "";
                    float KvarhLong = (float) Long.parseLong(Kvarh) / 1000.0F;
                    meterDataObj.totalConsumptionKvh = (int) KvarhLong;
                } catch (Exception var56) {
                    meterDataObj.totalConsumptionKvh = 0;
                }

                float MD_AF;
                String MD_A;
                try {
                    MD_AF = map.get("MD_A") != null ? Float.parseFloat(map.get("MD_A")) : 0.0F;
                    meterDataObj.maxDemandPH1 = MD_AF;
                } catch (Exception var55) {
                    try {
                        MD_A = map.get("MD_A") != null ? map.get("MD_A").split(" ")[0] : "";
                        MD_AF = Float.parseFloat(MD_A);
                        meterDataObj.maxDemandPH1 = MD_AF;
                    } catch (Exception var54) {
                        meterDataObj.maxDemandPH1 = 0.0F;
                    }
                }

                try {
                    MD_AF = map.get("PF_A") != null ? Float.parseFloat(map.get("PF_A")) : 0.0F;
                    meterDataObj.powerFactor = MD_AF;
                } catch (Exception var51) {
                    meterDataObj.powerFactor = 0.0F;
                }

                if (map.get("Irms") != null) {
                    try {
                        MD_AF = map.get("Irms") != null ? Float.parseFloat(map.get("Irms").replace("A", "").split(" ")[0]) : 0.0F;
                        meterDataObj.maxDemandPH1 = MD_AF;
                    } catch (Exception var50) {
                        try {
                            MD_A = map.get("Irms") != null ? map.get("Irms").replace("A", "").split(" ")[0] : "0.0";
                            MD_AF = Float.parseFloat(MD_A);
                            meterDataObj.maxDemandPH1 = MD_AF;
                        } catch (Exception var49) {
                            meterDataObj.maxDemandPH1 = 0.0F;
                        }
                    }

                    try {
                        MD_AF = map.get("Irms") != null ? Float.parseFloat(map.get("Irms").replace("A", "").split(" ")[1]) : 0.0F;
                        meterDataObj.maxDemandPH2 = MD_AF;
                    } catch (Exception var48) {
                        try {
                            MD_A = map.get("Irms") != null ? map.get("Irms").replace("A", "").split(" ")[1] : "0.0";
                            MD_AF = Float.parseFloat(MD_A);
                            meterDataObj.maxDemandPH2 = MD_AF;
                        } catch (Exception var47) {
                            meterDataObj.maxDemandPH2 = 0.0F;
                        }
                    }

                    try {
                        MD_AF = map.get("Irms") != null ? Float.parseFloat(map.get("Irms").replace("A", "").split(" ")[2]) : 0.0F;
                        meterDataObj.maxDemandPH3 = MD_AF;
                    } catch (Exception var46) {
                        try {
                            MD_A = map.get("Irms") != null ? map.get("Irms").replace("A", "").split(" ")[2] : "0.0";
                            MD_AF = Float.parseFloat(MD_A);
                            meterDataObj.maxDemandPH3 = MD_AF;
                        } catch (Exception var45) {
                            meterDataObj.maxDemandPH3 = 0.0F;
                        }
                    }
                } else if (map.get("L_Irms") != null) {
                    try {
                        MD_AF = map.get("L_Irms") != null ? Float.parseFloat(map.get("L_Irms").replace("A", "").split(" ")[0]) : 0.0F;
                        meterDataObj.maxDemandPH1 = MD_AF;
                    } catch (Exception var50) {
                        try {
                            MD_A = map.get("L_Irms") != null ? map.get("L_Irms").replace("A", "").split(" ")[0] : "0.0";
                            MD_AF = Float.parseFloat(MD_A);
                            meterDataObj.maxDemandPH1 = MD_AF;
                        } catch (Exception var49) {
                            meterDataObj.maxDemandPH1 = 0.0F;
                        }
                    }
                }

                if (map.get("Vrms") != null) {
                    if (map.get("Vrms").split(" ").length > 2) {
                        meterDataObj.instanteneousVolt = map.get("Vrms") != null ? map.get("Vrms").split(" ")[0] : "000V";
                        meterDataObj.instanteneousVolt2 = map.get("Vrms") != null ? map.get("Vrms").split(" ")[1] : "000V";
                        meterDataObj.instanteneousVolt3 = map.get("Vrms") != null ? map.get("Vrms").split(" ")[2] : "000V";
                        meterDataObj.meterPhase = 1;
                    } else {
                        meterDataObj.instanteneousVolt = map.get("Vrms");
                        meterDataObj.instanteneousVolt2 = "0";
                        meterDataObj.instanteneousVolt3 = "0";
                        meterDataObj.meterPhase = 2;
                    }
                } else {
                    meterDataObj.meterPhase = 2;
                }

                meterDataObj.clearAllTampers = "1";
                meterDataObj.clearTampers = "1";
                meterDataObj.lastTechnicalCode = "1;1;1";
                tmpStr = tmpStr + "M" + meterDataObj.meterStatus;
                if (isRelayOpened) {
                    tmpStr = tmpStr + "-R1";
                } else {
                    tmpStr = tmpStr + "-R0";
                }

                if (isBatteryFault) {
                    tmpStr = tmpStr + "-B1";
                } else {
                    tmpStr = tmpStr + "-B0";
                }

                if (isCoverOpened) {
                    tmpStr = tmpStr + "-S1";
                } else {
                    tmpStr = tmpStr + "-S0";
                }

                if (isEarthManipulation) {
                    tmpStr = tmpStr + "-E1";
                } else {
                    tmpStr = tmpStr + "-E0";
                }

                if (isReverseManipulation) {
                    tmpStr = tmpStr + "-V1";
                } else {
                    tmpStr = tmpStr + "-V0";
                }

                tmpStr = tmpStr + "-T0";
                meterDataObj.tempresList = tmpStr;
                meterDataObj.meterModel = MeterVersion;
                responseData.value = new Gson().toJson(meterDataObj);
                responseData.result = "success";
            }

            frameString = new Gson().toJson(responseData);
            return frameString;
        } catch (Exception var78) {
            responseData.pdu = var78.getMessage();
            responseData.result = "error";
            return new Gson().toJson(responseData);
        }
    }
}
