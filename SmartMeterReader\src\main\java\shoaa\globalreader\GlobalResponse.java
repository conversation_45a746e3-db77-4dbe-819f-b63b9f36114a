package shoaa.globalreader;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

import java.util.Locale;

import shoaa.smartmeterreader.ReadingResponse;

/**
 * Created by <PERSON> Darwish
 */

public class GlobalResponse extends ReadingResponse {
    private String meterModel = "0";
    private String ActivityType = "0";
    private String CurrentMonthlyConsum = "0";
    private String LastChargeDate = "0";
    private String Moneyconsm01 = "0";
    private String Moneyconsm02 = "0";
    private String Moneyconsm03 = "0";
    private String Moneyconsm04 = "0";
    private String Moneyconsm05 = "0";
    private String Moneyconsm06 = "0";
    private String Moneyconsm07 = "0";
    private String Moneyconsm08 = "0";
    private String Moneyconsm09 = "0";
    private String Moneyconsm10 = "0";
    private String Moneyconsm11 = "0";
    private String Moneyconsm12 = "0";
    private String SliceNo = "0";
    private String SumOfAllChargesPND = "0";
    private String SumOfAllChargesPSTR = "0";
    private String batteryStatus = "0";
    private String chargeCount = "0";
    private String clearAllTampers = "0";
    private String clearTampers = "0";
    private String consm1 = "0";
    private String consm10 = "0";
    private String consm11 = "0";
    private String consm12 = "0";
    private String consm2 = "0";
    private String consm3 = "0";
    private String consm4 = "0";
    private String consm5 = "0";
    private String consm6 = "0";
    private String consm7 = "0";
    private String consm8 = "0";
    private String consm9 = "0";
    private String consm_kvar1 = "0";
    private String consm_kvar10 = "0";
    private String consm_kvar11 = "0";
    private String consm_kvar12 = "0";
    private String consm_kvar2 = "0";
    private String consm_kvar3 = "0";
    private String consm_kvar4 = "0";
    private String consm_kvar5 = "0";
    private String consm_kvar6 = "0";
    private String consm_kvar7 = "0";
    private String consm_kvar8 = "0";
    private String consm_kvar9 = "0";
    private String controlCardState = "0";
    private String currentMonthConsumption = "0";
    private String currentMonthConsumptionMoney = "0";
    private String customerID = "0";
    private String date = "0";
    private String fWVersion = "0";
    private String installingDate = "0";
    private String maxDemandPH1 = "0";
    private String instanteneousVolt = "0";
    private String lastTechnicalCode = "0";
    private String maximumDemandDate = "0";
    private String maxDemandMonth1 = "0";
    private String maxDemandMonth2 = "0";
    private String maxDemandMonth3 = "0";
    private String maxDemandMonth4 = "0";
    private String maxDemandMonth5 = "0";
    private String maxDemandMonth6 = "0";
    private String maxDemandMonth7 = "0";
    private String maxDemandMonth8 = "0";
    private String maxDemandMonth9 = "0";
    private String maxDemandMonth10 = "0";
    private String maxDemandMonth11 = "0";
    private String maxDemandMonth12 = "0";
    private String maxDemandMonth1Date = "0";
    private String maxDemandMonth2Date = "0";
    private String maxDemandMonth3Date = "0";
    private String maxDemandMonth4Date = "0";
    private String maxDemandMonth5Date = "0";
    private String maxDemandMonth6Date = "0";
    private String maxDemandMonth7Date = "0";
    private String maxDemandMonth8Date = "0";
    private String maxDemandMonth9Date = "0";
    private String maxDemandMonth10Date = "0";
    private String maxDemandMonth11Date = "0";
    private String maxDemandMonth12Date = "0";
    private String meterId = "0";
    private String meterStatus = "0";
    private String powerFactor = "0";
    private String lastYearPowerFactor = "0";
    private String relayStatus = "0";
    private String remainKW = "0";
    private String remainMoney = "0";
    private String reverse = "0";
    private String setTimeAndDate = "0";
    private String tarrifActivationDate = "0";
    private String tarrifID = "0";
    private String technicalCode = "0";
    private String tempresList = "0";
    private String totalConsum = "0";
    private String totalConsumptionKvh = "0";
    private String unblanceInKW = "0";
    private String Deon = "0";
    private String meterType = "0";
    private String maxLoadKW = "0";

    public String getActivityType() {
        return ActivityType;
    }

    public void setActivityType(String activityType) {
        ActivityType = activityType;
    }

    public String getCurrentMonthlyConsum() {
        return CurrentMonthlyConsum;
    }

    public void setCurrentMonthlyConsum(String currentMonthlyConsum) {
        CurrentMonthlyConsum = currentMonthlyConsum;
    }

    public String getLastChargeDate() {
        return LastChargeDate;
    }

    public void setLastChargeDate(String lastChargeDate) {
        LastChargeDate = lastChargeDate;
    }

    public String getMoneyconsm01() {
        return Moneyconsm01;
    }

    public void setMoneyconsm01(String moneyconsm01) {
        Moneyconsm01 = moneyconsm01;
    }

    public String getMoneyconsm02() {
        return Moneyconsm02;
    }

    public void setMoneyconsm02(String moneyconsm02) {
        Moneyconsm02 = moneyconsm02;
    }

    public String getMoneyconsm03() {
        return Moneyconsm03;
    }

    public void setMoneyconsm03(String moneyconsm03) {
        Moneyconsm03 = moneyconsm03;
    }

    public String getMoneyconsm04() {
        return Moneyconsm04;
    }

    public void setMoneyconsm04(String moneyconsm04) {
        Moneyconsm04 = moneyconsm04;
    }

    public String getMoneyconsm05() {
        return Moneyconsm05;
    }

    public void setMoneyconsm05(String moneyconsm05) {
        Moneyconsm05 = moneyconsm05;
    }

    public String getMoneyconsm06() {
        return Moneyconsm06;
    }

    public void setMoneyconsm06(String moneyconsm06) {
        Moneyconsm06 = moneyconsm06;
    }

    public String getMoneyconsm07() {
        return Moneyconsm07;
    }

    public void setMoneyconsm07(String moneyconsm07) {
        Moneyconsm07 = moneyconsm07;
    }

    public String getMoneyconsm08() {
        return Moneyconsm08;
    }

    public void setMoneyconsm08(String moneyconsm08) {
        Moneyconsm08 = moneyconsm08;
    }

    public String getMoneyconsm09() {
        return Moneyconsm09;
    }

    public void setMoneyconsm09(String moneyconsm09) {
        Moneyconsm09 = moneyconsm09;
    }

    public String getMoneyconsm10() {
        return Moneyconsm10;
    }

    public void setMoneyconsm10(String moneyconsm10) {
        Moneyconsm10 = moneyconsm10;
    }

    public String getMaxDemandMonth1Date() {
        return maxDemandMonth1Date;
    }

    public void setMaxDemandMonth1Date(String maxDemandMonth1Date) {
        this.maxDemandMonth1Date = maxDemandMonth1Date;
    }

    public String getMaxDemandMonth2Date() {
        return maxDemandMonth2Date;
    }

    public void setMaxDemandMonth2Date(String maxDemandMonth2Date) {
        this.maxDemandMonth2Date = maxDemandMonth2Date;
    }

    public String getMaxDemandMonth3Date() {
        return maxDemandMonth3Date;
    }

    public void setMaxDemandMonth3Date(String maxDemandMonth3Date) {
        this.maxDemandMonth3Date = maxDemandMonth3Date;
    }

    public String getMaxDemandMonth4Date() {
        return maxDemandMonth4Date;
    }

    public void setMaxDemandMonth4Date(String maxDemandMonth4Date) {
        this.maxDemandMonth4Date = maxDemandMonth4Date;
    }

    public String getMaxDemandMonth5Date() {
        return maxDemandMonth5Date;
    }

    public void setMaxDemandMonth5Date(String maxDemandMonth5Date) {
        this.maxDemandMonth5Date = maxDemandMonth5Date;
    }

    public String getMaxDemandMonth6Date() {
        return maxDemandMonth6Date;
    }

    public void setMaxDemandMonth6Date(String maxDemandMonth6Date) {
        this.maxDemandMonth6Date = maxDemandMonth6Date;
    }

    public String getMaxDemandMonth7Date() {
        return maxDemandMonth7Date;
    }

    public void setMaxDemandMonth7Date(String maxDemandMonth7Date) {
        this.maxDemandMonth7Date = maxDemandMonth7Date;
    }

    public String getMaxDemandMonth8Date() {
        return maxDemandMonth8Date;
    }

    public void setMaxDemandMonth8Date(String maxDemandMonth8Date) {
        this.maxDemandMonth8Date = maxDemandMonth8Date;
    }

    public String getMaxDemandMonth9Date() {
        return maxDemandMonth9Date;
    }

    public void setMaxDemandMonth9Date(String maxDemandMonth9Date) {
        this.maxDemandMonth9Date = maxDemandMonth9Date;
    }

    public String getMaxDemandMonth10Date() {
        return maxDemandMonth10Date;
    }

    public void setMaxDemandMonth10Date(String maxDemandMonth10Date) {
        this.maxDemandMonth10Date = maxDemandMonth10Date;
    }

    public String getMaxDemandMonth11Date() {
        return maxDemandMonth11Date;
    }

    public void setMaxDemandMonth11Date(String maxDemandMonth11Date) {
        this.maxDemandMonth11Date = maxDemandMonth11Date;
    }

    public String getMaxDemandMonth12Date() {
        return maxDemandMonth12Date;
    }

    public void setMaxDemandMonth12Date(String maxDemandMonth12Date) {
        this.maxDemandMonth12Date = maxDemandMonth12Date;
    }

    public String getMaxDemandMonth1() {
        return maxDemandMonth1;
    }

    public void setMaxDemandMonth1(String maxDemandMonth1) {
        this.maxDemandMonth1 = maxDemandMonth1;
    }

    public String getMaxDemandMonth2() {
        return maxDemandMonth2;
    }

    public void setMaxDemandMonth2(String maxDemandMonth2) {
        this.maxDemandMonth2 = maxDemandMonth2;
    }

    public String getMaxDemandMonth3() {
        return maxDemandMonth3;
    }

    public void setMaxDemandMonth3(String maxDemandMonth3) {
        this.maxDemandMonth3 = maxDemandMonth3;
    }

    public String getMaxDemandMonth4() {
        return maxDemandMonth4;
    }

    public void setMaxDemandMonth4(String maxDemandMonth4) {
        this.maxDemandMonth4 = maxDemandMonth4;
    }

    public String getMaxDemandMonth5() {
        return maxDemandMonth5;
    }

    public void setMaxDemandMonth5(String maxDemandMonth5) {
        this.maxDemandMonth5 = maxDemandMonth5;
    }

    public String getMaxDemandMonth6() {
        return maxDemandMonth6;
    }

    public void setMaxDemandMonth6(String maxDemandMonth6) {
        this.maxDemandMonth6 = maxDemandMonth6;
    }

    public String getMaxDemandMonth7() {
        return maxDemandMonth7;
    }

    public void setMaxDemandMonth7(String maxDemandMonth7) {
        this.maxDemandMonth7 = maxDemandMonth7;
    }

    public String getMaxDemandMonth8() {
        return maxDemandMonth8;
    }

    public void setMaxDemandMonth8(String maxDemandMonth8) {
        this.maxDemandMonth8 = maxDemandMonth8;
    }

    public String getMaxDemandMonth9() {
        return maxDemandMonth9;
    }

    public void setMaxDemandMonth9(String maxDemandMonth9) {
        this.maxDemandMonth9 = maxDemandMonth9;
    }

    public String getMaxDemandMonth10() {
        return maxDemandMonth10;
    }

    public void setMaxDemandMonth10(String maxDemandMonth10) {
        this.maxDemandMonth10 = maxDemandMonth10;
    }

    public String getMaxDemandMonth11() {
        return maxDemandMonth11;
    }

    public void setMaxDemandMonth11(String maxDemandMonth11) {
        this.maxDemandMonth11 = maxDemandMonth11;
    }

    public String getMaxDemandMonth12() {
        return maxDemandMonth12;
    }

    public void setMaxDemandMonth12(String maxDemandMonth12) {
        this.maxDemandMonth12 = maxDemandMonth12;
    }

    public String getMoneyconsm11() {
        return Moneyconsm11;
    }

    public void setMoneyconsm11(String moneyconsm11) {
        Moneyconsm11 = moneyconsm11;
    }

    public String getMoneyconsm12() {
        return Moneyconsm12;
    }

    public void setMoneyconsm12(String moneyconsm12) {
        Moneyconsm12 = moneyconsm12;
    }

    public String getSliceNo() {
        return SliceNo;
    }

    public void setSliceNo(String sliceNo) {
        SliceNo = sliceNo;
    }

    public String getSumOfAllChargesPND() {
        return SumOfAllChargesPND;
    }

    public void setSumOfAllChargesPND(String sumOfAllChargesPND) {
        SumOfAllChargesPND = sumOfAllChargesPND;
    }

    public String getSumOfAllChargesPSTR() {
        return SumOfAllChargesPSTR;
    }

    public void setSumOfAllChargesPSTR(String sumOfAllChargesPSTR) {
        SumOfAllChargesPSTR = sumOfAllChargesPSTR;
    }

    public String getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(String batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public String getChargeCount() {
        return chargeCount;
    }

    public void setChargeCount(String chargeCount) {
        this.chargeCount = chargeCount;
    }

    public String getClearAllTampers() {
        return clearAllTampers;
    }

    public void setClearAllTampers(String clearAllTampers) {
        this.clearAllTampers = clearAllTampers;
    }

    public String getClearTampers() {
        return clearTampers;
    }

    public void setClearTampers(String clearTampers) {
        this.clearTampers = clearTampers;
    }

    public String getConsm1() {
        return consm1;
    }

    public void setConsm1(String consm1) {
        this.consm1 = consm1;
    }

    public String getConsm10() {
        return consm10;
    }

    public void setConsm10(String consm10) {
        this.consm10 = consm10;
    }

    public String getConsm11() {
        return consm11;
    }

    public void setConsm11(String consm11) {
        this.consm11 = consm11;
    }

    public String getConsm12() {
        return consm12;
    }

    public void setConsm12(String consm12) {
        this.consm12 = consm12;
    }

    public String getConsm2() {
        return consm2;
    }

    public void setConsm2(String consm2) {
        this.consm2 = consm2;
    }

    public String getConsm3() {
        return consm3;
    }

    public void setConsm3(String consm3) {
        this.consm3 = consm3;
    }

    public String getConsm4() {
        return consm4;
    }

    public void setConsm4(String consm4) {
        this.consm4 = consm4;
    }

    public String getConsm5() {
        return consm5;
    }

    public void setConsm5(String consm5) {
        this.consm5 = consm5;
    }

    public String getConsm6() {
        return consm6;
    }

    public void setConsm6(String consm6) {
        this.consm6 = consm6;
    }

    public String getConsm7() {
        return consm7;
    }

    public void setConsm7(String consm7) {
        this.consm7 = consm7;
    }

    public String getConsm8() {
        return consm8;
    }

    public void setConsm8(String consm8) {
        this.consm8 = consm8;
    }

    public String getConsm9() {
        return consm9;
    }

    public void setConsm9(String consm9) {
        this.consm9 = consm9;
    }

    public String getConsm_kvar1() {
        return consm_kvar1;
    }

    public void setConsm_kvar1(String consm_kvar1) {
        this.consm_kvar1 = consm_kvar1;
    }

    public String getConsm_kvar10() {
        return consm_kvar10;
    }

    public void setConsm_kvar10(String consm_kvar10) {
        this.consm_kvar10 = consm_kvar10;
    }

    public String getConsm_kvar11() {
        return consm_kvar11;
    }

    public void setConsm_kvar11(String consm_kvar11) {
        this.consm_kvar11 = consm_kvar11;
    }

    public String getConsm_kvar12() {
        return consm_kvar12;
    }

    public void setConsm_kvar12(String consm_kvar12) {
        this.consm_kvar12 = consm_kvar12;
    }

    public String getConsm_kvar2() {
        return consm_kvar2;
    }

    public void setConsm_kvar2(String consm_kvar2) {
        this.consm_kvar2 = consm_kvar2;
    }

    public String getConsm_kvar3() {
        return consm_kvar3;
    }

    public void setConsm_kvar3(String consm_kvar3) {
        this.consm_kvar3 = consm_kvar3;
    }

    public String getConsm_kvar4() {
        return consm_kvar4;
    }

    public void setConsm_kvar4(String consm_kvar4) {
        this.consm_kvar4 = consm_kvar4;
    }

    public String getConsm_kvar5() {
        return consm_kvar5;
    }

    public void setConsm_kvar5(String consm_kvar5) {
        this.consm_kvar5 = consm_kvar5;
    }

    public String getConsm_kvar6() {
        return consm_kvar6;
    }

    public void setConsm_kvar6(String consm_kvar6) {
        this.consm_kvar6 = consm_kvar6;
    }

    public String getConsm_kvar7() {
        return consm_kvar7;
    }

    public void setConsm_kvar7(String consm_kvar7) {
        this.consm_kvar7 = consm_kvar7;
    }

    public String getConsm_kvar8() {
        return consm_kvar8;
    }

    public void setConsm_kvar8(String consm_kvar8) {
        this.consm_kvar8 = consm_kvar8;
    }

    public String getConsm_kvar9() {
        return consm_kvar9;
    }

    public void setConsm_kvar9(String consm_kvar9) {
        this.consm_kvar9 = consm_kvar9;
    }

    public String getControlCardState() {
        return controlCardState;
    }

    public void setControlCardState(String controlCardState) {
        this.controlCardState = controlCardState;
    }

    public String getCurrentMonthConsumption() {
        return currentMonthConsumption;
    }

    public void setCurrentMonthConsumption(String currentMonthConsumption) {
        this.currentMonthConsumption = currentMonthConsumption;
    }

    public String getCurrentMonthConsumptionMoney() {
        return currentMonthConsumptionMoney;
    }

    public void setCurrentMonthConsumptionMoney(String currentMonthConsumptionMoney) {
        this.currentMonthConsumptionMoney = currentMonthConsumptionMoney;
    }

    public String getCustomerID() {
        return customerID;
    }

    public void setCustomerID(String customerID) {
        this.customerID = customerID;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getfWVersion() {
        return fWVersion;
    }

    public void setfWVersion(String fWVersion) {
        this.fWVersion = fWVersion;
    }

    public String getInstallingDate() {
        return installingDate;
    }

    public void setInstallingDate(String installingDate) {
        this.installingDate = installingDate;
    }

    public String getMaxDemandPH1() {
        return maxDemandPH1;
    }

    public void setMaxDemandPH1(String maxDemandPH1) {
        this.maxDemandPH1 = maxDemandPH1;
    }

    public String getInstanteneousVolt() {
        return instanteneousVolt;
    }

    public void setInstanteneousVolt(String instanteneousVolt) {
        this.instanteneousVolt = instanteneousVolt;
    }

    public String getLastTechnicalCode() {
        return lastTechnicalCode;
    }

    public void setLastTechnicalCode(String lastTechnicalCode) {
        this.lastTechnicalCode = lastTechnicalCode;
    }

    public String getMaximumDemandDate() {
        return maximumDemandDate;
    }

    public void setMaximumDemandDate(String maximumDemandDate) {
        this.maximumDemandDate = maximumDemandDate;
    }

    public String getMeterId() {
        return meterId;
    }

    public void setMeterId(String meterId) {
        this.meterId = meterId;
    }

    public String getMeterStatus() {
        return meterStatus;
    }

    public void setMeterStatus(String meterStatus) {
        this.meterStatus = meterStatus;
    }

    public String getPowerFactor() {
        return powerFactor;
    }

    public void setPowerFactor(String powerFactor) {
        this.powerFactor = powerFactor;
    }

    public String getRelayStatus() {
        return relayStatus;
    }

    public void setRelayStatus(String relayStatus) {
        this.relayStatus = relayStatus;
    }

    public String getRemainKW() {
        return remainKW;
    }

    public void setRemainKW(String remainKW) {
        this.remainKW = remainKW;
    }

    public String getRemainMoney() {
        return remainMoney;
    }

    public void setRemainMoney(String remainMoney) {
        this.remainMoney = remainMoney;
    }

    public String getReverse() {
        return reverse;
    }

    public void setReverse(String reverse) {
        this.reverse = reverse;
    }

    public String getSetTimeAndDate() {
        return setTimeAndDate;
    }

    public void setSetTimeAndDate(String setTimeAndDate) {
        this.setTimeAndDate = setTimeAndDate;
    }

    public String getTarrifActivationDate() {
        return tarrifActivationDate;
    }

    public void setTarrifActivationDate(String tarrifActivationDate) {
        this.tarrifActivationDate = tarrifActivationDate;
    }

    public String getTarrifID() {
        return tarrifID;
    }

    public void setTarrifID(String tarrifID) {
        this.tarrifID = tarrifID;
    }

    public String getTechnicalCode() {
        return technicalCode;
    }

    public void setTechnicalCode(String technicalCode) {
        this.technicalCode = technicalCode;
    }

    public String getTempresList() {
        return tempresList;
    }

    public void setTempresList(String tempresList) {
        this.tempresList = tempresList;
    }

    public String getTotalConsum() {
        return totalConsum;
    }

    public void setTotalConsum(String totalConsum) {
        this.totalConsum = totalConsum;
    }

    public String getTotalConsumptionKvh() {
        return totalConsumptionKvh;
    }

    public void setTotalConsumptionKvh(String totalConsumptionKvh) {
        this.totalConsumptionKvh = totalConsumptionKvh;
    }

    public String getUnblanceInKW() {
        return unblanceInKW;
    }

    public void setUnblanceInKW(String unblanceInKW) {
        this.unblanceInKW = unblanceInKW;
    }

    public String getDeon() {
        return Deon;
    }

    public void setDeon(String deon) {
        Deon = deon;
    }

    public String getMaxLoadKW() {
        return maxLoadKW;
    }

    public void setMaxLoadKW(String maxLoadKW) {
        this.maxLoadKW = maxLoadKW;
    }

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public String getMeterModel() {
        return meterModel;
    }

    public void setMeterModel(String meterModel) {
        meterModel = meterModel;
    }

    public void fromJson(String json) {
        try {
            MeterData meterData = new Gson().fromJson(json, MeterData.class);
            if (meterData.ActivityType != null)
                this.ActivityType = meterData.ActivityType.replace("\t", "_");
            this.CurrentMonthlyConsum = String.valueOf(meterData.CurrentMonthlyConsum).replace("\t", "_");
            if (meterData.LastChargeDate != null)
                this.LastChargeDate = meterData.LastChargeDate.replace("\t", "_");
            this.meterModel = meterData.meterModel;
            this.Moneyconsm01 = String.valueOf(meterData.Moneyconsm01).replace("\t", "_");
            this.Moneyconsm02 = String.valueOf(meterData.Moneyconsm02).replace("\t", "_");
            this.Moneyconsm03 = String.valueOf(meterData.Moneyconsm03).replace("\t", "_");
            this.Moneyconsm04 = String.valueOf(meterData.Moneyconsm04).replace("\t", "_");
            this.Moneyconsm05 = String.valueOf(meterData.Moneyconsm05).replace("\t", "_");
            this.Moneyconsm06 = String.valueOf(meterData.Moneyconsm06).replace("\t", "_");
            this.Moneyconsm07 = String.valueOf(meterData.Moneyconsm07).replace("\t", "_");
            this.Moneyconsm08 = String.valueOf(meterData.Moneyconsm08).replace("\t", "_");
            this.Moneyconsm09 = String.valueOf(meterData.Moneyconsm09).replace("\t", "_");
            this.Moneyconsm10 = String.valueOf(meterData.Moneyconsm10).replace("\t", "_");
            this.Moneyconsm11 = String.valueOf(meterData.Moneyconsm11).replace("\t", "_");
            this.Moneyconsm12 = String.valueOf(meterData.Moneyconsm12).replace("\t", "_");
            this.SliceNo = String.valueOf(meterData.SliceNo).replace("\t", "_");
            this.SumOfAllChargesPND = String.valueOf(meterData.SumOfAllChargesPND).replace("\t", "_");
            this.SumOfAllChargesPSTR = String.valueOf(meterData.SumOfAllChargesPSTR).replace("\t", "_");
            this.batteryStatus = String.valueOf(meterData.batteryStatus).replace("\t", "_");
            this.chargeCount = String.valueOf(meterData.chargeCount).replace("\t", "_");
            if (meterData.clearAllTampers != null)
                this.clearAllTampers = meterData.clearAllTampers.replace("\t", "_");
            if (meterData.clearTampers != null)
                this.clearTampers = meterData.clearTampers.replace("\t", "_");
            this.consm1 = String.valueOf(meterData.consm1).replace("\t", "_");
            this.consm2 = String.valueOf(meterData.consm2).replace("\t", "_");
            this.consm3 = String.valueOf(meterData.consm3).replace("\t", "_");
            this.consm4 = String.valueOf(meterData.consm4).replace("\t", "_");
            this.consm5 = String.valueOf(meterData.consm5).replace("\t", "_");
            this.consm6 = String.valueOf(meterData.consm6).replace("\t", "_");
            this.consm7 = String.valueOf(meterData.consm7).replace("\t", "_");
            this.consm8 = String.valueOf(meterData.consm8).replace("\t", "_");
            this.consm9 = String.valueOf(meterData.consm9).replace("\t", "_");
            this.consm10 = String.valueOf(meterData.consm10).replace("\t", "_");
            this.consm11 = String.valueOf(meterData.consm11).replace("\t", "_");
            this.consm12 = String.valueOf(meterData.consm12).replace("\t", "_");
            this.consm_kvar1 = String.valueOf(meterData.consm_kvar1).replace("\t", "_");
            this.consm_kvar2 = String.valueOf(meterData.consm_kvar2).replace("\t", "_");
            this.consm_kvar3 = String.valueOf(meterData.consm_kvar3).replace("\t", "_");
            this.consm_kvar4 = String.valueOf(meterData.consm_kvar4).replace("\t", "_");
            this.consm_kvar5 = String.valueOf(meterData.consm_kvar5).replace("\t", "_");
            this.consm_kvar6 = String.valueOf(meterData.consm_kvar6).replace("\t", "_");
            this.consm_kvar7 = String.valueOf(meterData.consm_kvar7).replace("\t", "_");
            this.consm_kvar8 = String.valueOf(meterData.consm_kvar8).replace("\t", "_");
            this.consm_kvar9 = String.valueOf(meterData.consm_kvar9).replace("\t", "_");
            this.consm_kvar10 = String.valueOf(meterData.consm_kvar10).replace("\t", "_");
            this.consm_kvar11 = String.valueOf(meterData.consm_kvar11).replace("\t", "_");
            this.consm_kvar12 = String.valueOf(meterData.consm_kvar12).replace("\t", "_");
            this.maxDemandMonth1 = String.valueOf(meterData.maxim_demand_month_1).replace("\t", "_");
            this.maxDemandMonth2 = String.valueOf(meterData.maxim_demand_month_2).replace("\t", "_");
            this.maxDemandMonth3 = String.valueOf(meterData.maxim_demand_month_3).replace("\t", "_");
            this.maxDemandMonth4 = String.valueOf(meterData.maxim_demand_month_4).replace("\t", "_");
            this.maxDemandMonth5 = String.valueOf(meterData.maxim_demand_month_5).replace("\t", "_");
            this.maxDemandMonth6 = String.valueOf(meterData.maxim_demand_month_6).replace("\t", "_");
            this.maxDemandMonth7 = String.valueOf(meterData.maxim_demand_month_7).replace("\t", "_");
            this.maxDemandMonth8 = String.valueOf(meterData.maxim_demand_month_8).replace("\t", "_");
            this.maxDemandMonth9 = String.valueOf(meterData.maxim_demand_month_9).replace("\t", "_");
            this.maxDemandMonth10 = String.valueOf(meterData.maxim_demand_month_10).replace("\t", "_");
            this.maxDemandMonth11 = String.valueOf(meterData.maxim_demand_month_11).replace("\t", "_");
            this.maxDemandMonth12 = String.valueOf(meterData.maxim_demand_month_12).replace("\t", "_");
            this.maxDemandMonth1Date = String.valueOf(meterData.maxim_demand_month_date_1).replace("\t", "_");
            this.maxDemandMonth2Date = String.valueOf(meterData.maxim_demand_month_date_2).replace("\t", "_");
            this.maxDemandMonth3Date = String.valueOf(meterData.maxim_demand_month_date_3).replace("\t", "_");
            this.maxDemandMonth4Date = String.valueOf(meterData.maxim_demand_month_date_4).replace("\t", "_");
            this.maxDemandMonth5Date = String.valueOf(meterData.maxim_demand_month_date_5).replace("\t", "_");
            this.maxDemandMonth6Date = String.valueOf(meterData.maxim_demand_month_date_6).replace("\t", "_");
            this.maxDemandMonth7Date = String.valueOf(meterData.maxim_demand_month_date_7).replace("\t", "_");
            this.maxDemandMonth8Date = String.valueOf(meterData.maxim_demand_month_date_8).replace("\t", "_");
            this.maxDemandMonth9Date = String.valueOf(meterData.maxim_demand_month_date_9).replace("\t", "_");
            this.maxDemandMonth10Date = String.valueOf(meterData.maxim_demand_month_date_10).replace("\t", "_");
            this.maxDemandMonth11Date = String.valueOf(meterData.maxim_demand_month_date_11).replace("\t", "_");
            this.maxDemandMonth12Date = String.valueOf(meterData.maxim_demand_month_date_12).replace("\t", "_");
            if (meterData.controlCardState != null)
                this.controlCardState = meterData.controlCardState.replace("\t", "_");
            this.currentMonthConsumption = String.valueOf(meterData.currentMonthConsumption).replace("\t", "_");
            this.currentMonthConsumptionMoney = String.valueOf(meterData.currentMonthConsumptionMoney).replace("\t", "_");
            if (meterData.customerID != null)
                this.customerID = meterData.customerID.replace("\t", "_");
            if (meterData.date != null)
                this.date = meterData.date.replace("\t", "_");
            if (meterData.fWVersion != null)
                this.fWVersion = meterData.fWVersion.replace("\t", "_");
            if (meterData.installingDate != null)
                this.installingDate = meterData.installingDate.replace("\t", "_");
            this.maxDemandPH1 = String.valueOf(meterData.maxDemandPH1).replace("\t", "_");
            if (meterData.instanteneousVolt != null)
                this.instanteneousVolt = meterData.instanteneousVolt.replace("\t", "_");
            if (meterData.lastTechnicalCode != null)
                this.lastTechnicalCode = meterData.lastTechnicalCode.replace("\t", "_");
            if (meterData.maximumDemandDate != null)
                this.maximumDemandDate = meterData.maximumDemandDate;
            if (meterData.meterID != null)
                this.meterId = meterData.meterID.replace("\t", "_");
            if (meterData.meterStatus != null)
                this.meterStatus = meterData.meterStatus.replace("\t", "_");
            this.powerFactor = String.valueOf(meterData.powerFactor).replace("\t", "_");
            this.lastYearPowerFactor = String.valueOf(meterData.lastYearPowerFactor).replace("\t", "_");
            this.relayStatus = String.valueOf(meterData.relayStatus).replace("\t", "_");
            if (meterData.remainKW != null)
                this.remainKW = meterData.remainKW.replace("\t", "_");
            if (meterData.remainMoney != null)
                this.remainMoney = meterData.remainMoney.replace("\t", "_");
            this.reverse = String.valueOf(meterData.reverse).replace("\t", "_");
            if (meterData.setTimeAndDate != null)
                this.setTimeAndDate = meterData.setTimeAndDate.replace("\t", "_");
            if (meterData.tarrifActivationDate != null)
                this.tarrifActivationDate = meterData.tarrifActivationDate.replace("\t", "_");
            if (meterData.tarrifID != null)
                this.tarrifID = meterData.tarrifID.replace("\t", "_");
            if (meterData.technicalCode != null)
                this.technicalCode = meterData.technicalCode.replace("\t", "_");
            if (meterData.tempresList != null)
                this.tempresList = meterData.tempresList.replace("\t", "_");
            if (meterData.totalConsum != null)
                this.totalConsum = meterData.totalConsum.replace("\t", "_");
            this.totalConsumptionKvh = String.valueOf(meterData.totalConsumptionKvh).replace("\t", "_");
            this.unblanceInKW = String.valueOf(meterData.unblanceInKW).replace("\t", "_");
            this.Deon = String.valueOf(meterData.Deon).replace("\t", "_");
            this.maxLoadKW = String.valueOf(meterData.maxLoadKW).replace("\t", "_");
            if (meterData.customerID != null)
                this.customerID = meterData.customerID.replace("\t", "_");
            if (meterData.meterPhase == 2) {
                meterType = "Single";
            } else if (meterData.meterPhase == 1) {
                meterType = "3Phase";
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @NonNull
    @Override
    public String toString() {
        return "GlobalResponse : " + '\n' +
                "meterType : " + meterType + '\n' +
                "ActivityType : " + ActivityType + '\n' +
                "CurrentMonthlyConsum : " + CurrentMonthlyConsum + '\n' +
                "LastChargeDate : " + LastChargeDate + '\n' +
                "Moneyconsm01 : " + Moneyconsm01 + '\n' +
                "Moneyconsm02 : " + Moneyconsm02 + '\n' +
                "Moneyconsm03 : " + Moneyconsm03 + '\n' +
                "Moneyconsm04 : " + Moneyconsm04 + '\n' +
                "Moneyconsm05 : " + Moneyconsm05 + '\n' +
                "Moneyconsm06 : " + Moneyconsm06 + '\n' +
                "Moneyconsm07 : " + Moneyconsm07 + '\n' +
                "Moneyconsm08 : " + Moneyconsm08 + '\n' +
                "Moneyconsm09 : " + Moneyconsm09 + '\n' +
                "Moneyconsm10 : " + Moneyconsm10 + '\n' +
                "Moneyconsm11 : " + Moneyconsm11 + '\n' +
                "Moneyconsm12 : " + Moneyconsm12 + '\n' +
                "SliceNo : " + SliceNo + '\n' +
                "SumOfAllChargesPND : " + SumOfAllChargesPND + '\n' +
                "SumOfAllChargesPSTR : " + SumOfAllChargesPSTR + '\n' +
                "batteryStatus : " + batteryStatus + '\n' +
                "chargeCount : " + chargeCount + '\n' +
                "clearAllTampers : " + clearAllTampers + '\n' +
                "clearTampers : " + clearTampers + '\n' +
                "consm1 : " + consm1 + '\n' +
                "consm10 : " + consm10 + '\n' +
                "consm11 : " + consm11 + '\n' +
                "consm12 : " + consm12 + '\n' +
                "consm2 : " + consm2 + '\n' +
                "consm3 : " + consm3 + '\n' +
                "consm4 : " + consm4 + '\n' +
                "consm5 : " + consm5 + '\n' +
                "consm6 : " + consm6 + '\n' +
                "consm7 : " + consm7 + '\n' +
                "consm8 : " + consm8 + '\n' +
                "consm9 : " + consm9 + '\n' +
                "consm_kvar1 : " + consm_kvar1 + '\n' +
                "consm_kvar10 : " + consm_kvar10 + '\n' +
                "consm_kvar11 : " + consm_kvar11 + '\n' +
                "consm_kvar12 : " + consm_kvar12 + '\n' +
                "consm_kvar2 : " + consm_kvar2 + '\n' +
                "consm_kvar3 : " + consm_kvar3 + '\n' +
                "consm_kvar4 : " + consm_kvar4 + '\n' +
                "consm_kvar5 : " + consm_kvar5 + '\n' +
                "consm_kvar6 : " + consm_kvar6 + '\n' +
                "consm_kvar7 : " + consm_kvar7 + '\n' +
                "consm_kvar8 : " + consm_kvar8 + '\n' +
                "consm_kvar9 : " + consm_kvar9 + '\n' +
                "maxDemandMonth1 : " + maxDemandMonth1 + '\n' +
                "maxDemandMonth2 : " + maxDemandMonth2 + '\n' +
                "maxDemandMonth3 : " + maxDemandMonth3 + '\n' +
                "maxDemandMonth4 : " + maxDemandMonth4 + '\n' +
                "maxDemandMonth5 : " + maxDemandMonth5 + '\n' +
                "maxDemandMonth6 : " + maxDemandMonth6 + '\n' +
                "maxDemandMonth7 : " + maxDemandMonth7 + '\n' +
                "maxDemandMonth8 : " + maxDemandMonth8 + '\n' +
                "maxDemandMonth9 : " + maxDemandMonth9 + '\n' +
                "maxDemandMonth10 : " + maxDemandMonth10 + '\n' +
                "maxDemandMonth11 : " + maxDemandMonth11 + '\n' +
                "maxDemandMonth12 : " + maxDemandMonth12 + '\n' +
                "maxDemandMonth1Date : " + maxDemandMonth1Date + '\n' +
                "maxDemandMonth2Date : " + maxDemandMonth2Date + '\n' +
                "maxDemandMonth3Date : " + maxDemandMonth3Date + '\n' +
                "maxDemandMonth4Date : " + maxDemandMonth4Date + '\n' +
                "maxDemandMonth5Date : " + maxDemandMonth5Date + '\n' +
                "maxDemandMonth6Date : " + maxDemandMonth6Date + '\n' +
                "maxDemandMonth7Date : " + maxDemandMonth7Date + '\n' +
                "maxDemandMonth8Date : " + maxDemandMonth8Date + '\n' +
                "maxDemandMonth9Date : " + maxDemandMonth9Date + '\n' +
                "maxDemandMonth10Date : " + maxDemandMonth10Date + '\n' +
                "maxDemandMonth11Date : " + maxDemandMonth11Date + '\n' +
                "maxDemandMonth12Date : " + maxDemandMonth12Date + '\n' +
                "controlCardState : " + controlCardState + '\n' +
                "currentMonthConsumption : " + currentMonthConsumption + '\n' +
                "customerID : " + customerID + '\n' +
                "date : " + date + '\n' +
                "fWVersion : " + fWVersion + '\n' +
                "installingDate : " + installingDate + '\n' +
                "instanteneousCurrent : " + "0" + '\n' +
                "instanteneousVolt : " + instanteneousVolt + '\n' +
                "lastTechnicalCode : " + lastTechnicalCode + '\n' +
                "maximumDemandDate : " + maximumDemandDate + '\n' +
                "meterID : " + meterId + '\n' +
                "meterStatus : " + meterStatus + '\n' +
                "powerFactor : " + powerFactor + '\n' +
                "relayStatus : " + relayStatus + '\n' +
                "remainKW : " + remainKW + '\n' +
                "remainMoney : " + remainMoney + '\n' +
                "reverse : " + reverse + '\n' +
                "setTimeAndDate : " + setTimeAndDate + '\n' +
                "tarrifActivationDate : " + tarrifActivationDate + '\n' +
                "tarrifID : " + tarrifID + '\n' +
                "technicalCode : " + technicalCode + '\n' +
                "tempresList : " + tempresList + '\n' +
                "totalConsum : " + totalConsum + '\n' +
                "totalConsumptionKvh : " + totalConsumptionKvh + '\n' +
                "unblanceInKW : " + unblanceInKW + '\n' +
                "Deon : " + Deon + '\n' +
                "maxLoadKW : " + maxLoadKW + '\n' +
                "readingResult=" + readingResult + '\n' +
                "message : " + message;
    }


    @NonNull
    @Override
    public String toFileFormate() {
        String temperList = getTempresList().toLowerCase(Locale.ROOT).replaceAll(" ", "").replaceAll("-", "");
        int meterStatusIndex = temperList.indexOf("m") + 1;
        int relayStatusIndex = temperList.indexOf("r") + 1;
        int batteryStatusIndex = temperList.indexOf("b") + 1;
        int sideCoverStatusIndex = temperList.indexOf("s") + 1;
        int topCoverStatusIndex = temperList.indexOf("t") + 1;
        int meterStatus = 0;
        int relayStatus = 0;
        int batteryStatus = 0;
        int sideCoverStatus = 0;
        int topCoverStatus = 0;
        try {
            if (meterStatusIndex > 0 && meterStatusIndex < temperList.length())
                meterStatus = Integer.parseInt(temperList.substring(meterStatusIndex, meterStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
        } catch (Exception ignored) {
        }
        try {
            if (relayStatusIndex > 0 && relayStatusIndex < temperList.length())
                relayStatus = Integer.parseInt(temperList.substring(relayStatusIndex, relayStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
        } catch (Exception ignored) {
        }
        try {
            if (batteryStatusIndex > 0 && batteryStatusIndex < temperList.length())
                batteryStatus = Integer.parseInt(temperList.substring(batteryStatusIndex, batteryStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
        } catch (Exception ignored) {
        }
        try {
            if (sideCoverStatusIndex > 0 && sideCoverStatusIndex < temperList.length())
                sideCoverStatus = Integer.parseInt(temperList.substring(sideCoverStatusIndex, sideCoverStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
        } catch (Exception ignored) {
        }
        try {
            if (topCoverStatusIndex > 0 && topCoverStatusIndex < temperList.length())
                topCoverStatus = Integer.parseInt(temperList.substring(topCoverStatusIndex, topCoverStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
        } catch (Exception ignored) {
        }

        return
                "meterType\t" + meterType + "\n" +
                        "ITEM_1_NEW_BASEITEM_Meter_ID\t" + meterId + "\n" +
                        "ITEM_2_NEW_BASEITEM_Customer_ID\t" + customerID + "\n" +
                        "ITEM_3_NEW_BASEITEM_CardID\t" + "0" + "\n" +
                        "ITEM_4_NEW_BASEITEM_fw_version\t" + fWVersion + "\n" +
                        "ITEM_5_NEW_BASEITEM_ActivityType\t" + getActivityType() + "\n" +
                        "ITEM_6_NEW_BASEITEM_curent_Power_factor\t" + powerFactor + "\n" +
                        "ITEM_7_NEW_BASEITEM_last_year_Power_factor\t" + lastYearPowerFactor + "\n" +
                        "ITEM_8_NEW_BASEITEM_installing_technican_code\t" + getTechnicalCode() + "\n" +
                        "ITEM_9_NEW_BASEITEM_installing_Date_and_time\t" + getInstallingDate() + "\n" +
                        "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time\t" + getDate() + "\n" +
                        "ITEM_11_NEW_BASEITEM_Current_tarrif_installing\t" + getTarrifID() + "\n" +
                        "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date\t" + getTarrifActivationDate() + "\n" +
                        "ITEM_13_NEW_BASEITEM_Meter_status\t" + meterStatus + "\n" +
                        "ITEM_14_NEW_BASEITEM_Relay_status\t" + relayStatus + "\n" +
                        "ITEM_15_NEW_BASEITEM_battery_status\t" + batteryStatus + "\n" +
                        "ITEM_16_NEW_BASEITEM_Top_cover_status\t" + topCoverStatus + "\n" +
                        "ITEM_17_NEW_BASEITEM_Side_cover_status\t" + sideCoverStatus + "\n" +
                        "ITEM_18_NEW_BASEITEM_Technical_code_event_1\t" + lastTechnicalCode + "\n" +
                        "ITEM_19_NEW_BASEITEM_event_type_1\t" + controlCardState + "\n" +
                        "ITEM_20_NEW_BASEITEM_event_Date_1\t" + "0" + "\n" +
                        "ITEM_21_NEW_BASEITEM_Technical_code_event_2\t" + "0" + "\n" +
                        "ITEM_22_NEW_BASEITEM_event_type_2\t" + "0" + "\n" +
                        "ITEM_23_NEW_BASEITEM_event_Date_2\t" + "0" + "\n" +
                        "ITEM_24_NEW_BASEITEM_Technical_code_event_3\t" + "0" + "\n" +
                        "ITEM_25_NEW_BASEITEM_event_type_3\t" + "0" + "\n" +
                        "ITEM_26_NEW_BASEITEM_event_Date_3\t" + "0" + "\n" +
                        "ITEM_27_NEW_BASEITEM_recharge_number\t" + getChargeCount() + "\n" +
                        "ITEM_28_NEW_BASEITEM_Recharge_Amount\t" + Double.parseDouble(getSumOfAllChargesPND().replaceAll("[^0-9.-]", "") + "." + getSumOfAllChargesPSTR().replaceAll("[^0-9.-]", "")) + "\n" +
                        "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time\t" + getLastChargeDate() + "\n" +
                        "ITEM_30_NEW_BASEITEM_remaining_credit_kw\t" + remainKW + "\n" +
                        "ITEM_31_NEW_BASEITEM_remaining_credit_mony\t" + remainMoney + "\n" +
                        "ITEM_32_NEW_BASEITEM_Debts\t" + getDeon() + "\n" +
                        "ITEM_33_NEW_BASEITEM_Total_consumption_kw\t" + totalConsum + "\n" +
                        "ITEM_34_NEW_BASEITEM_Total_consumption_mony\t" + "0" + "\n" +
                        "ITEM_35_NEW_BASEITEM_Total_consumption_kvar\t" + totalConsumptionKvh + "\n" +
                        "ITEM_36_NEW_BASEITEM_Current_Demand\t" + "0" + "\n" +
                        "ITEM_37_NEW_BASEITEM_Maximum_Demand\t" + getMaxDemandPH1() + "\n" +
                        "ITEM_38_NEW_BASEITEM_Maximum_Demand_date\t" + maximumDemandDate + "\n" +
                        "ITEM_39_NEW_BASEITEM_instanteneous_volt\t" + instanteneousVolt + "\n" +
                        "ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere\t" + "0" + "\n" +
                        "ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral\t" + "0" + "\n" +
                        "ITEM_42_NEW_BASEITEM_reverse_Kwh\t" + getReverse() + "\n" +
                        "ITEM_43_NEW_BASEITEM_unbalance_Kwh\t" + getUnblanceInKW() + "\n" +
                        "ITEM_44_NEW_BASEITEM_current_month_consumption_KW\t" + getCurrentMonthConsumption() + "\n" +
                        "ITEM_45_NEW_BASEITEM_current_month_consumption_MONY\t" + getCurrentMonthConsumptionMoney() + "\n" +
                        "ITEM_46_NEW_BASEITEM_1_month_consumption_kWh\t" + getConsm1() + "\n" +
                        "ITEM_47_NEW_BASEITEM_2_month_consumption_kWh\t" + getConsm2() + "\n" +
                        "ITEM_48_NEW_BASEITEM_3_month_consumption_kWh\t" + getConsm3() + "\n" +
                        "ITEM_49_NEW_BASEITEM_4_month_consumption_kWh\t" + getConsm4() + "\n" +
                        "ITEM_50_NEW_BASEITEM_5_month_consumption_kWh\t" + getConsm5() + "\n" +
                        "ITEM_51_NEW_BASEITEM_6_month_consumption_kWh\t" + getConsm6() + "\n" +
                        "ITEM_52_NEW_BASEITEM_7_month_consumption_kWh\t" + getConsm7() + "\n" +
                        "ITEM_53_NEW_BASEITEM_8_month_consumption_kWh\t" + getConsm8() + "\n" +
                        "ITEM_54_NEW_BASEITEM_9_month_consumption_kWh\t" + getConsm9() + "\n" +
                        "ITEM_55_NEW_BASEITEM_10_month_consumption_kWh\t" + getConsm10() + "\n" +
                        "ITEM_56_NEW_BASEITEM_11_month_consumption_kWh\t" + getConsm11() + "\n" +
                        "ITEM_57_NEW_BASEITEM_12_month_consumption_kWh\t" + getConsm12() + "\n" +
                        "ITEM_58_NEW_BASEITEM_1_month_consumption_Mony\t" + getMoneyconsm01() + "\n" +
                        "ITEM_59_NEW_BASEITEM_2_month_consumption_Mony\t" + getMoneyconsm02() + "\n" +
                        "ITEM_60_NEW_BASEITEM_3_month_consumption_Mony\t" + getMoneyconsm03() + "\n" +
                        "ITEM_61_NEW_BASEITEM_4_month_consumption_Mony\t" + getMoneyconsm04() + "\n" +
                        "ITEM_62_NEW_BASEITEM_5_month_consumption_Mony\t" + getMoneyconsm05() + "\n" +
                        "ITEM_63_NEW_BASEITEM_6_month_consumption_Mony\t" + getMoneyconsm06() + "\n" +
                        "ITEM_64_NEW_BASEITEM_7_month_consumption_Mony\t" + getMoneyconsm07() + "\n" +
                        "ITEM_65_NEW_BASEITEM_8_month_consumption_Mony\t" + getMoneyconsm08() + "\n" +
                        "ITEM_66_NEW_BASEITEM_9_month_consumption_Mony\t" + getMoneyconsm09() + "\n" +
                        "ITEM_67_NEW_BASEITEM_10_month_consumption_Mony\t" + getMoneyconsm10() + "\n" +
                        "ITEM_68_NEW_BASEITEM_11_month_consumption_Mony\t" + getMoneyconsm11() + "\n" +
                        "ITEM_69_NEW_BASEITEM_12_month_consumption_Mony\t" + getMoneyconsm12() + "\n" +
                        "ITEM_70_NEW_BASEITEM_maxim_demand_month_1\t" + getMaxDemandMonth1() + "\n" +
                        "ITEM_71_NEW_BASEITEM_maxim_demand_month_2\t" + getMaxDemandMonth2() + "\n" +
                        "ITEM_72_NEW_BASEITEM_maxim_demand_month_3\t" + getMaxDemandMonth3() + "\n" +
                        "ITEM_73_NEW_BASEITEM_maxim_demand_month_4\t" + getMaxDemandMonth4() + "\n" +
                        "ITEM_74_NEW_BASEITEM_maxim_demand_month_5\t" + getMaxDemandMonth5() + "\n" +
                        "ITEM_75_NEW_BASEITEM_maxim_demand_month_6\t" + getMaxDemandMonth6() + "\n" +
                        "ITEM_76_NEW_BASEITEM_maxim_demand_month_7\t" + getMaxDemandMonth7() + "\n" +
                        "ITEM_77_NEW_BASEITEM_maxim_demand_month_8\t" + getMaxDemandMonth8() + "\n" +
                        "ITEM_78_NEW_BASEITEM_maxim_demand_month_9\t" + getMaxDemandMonth9() + "\n" +
                        "ITEM_79_NEW_BASEITEM_maxim_demand_month_10\t" + getMaxDemandMonth10() + "\n" +
                        "ITEM_80_NEW_BASEITEM_maxim_demand_month_11\t" + getMaxDemandMonth11() + "\n" +
                        "ITEM_81_NEW_BASEITEM_maxim_demand_month_12\t" + getMaxDemandMonth12() + "\n" +
                        "ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1\t" + getMaxDemandMonth1Date() + "\n" +
                        "ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2\t" + getMaxDemandMonth2Date() + "\n" +
                        "ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3\t" + getMaxDemandMonth3Date() + "\n" +
                        "ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4\t" + getMaxDemandMonth4Date() + "\n" +
                        "ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5\t" + getMaxDemandMonth5Date() + "\n" +
                        "ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6\t" + getMaxDemandMonth6Date() + "\n" +
                        "ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7\t" + getMaxDemandMonth7Date() + "\n" +
                        "ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8\t" + getMaxDemandMonth8Date() + "\n" +
                        "ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9\t" + getMaxDemandMonth9Date() + "\n" +
                        "ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10\t" + getMaxDemandMonth10Date() + "\n" +
                        "ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11\t" + getMaxDemandMonth11Date() + "\n" +
                        "ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12\t" + getMaxDemandMonth12Date() + "\n" +
                        "ITEM_94_NEW_BASEITEM_kvar_consumption_month_1\t" + getConsm_kvar1() + "\n" +
                        "ITEM_95_NEW_BASEITEM_kvar_consumption_month_2\t" + getConsm_kvar2() + "\n" +
                        "ITEM_96_NEW_BASEITEM_kvar_consumption_month_3\t" + getConsm_kvar3() + "\n" +
                        "ITEM_97_NEW_BASEITEM_kvar_consumption_month_4\t" + getConsm_kvar4() + "\n" +
                        "ITEM_98_NEW_BASEITEM_kvar_consumption_month_5\t" + getConsm_kvar5() + "\n" +
                        "ITEM_99_NEW_BASEITEM_kvar_consumption_month_6\t" + getConsm_kvar6() + "\n" +
                        "ITEM_100_NEW_BASEITEM_kvar_consumption_month_7\t" + getConsm_kvar7() + "\n" +
                        "ITEM_101_NEW_BASEITEM_kvar_consumption_month_8\t" + getConsm_kvar8() + "\n" +
                        "ITEM_102_NEW_BASEITEM_kvar_consumption_month_9\t" + getConsm_kvar9() + "\n" +
                        "ITEM_103_NEW_BASEITEM_kvar_consumption_month_10\t" + getConsm_kvar10() + "\n" +
                        "ITEM_104_NEW_BASEITEM_kvar_consumption_month_11\t" + getConsm_kvar11() + "\n" +
                        "ITEM_105_NEW_BASEITEM_kvar_consumption_month_12\t" + getConsm_kvar12() + "\n";
    }
}
