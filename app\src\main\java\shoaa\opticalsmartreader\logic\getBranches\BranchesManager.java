package shoaa.opticalsmartreader.logic.getBranches;

import androidx.annotation.NonNull;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import shoaa.opticalsmartreader.api.APIClient;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.logic.login.LoginManager;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Branch;
import shoaa.opticalsmartreader.ui.MainActivity;

public class BranchesManager {
    public static Branch[] branches = null;

    public static void loadAllBranches(MainActivity mainActivity, AppUser loginUser, ApiResult apiResult) {
        LoginManager.login(mainActivity, loginUser.getUserName(), loginUser.getPassword(), new ApiResult() {
            @Override
            public void onSuccess() {
                getAllBranches(loginUser, apiResult);
            }

            @Override
            public void onFailed(int code, String reason) {
                apiResult.onFailed(0, "");

            }

        });
    }

    private static void getAllBranches(AppUser loginUser, ApiResult apiResult) {
        Call<Branch[]> allBranches = APIClient.getService().getBranches(loginUser.getSESSION_ID(), "LUBranches", "0", "0");
        allBranches.enqueue(new Callback<Branch[]>() {
            @Override
            public void onResponse(@NonNull Call<Branch[]> call, @NonNull Response<Branch[]> response) {
                if (response.isSuccessful()) {
                    if (response.body() == null || response.body().length == 0) {
                        branches = null;
                        apiResult.onFailed(1, "");
                    } else {
                        branches = response.body();
                        apiResult.onSuccess();
                    }
                } else {
                    branches = null;
                    apiResult.onFailed(2, "");
                }
            }

            @Override
            public void onFailure(@NonNull Call<Branch[]> call, @NonNull Throwable t) {
                branches = null;
                apiResult.onFailed(3, "");
            }
        });
    }
}
