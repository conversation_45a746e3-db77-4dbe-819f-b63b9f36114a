<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:layoutDirection="rtl"
    android:padding="10dp"
    tools:context=".ui.MomarsaActivity">


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tvTitle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:text="ممارسة جديدة"
                android:textColor="@color/primary"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tvDesc"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="يجب ادخال البيانات الاتية للممارسة"
                android:textColor="@color/red"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/tvTitle2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle1" />

            <TextView
                android:id="@+id/tvTitle2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="نوع النشاط"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/rd"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvDesc" />

            <RadioGroup
                android:id="@+id/rd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                app:layout_constraintBottom_toTopOf="@id/tvTitle4"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle2">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <RadioButton
                    android:id="@+id/rdHome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:buttonTint="@color/primary"
                    android:text="منزلي"
                    android:textColor="@color/black" />

                <RadioButton
                    android:id="@+id/rdShop"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:buttonTint="@color/primary"
                    android:text="تجاري"
                    android:textColor="@color/black" />

                <RadioButton
                    android:id="@+id/rdPower"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:buttonTint="@color/primary"
                    android:text="قوي محركة"
                    android:textColor="@color/black" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

            </RadioGroup>

            <TextView
                android:id="@+id/tvTitle4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="نوع الممارسة"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/rd2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rd" />

            <RadioGroup
                android:id="@+id/rd2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                app:layout_constraintBottom_toTopOf="@id/tvTitle3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle4">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <RadioButton
                    android:id="@+id/rdValid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:buttonTint="@color/primary"
                    android:text="ممارسة صحيحة"
                    android:textColor="@color/black" />

                <RadioButton
                    android:id="@+id/rdInvalid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:buttonTint="@color/primary"
                    android:text="ممارسة منتهية"
                    android:textColor="@color/black" />


                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

            </RadioGroup>


            <TextView
                android:id="@+id/tvTitle3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="مرجع الحساب لأقرب مشترك"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/refLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rd2" />

            <LinearLayout
                android:id="@+id/refLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:layout_constraintBottom_toTopOf="@id/tvName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle3">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <EditText
                    android:id="@+id/etArea"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:digits="0123456789٠١٢٣٤٥٦٧٨٩"
                    android:gravity="center"
                    android:hint="المنطقة"
                    android:inputType="number"
                    android:maxLength="4"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <EditText
                    android:id="@+id/etDay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:digits="0123456789٠١٢٣٤٥٦٧٨٩"
                    android:gravity="center"
                    android:hint="اليومية"
                    android:inputType="number"
                    android:maxLength="4"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="الاسم"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/nameLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/refLayout" />

            <LinearLayout
                android:id="@+id/nameLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:layout_constraintBottom_toTopOf="@id/tvAddress"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvName">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <EditText
                    android:id="@+id/etName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:hint="الاسم بالكامل"
                    android:inputType="text"
                    android:maxLength="30"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />


            </LinearLayout>

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="العنوان"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/addressLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/nameLayout" />

            <LinearLayout
                android:id="@+id/addressLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:layout_constraintBottom_toTopOf="@id/tvNumber"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAddress">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <EditText
                    android:id="@+id/etAddress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:hint="العنوان تفصيلي"
                    android:inputType="text"
                    android:maxLength="60"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />


            </LinearLayout>

            <TextView
                android:id="@+id/tvNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:text="رقم الممارسة"
                android:textColor="@color/primary"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/numberLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/addressLayout" />

            <LinearLayout
                android:id="@+id/numberLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/my_custom_background_2"
                android:orientation="horizontal"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:layout_constraintBottom_toTopOf="@id/image"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvNumber">

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />

                <EditText
                    android:id="@+id/etNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:digits="0123456789٠١٢٣٤٥٦٧٨٩"
                    android:gravity="center"
                    android:hint="رقم الممارسة"
                    android:maxLength="10"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey" />

                <androidx.legacy.widget.Space
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5" />


            </LinearLayout>

            <ImageView
                android:id="@+id/image"
                android:layout_width="192dp"
                android:layout_height="276dp"
                android:layout_marginBottom="20dp"
                android:scaleType="centerCrop"
                android:src="@drawable/image_place_holder"
                app:layout_constraintBottom_toTopOf="@id/btnCapture"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/numberLayout" />

            <Button
                android:id="@+id/btnCapture"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:enabled="true"
                android:text="التقاط صورة"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/etNote"
                app:layout_constraintEnd_toEndOf="@id/guideline1"
                app:layout_constraintStart_toStartOf="@id/guideline2"
                app:layout_constraintTop_toBottomOf="@id/image" />


            <EditText
                android:id="@+id/etNote"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:layout_weight="1"
                android:background="@drawable/my_custom_background_2"
                android:gravity="center"
                android:hint="ملاحظة"
                android:inputType="text"
                android:maxLength="30"
                android:maxLines="1"
                android:padding="10dp"
                android:textColor="@color/black"
                android:textColorHint="@color/grey"
                app:layout_constraintBottom_toTopOf="@id/btnSave"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btnCapture" />

            <Button
                android:id="@+id/btnSave"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:enabled="false"
                android:text="@string/save"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/guideline1"
                app:layout_constraintStart_toStartOf="@id/guideline2"
                app:layout_constraintTop_toBottomOf="@id/etNote" />


            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.9" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.1" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>