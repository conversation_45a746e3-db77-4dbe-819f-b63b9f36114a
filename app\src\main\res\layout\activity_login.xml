<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg"
    android:layoutDirection="rtl"
    android:padding="15dp"
    tools:context=".ui.MainActivity">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.3" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.9" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline4"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.98" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline5"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.8" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline6"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.45" />

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:contentDescription="@string/logo"
        android:padding="25dp"
        android:src="@drawable/logo"
        app:layout_constraintBottom_toTopOf="@id/guideline1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guideline" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/user_nameLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="@string/user_name"
        android:textColorHint="@color/primary"
        app:boxBackgroundColor="@color/on_primary"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:layout_constraintBottom_toTopOf="@id/passwordLayout"
        app:layout_constraintEnd_toStartOf="@id/guideline3"
        app:layout_constraintStart_toEndOf="@id/guideline2"
        app:layout_constraintTop_toBottomOf="@id/guideline1">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/user_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lines="1"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/primary"
            android:textColorHint="@color/primary" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:hint="@string/password"
        android:textColorHint="@color/primary"
        app:boxBackgroundColor="@color/on_primary"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:layout_constraintBottom_toTopOf="@id/passwordLayout"
        app:layout_constraintEnd_toStartOf="@id/guideline3"
        app:layout_constraintStart_toEndOf="@id/guideline2"
        app:layout_constraintTop_toBottomOf="@id/guideline6">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:lines="1"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/primary"
            android:textColorHint="@color/primary" />

    </com.google.android.material.textfield.TextInputLayout>


    <Button
        android:id="@+id/btn_login"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/login"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/branch_name"
        app:layout_constraintEnd_toStartOf="@id/guideline3"
        app:layout_constraintStart_toEndOf="@id/guideline2"
        app:layout_constraintTop_toBottomOf="@id/passwordLayout" />

    <!--    <CheckBox-->
    <!--        android:id="@+id/chk_remember"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layoutDirection="rtl"-->
    <!--        android:text="@string/remember_password"-->
    <!--        android:textColor="@color/on_primary"-->
    <!--        android:textStyle="bold"-->
    <!--        android:theme="@style/Theme.checkBoxTheme"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/guideline5"-->
    <!--        app:layout_constraintEnd_toStartOf="@id/branch_name"-->
    <!--        app:layout_constraintHorizontal_chainStyle="spread_inside"-->
    <!--        app:layout_constraintStart_toStartOf="@id/guideline2"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/btn_login" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/branch_name"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:text="@string/test_branch"-->
    <!--        android:textColor="@color/on_primary"-->
    <!--        android:textStyle="bold"-->
    <!--        android:gravity="center"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/chk_remember"-->
    <!--        app:layout_constraintEnd_toStartOf="@id/guideline3"-->
    <!--        app:layout_constraintStart_toEndOf="@id/chk_remember"-->
    <!--        app:layout_constraintTop_toTopOf="@id/chk_remember" />-->
    <TextView
        android:id="@+id/branch_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/test_branch"
        android:textColor="@color/on_primary"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/guideline5"
        app:layout_constraintEnd_toStartOf="@id/guideline3"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintTop_toBottomOf="@id/btn_login" />

    <TextView
        android:id="@+id/copyright"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/shoaa_company_app_version"
        android:textColor="@color/primary"
        app:layout_constraintBottom_toTopOf="@id/guideline4"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>