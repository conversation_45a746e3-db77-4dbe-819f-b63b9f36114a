package shoaa.esmcreader;

import androidx.annotation.NonNull;

import shoaa.smartmeterreader.ReadingResponse;

/**
 * Created by Islam Darwish
 */

public class EsmcResponse extends ReadingResponse {
    private String meterModel = "0";
    private String meterId = "0";
    private String customerId = "0";
    private String cardId = "0";
    private String fwVersion = "0";
    private String activityType = "0";
    private String curentPowerFactor = "0";
    private String lastYearPowerFactor = "0";
    private String installingTechnicanCode = "0";
    private String installingDateAndTime = "0";
    private String meterDateAndTime = "0";
    private String currentTarrifInstalling = "0";
    private String currentTariffActivationDate = "0";
    private String meterStatus = "0";
    private String relayStatus = "0";
    private String batteryStatus = "0";
    private String topCoverStatus = "0";
    private String sideCoverStatus = "0";
    private String technicalCodeEvent1 = "0";
    private String eventType1 = "0";
    private String eventDate1 = "0";
    private String technicalCodeEvent2 = "0";
    private String eventType2 = "0";
    private String eventDate2 = "0";
    private String technicalCodeEvent3 = "0";
    private String eventType3 = "0";
    private String eventDate3 = "0";
    private String rechargeNumber = "0";
    private String rechargeAmount = "0";
    private String lastRechargeDateAndTime = "0";
    private String remainingCreditKw = "0";
    private String remainingCreditMoney = "0";
    private String debts = "0";
    private String totalConsumptionKw = "0";
    private String totalConsumptionMoney = "0";
    private String totalConsumptionKvar = "0";
    private String currentDemand = "0";
    private String maximumDemand = "0";
    private String maximumDemandDate = "0";
    private String instanteneousVolt = "0";
    private String instanteneousCurrentPhaseAmpere = "0";
    private String instanteneousCurrentNeutral = "0";
    private String reverseKwh = "0";
    private String unbalanceKwh = "0";
    private String currentMonthConsumptionKW = "0";
    private String currentMonthConsumptionMoney = "0";
    private String month1ConsumptionKWh = "0";
    private String month2ConsumptionKWh = "0";
    private String month3ConsumptionKWh = "0";
    private String month4ConsumptionKWh = "0";
    private String month5ConsumptionKWh = "0";
    private String month6ConsumptionKWh = "0";
    private String month7ConsumptionKWh = "0";
    private String month8ConsumptionKWh = "0";
    private String month9ConsumptionKWh = "0";
    private String month10ConsumptionKWh = "0";
    private String month11ConsumptionKWh = "0";
    private String month12ConsumptionKWh = "0";
    private String month1ConsumptionMoney = "0";
    private String month2ConsumptionMoney = "0";
    private String month3ConsumptionMoney = "0";
    private String month4ConsumptionMoney = "0";
    private String month5ConsumptionMoney = "0";
    private String month6ConsumptionMoney = "0";
    private String month7ConsumptionMoney = "0";
    private String month8ConsumptionMoney = "0";
    private String month9ConsumptionMoney = "0";
    private String month10ConsumptionMoney = "0";
    private String month11ConsumptionMoney = "0";
    private String month12ConsumptionMoney = "0";
    private String maximDemandMonth1 = "0";
    private String maximDemandMonth2 = "0";
    private String maximDemandMonth3 = "0";
    private String maximDemandMonth4 = "0";
    private String maximDemandMonth5 = "0";
    private String maximDemandMonth6 = "0";
    private String maximDemandMonth7 = "0";
    private String maximDemandMonth8 = "0";
    private String maximDemandMonth9 = "0";
    private String maximDemandMonth10 = "0";
    private String maximDemandMonth11 = "0";
    private String maximDemandMonth12 = "0";
    private String maximDemandMonth1Date = "0";
    private String maximDemandMonth2Date = "0";
    private String maximDemandMonth3Date = "0";
    private String maximDemandMonth4Date = "0";
    private String maximDemandMonth5Date = "0";
    private String maximDemandMonth6Date = "0";
    private String maximDemandMonth7Date = "0";
    private String maximDemandMonth8Date = "0";
    private String maximDemandMonth9Date = "0";
    private String maximDemandMonth10Date = "0";
    private String maximDemandMonth11Date = "0";
    private String maximDemandMonth12Date = "0";
    private String month1ConsumptionKvar = "0";
    private String month2ConsumptionKvar = "0";
    private String month3ConsumptionKvar = "0";
    private String month4ConsumptionKvar = "0";
    private String month5ConsumptionKvar = "0";
    private String month6ConsumptionKvar = "0";
    private String month7ConsumptionKvar = "0";
    private String month8ConsumptionKvar = "0";
    private String month9ConsumptionKvar = "0";
    private String month10ConsumptionKvar = "0";
    private String month11ConsumptionKvar = "0";
    private String month12ConsumptionKvar = "0";
    private String meterType = "0";

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public String getMeterId() {
        return meterId;
    }

    public void setMeterId(String meterId) {
        if (meterId != null && !meterId.isEmpty() && !meterId.equalsIgnoreCase("null"))
            this.meterId = meterId.replace("\t", "_");
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        if (customerId != null && !customerId.isEmpty() && !customerId.equalsIgnoreCase("null"))
            this.customerId = customerId.replace("\t", "_");
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        if (cardId != null && !cardId.isEmpty() && !cardId.equalsIgnoreCase("null"))
            this.cardId = cardId.replace("\t", "_");
    }

    public String getFwVersion() {
        return fwVersion;
    }

    public void setFwVersion(String fwVersion) {
        if (fwVersion != null && !fwVersion.isEmpty() && !fwVersion.equalsIgnoreCase("null"))
            this.fwVersion = fwVersion.replace("\t", "_");
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        if (activityType != null && !activityType.isEmpty() && !activityType.equalsIgnoreCase("null"))
            this.activityType = activityType.replace("\t", "_");
    }

    public String getCurentPowerFactor() {
        return curentPowerFactor;
    }

    public void setCurentPowerFactor(String curentPowerFactor) {
        if (curentPowerFactor != null && !curentPowerFactor.isEmpty() && !curentPowerFactor.equalsIgnoreCase("null"))
            this.curentPowerFactor = curentPowerFactor.replace("\t", "_");
    }

    public String getLastYearPowerFactor() {
        return lastYearPowerFactor;
    }

    public void setLastYearPowerFactor(String lastYearPowerFactor) {
        if (lastYearPowerFactor != null && !lastYearPowerFactor.isEmpty() && !lastYearPowerFactor.equalsIgnoreCase("null"))
            this.lastYearPowerFactor = lastYearPowerFactor.replace("\t", "_");
    }

    public String getInstallingTechnicanCode() {
        return installingTechnicanCode;
    }

    public void setInstallingTechnicanCode(String installingTechnicanCode) {
        if (installingTechnicanCode != null && !installingTechnicanCode.isEmpty() && !installingTechnicanCode.equalsIgnoreCase("null"))
            this.installingTechnicanCode = installingTechnicanCode.replace("\t", "_");
    }

    public String getInstallingDateAndTime() {
        return installingDateAndTime;
    }

    public void setInstallingDateAndTime(String installingDateAndTime) {
        if (installingDateAndTime != null && !installingDateAndTime.isEmpty() && !installingDateAndTime.equalsIgnoreCase("null"))
            this.installingDateAndTime = installingDateAndTime.replace("\t", "_");
    }

    public String getMeterDateAndTime() {
        return meterDateAndTime;
    }

    public void setMeterDateAndTime(String meterDateAndTime) {
        if (meterDateAndTime != null && !meterDateAndTime.isEmpty() && !meterDateAndTime.equalsIgnoreCase("null"))
            this.meterDateAndTime = meterDateAndTime.replace("\t", "_");
    }

    public String getCurrentTarrifInstalling() {
        return currentTarrifInstalling;
    }

    public void setCurrentTarrifInstalling(String currentTarrifInstalling) {
        if (currentTarrifInstalling != null && !currentTarrifInstalling.isEmpty() && !currentTarrifInstalling.equalsIgnoreCase("null"))
            this.currentTarrifInstalling = currentTarrifInstalling.replace("\t", "_");
    }

    public String getCurrentTariffActivationDate() {
        return currentTariffActivationDate;
    }

    public void setCurrentTariffActivationDate(String currentTariffActivationDate) {
        if (currentTariffActivationDate != null && !currentTariffActivationDate.isEmpty() && !currentTariffActivationDate.equalsIgnoreCase("null"))
            this.currentTariffActivationDate = currentTariffActivationDate.replace("\t", "_");
    }

    public String getMeterStatus() {
        return meterStatus;
    }

    public void setMeterStatus(String meterStatus) {
        if (meterStatus != null && !meterStatus.isEmpty() && !meterStatus.equalsIgnoreCase("null"))
            this.meterStatus = meterStatus.replace("\t", "_");
    }

    public String getRelayStatus() {
        return relayStatus;
    }

    public void setRelayStatus(String relayStatus) {
        if (relayStatus != null && !relayStatus.isEmpty() && !relayStatus.equalsIgnoreCase("null"))
            this.relayStatus = relayStatus.replace("\t", "_");
    }

    public String getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(String batteryStatus) {
        if (batteryStatus != null && !batteryStatus.isEmpty() && !batteryStatus.equalsIgnoreCase("null"))
            this.batteryStatus = batteryStatus.replace("\t", "_");
    }

    public String getTopCoverStatus() {
        return topCoverStatus;
    }

    public void setTopCoverStatus(String topCoverStatus) {
        if (topCoverStatus != null && !topCoverStatus.isEmpty() && !topCoverStatus.equalsIgnoreCase("null"))
            this.topCoverStatus = topCoverStatus.replace("\t", "_");
    }

    public String getSideCoverStatus() {
        return sideCoverStatus;
    }

    public void setSideCoverStatus(String sideCoverStatus) {
        if (sideCoverStatus != null && !sideCoverStatus.isEmpty() && !sideCoverStatus.equalsIgnoreCase("null"))
            this.sideCoverStatus = sideCoverStatus.replace("\t", "_");
    }

    public String getTechnicalCodeEvent1() {
        return technicalCodeEvent1;
    }

    public void setTechnicalCodeEvent1(String technicalCodeEvent1) {
        if (technicalCodeEvent1 != null && !technicalCodeEvent1.isEmpty() && !technicalCodeEvent1.equalsIgnoreCase("null"))
            this.technicalCodeEvent1 = technicalCodeEvent1.replace("\t", "_");
    }

    public String getEventType1() {
        return eventType1;
    }

    public void setEventType1(String eventType1) {
        if (eventType1 != null && !eventType1.isEmpty() && !eventType1.equalsIgnoreCase("null"))
            this.eventType1 = eventType1.replace("\t", "_");
    }

    public String getEventDate1() {
        return eventDate1;
    }

    public void setEventDate1(String eventDate1) {
        if (eventDate1 != null && !eventDate1.isEmpty() && !eventDate1.equalsIgnoreCase("null"))
            this.eventDate1 = eventDate1.replace("\t", "_");
    }

    public String getTechnicalCodeEvent2() {
        return technicalCodeEvent2;
    }

    public void setTechnicalCodeEvent2(String technicalCodeEvent2) {
        if (technicalCodeEvent2 != null && !technicalCodeEvent2.isEmpty() && !technicalCodeEvent2.equalsIgnoreCase("null"))
            this.technicalCodeEvent2 = technicalCodeEvent2.replace("\t", "_");
    }

    public String getEventType2() {
        return eventType2;
    }

    public void setEventType2(String eventType2) {
        if (eventType2 != null && !eventType2.isEmpty() && !eventType2.equalsIgnoreCase("null"))
            this.eventType2 = eventType2.replace("\t", "_");
    }

    public String getEventDate2() {
        return eventDate2;
    }

    public void setEventDate2(String eventDate2) {
        if (eventDate2 != null && !eventDate2.isEmpty() && !eventDate2.equalsIgnoreCase("null"))
            this.eventDate2 = eventDate2.replace("\t", "_");
    }

    public String getTechnicalCodeEvent3() {
        return technicalCodeEvent3;
    }

    public void setTechnicalCodeEvent3(String technicalCodeEvent3) {
        if (technicalCodeEvent3 != null && !technicalCodeEvent3.isEmpty() && !technicalCodeEvent3.equalsIgnoreCase("null"))
            this.technicalCodeEvent3 = technicalCodeEvent3.replace("\t", "_");
    }

    public String getEventType3() {
        return eventType3;
    }

    public void setEventType3(String eventType3) {
        if (eventType3 != null && !eventType3.isEmpty() && !eventType3.equalsIgnoreCase("null"))
            this.eventType3 = eventType3.replace("\t", "_");
    }

    public String getEventDate3() {
        return eventDate3;
    }

    public void setEventDate3(String eventDate3) {
        if (eventDate3 != null && !eventDate3.isEmpty() && !eventDate3.equalsIgnoreCase("null"))
            this.eventDate3 = eventDate3.replace("\t", "_");
    }

    public String getRechargeNumber() {
        return rechargeNumber;
    }

    public void setRechargeNumber(String rechargeNumber) {
        if (rechargeNumber != null && !rechargeNumber.isEmpty() && !rechargeNumber.equalsIgnoreCase("null"))
            this.rechargeNumber = rechargeNumber.replace("\t", "_");
    }

    public String getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(String rechargeAmount) {
        if (rechargeAmount != null && !rechargeAmount.isEmpty() && !rechargeAmount.equalsIgnoreCase("null"))
            this.rechargeAmount = rechargeAmount.replace("\t", "_");
    }

    public String getLastRechargeDateAndTime() {
        return lastRechargeDateAndTime;
    }

    public void setLastRechargeDateAndTime(String lastRechargeDateAndTime) {
        if (lastRechargeDateAndTime != null && !lastRechargeDateAndTime.isEmpty() && !lastRechargeDateAndTime.equalsIgnoreCase("null"))
            this.lastRechargeDateAndTime = lastRechargeDateAndTime.replace("\t", "_");
    }

    public String getRemainingCreditKw() {
        return remainingCreditKw;
    }

    public void setRemainingCreditKw(String remainingCreditKw) {
        if (remainingCreditKw != null && !remainingCreditKw.isEmpty() && !remainingCreditKw.equalsIgnoreCase("null"))
            this.remainingCreditKw = remainingCreditKw.replace("\t", "_");
    }

    public String getRemainingCreditMoney() {
        return remainingCreditMoney;
    }

    public void setRemainingCreditMoney(String remainingCreditMoney) {
        if (remainingCreditMoney != null && !remainingCreditMoney.isEmpty() && !remainingCreditMoney.equalsIgnoreCase("null"))
            this.remainingCreditMoney = remainingCreditMoney.replace("\t", "_");
    }

    public String getDebts() {
        return debts;
    }

    public void setDebts(String debts) {
        if (debts != null && !debts.isEmpty() && !debts.equalsIgnoreCase("null"))
            this.debts = debts.replace("\t", "_");
    }

    public String getTotalConsumptionKw() {
        return totalConsumptionKw;
    }

    public void setTotalConsumptionKw(String totalConsumptionKw) {
        if (totalConsumptionKw != null && !totalConsumptionKw.isEmpty() && !totalConsumptionKw.equalsIgnoreCase("null"))
            this.totalConsumptionKw = totalConsumptionKw.replace("\t", "_");
    }

    public String getTotalConsumptionMoney() {
        return totalConsumptionMoney;
    }

    public void setTotalConsumptionMoney(String totalConsumptionMoney) {
        if (totalConsumptionMoney != null && !totalConsumptionMoney.isEmpty() && !totalConsumptionMoney.equalsIgnoreCase("null"))
            this.totalConsumptionMoney = totalConsumptionMoney.replace("\t", "_");
    }

    public String getTotalConsumptionKvar() {
        return totalConsumptionKvar;
    }

    public void setTotalConsumptionKvar(String totalConsumptionKvar) {
        if (totalConsumptionKvar != null && !totalConsumptionKvar.isEmpty() && !totalConsumptionKvar.equalsIgnoreCase("null"))
            this.totalConsumptionKvar = totalConsumptionKvar.replace("\t", "_");
    }

    public String getCurrentDemand() {
        return currentDemand;
    }

    public void setCurrentDemand(String currentDemand) {
        if (currentDemand != null && !currentDemand.isEmpty() && !currentDemand.equalsIgnoreCase("null"))
            this.currentDemand = currentDemand.replace("\t", "_");
    }

    public String getMaximumDemand() {
        return maximumDemand;
    }

    public void setMaximumDemand(String maximumDemand) {
        if (maximumDemand != null && !maximumDemand.isEmpty() && !maximumDemand.equalsIgnoreCase("null"))
            this.maximumDemand = maximumDemand.replace("\t", "_");
    }

    public String getMaximumDemandDate() {
        return maximumDemandDate;
    }

    public void setMaximumDemandDate(String maximumDemandDate) {
        if (maximumDemandDate != null && !maximumDemandDate.isEmpty() && !maximumDemandDate.equalsIgnoreCase("null"))
            this.maximumDemandDate = maximumDemandDate.replace("\t", "_");
    }

    public String getInstanteneousVolt() {
        return instanteneousVolt;
    }

    public void setInstanteneousVolt(String instanteneousVolt) {
        if (instanteneousVolt != null && !instanteneousVolt.isEmpty() && !instanteneousVolt.equalsIgnoreCase("null"))
            this.instanteneousVolt = instanteneousVolt.replace("\t", "_");
    }

    public String getInstanteneousCurrentPhaseAmpere() {
        return instanteneousCurrentPhaseAmpere;
    }

    public void setInstanteneousCurrentPhaseAmpere(String instanteneousCurrentPhaseAmpere) {
        if (instanteneousCurrentPhaseAmpere != null && !instanteneousCurrentPhaseAmpere.isEmpty() && !instanteneousCurrentPhaseAmpere.equalsIgnoreCase("null"))
            this.instanteneousCurrentPhaseAmpere = instanteneousCurrentPhaseAmpere.replace("\t", "_");
    }

    public String getInstanteneousCurrentNeutral() {
        return instanteneousCurrentNeutral;
    }

    public void setInstanteneousCurrentNeutral(String instanteneousCurrentNeutral) {
        if (instanteneousCurrentNeutral != null && !instanteneousCurrentNeutral.isEmpty() && !instanteneousCurrentNeutral.equalsIgnoreCase("null"))
            this.instanteneousCurrentNeutral = instanteneousCurrentNeutral.replace("\t", "_");
    }

    public String getReverseKwh() {
        return reverseKwh;
    }

    public void setReverseKwh(String reverseKwh) {
        if (reverseKwh != null && !reverseKwh.isEmpty() && !reverseKwh.equalsIgnoreCase("null"))
            this.reverseKwh = reverseKwh.replace("\t", "_");
    }

    public String getUnbalanceKwh() {
        return unbalanceKwh;
    }

    public void setUnbalanceKwh(String unbalanceKwh) {
        if (unbalanceKwh != null && !unbalanceKwh.isEmpty() && !unbalanceKwh.equalsIgnoreCase("null"))
            this.unbalanceKwh = unbalanceKwh.replace("\t", "_");
    }

    public String getCurrentMonthConsumptionKW() {
        return currentMonthConsumptionKW;
    }

    public void setCurrentMonthConsumptionKW(String currentMonthConsumptionKW) {
        if (currentMonthConsumptionKW != null && !currentMonthConsumptionKW.isEmpty() && !currentMonthConsumptionKW.equalsIgnoreCase("null"))
            this.currentMonthConsumptionKW = currentMonthConsumptionKW.replace("\t", "_");
    }

    public String getCurrentMonthConsumptionMoney() {
        return currentMonthConsumptionMoney;
    }

    public void setCurrentMonthConsumptionMoney(String currentMonthConsumptionMoney) {
        if (currentMonthConsumptionMoney != null && !currentMonthConsumptionMoney.isEmpty() && !currentMonthConsumptionMoney.equalsIgnoreCase("null"))
            this.currentMonthConsumptionMoney = currentMonthConsumptionMoney.replace("\t", "_");
    }

    public String getMonth1ConsumptionKWh() {
        return month1ConsumptionKWh;
    }

    public void setMonth1ConsumptionKWh(String month1ConsumptionKWh) {
        if (month1ConsumptionKWh != null && !month1ConsumptionKWh.isEmpty() && !month1ConsumptionKWh.equalsIgnoreCase("null"))
            this.month1ConsumptionKWh = month1ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth2ConsumptionKWh() {
        return month2ConsumptionKWh;
    }

    public void setMonth2ConsumptionKWh(String month2ConsumptionKWh) {
        if (month2ConsumptionKWh != null && !month2ConsumptionKWh.isEmpty() && !month2ConsumptionKWh.equalsIgnoreCase("null"))
            this.month2ConsumptionKWh = month2ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth3ConsumptionKWh() {
        return month3ConsumptionKWh;
    }

    public void setMonth3ConsumptionKWh(String month3ConsumptionKWh) {
        if (month3ConsumptionKWh != null && !month3ConsumptionKWh.isEmpty() && !month3ConsumptionKWh.equalsIgnoreCase("null"))
            this.month3ConsumptionKWh = month3ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth4ConsumptionKWh() {
        return month4ConsumptionKWh;
    }

    public void setMonth4ConsumptionKWh(String month4ConsumptionKWh) {
        if (month4ConsumptionKWh != null && !month4ConsumptionKWh.isEmpty() && !month4ConsumptionKWh.equalsIgnoreCase("null"))
            this.month4ConsumptionKWh = month4ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth5ConsumptionKWh() {
        return month5ConsumptionKWh;
    }

    public void setMonth5ConsumptionKWh(String month5ConsumptionKWh) {
        if (month5ConsumptionKWh != null && !month5ConsumptionKWh.isEmpty() && !month5ConsumptionKWh.equalsIgnoreCase("null"))
            this.month5ConsumptionKWh = month5ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth6ConsumptionKWh() {
        return month6ConsumptionKWh;
    }

    public void setMonth6ConsumptionKWh(String month6ConsumptionKWh) {
        if (month6ConsumptionKWh != null && !month6ConsumptionKWh.isEmpty() && !month6ConsumptionKWh.equalsIgnoreCase("null"))
            this.month6ConsumptionKWh = month6ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth7ConsumptionKWh() {
        return month7ConsumptionKWh;
    }

    public void setMonth7ConsumptionKWh(String month7ConsumptionKWh) {
        if (month7ConsumptionKWh != null && !month7ConsumptionKWh.isEmpty() && !month7ConsumptionKWh.equalsIgnoreCase("null"))
            this.month7ConsumptionKWh = month7ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth8ConsumptionKWh() {
        return month8ConsumptionKWh;
    }

    public void setMonth8ConsumptionKWh(String month8ConsumptionKWh) {
        if (month8ConsumptionKWh != null && !month8ConsumptionKWh.isEmpty() && !month8ConsumptionKWh.equalsIgnoreCase("null"))
            this.month8ConsumptionKWh = month8ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth9ConsumptionKWh() {
        return month9ConsumptionKWh;
    }

    public void setMonth9ConsumptionKWh(String month9ConsumptionKWh) {
        if (month9ConsumptionKWh != null && !month9ConsumptionKWh.isEmpty() && !month9ConsumptionKWh.equalsIgnoreCase("null"))
            this.month9ConsumptionKWh = month9ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth10ConsumptionKWh() {
        return month10ConsumptionKWh;
    }

    public void setMonth10ConsumptionKWh(String month10ConsumptionKWh) {
        if (month10ConsumptionKWh != null && !month10ConsumptionKWh.isEmpty() && !month10ConsumptionKWh.equalsIgnoreCase("null"))
            this.month10ConsumptionKWh = month10ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth11ConsumptionKWh() {
        return month11ConsumptionKWh;
    }

    public void setMonth11ConsumptionKWh(String month11ConsumptionKWh) {
        if (month11ConsumptionKWh != null && !month11ConsumptionKWh.isEmpty() && !month11ConsumptionKWh.equalsIgnoreCase("null"))
            this.month11ConsumptionKWh = month11ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth12ConsumptionKWh() {
        return month12ConsumptionKWh;
    }

    public void setMonth12ConsumptionKWh(String month12ConsumptionKWh) {
        if (month12ConsumptionKWh != null && !month12ConsumptionKWh.isEmpty() && !month12ConsumptionKWh.equalsIgnoreCase("null"))
            this.month12ConsumptionKWh = month12ConsumptionKWh.replace("\t", "_");
    }

    public String getMonth1ConsumptionMoney() {
        return month1ConsumptionMoney;
    }

    public void setMonth1ConsumptionMoney(String month1ConsumptionMoney) {
        if (month1ConsumptionMoney != null && !month1ConsumptionMoney.isEmpty() && !month1ConsumptionMoney.equalsIgnoreCase("null"))
            this.month1ConsumptionMoney = month1ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth2ConsumptionMoney() {
        return month2ConsumptionMoney;
    }

    public void setMonth2ConsumptionMoney(String month2ConsumptionMoney) {
        if (month2ConsumptionMoney != null && !month2ConsumptionMoney.isEmpty() && !month2ConsumptionMoney.equalsIgnoreCase("null"))
            this.month2ConsumptionMoney = month2ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth3ConsumptionMoney() {
        return month3ConsumptionMoney;
    }

    public void setMonth3ConsumptionMoney(String month3ConsumptionMoney) {
        if (month3ConsumptionMoney != null && !month3ConsumptionMoney.isEmpty() && !month3ConsumptionMoney.equalsIgnoreCase("null"))
            this.month3ConsumptionMoney = month3ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth4ConsumptionMoney() {
        return month4ConsumptionMoney;
    }

    public void setMonth4ConsumptionMoney(String month4ConsumptionMoney) {
        if (month4ConsumptionMoney != null && !month4ConsumptionMoney.isEmpty() && !month4ConsumptionMoney.equalsIgnoreCase("null"))
            this.month4ConsumptionMoney = month4ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth5ConsumptionMoney() {
        return month5ConsumptionMoney;
    }

    public void setMonth5ConsumptionMoney(String month5ConsumptionMoney) {
        if (month5ConsumptionMoney != null && !month5ConsumptionMoney.isEmpty() && !month5ConsumptionMoney.equalsIgnoreCase("null"))
            this.month5ConsumptionMoney = month5ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth6ConsumptionMoney() {
        return month6ConsumptionMoney;
    }

    public void setMonth6ConsumptionMoney(String month6ConsumptionMoney) {
        if (month6ConsumptionMoney != null && !month6ConsumptionMoney.isEmpty() && !month6ConsumptionMoney.equalsIgnoreCase("null"))
            this.month6ConsumptionMoney = month6ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth7ConsumptionMoney() {
        return month7ConsumptionMoney;
    }

    public void setMonth7ConsumptionMoney(String month7ConsumptionMoney) {
        if (month7ConsumptionMoney != null && !month7ConsumptionMoney.isEmpty() && !month7ConsumptionMoney.equalsIgnoreCase("null"))
            this.month7ConsumptionMoney = month7ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth8ConsumptionMoney() {
        return month8ConsumptionMoney;
    }

    public void setMonth8ConsumptionMoney(String month8ConsumptionMoney) {
        if (month8ConsumptionMoney != null && !month8ConsumptionMoney.isEmpty() && !month8ConsumptionMoney.equalsIgnoreCase("null"))
            this.month8ConsumptionMoney = month8ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth9ConsumptionMoney() {
        return month9ConsumptionMoney;
    }

    public void setMonth9ConsumptionMoney(String month9ConsumptionMoney) {
        if (month9ConsumptionMoney != null && !month9ConsumptionMoney.isEmpty() && !month9ConsumptionMoney.equalsIgnoreCase("null"))
            this.month9ConsumptionMoney = month9ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth10ConsumptionMoney() {
        return month10ConsumptionMoney;
    }

    public void setMonth10ConsumptionMoney(String month10ConsumptionMoney) {
        if (month10ConsumptionMoney != null && !month10ConsumptionMoney.isEmpty() && !month10ConsumptionMoney.equalsIgnoreCase("null"))
            this.month10ConsumptionMoney = month10ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth11ConsumptionMoney() {
        return month11ConsumptionMoney;
    }

    public void setMonth11ConsumptionMoney(String month11ConsumptionMoney) {
        if (month11ConsumptionMoney != null && !month11ConsumptionMoney.isEmpty() && !month11ConsumptionMoney.equalsIgnoreCase("null"))
            this.month11ConsumptionMoney = month11ConsumptionMoney.replace("\t", "_");
    }

    public String getMonth12ConsumptionMoney() {
        return month12ConsumptionMoney;
    }

    public void setMonth12ConsumptionMoney(String month12ConsumptionMoney) {
        if (month12ConsumptionMoney != null && !month12ConsumptionMoney.isEmpty() && !month12ConsumptionMoney.equalsIgnoreCase("null"))
            this.month12ConsumptionMoney = month12ConsumptionMoney.replace("\t", "_");
    }

    public String getMaximDemandMonth1() {
        return maximDemandMonth1;
    }

    public void setMaximDemandMonth1(String maximDemandMonth1) {
        if (maximDemandMonth1 != null && !maximDemandMonth1.isEmpty() && !maximDemandMonth1.equalsIgnoreCase("null"))
            this.maximDemandMonth1 = maximDemandMonth1.replace("\t", "_");
    }

    public String getMaximDemandMonth2() {
        return maximDemandMonth2;
    }

    public void setMaximDemandMonth2(String maximDemandMonth2) {
        if (maximDemandMonth2 != null && !maximDemandMonth2.isEmpty() && !maximDemandMonth2.equalsIgnoreCase("null"))
            this.maximDemandMonth2 = maximDemandMonth2.replace("\t", "_");
    }

    public String getMaximDemandMonth3() {
        return maximDemandMonth3;
    }

    public void setMaximDemandMonth3(String maximDemandMonth3) {
        if (maximDemandMonth3 != null && !maximDemandMonth3.isEmpty() && !maximDemandMonth3.equalsIgnoreCase("null"))
            this.maximDemandMonth3 = maximDemandMonth3.replace("\t", "_");
    }

    public String getMaximDemandMonth4() {
        return maximDemandMonth4;
    }

    public void setMaximDemandMonth4(String maximDemandMonth4) {
        if (maximDemandMonth4 != null && !maximDemandMonth4.isEmpty() && !maximDemandMonth4.equalsIgnoreCase("null"))
            this.maximDemandMonth4 = maximDemandMonth4.replace("\t", "_");
    }

    public String getMaximDemandMonth5() {
        return maximDemandMonth5;
    }

    public void setMaximDemandMonth5(String maximDemandMonth5) {
        if (maximDemandMonth5 != null && !maximDemandMonth5.isEmpty() && !maximDemandMonth5.equalsIgnoreCase("null"))
            this.maximDemandMonth5 = maximDemandMonth5.replace("\t", "_");
    }

    public String getMaximDemandMonth6() {
        return maximDemandMonth6;
    }

    public void setMaximDemandMonth6(String maximDemandMonth6) {
        if (maximDemandMonth6 != null && !maximDemandMonth6.isEmpty() && !maximDemandMonth6.equalsIgnoreCase("null"))
            this.maximDemandMonth6 = maximDemandMonth6.replace("\t", "_");
    }

    public String getMaximDemandMonth7() {
        return maximDemandMonth7;
    }

    public void setMaximDemandMonth7(String maximDemandMonth7) {
        if (maximDemandMonth7 != null && !maximDemandMonth7.isEmpty() && !maximDemandMonth7.equalsIgnoreCase("null"))
            this.maximDemandMonth7 = maximDemandMonth7.replace("\t", "_");
    }

    public String getMaximDemandMonth8() {
        return maximDemandMonth8;
    }

    public void setMaximDemandMonth8(String maximDemandMonth8) {
        if (maximDemandMonth8 != null && !maximDemandMonth8.isEmpty() && !maximDemandMonth8.equalsIgnoreCase("null"))
            this.maximDemandMonth8 = maximDemandMonth8.replace("\t", "_");
    }

    public String getMaximDemandMonth9() {
        return maximDemandMonth9;
    }

    public void setMaximDemandMonth9(String maximDemandMonth9) {
        if (maximDemandMonth9 != null && !maximDemandMonth9.isEmpty() && !maximDemandMonth9.equalsIgnoreCase("null"))
            this.maximDemandMonth9 = maximDemandMonth9.replace("\t", "_");
    }

    public String getMaximDemandMonth10() {
        return maximDemandMonth10;
    }

    public void setMaximDemandMonth10(String maximDemandMonth10) {
        if (maximDemandMonth10 != null && !maximDemandMonth10.isEmpty() && !maximDemandMonth10.equalsIgnoreCase("null"))
            this.maximDemandMonth10 = maximDemandMonth10.replace("\t", "_");
    }

    public String getMaximDemandMonth11() {
        return maximDemandMonth11;
    }

    public void setMaximDemandMonth11(String maximDemandMonth11) {
        if (maximDemandMonth11 != null && !maximDemandMonth11.isEmpty() && !maximDemandMonth11.equalsIgnoreCase("null"))
            this.maximDemandMonth11 = maximDemandMonth11.replace("\t", "_");
    }

    public String getMaximDemandMonth12() {
        return maximDemandMonth12;
    }

    public void setMaximDemandMonth12(String maximDemandMonth12) {
        if (maximDemandMonth12 != null && !maximDemandMonth12.isEmpty() && !maximDemandMonth12.equalsIgnoreCase("null"))
            this.maximDemandMonth12 = maximDemandMonth12.replace("\t", "_");
    }

    public String getMaximDemandMonth1Date() {
        return maximDemandMonth1Date;
    }

    public void setMaximDemandMonth1Date(String maximDemandMonth1Date) {
        if (maximDemandMonth1Date != null && !maximDemandMonth1Date.isEmpty() && !maximDemandMonth1Date.equalsIgnoreCase("null"))
            this.maximDemandMonth1Date = maximDemandMonth1Date.replace("\t", "_");
    }

    public String getMaximDemandMonth2Date() {
        return maximDemandMonth2Date;
    }

    public void setMaximDemandMonth2Date(String maximDemandMonth2Date) {
        if (maximDemandMonth2Date != null && !maximDemandMonth2Date.isEmpty() && !maximDemandMonth2Date.equalsIgnoreCase("null"))
            this.maximDemandMonth2Date = maximDemandMonth2Date.replace("\t", "_");
    }

    public String getMaximDemandMonth3Date() {
        return maximDemandMonth3Date;
    }

    public void setMaximDemandMonth3Date(String maximDemandMonth3Date) {
        if (maximDemandMonth3Date != null && !maximDemandMonth3Date.isEmpty() && !maximDemandMonth3Date.equalsIgnoreCase("null"))
            this.maximDemandMonth3Date = maximDemandMonth3Date.replace("\t", "_");
    }

    public String getMaximDemandMonth4Date() {
        return maximDemandMonth4Date;
    }

    public void setMaximDemandMonth4Date(String maximDemandMonth4Date) {
        if (maximDemandMonth4Date != null && !maximDemandMonth4Date.isEmpty() && !maximDemandMonth4Date.equalsIgnoreCase("null"))
            this.maximDemandMonth4Date = maximDemandMonth4Date.replace("\t", "_");
    }

    public String getMaximDemandMonth5Date() {
        return maximDemandMonth5Date;
    }

    public void setMaximDemandMonth5Date(String maximDemandMonth5Date) {
        if (maximDemandMonth5Date != null && !maximDemandMonth5Date.isEmpty() && !maximDemandMonth5Date.equalsIgnoreCase("null"))
            this.maximDemandMonth5Date = maximDemandMonth5Date.replace("\t", "_");
    }

    public String getMaximDemandMonth6Date() {
        return maximDemandMonth6Date;
    }

    public void setMaximDemandMonth6Date(String maximDemandMonth6Date) {
        if (maximDemandMonth6Date != null && !maximDemandMonth6Date.isEmpty() && !maximDemandMonth6Date.equalsIgnoreCase("null"))
            this.maximDemandMonth6Date = maximDemandMonth6Date.replace("\t", "_");
    }

    public String getMaximDemandMonth7Date() {
        return maximDemandMonth7Date;
    }

    public void setMaximDemandMonth7Date(String maximDemandMonth7Date) {
        if (maximDemandMonth7Date != null && !maximDemandMonth7Date.isEmpty() && !maximDemandMonth7Date.equalsIgnoreCase("null"))
            this.maximDemandMonth7Date = maximDemandMonth7Date.replace("\t", "_");
    }

    public String getMaximDemandMonth8Date() {
        return maximDemandMonth8Date;
    }

    public void setMaximDemandMonth8Date(String maximDemandMonth8Date) {
        if (maximDemandMonth8Date != null && !maximDemandMonth8Date.isEmpty() && !maximDemandMonth8Date.equalsIgnoreCase("null"))
            this.maximDemandMonth8Date = maximDemandMonth8Date.replace("\t", "_");
    }

    public String getMaximDemandMonth9Date() {
        return maximDemandMonth9Date;
    }

    public void setMaximDemandMonth9Date(String maximDemandMonth9Date) {
        if (maximDemandMonth9Date != null && !maximDemandMonth9Date.isEmpty() && !maximDemandMonth9Date.equalsIgnoreCase("null"))
            this.maximDemandMonth9Date = maximDemandMonth9Date.replace("\t", "_");
    }

    public String getMaximDemandMonth10Date() {
        return maximDemandMonth10Date;
    }

    public void setMaximDemandMonth10Date(String maximDemandMonth10Date) {
        if (maximDemandMonth10Date != null && !maximDemandMonth10Date.isEmpty() && !maximDemandMonth10Date.equalsIgnoreCase("null"))
            this.maximDemandMonth10Date = maximDemandMonth10Date.replace("\t", "_");
    }

    public String getMaximDemandMonth11Date() {
        return maximDemandMonth11Date;
    }

    public void setMaximDemandMonth11Date(String maximDemandMonth11Date) {
        if (maximDemandMonth11Date != null && !maximDemandMonth11Date.isEmpty() && !maximDemandMonth11Date.equalsIgnoreCase("null"))
            this.maximDemandMonth11Date = maximDemandMonth11Date.replace("\t", "_");
    }

    public String getMaximDemandMonth12Date() {
        return maximDemandMonth12Date;
    }

    public void setMaximDemandMonth12Date(String maximDemandMonth12Date) {
        if (maximDemandMonth12Date != null && !maximDemandMonth12Date.isEmpty() && !maximDemandMonth12Date.equalsIgnoreCase("null"))
            this.maximDemandMonth12Date = maximDemandMonth12Date.replace("\t", "_");
    }

    public String getMonth1ConsumptionKvar() {
        return month1ConsumptionKvar;
    }

    public void setMonth1ConsumptionKvar(String month1ConsumptionKvar) {
        if (month1ConsumptionKvar != null && !month1ConsumptionKvar.isEmpty() && !month1ConsumptionKvar.equalsIgnoreCase("null"))
            this.month1ConsumptionKvar = month1ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth2ConsumptionKvar() {
        return month2ConsumptionKvar;
    }

    public void setMonth2ConsumptionKvar(String month2ConsumptionKvar) {
        if (month2ConsumptionKvar != null && !month2ConsumptionKvar.isEmpty() && !month2ConsumptionKvar.equalsIgnoreCase("null"))
            this.month2ConsumptionKvar = month2ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth3ConsumptionKvar() {
        return month3ConsumptionKvar;
    }

    public void setMonth3ConsumptionKvar(String month3ConsumptionKvar) {
        if (month3ConsumptionKvar != null && !month3ConsumptionKvar.isEmpty() && !month3ConsumptionKvar.equalsIgnoreCase("null"))
            this.month3ConsumptionKvar = month3ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth4ConsumptionKvar() {
        return month4ConsumptionKvar;
    }

    public void setMonth4ConsumptionKvar(String month4ConsumptionKvar) {
        if (month4ConsumptionKvar != null && !month4ConsumptionKvar.isEmpty() && !month4ConsumptionKvar.equalsIgnoreCase("null"))
            this.month4ConsumptionKvar = month4ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth5ConsumptionKvar() {
        return month5ConsumptionKvar;
    }

    public void setMonth5ConsumptionKvar(String month5ConsumptionKvar) {
        if (month5ConsumptionKvar != null && !month5ConsumptionKvar.isEmpty() && !month5ConsumptionKvar.equalsIgnoreCase("null"))
            this.month5ConsumptionKvar = month5ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth6ConsumptionKvar() {
        return month6ConsumptionKvar;
    }

    public void setMonth6ConsumptionKvar(String month6ConsumptionKvar) {
        if (month6ConsumptionKvar != null && !month6ConsumptionKvar.isEmpty() && !month6ConsumptionKvar.equalsIgnoreCase("null"))
            this.month6ConsumptionKvar = month6ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth7ConsumptionKvar() {
        return month7ConsumptionKvar;
    }

    public void setMonth7ConsumptionKvar(String month7ConsumptionKvar) {
        if (month7ConsumptionKvar != null && !month7ConsumptionKvar.isEmpty() && !month7ConsumptionKvar.equalsIgnoreCase("null"))
            this.month7ConsumptionKvar = month7ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth8ConsumptionKvar() {
        return month8ConsumptionKvar;
    }

    public void setMonth8ConsumptionKvar(String month8ConsumptionKvar) {
        if (month8ConsumptionKvar != null && !month8ConsumptionKvar.isEmpty() && !month8ConsumptionKvar.equalsIgnoreCase("null"))
            this.month8ConsumptionKvar = month8ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth9ConsumptionKvar() {
        return month9ConsumptionKvar;
    }

    public void setMonth9ConsumptionKvar(String month9ConsumptionKvar) {
        if (month9ConsumptionKvar != null && !month9ConsumptionKvar.isEmpty() && !month9ConsumptionKvar.equalsIgnoreCase("null"))
            this.month9ConsumptionKvar = month9ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth10ConsumptionKvar() {
        return month10ConsumptionKvar;
    }

    public void setMonth10ConsumptionKvar(String month10ConsumptionKvar) {
        if (month10ConsumptionKvar != null && !month10ConsumptionKvar.isEmpty() && !month10ConsumptionKvar.equalsIgnoreCase("null"))
            this.month10ConsumptionKvar = month10ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth11ConsumptionKvar() {
        return month11ConsumptionKvar;
    }

    public void setMonth11ConsumptionKvar(String month11ConsumptionKvar) {
        if (month11ConsumptionKvar != null && !month11ConsumptionKvar.isEmpty() && !month11ConsumptionKvar.equalsIgnoreCase("null"))
            this.month11ConsumptionKvar = month11ConsumptionKvar.replace("\t", "_");
    }

    public String getMonth12ConsumptionKvar() {
        return month12ConsumptionKvar;
    }

    public void setMonth12ConsumptionKvar(String month12ConsumptionKvar) {
        if (month12ConsumptionKvar != null && !month12ConsumptionKvar.isEmpty() && !month12ConsumptionKvar.equalsIgnoreCase("null"))
            this.month12ConsumptionKvar = month12ConsumptionKvar.replace("\t", "_");
    }

    public String getMeterModel() {
        return meterModel;
    }

    public void setMeterModel(String meterModel) {
        this.meterModel = meterModel;
    }

    @NonNull
    @Override
    public String toString() {
        return "EsmcResponse : " + '\n' +
                "meterType : " + meterType + '\n' +
                "meterId : " + meterId + '\n' +
                "customerId : " + customerId + '\n' +
                "cardId : " + cardId + '\n' +
                "fwVersion : " + fwVersion + '\n' +
                "activityType : " + activityType + '\n' +
                "curentPowerFactor : " + curentPowerFactor + '\n' +
                "lastYearPowerFactor : " + lastYearPowerFactor + '\n' +
                "installingTechnicanCode : " + installingTechnicanCode + '\n' +
                "installingDateAndTime : " + installingDateAndTime + '\n' +
                "meterDateAndTime : " + meterDateAndTime + '\n' +
                "currentTarrifInstalling : " + currentTarrifInstalling + '\n' +
                "currentTariffActivationDate : " + currentTariffActivationDate + '\n' +
                "meterStatus : " + meterStatus + '\n' +
                "relayStatus : " + relayStatus + '\n' +
                "batteryStatus : " + batteryStatus + '\n' +
                "topCoverStatus : " + topCoverStatus + '\n' +
                "sideCoverStatus : " + sideCoverStatus + '\n' +
                "technicalCodeEvent1 : " + technicalCodeEvent1 + '\n' +
                "eventType1 : " + eventType1 + '\n' +
                "eventDate1 : " + eventDate1 + '\n' +
                "technicalCodeEvent2 : " + technicalCodeEvent2 + '\n' +
                "eventType2 : " + eventType2 + '\n' +
                "eventDate2 : " + eventDate2 + '\n' +
                "technicalCodeEvent3 : " + technicalCodeEvent3 + '\n' +
                "eventType3 : " + eventType3 + '\n' +
                "eventDate3 : " + eventDate3 + '\n' +
                "rechargeNumber : " + rechargeNumber + '\n' +
                "rechargeAmount : " + rechargeAmount + '\n' +
                "lastRechargeDateAndTime : " + lastRechargeDateAndTime + '\n' +
                "remainingCreditKw : " + remainingCreditKw + '\n' +
                "remainingCreditMoney : " + remainingCreditMoney + '\n' +
                "debts : " + debts + '\n' +
                "totalConsumptionKw : " + totalConsumptionKw + '\n' +
                "totalConsumptionMoney : " + totalConsumptionMoney + '\n' +
                "totalConsumptionKvar : " + totalConsumptionKvar + '\n' +
                "currentDemand : " + currentDemand + '\n' +
                "maximumDemand : " + maximumDemand + '\n' +
                "maximumDemandDate : " + maximumDemandDate + '\n' +
                "instanteneousVolt : " + instanteneousVolt + '\n' +
                "instanteneousCurrentPhaseAmpere : " + instanteneousCurrentPhaseAmpere + '\n' +
                "instanteneousCurrentNeutral : " + instanteneousCurrentNeutral + '\n' +
                "reverseKwh : " + reverseKwh + '\n' +
                "unbalanceKwh : " + unbalanceKwh + '\n' +
                "currentMonthConsumptionKW : " + currentMonthConsumptionKW + '\n' +
                "currentMonthConsumptionMoney : " + currentMonthConsumptionMoney + '\n' +
                "month1ConsumptionKWh : " + month1ConsumptionKWh + '\n' +
                "month2ConsumptionKWh : " + month2ConsumptionKWh + '\n' +
                "month3ConsumptionKWh : " + month3ConsumptionKWh + '\n' +
                "month4ConsumptionKWh : " + month4ConsumptionKWh + '\n' +
                "month5ConsumptionKWh : " + month5ConsumptionKWh + '\n' +
                "month6ConsumptionKWh : " + month6ConsumptionKWh + '\n' +
                "month7ConsumptionKWh : " + month7ConsumptionKWh + '\n' +
                "month8ConsumptionKWh : " + month8ConsumptionKWh + '\n' +
                "month9ConsumptionKWh : " + month9ConsumptionKWh + '\n' +
                "month10ConsumptionKWh : " + month10ConsumptionKWh + '\n' +
                "month11ConsumptionKWh : " + month11ConsumptionKWh + '\n' +
                "month12ConsumptionKWh : " + month12ConsumptionKWh + '\n' +
                "month1ConsumptionMoney : " + month1ConsumptionMoney + '\n' +
                "month2ConsumptionMoney : " + month2ConsumptionMoney + '\n' +
                "month3ConsumptionMoney : " + month3ConsumptionMoney + '\n' +
                "month4ConsumptionMoney : " + month4ConsumptionMoney + '\n' +
                "month5ConsumptionMoney : " + month5ConsumptionMoney + '\n' +
                "month6ConsumptionMoney : " + month6ConsumptionMoney + '\n' +
                "month7ConsumptionMoney : " + month7ConsumptionMoney + '\n' +
                "month8ConsumptionMoney : " + month8ConsumptionMoney + '\n' +
                "month9ConsumptionMoney : " + month9ConsumptionMoney + '\n' +
                "month10ConsumptionMoney : " + month10ConsumptionMoney + '\n' +
                "month11ConsumptionMoney : " + month11ConsumptionMoney + '\n' +
                "month12ConsumptionMoney : " + month12ConsumptionMoney + '\n' +
                "maximDemandMonth1 : " + maximDemandMonth1 + '\n' +
                "maximDemandMonth2 : " + maximDemandMonth2 + '\n' +
                "maximDemandMonth3 : " + maximDemandMonth3 + '\n' +
                "maximDemandMonth4 : " + maximDemandMonth4 + '\n' +
                "maximDemandMonth5 : " + maximDemandMonth5 + '\n' +
                "maximDemandMonth6 : " + maximDemandMonth6 + '\n' +
                "maximDemandMonth7 : " + maximDemandMonth7 + '\n' +
                "maximDemandMonth8 : " + maximDemandMonth8 + '\n' +
                "maximDemandMonth9 : " + maximDemandMonth9 + '\n' +
                "maximDemandMonth10 : " + maximDemandMonth10 + '\n' +
                "maximDemandMonth11 : " + maximDemandMonth11 + '\n' +
                "maximDemandMonth12 : " + maximDemandMonth12 + '\n' +
                "maximDemandMonth1Date : " + maximDemandMonth1Date + '\n' +
                "maximDemandMonth2Date : " + maximDemandMonth2Date + '\n' +
                "maximDemandMonth3Date : " + maximDemandMonth3Date + '\n' +
                "maximDemandMonth4Date : " + maximDemandMonth4Date + '\n' +
                "maximDemandMonth5Date : " + maximDemandMonth5Date + '\n' +
                "maximDemandMonth6Date : " + maximDemandMonth6Date + '\n' +
                "maximDemandMonth7Date : " + maximDemandMonth7Date + '\n' +
                "maximDemandMonth8Date : " + maximDemandMonth8Date + '\n' +
                "maximDemandMonth9Date : " + maximDemandMonth9Date + '\n' +
                "maximDemandMonth10Date : " + maximDemandMonth10Date + '\n' +
                "maximDemandMonth11Date : " + maximDemandMonth11Date + '\n' +
                "maximDemandMonth12Date : " + maximDemandMonth12Date + '\n' +
                "month1ConsumptionKvar : " + month1ConsumptionKvar + '\n' +
                "month2ConsumptionKvar : " + month2ConsumptionKvar + '\n' +
                "month3ConsumptionKvar : " + month3ConsumptionKvar + '\n' +
                "month4ConsumptionKvar : " + month4ConsumptionKvar + '\n' +
                "month5ConsumptionKvar : " + month5ConsumptionKvar + '\n' +
                "month6ConsumptionKvar : " + month6ConsumptionKvar + '\n' +
                "month7ConsumptionKvar : " + month7ConsumptionKvar + '\n' +
                "month8ConsumptionKvar : " + month8ConsumptionKvar + '\n' +
                "month9ConsumptionKvar : " + month9ConsumptionKvar + '\n' +
                "month10ConsumptionKvar : " + month10ConsumptionKvar + '\n' +
                "month11ConsumptionKvar : " + month11ConsumptionKvar + '\n' +
                "month12ConsumptionKvar : " + month12ConsumptionKvar + '\n' +
                "readingResult=" + readingResult + '\n' +
                "message : " + message;
    }

    @NonNull
    @Override
    public String toFileFormate() {
//        int mMeterState = 0;
//        int battery = 0;
//        int relay = 0;
//        int topCover = 0;
//        int sideCover = 0;
//        String[] meterStatusArray = getMeterStatus().split(",");
//        for (String name : meterStatusArray) {
//            if (name.toLowerCase(Locale.ROOT).contains("meterstatus")) {
//                int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
//                String s = name.toLowerCase(Locale.ROOT).substring(index);
//                int m = 1;
//                try {
//                    m = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
//                } catch (Exception ignored) {
//                }
//                if (s.startsWith("normal") || m == 0)
//                    mMeterState = 0;
//                else
//                    mMeterState = 1;
//            } else if (name.toLowerCase(Locale.ROOT).contains("relay")) {
//                int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
//                String s = name.toLowerCase(Locale.ROOT).substring(index);
//                int r = 1;
//                try {
//                    r = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
//                } catch (Exception ignored) {
//                }
//                if (s.startsWith("connected") || r == 0)
//                    relay = 0;
//                else
//                    relay = 1;
//            } else if (name.toLowerCase(Locale.ROOT).contains("battery")) {
//                int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
//                String s = name.toLowerCase(Locale.ROOT).substring(index);
//                int b = 1;
//                try {
//                    b = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
//                } catch (Exception ignored) {
//                }
//                if (s.startsWith("normal") || b == 0)
//                    battery = 0;
//                else
//                    battery = 1;
//            }
//        }
//        topCover = getTopCoverStatus().toLowerCase(Locale.ROOT).contains("top cover open") ? 1 : 0;
//        sideCover = getTopCoverStatus().toLowerCase(Locale.ROOT).contains("terminal cover open") ? 1 : 0;
//

        return
                "meterType\t" + meterType + "\n" +
                        "ITEM_1_NEW_BASEITEM_Meter_ID\t" + meterId + "\n" +
                        "ITEM_2_NEW_BASEITEM_Customer_ID\t" + customerId + "\n" +
                        "ITEM_3_NEW_BASEITEM_CardID\t" + cardId + "\n" +
                        "ITEM_4_NEW_BASEITEM_fw_version\t" + fwVersion + "\n" +
                        "ITEM_5_NEW_BASEITEM_ActivityType\t" + activityType + "\n" +
                        "ITEM_6_NEW_BASEITEM_curent_Power_factor\t" + curentPowerFactor + "\n" +
                        "ITEM_7_NEW_BASEITEM_last_year_Power_factor\t" + lastYearPowerFactor + "\n" +
                        "ITEM_8_NEW_BASEITEM_installing_technican_code\t" + installingTechnicanCode + "\n" +
                        "ITEM_9_NEW_BASEITEM_installing_Date_and_time\t" + installingDateAndTime + "\n" +
                        "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time\t" + meterDateAndTime + "\n" +
                        "ITEM_11_NEW_BASEITEM_Current_tarrif_installing\t" + currentTarrifInstalling + "\n" +
                        "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date\t" + currentTariffActivationDate + "\n" +
                        "ITEM_13_NEW_BASEITEM_Meter_status\t" + meterStatus + "\n" +
                        "ITEM_14_NEW_BASEITEM_Relay_status\t" + relayStatus + "\n" +
                        "ITEM_15_NEW_BASEITEM_battery_status\t" + batteryStatus + "\n" +
                        "ITEM_16_NEW_BASEITEM_Top_cover_status\t" + topCoverStatus + "\n" +
                        "ITEM_17_NEW_BASEITEM_Side_cover_status\t" + sideCoverStatus + "\n" +
//                        "ITEM_13_NEW_BASEITEM_Meter_status\t" + mMeterState + "\n" +
//                        "ITEM_14_NEW_BASEITEM_Relay_status\t" + relay + "\n" +
//                        "ITEM_15_NEW_BASEITEM_battery_status\t" + battery + "\n" +
//                        "ITEM_16_NEW_BASEITEM_Top_cover_status\t" + topCover + "\n" +
//                        "ITEM_17_NEW_BASEITEM_Side_cover_status\t" + sideCover + "\n" +
                        "ITEM_18_NEW_BASEITEM_Technical_code_event_1\t" + technicalCodeEvent1 + "\n" +
                        "ITEM_19_NEW_BASEITEM_event_type_1\t" + eventType1 + "\n" +
                        "ITEM_20_NEW_BASEITEM_event_Date_1\t" + eventDate1 + "\n" +
                        "ITEM_21_NEW_BASEITEM_Technical_code_event_2\t" + technicalCodeEvent2 + "\n" +
                        "ITEM_22_NEW_BASEITEM_event_type_2\t" + eventType2 + "\n" +
                        "ITEM_23_NEW_BASEITEM_event_Date_2\t" + eventDate2 + "\n" +
                        "ITEM_24_NEW_BASEITEM_Technical_code_event_3\t" + technicalCodeEvent3 + "\n" +
                        "ITEM_25_NEW_BASEITEM_event_type_3\t" + eventType3 + "\n" +
                        "ITEM_26_NEW_BASEITEM_event_Date_3\t" + eventDate3 + "\n" +
                        "ITEM_27_NEW_BASEITEM_recharge_number\t" + rechargeNumber + "\n" +
                        "ITEM_28_NEW_BASEITEM_Recharge_Amount\t" + rechargeAmount + "\n" +
                        "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time\t" + lastRechargeDateAndTime + "\n" +
                        "ITEM_30_NEW_BASEITEM_remaining_credit_kw\t" + remainingCreditKw + "\n" +
                        "ITEM_31_NEW_BASEITEM_remaining_credit_mony\t" + remainingCreditMoney + "\n" +
                        "ITEM_32_NEW_BASEITEM_Debts\t" + debts + "\n" +
                        "ITEM_33_NEW_BASEITEM_Total_consumption_kw\t" + totalConsumptionKw + "\n" +
                        "ITEM_34_NEW_BASEITEM_Total_consumption_mony\t" + totalConsumptionMoney + "\n" +
                        "ITEM_35_NEW_BASEITEM_Total_consumption_kvar\t" + totalConsumptionKvar + "\n" +
                        "ITEM_36_NEW_BASEITEM_Current_Demand\t" + currentDemand + "\n" +
                        "ITEM_37_NEW_BASEITEM_Maximum_Demand\t" + maximumDemand + "\n" +
                        "ITEM_38_NEW_BASEITEM_Maximum_Demand_date\t" + maximumDemandDate + "\n" +
                        "ITEM_39_NEW_BASEITEM_instanteneous_volt\t" + instanteneousVolt + "\n" +
                        "ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere\t" + instanteneousCurrentPhaseAmpere + "\n" +
                        "ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral\t" + instanteneousCurrentNeutral + "\n" +
                        "ITEM_42_NEW_BASEITEM_reverse_Kwh\t" + reverseKwh + "\n" +
                        "ITEM_43_NEW_BASEITEM_unbalance_Kwh\t" + unbalanceKwh + "\n" +
                        "ITEM_44_NEW_BASEITEM_current_month_consumption_KW\t" + currentMonthConsumptionKW + "\n" +
                        "ITEM_45_NEW_BASEITEM_current_month_consumption_MONY\t" + currentMonthConsumptionMoney + "\n" +
                        "ITEM_46_NEW_BASEITEM_1_month_consumption_kWh\t" + month1ConsumptionKWh + "\n" +
                        "ITEM_47_NEW_BASEITEM_2_month_consumption_kWh\t" + month2ConsumptionKWh + "\n" +
                        "ITEM_48_NEW_BASEITEM_3_month_consumption_kWh\t" + month3ConsumptionKWh + "\n" +
                        "ITEM_49_NEW_BASEITEM_4_month_consumption_kWh\t" + month4ConsumptionKWh + "\n" +
                        "ITEM_50_NEW_BASEITEM_5_month_consumption_kWh\t" + month5ConsumptionKWh + "\n" +
                        "ITEM_51_NEW_BASEITEM_6_month_consumption_kWh\t" + month6ConsumptionKWh + "\n" +
                        "ITEM_52_NEW_BASEITEM_7_month_consumption_kWh\t" + month7ConsumptionKWh + "\n" +
                        "ITEM_53_NEW_BASEITEM_8_month_consumption_kWh\t" + month8ConsumptionKWh + "\n" +
                        "ITEM_54_NEW_BASEITEM_9_month_consumption_kWh\t" + month9ConsumptionKWh + "\n" +
                        "ITEM_55_NEW_BASEITEM_10_month_consumption_kWh\t" + month10ConsumptionKWh + "\n" +
                        "ITEM_56_NEW_BASEITEM_11_month_consumption_kWh\t" + month11ConsumptionKWh + "\n" +
                        "ITEM_57_NEW_BASEITEM_12_month_consumption_kWh\t" + month12ConsumptionKWh + "\n" +
                        "ITEM_58_NEW_BASEITEM_1_month_consumption_Mony\t" + month1ConsumptionMoney + "\n" +
                        "ITEM_59_NEW_BASEITEM_2_month_consumption_Mony\t" + month2ConsumptionMoney + "\n" +
                        "ITEM_60_NEW_BASEITEM_3_month_consumption_Mony\t" + month3ConsumptionMoney + "\n" +
                        "ITEM_61_NEW_BASEITEM_4_month_consumption_Mony\t" + month4ConsumptionMoney + "\n" +
                        "ITEM_62_NEW_BASEITEM_5_month_consumption_Mony\t" + month5ConsumptionMoney + "\n" +
                        "ITEM_63_NEW_BASEITEM_6_month_consumption_Mony\t" + month6ConsumptionMoney + "\n" +
                        "ITEM_64_NEW_BASEITEM_7_month_consumption_Mony\t" + month7ConsumptionMoney + "\n" +
                        "ITEM_65_NEW_BASEITEM_8_month_consumption_Mony\t" + month8ConsumptionMoney + "\n" +
                        "ITEM_66_NEW_BASEITEM_9_month_consumption_Mony\t" + month9ConsumptionMoney + "\n" +
                        "ITEM_67_NEW_BASEITEM_10_month_consumption_Mony\t" + month10ConsumptionMoney + "\n" +
                        "ITEM_68_NEW_BASEITEM_11_month_consumption_Mony\t" + month11ConsumptionMoney + "\n" +
                        "ITEM_69_NEW_BASEITEM_12_month_consumption_Mony\t" + month12ConsumptionMoney + "\n" +
                        "ITEM_70_NEW_BASEITEM_maxim_demand_month_1\t" + maximDemandMonth1 + "\n" +
                        "ITEM_71_NEW_BASEITEM_maxim_demand_month_2\t" + maximDemandMonth2 + "\n" +
                        "ITEM_72_NEW_BASEITEM_maxim_demand_month_3\t" + maximDemandMonth3 + "\n" +
                        "ITEM_73_NEW_BASEITEM_maxim_demand_month_4\t" + maximDemandMonth4 + "\n" +
                        "ITEM_74_NEW_BASEITEM_maxim_demand_month_5\t" + maximDemandMonth5 + "\n" +
                        "ITEM_75_NEW_BASEITEM_maxim_demand_month_6\t" + maximDemandMonth6 + "\n" +
                        "ITEM_76_NEW_BASEITEM_maxim_demand_month_7\t" + maximDemandMonth7 + "\n" +
                        "ITEM_77_NEW_BASEITEM_maxim_demand_month_8\t" + maximDemandMonth8 + "\n" +
                        "ITEM_78_NEW_BASEITEM_maxim_demand_month_9\t" + maximDemandMonth9 + "\n" +
                        "ITEM_79_NEW_BASEITEM_maxim_demand_month_10\t" + maximDemandMonth10 + "\n" +
                        "ITEM_80_NEW_BASEITEM_maxim_demand_month_11\t" + maximDemandMonth11 + "\n" +
                        "ITEM_81_NEW_BASEITEM_maxim_demand_month_12\t" + maximDemandMonth12 + "\n" +
                        "ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1\t" + maximDemandMonth1Date + "\n" +
                        "ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2\t" + maximDemandMonth2Date + "\n" +
                        "ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3\t" + maximDemandMonth3Date + "\n" +
                        "ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4\t" + maximDemandMonth4Date + "\n" +
                        "ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5\t" + maximDemandMonth5Date + "\n" +
                        "ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6\t" + maximDemandMonth6Date + "\n" +
                        "ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7\t" + maximDemandMonth7Date + "\n" +
                        "ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8\t" + maximDemandMonth8Date + "\n" +
                        "ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9\t" + maximDemandMonth9Date + "\n" +
                        "ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10\t" + maximDemandMonth10Date + "\n" +
                        "ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11\t" + maximDemandMonth11Date + "\n" +
                        "ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12\t" + maximDemandMonth12Date + "\n" +
                        "ITEM_94_NEW_BASEITEM_kvar_consumption_month_1\t" + month1ConsumptionKvar + "\n" +
                        "ITEM_95_NEW_BASEITEM_kvar_consumption_month_2\t" + month2ConsumptionKvar + "\n" +
                        "ITEM_96_NEW_BASEITEM_kvar_consumption_month_3\t" + month3ConsumptionKvar + "\n" +
                        "ITEM_97_NEW_BASEITEM_kvar_consumption_month_4\t" + month4ConsumptionKvar + "\n" +
                        "ITEM_98_NEW_BASEITEM_kvar_consumption_month_5\t" + month5ConsumptionKvar + "\n" +
                        "ITEM_99_NEW_BASEITEM_kvar_consumption_month_6\t" + month6ConsumptionKvar + "\n" +
                        "ITEM_100_NEW_BASEITEM_kvar_consumption_month_7\t" + month7ConsumptionKvar + "\n" +
                        "ITEM_101_NEW_BASEITEM_kvar_consumption_month_8\t" + month8ConsumptionKvar + "\n" +
                        "ITEM_102_NEW_BASEITEM_kvar_consumption_month_9\t" + month9ConsumptionKvar + "\n" +
                        "ITEM_103_NEW_BASEITEM_kvar_consumption_month_10\t" + month10ConsumptionKvar + "\n" +
                        "ITEM_104_NEW_BASEITEM_kvar_consumption_month_11\t" + month11ConsumptionKvar + "\n" +
                        "ITEM_105_NEW_BASEITEM_kvar_consumption_month_12\t" + month12ConsumptionKvar + "\n";
    }
}
