<component name="libraryTable">
  <library name="Gradle: androidx.viewpager:viewpager:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/21e8ccd24c4a3fe512a0fea2ddcffb90/transformed/viewpager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/108bc5cf2697da40ede3f14aa3e14661/transformed/viewpager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/42a5e12d1f524c87a610b6ace56aacda/transformed/viewpager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dcced50e131cc7e1c1ff794151889672/transformed/viewpager-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7919586390088366751056d4b17417a2/transformed/viewpager-1.0.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/dcced50e131cc7e1c1ff794151889672/transformed/viewpager-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dcced50e131cc7e1c1ff794151889672/transformed/viewpager-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.viewpager/viewpager/1.0.0/db045f92188b9d247d5f556866f8861ab68528f0/viewpager-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>