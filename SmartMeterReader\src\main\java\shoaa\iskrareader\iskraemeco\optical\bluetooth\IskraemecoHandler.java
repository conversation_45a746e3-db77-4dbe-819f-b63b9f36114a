package shoaa.iskrareader.iskraemeco.optical.bluetooth;

import com.google.gson.Gson;

import shoaa.iskrareader.iskraemeco.optical.bluetooth.model.RequestModel;
import shoaa.iskrareader.iskraemeco.optical.bluetooth.model.ResponseModel;
import shoaa.iskrareader.iskraemeco.optical.bluetooth.utils.CommandCentral;
import shoaa.iskrareader.iskraemeco.optical.bluetooth.utils.ParametersResponseHandler;

public class IskraemecoHandler {
    public String handle(String json) {
        String jsonResponseString = null;
        try {
            ResponseModel jsonResponse = new ResponseModel();
            RequestModel jsonRequest = new Gson().fromJson(json, RequestModel.class);
            if (jsonRequest.getService().equalsIgnoreCase("handshake")) {
                jsonResponse = handleHandShake(jsonRequest);
            } else if (jsonRequest.getService().equalsIgnoreCase("acknowledge")) {
                jsonResponse.setResult("success");
                jsonResponse.setPdu(null);
                jsonResponse.setService("acknowledge");
                jsonResponse.setValue(null);
            } else {
                jsonResponse = handleParameterCommands(jsonRequest);
            }
            jsonResponseString = new Gson().toJson(jsonResponse);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonResponseString;
    }

    private ResponseModel handleHandShake(RequestModel jsonRequest) {
        CommandCentral commandCentral = new CommandCentral();
        ResponseModel jsonResponse = new ResponseModel();
        if (jsonRequest.getPdu() == null) {
            jsonResponse.setPdu(commandCentral.getHandShakeCommand().toUpperCase());
            jsonResponse.setService("handshake");
            jsonResponse.setResult("continue");
            jsonResponse.setValue(null);
        } else {
            String meterModelResult = commandCentral.validateHandShakeCommand(jsonRequest.getPdu().toUpperCase());
            if (meterModelResult != null) {
                String acknowledgePDU = commandCentral.getacknowledgeCommand();
                jsonResponse.setResult("continue");
                jsonResponse.setPdu(acknowledgePDU);
                jsonResponse.setService("handshake");
                jsonResponse.setValue(meterModelResult);
            } else {
                jsonResponse.setResult("failure");
                jsonResponse.setPdu("null");
                jsonResponse.setService("handshake");
                jsonResponse.setValue("null");
            }
        }
        return jsonResponse;
    }

    private ResponseModel handleParameterCommands(RequestModel jsonRequest) {
        CommandCentral commandCentral = new CommandCentral();
        ParametersResponseHandler parametersResponseHandler = new ParametersResponseHandler();
        ResponseModel jsonResponse = new ResponseModel();
        if (jsonRequest.getPdu() == null) {
            jsonResponse.setPdu(commandCentral.getIskraParameterCommand(jsonRequest.getItem()));
            jsonResponse.setService("get");
            jsonResponse.setResult("continue");
            jsonResponse.setValue("null");
        } else {
            String parameterValue = commandCentral.validateParameterCommand(jsonRequest.getPdu());
            jsonResponse.setResult("success");
            jsonResponse.setPdu("null");
            jsonResponse.setService("get");
            try {
                String resultFormatted = parametersResponseHandler.checkIfResponseNeedFormatting(parameterValue, jsonRequest.getItem());
                jsonResponse.setValue(resultFormatted);
            } catch (Exception ex) {
                jsonResponse.setValue("null");
            }
        }
        return jsonResponse;
    }
}
