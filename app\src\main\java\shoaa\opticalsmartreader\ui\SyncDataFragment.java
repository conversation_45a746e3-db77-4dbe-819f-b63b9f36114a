package shoaa.opticalsmartreader.ui;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import shoaa.common.GsonDateSerialization;
import shoaa.opticalsmartreader.BuildConfig;
import shoaa.opticalsmartreader.FileEncryptionUtil;
import shoaa.opticalsmartreader.MainApplication;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.Utils;
import shoaa.opticalsmartreader.api.APIClient;
import shoaa.opticalsmartreader.api.ApiResult;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.CycleDate.CycleDateManager;
import shoaa.opticalsmartreader.logic.MeterData.DatabaseMeterData;
import shoaa.opticalsmartreader.logic.getAllClients.ClientsManager;
import shoaa.opticalsmartreader.logic.login.LoginManager;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.CycleDate;
import shoaa.opticalsmartreader.models.MeterCheck;
import shoaa.opticalsmartreader.models.MiniDatabaseMeterData;
import shoaa.opticalsmartreader.models.ServerMeterObject;


public class SyncDataFragment extends Fragment {
    CheckBox chkSelectAll;
    CheckBox chkRead;
    CheckBox chkClosed;
    CheckBox chkCanNotReach;
    CheckBox chkCanNotRead;
    CheckBox chkError;
    CheckBox chkGlass;
    CheckBox chkStolen;
    CheckBox chkUp;
    CheckBox chkLost;
    CheckBox chkNotFound;
    CheckBox chkBeach;
    CheckBox chkNotConnected;
    CheckBox chkRefused;
    CheckBox chkViolation;
    CheckBox chkDestroy;
    Button btnSync;
    Button btnSendBackup;
    int mSuccessCount = 0;
    int mFailedCount = 0;
    boolean mGetData = false;
    List<MiniDatabaseMeterData> databaseMeterDataList = null;
    boolean syncWait = false;
    boolean sendSyncWait = false;
    boolean syncContinue = true;
    SyncDialog syncDialog;
    int tryCount = 0;
    File dir;
    File dbFile;

    public SyncDataFragment() {
        // Required empty public constructor
    }

    public static void copy(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src)) {
            try (OutputStream out = new FileOutputStream(dst)) {
                // Transfer bytes from in to out
                byte[] buf = new byte[1024];
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
            }
        }catch (Exception e){
            Log.d("popopopop00", "copy: "+ e.getMessage());
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View layout = inflater.inflate(R.layout.fragment_sync_data, container, false);
        chkSelectAll = layout.findViewById(R.id.chk_selectAll);
        chkRead = layout.findViewById(R.id.chk_sync_read);
        chkClosed = layout.findViewById(R.id.chk_sync_closed);
        chkCanNotReach = layout.findViewById(R.id.chk_sync_can_not_reach);
        chkCanNotRead = layout.findViewById(R.id.chk_sync_can_not_read);
        chkError = layout.findViewById(R.id.chk_sync_error);
        chkGlass = layout.findViewById(R.id.chk_sync_glass);
        chkStolen = layout.findViewById(R.id.chk_sync_stolen);
        chkUp = layout.findViewById(R.id.chk_up);
        chkNotFound = layout.findViewById(R.id.chk_sync_notFound);
        chkLost = layout.findViewById(R.id.chk_sync_Lost);
        chkBeach = layout.findViewById(R.id.chk_beach);
        chkNotConnected = layout.findViewById(R.id.chk_notConnected);
        chkRefused = layout.findViewById(R.id.chk_refused);
        chkViolation = layout.findViewById(R.id.chk_violation);
        chkDestroy = layout.findViewById(R.id.chk_destroy);
        btnSync = layout.findViewById(R.id.btn_sync);
        btnSendBackup = layout.findViewById(R.id.btn_send_backup);
        dir = new File(MainApplication.getInstance().getCurrentActivity().getFilesDir(), "Backup");
        //dbFile = new File(dir, "backup_" + AppUser.getInstance(MainApplication.getInstance().getCurrentActivity()).getBrHndsa() + "_" + AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getUSER_ID() + ".db.enc");
        dbFile = new File(dir, "backup_" + AppUser.getInstance(MainApplication.getInstance().getCurrentActivity()).getBrHndsa() + "_" + AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getUSER_ID() + ".db");

        Log.d("popopopop", "onCreateView: "+dbFile.getAbsolutePath());

        //region UI Logic
        chkSelectAll.setOnCheckedChangeListener((compoundButton, b) -> {
            if (b) {
                chkRead.setChecked(true);
                chkClosed.setChecked(true);
                chkCanNotRead.setChecked(true);
                chkCanNotReach.setChecked(true);
                chkError.setChecked(true);
                chkGlass.setChecked(true);
                chkStolen.setChecked(true);
                chkUp.setChecked(true);
                chkLost.setChecked(true);
                chkNotFound.setChecked(true);
                chkBeach.setChecked(true);
                chkNotConnected.setChecked(true);
                chkRefused.setChecked(true);
                chkViolation.setChecked(true);
                chkDestroy.setChecked(true);
            } else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked()) {
                    chkRead.setChecked(false);
                    chkClosed.setChecked(false);
                    chkCanNotReach.setChecked(false);
                    chkCanNotRead.setChecked(false);
                    chkError.setChecked(false);
                    chkGlass.setChecked(false);
                    chkStolen.setChecked(false);
                    chkUp.setChecked(false);
                    chkLost.setChecked(false);
                    chkNotFound.setChecked(false);
                    chkBeach.setChecked(false);
                    chkNotConnected.setChecked(false);
                    chkRefused.setChecked(false);
                    chkViolation.setChecked(false);
                    chkDestroy.setChecked(false);
                }
            }
        });
        chkRead.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkClosed.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkError.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkGlass.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkStolen.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkUp.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkLost.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkNotFound.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkBeach.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkNotConnected.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkRefused.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkViolation.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkGlass.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkDestroy.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkCanNotRead.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        chkCanNotReach.setOnCheckedChangeListener((compoundButton, b) -> {
            if (!b)
                chkSelectAll.setChecked(false);
            else {
                if (chkRead.isChecked() &&
                        chkClosed.isChecked() &&
                        chkError.isChecked() &&
                        chkGlass.isChecked() &&
                        chkCanNotReach.isChecked() &&
                        chkCanNotRead.isChecked() &&
                        chkStolen.isChecked() &&
                        chkUp.isChecked() &&
                        chkLost.isChecked() &&
                        chkNotFound.isChecked() &&
                        chkBeach.isChecked() &&
                        chkNotConnected.isChecked() &&
                        chkRefused.isChecked() &&
                        chkViolation.isChecked() &&
                        chkDestroy.isChecked())
                    chkSelectAll.setChecked(true);
            }
        });
        //endregion

        chkSelectAll.setChecked(true);

        btnSync.setOnClickListener(view -> {
            mSuccessCount = 0;
            mFailedCount = 0;
            mGetData = false;
            databaseMeterDataList = null;
            syncData();
        });
        btnSendBackup.setOnClickListener(view -> {
            //
           /*-------------------------
           * For Hoda Test The response file ( But remove the above shareMultiple method )
           * -----------------------------*/
            //shareMultiple();

            List<File> fileList = new ArrayList<>();
            if (dbFile.exists()) {
                fileList.add(dbFile);
            }
            if (fileList.size() > 0) {
                shareMultiple(fileList, ((MainActivity) MainApplication.getInstance().getCurrentActivity()));
            }
        });
        btnSendBackup.setEnabled(dbFile.exists() && dbFile.canRead() && dbFile.canWrite());
        return layout;
    }


    public void shareMultiple() {
        File outDir = new File(getActivity().getFilesDir(), "dataDir");
        if (!outDir.exists() || outDir.listFiles().length == 0)
            return;
        List<File> files = new ArrayList<>();
        for (File outFile : outDir.listFiles())
            files.add(outFile);
        if (dbFile.exists()) {
            files.add(dbFile);
            }
        ArrayList<Uri> uris = new ArrayList<>();

        for (File file : files) {
            uris.add(FileProvider.getUriForFile(getActivity().getApplicationContext(), getActivity().getApplication().getPackageName() + ".fileprovider", file));
        }
        final Intent intent = new Intent(Intent.ACTION_SEND_MULTIPLE);
        intent.setType("*/*");
        intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris);
        this.startActivity(Intent.createChooser(intent, "ارسال النسخة الاحتياطية"));
    }


    public void shareMultiple(List<File> files, Context context) {
        ArrayList<Uri> uris = new ArrayList<>();
        for (File file : files) {
            uris.add(FileProvider.getUriForFile(((MainActivity) MainApplication.getInstance().getCurrentActivity()), ((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplication().getPackageName() + ".fileprovider", file));
        }
        final Intent intent = new Intent(Intent.ACTION_SEND_MULTIPLE);
        intent.setType("*/*");
        intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, uris);
        context.startActivity(Intent.createChooser(intent, "ارسال النسخة الاحتياطية"));
    }

    private void syncData() {
        syncContinue = true;
        syncWait = false;
        new Thread(() -> {
            if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();

            databaseMeterDataList = shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().getAllMini();
            if (!chkSelectAll.isChecked()) {
                if (!chkRead.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 0);
                }
                if (!chkClosed.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 1);
                }
                if (!chkCanNotReach.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> (databaseMeterData.READING_CODE == 2 && databaseMeterData.DAMAGE_CODE == 1));
                }
                if (!chkCanNotRead.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> (databaseMeterData.READING_CODE == 2 && databaseMeterData.DAMAGE_CODE == 2));
                }
                if (!chkError.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> (databaseMeterData.READING_CODE == 2 && databaseMeterData.DAMAGE_CODE == 3));
                }
                if (!chkGlass.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 3);
                }
                if (!chkStolen.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 4);
                }
                if (!chkUp.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 5);
                }
                if (!chkNotFound.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 6);
                }
                if (!chkLost.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 7);
                }
                if (!chkBeach.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 8);
                }
                if (!chkNotConnected.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 9);
                }
                if (!chkRefused.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 10);
                }
                if (!chkViolation.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 12);
                }
                if (!chkDestroy.isChecked()) {
                    databaseMeterDataList.removeIf(databaseMeterData -> databaseMeterData.READING_CODE == 13);
                }
            }


            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                syncDialog = new SyncDialog(((MainActivity) MainApplication.getInstance().getCurrentActivity()), text -> {
                });
                syncDialog.setCancelable(false);
                syncDialog.show();
                new Thread(() -> {
                    try {
                        if (databaseMeterDataList.size() > 0) {
                            syncDialog.addLine("جاري اخذ نسخة احتياطية");
                            if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                                shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();

                            if (!dir.exists())
                                dir.mkdirs();
                            if (dbFile.exists())
                                dbFile.delete();
                            copy(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getDatabasePath("shoaa_database"), dbFile);
                            //FileEncryptionUtil.encryptFile(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getDatabasePath("shoaa_database"), dbFile);
                            syncDialog.addLine("تم اخذ نسخة احتياطية");
                            syncContinue = true;
                            syncWait = false;
                        }
                    } catch (Exception e) {
                        syncDialog.addLine("خطأ في اخذ نسخة احتياطية");
                        syncDialog.addLine(e.getMessage());
                        syncContinue = true;
                        syncWait = false;
                    }
                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> btnSendBackup.setEnabled(dbFile.exists() && dbFile.canRead() && dbFile.canWrite()));
                    if (syncContinue) {
                        if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                            shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                        syncDialog.addLine("جاري تسجيل الدخول");
                        syncWait = true;
                        syncContinue = true;
                        tryCount = 0;
                        LoginManager.login(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getUserName(), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getPassword(), new ApiResult() {
                            @Override
                            public void onSuccess() {
                                syncDialog.addLine("تم تسجيل الدخول");
                                AppUser loginUser = AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
                                boolean valid = false;
                                if (loginUser.getVERSION().equalsIgnoreCase(BuildConfig.VERSION_NAME)) {
                                    valid = true;
                                } else {
                                    for (String version : BuildConfig.VALID_OLD_VERSIONS) {
                                        if (loginUser.getVERSION().equalsIgnoreCase(version)) {
                                            valid = true;
                                            break;
                                        }
                                    }
                                }
                                if (!valid) {
                                    shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("app_user", "").apply();
                                    syncDialog.addLine("يرجي تحديث نسخة البرنامج الي اصدار " + loginUser.getVERSION());
                                    syncWait = false;
                                    syncContinue = false;
                                } else {
                                    syncWait = false;
                                    syncContinue = true;
                                }
                            }

                            @Override
                            public void onFailed(int code, String reason) {
                                if (tryCount < 5) {
                                    tryCount++;
                                    LoginManager.login(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getUserName(), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getPassword(), this);
                                } else {
                                    syncDialog.addLine("غير قادر علي الاتصال بالسيرفر يرجي التاكد من اتصال الانترنت");
                                    syncWait = false;
                                    syncContinue = false;
                                }
                            }

                        });
                        while (syncWait) {
                            try {
                                Thread.sleep(100);
                            } catch (Exception ignore) {
                            }
                        }
                        if (syncContinue) {
                            if (databaseMeterDataList.size() > 0) {
                                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                syncDialog.addLine("جاري مزامنه البيانات " + 0 + "%");
                                syncWait = true;
                                for (int i = 0; i < databaseMeterDataList.size(); i++) {
                                    while (sendSyncWait) {
                                        try {
                                            Thread.sleep(100);
                                        } catch (InterruptedException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    sendSyncWait = true;
                                    int finalI = i;
                                    tryCount = 0;
                                    sendMeterData(databaseMeterDataList.get(i), new ApiResult() {
                                        @Override
                                        public void onSuccess() {
                                            if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                                                shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                                                float p = (finalI /
                                                        (float) databaseMeterDataList.size()) * 100;
                                                syncDialog.addLine("جاري مزامنه البيانات " + ((int) p) + "%");
                                            });
                                            new Thread(() -> {
                                                DatabaseMeterData databaseMeterData = shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().find(databaseMeterDataList.get(finalI).COVER_METER_ID, databaseMeterDataList.get(finalI).FACTORY_CODE);
                                                if (databaseMeterData != null)
                                                    shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().delete(databaseMeterData);
                                            }).start();
                                            mSuccessCount++;
                                            sendSyncWait = false;
                                            if (finalI == databaseMeterDataList.size() - 1)
                                                syncWait = false;
                                        }

                                        @Override
                                        public void onFailed(int code, String reason) {
                                            if (tryCount < 5 && (code != 0 || reason.equalsIgnoreCase("timeout"))) {
                                                tryCount++;
                                                sendMeterData(databaseMeterDataList.get(finalI), this);
                                            } else {
                                                float p = (finalI /
                                                        (float) databaseMeterDataList.size()) * 100;
                                                syncDialog.addLine("جاري مزامنه البيانات " + ((int) p) + "%");
                                                if (!reason.equalsIgnoreCase("timeout"))
                                                    syncDialog.addLine("خطأ : " + reason);
                                                else
                                                    syncDialog.addLine("خطأ : " + "غير قادر علي الاتصال بالسيرفر 3");
                                                mFailedCount++;
                                                sendSyncWait = false;
                                                if (finalI == databaseMeterDataList.size() - 1)
                                                    syncWait = false;
                                            }
                                        }
                                    });
                                }
                                while (syncWait) {
                                    try {
                                        Thread.sleep(100);
                                    } catch (Exception ignore) {
                                    }
                                }
                                syncDialog.addLine("جاري مزامنه البيانات " + (100) + "%");

                                if (databaseMeterDataList.size() > 0)
                                    syncDialog.addLine("تم مزامنة " + (mSuccessCount) + " من اجمالي محدد " + databaseMeterDataList.size() + " بنجاح");
                            }
                            if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                                shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                            syncDialog.addLine("جاري مزامنة بيانات المشتركين");
                            tryCount = 0;
                            syncWait = true;
                            syncContinue = true;
                            CycleDateManager.loadCycleDate(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())), new ApiResult() {
                                @Override
                                public void onSuccess() {
                                    syncWait = false;
                                    syncContinue = true;
                                }

                                @Override
                                public void onFailed(int code, String reason) {
                                    if (tryCount < 5) {
                                        tryCount++;
                                        CycleDateManager.loadCycleDate(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())), this);
                                    } else {
                                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> syncDialog.addLine("فشل في مزامنة دورة الكشف"));
                                        syncWait = false;
                                        syncContinue = false;
                                    }
                                }

                            });

                            while (syncWait) {
                                try {
                                    Thread.sleep(100);
                                } catch (Exception ignore) {
                                }
                            }
                            syncWait = true;
                            tryCount = 0;
                            if (syncContinue) {
                                ClientsManager.loadAllClients(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())), new ApiResult() {
                                    @SuppressLint("NotifyDataSetChanged")
                                    @Override
                                    public void onSuccess() {
                                        new Thread(() -> {
                                            List<Client> clientList = shoaa.opticalsmartreader.logic.Utils.appDatabase.clientDao().getAll();
                                            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                                                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.loadClientsData();
                                                syncDialog.addLine("تمت مزامنة بيانات المشتركين بنجاح");
                                                syncDialog.addLine("تم استلام عدد : " + clientList.size() + " مشترك");
                                                syncWait = false;
                                                syncContinue = true;
                                            });
                                        }).start();
                                    }

                                    @Override
                                    public void onFailed(int code, String reason) {
                                        if (tryCount < 5) {
                                            tryCount++;
                                            ClientsManager.loadAllClients(((MainActivity) MainApplication.getInstance().getCurrentActivity()), AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())), this);
                                        } else {
                                            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                                                syncDialog.addLine("خطأ : " + reason);
                                                syncWait = false;
                                                syncContinue = false;
                                            });
                                        }
                                    }
                                });
                                while (syncWait) {
                                    try {
                                        Thread.sleep(100);
                                    } catch (Exception ignore) {
                                    }
                                }

                                /////
                            }

                            ///////
                        }
                        ////////////
                    }
                    tryCount = 0;
                    syncDialog.showBtn();
                }).start();
            });
        }).start();

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).checkTimeAutomatic();
        btnSendBackup.setEnabled(dbFile.exists() && dbFile.canRead() && dbFile.canWrite());
        AppUser appUser = AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
        if (appUser == null ||
                appUser.getUSER_ID() == null ||
                appUser.getUSER_ID().isEmpty() ||
                appUser.getUserName() == null ||
                appUser.getUserName().isEmpty() ||
                appUser.getPassword() == null ||
                appUser.getPassword().isEmpty()) {
            shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent(((MainActivity) MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).finish();
            startActivity(i);
        }
    }

    void sendMeterData(MiniDatabaseMeterData miniDatabaseMeterData, ApiResult apiResult) {
        new Thread(() -> {
            DatabaseMeterData databaseMeterData = shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().find(miniDatabaseMeterData.COVER_METER_ID, miniDatabaseMeterData.FACTORY_CODE);
            if (databaseMeterData != null) {
                ServerMeterObject serverMeterObject = new ServerMeterObject();
                try {
                    serverMeterObject.ShoaaPrePaid = URLEncoder.encode(databaseMeterData.toWebFormat(), StandardCharsets.UTF_8.toString());
                } catch (UnsupportedEncodingException e) {
                    serverMeterObject.ShoaaPrePaid = databaseMeterData.toWebFormat();
                }
                Call<HashMap<String, Object>> sendCall = APIClient.getService().sendMeterData(serverMeterObject.getRequest());
                sendCall.enqueue(new Callback<HashMap<String, Object>>() {
                    @Override
                    public void onResponse(@NonNull Call<HashMap<String, Object>> call, @NonNull Response<HashMap<String, Object>> response) {
                        if (response.isSuccessful()) {
                            if (response.body() != null) {
                                if (response.body().containsKey("Error")) {
                                    if (apiResult != null) {
                                        String message = (String) response.body().get("Error");
                                        apiResult.onFailed(0, message);
                                    }
                                    return;
                                }

                                if (apiResult != null) {
                                    if (((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList == null) {
                                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList = new ArrayList<>();
                                    }
                                    if (databaseMeterData.READING_DATA_FLAG == 1) {
                                        CycleDate cycleDate = new CycleDate();
                                        cycleDate.CycleMonth = databaseMeterData.CIRCLE_MONTH;
                                        cycleDate.CycleYear = databaseMeterData.CIRCLE_YEAR;
                                        MeterCheck meterCheck = new MeterCheck();
                                        meterCheck.cycleDate = cycleDate;
                                        meterCheck.factoryCode = databaseMeterData.FACTORY_CODE;
                                        meterCheck.coverMeterId = databaseMeterData.COVER_METER_ID;
                                        meterCheck.originalOpticalMeterId = databaseMeterData.ORIGINAL_OPTICAL_METER_ID;
                                        meterCheck.opticalCustomerId = databaseMeterData.OPTICAL_Customer_ID;
                                        meterCheck.cardID = databaseMeterData.ITEM_3_NEW_BASEITEM_CardID;
                                        meterCheck.sideCover = databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status;
                                        meterCheck.topCover = databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status;
                                        meterCheck.syncDate = Calendar.getInstance().getTime();

                                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.removeIf(meterCheck1 -> (
                                                databaseMeterData.CIRCLE_MONTH == meterCheck1.cycleDate.CycleMonth &&
                                                        databaseMeterData.CIRCLE_YEAR == meterCheck1.cycleDate.CycleYear &&
                                                        databaseMeterData.FACTORY_CODE == meterCheck1.factoryCode &&
                                                        databaseMeterData.COVER_METER_ID == meterCheck1.coverMeterId &&
                                                        databaseMeterData.ORIGINAL_OPTICAL_METER_ID == meterCheck1.originalOpticalMeterId
                                                        && databaseMeterData.OPTICAL_Customer_ID.equalsIgnoreCase(meterCheck1.opticalCustomerId)
                                                        && databaseMeterData.ITEM_3_NEW_BASEITEM_CardID.equalsIgnoreCase(meterCheck1.cardID) &&
                                                        databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status == meterCheck1.sideCover &&
                                                        databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status == meterCheck1.topCover));
                                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.add(meterCheck);
                                        File syncDataFile = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), ".ShoaaData/data.db");
                                        if (syncDataFile.exists()) {
                                            syncDataFile.delete();
                                        }

                                        Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new GsonDateSerialization()).create();
                                        String newSyncData = gson.toJson(((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList);
                                        Utils.writeStringAsFile(Objects.requireNonNull(syncDataFile.getParentFile()), newSyncData, syncDataFile.getName());
                                    }
                                    apiResult.onSuccess();
                                }
                            } else {
                                if (apiResult != null) {
                                    apiResult.onFailed(1, "غير قادر علي الاتصال بالسيرفر 1");
                                }
                            }
                        } else {
                            if (apiResult != null) {
                                apiResult.onFailed(2, "2 غير قادر علي الاتصال بالسيرفر");
                            }
                        }
                    }

                    @Override
                    public void onFailure(@NonNull Call<HashMap<String, Object>> call, @NonNull Throwable t) {
                        if (apiResult != null)
                            apiResult.onFailed(3, t.getMessage());

                    }
                });
            } else {
                if (apiResult != null)
                    apiResult.onFailed(404, "غير قادر علي ارسال البيانات 404");
            }
        }).start();
    }
}