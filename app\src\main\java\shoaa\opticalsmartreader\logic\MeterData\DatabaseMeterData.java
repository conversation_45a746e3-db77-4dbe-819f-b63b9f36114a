package shoaa.opticalsmartreader.logic.MeterData;


import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;

import com.google.common.io.BaseEncoding;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import shoaa.common.DateUtil;
import shoaa.electrometerreader.ElectrometerResponse;
import shoaa.esmcreader.EsmcResponse;
import shoaa.globalreader.GlobalResponse;
import shoaa.gpireader.GpiResponse;
import shoaa.hay2areader.Hay2aResponse;
import shoaa.iskrareader.IskraResponse;
import shoaa.maasarareader.MaasaraResponse;
import shoaa.opticalsmartreader.BuildConfig;
import shoaa.opticalsmartreader.logic.AppLocationManager;
import shoaa.opticalsmartreader.logic.BitmapHelper;
import shoaa.opticalsmartreader.logic.Enums;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.CycleDate;
import shoaa.opticalsmartreader.ui.MainActivity;
import shoaa.smartmeterreader.ReadingResponse;

@Entity(tableName = "meters_data", primaryKeys = {"COVER_METER_ID", "CUSTOMER_BRANCH_CODE", "FACTORY_CODE"})
public class DatabaseMeterData {
    @ColumnInfo(name = "AUTHENTICATION_KEY")
    public String AUTHENTICATION_KEY = "MTAy0pss!sQLlO@2O2O";
    @ColumnInfo(name = "FACTORY_CODE")
    public int FACTORY_CODE = 0;
    @ColumnInfo(name = "FACTORY_TYPE")
    public int FACTORY_TYPE = 0;
    @ColumnInfo(name = "METER_MODEL")
    public String METER_MODEL = "1";
    @ColumnInfo(name = "METER_MODEL_NAME")
    public String METER_MODEL_NAME = BuildConfig.VERSION_NAME;
    @ColumnInfo(name = "COVER_METER_ID")
    public long COVER_METER_ID = 0;
    @ColumnInfo(name = "ORIGINAL_OPTICAL_METER_ID")
    public long ORIGINAL_OPTICAL_METER_ID = 0;
    @ColumnInfo(name = "OPTICAL_METER_ID")
    public long OPTICAL_METER_ID = 0;
    @ColumnInfo(name = "CUSTOMER_METER_ID")
    public long CUSTOMER_METER_ID = 0;
    @ColumnInfo(name = "CUSTOMER_ROW_INDEX")
    public long CUSTOMER_ROW_INDEX = 0;
    @ColumnInfo(name = "CUSTOMER_BRANCH_CODE")
    public int CUSTOMER_BRANCH_CODE = 0;
    @ColumnInfo(name = "CUSTOMER_AREA")
    public int CUSTOMER_AREA = 0;
    @ColumnInfo(name = "CUSTOMER_DAY")
    public int CUSTOMER_DAY = 0;
    @ColumnInfo(name = "CUSTOMER_MAIN")
    public int CUSTOMER_MAIN = 0;
    @ColumnInfo(name = "CUSTOMER_SUB")
    public int CUSTOMER_SUB = 0;
    @ColumnInfo(name = "READING_CODE")
    public int READING_CODE = 0;
    @ColumnInfo(name = "LOST_READING_CODE")
    public int LOST_READING_CODE = 0;
    @ColumnInfo(name = "DAMAGE_CODE")
    public int DAMAGE_CODE = 0;
    @ColumnInfo(name = "READING_DATA_FLAG")
    public int READING_DATA_FLAG = 0;
    @ColumnInfo(name = "USER_ID")
    public long USER_ID = 0;
    @ColumnInfo(name = "OPTICAL_SERIAL_NUMBER")
    public int OPTICAL_SERIAL_NUMBER = 0;
    @ColumnInfo(name = "READING_DATE")
    public String READING_DATE = "";
    @ColumnInfo(name = "METER_LOCATION")
    public String METER_LOCATION = "";
    @ColumnInfo(name = "IMAGE_FLAG")
    public int IMAGE_FLAG = 0;
    @ColumnInfo(name = "IMAGE_NAME")
    public String IMAGE_NAME = "";
    @ColumnInfo(name = "CIRCLE_MONTH")
    public int CIRCLE_MONTH = 0;
    @ColumnInfo(name = "CIRCLE_YEAR")
    public int CIRCLE_YEAR = 0;
    @ColumnInfo(name = "READER_NOTS")
    public String READER_NOTS = "";
    @ColumnInfo(name = "ITEM_1_NEW_BASEITEM_Meter_ID")
    public long ITEM_1_NEW_BASEITEM_Meter_ID = 0;
    @ColumnInfo(name = "OPTICAL_Customer_ID")
    public String OPTICAL_Customer_ID = "0";
    @ColumnInfo(name = "ITEM_2_NEW_BASEITEM_Customer_ID")
    public String ITEM_2_NEW_BASEITEM_Customer_ID = "0";
    @ColumnInfo(name = "ITEM_3_NEW_BASEITEM_CardID")
    public String ITEM_3_NEW_BASEITEM_CardID = "0";
    @ColumnInfo(name = "ITEM_4_NEW_BASEITEM_fw_version")
    public String ITEM_4_NEW_BASEITEM_fw_version = "0";
    @ColumnInfo(name = "ITEM_5_NEW_BASEITEM_ActivityType")
    public String ITEM_5_NEW_BASEITEM_ActivityType = "0";
    @ColumnInfo(name = "ITEM_6_NEW_BASEITEM_curent_Power_factor")
    public String ITEM_6_NEW_BASEITEM_curent_Power_factor = "0";
    @ColumnInfo(name = "ITEM_7_NEW_BASEITEM_last_year_Power_factor")
    public String ITEM_7_NEW_BASEITEM_last_year_Power_factor = "0";
    @ColumnInfo(name = "ITEM_8_NEW_BASEITEM_installing_technican_code")
    public String ITEM_8_NEW_BASEITEM_installing_technican_code = "0";
    @ColumnInfo(name = "ITEM_9_NEW_BASEITEM_installing_Date_and_time")
    public String ITEM_9_NEW_BASEITEM_installing_Date_and_time = "0";
    @ColumnInfo(name = "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time")
    public String ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = "0";
    @ColumnInfo(name = "ITEM_11_NEW_BASEITEM_Current_tarrif_installing")
    public String ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "0";
    @ColumnInfo(name = "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date")
    public String ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = "0";
    @ColumnInfo(name = "ITEM_13_NEW_BASEITEM_Meter_status")
    public int ITEM_13_NEW_BASEITEM_Meter_status = 0;
    @ColumnInfo(name = "ITEM_14_NEW_BASEITEM_Relay_status")
    public int ITEM_14_NEW_BASEITEM_Relay_status = 0;
    @ColumnInfo(name = "ITEM_15_NEW_BASEITEM_battery_status")
    public int ITEM_15_NEW_BASEITEM_battery_status = 0;
    @ColumnInfo(name = "ITEM_16_NEW_BASEITEM_Top_cover_status")
    public int ITEM_16_NEW_BASEITEM_Top_cover_status = 0;
    @ColumnInfo(name = "ITEM_17_NEW_BASEITEM_Side_cover_status")
    public int ITEM_17_NEW_BASEITEM_Side_cover_status = 0;
    @ColumnInfo(name = "ITEM_18_NEW_BASEITEM_Technical_code_event_1")
    public String ITEM_18_NEW_BASEITEM_Technical_code_event_1 = "0";
    @ColumnInfo(name = "ITEM_19_NEW_BASEITEM_event_type_1")
    public String ITEM_19_NEW_BASEITEM_event_type_1 = "0";
    @ColumnInfo(name = "ITEM_20_NEW_BASEITEM_event_Date_1")
    public String ITEM_20_NEW_BASEITEM_event_Date_1 = "0";
    @ColumnInfo(name = "ITEM_21_NEW_BASEITEM_Technical_code_event_2")
    public String ITEM_21_NEW_BASEITEM_Technical_code_event_2 = "0";
    @ColumnInfo(name = "ITEM_22_NEW_BASEITEM_event_type_2")
    public String ITEM_22_NEW_BASEITEM_event_type_2 = "0";
    @ColumnInfo(name = "ITEM_23_NEW_BASEITEM_event_Date_2")
    public String ITEM_23_NEW_BASEITEM_event_Date_2 = "0";
    @ColumnInfo(name = "ITEM_24_NEW_BASEITEM_Technical_code_event_3")
    public String ITEM_24_NEW_BASEITEM_Technical_code_event_3 = "0";
    @ColumnInfo(name = "ITEM_25_NEW_BASEITEM_event_type_3")
    public String ITEM_25_NEW_BASEITEM_event_type_3 = "0";
    @ColumnInfo(name = "ITEM_26_NEW_BASEITEM_event_Date_3")
    public String ITEM_26_NEW_BASEITEM_event_Date_3 = "0";
    @ColumnInfo(name = "ITEM_27_NEW_BASEITEM_recharge_number")
    public int ITEM_27_NEW_BASEITEM_recharge_number = 0;
    @ColumnInfo(name = "ITEM_28_NEW_BASEITEM_Recharge_Amount")
    public double ITEM_28_NEW_BASEITEM_Recharge_Amount = 0;
    @ColumnInfo(name = "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time")
    public String ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = "0";
    @ColumnInfo(name = "ITEM_30_NEW_BASEITEM_remaining_credit_kw")
    public double ITEM_30_NEW_BASEITEM_remaining_credit_kw = 0;
    @ColumnInfo(name = "ITEM_31_NEW_BASEITEM_remaining_credit_mony")
    public double ITEM_31_NEW_BASEITEM_remaining_credit_mony = 0;
    @ColumnInfo(name = "ITEM_32_NEW_BASEITEM_Debts")
    public double ITEM_32_NEW_BASEITEM_Debts = 0;
    @ColumnInfo(name = "ITEM_33_NEW_BASEITEM_Total_consumption_kw")
    public double ITEM_33_NEW_BASEITEM_Total_consumption_kw = 0;
    @ColumnInfo(name = "ITEM_34_NEW_BASEITEM_Total_consumption_mony")
    public double ITEM_34_NEW_BASEITEM_Total_consumption_mony = 0;
    @ColumnInfo(name = "ITEM_35_NEW_BASEITEM_Total_consumption_kvar")
    public double ITEM_35_NEW_BASEITEM_Total_consumption_kvar = 0;
    @ColumnInfo(name = "ITEM_36_NEW_BASEITEM_Current_Demand")
    public double ITEM_36_NEW_BASEITEM_Current_Demand = 0;
    @ColumnInfo(name = "ITEM_37_NEW_BASEITEM_Maximum_Demand")
    public double ITEM_37_NEW_BASEITEM_Maximum_Demand = 0;
    @ColumnInfo(name = "ITEM_38_NEW_BASEITEM_Maximum_Demand_date")
    public String ITEM_38_NEW_BASEITEM_Maximum_Demand_date = "0";
    @ColumnInfo(name = "ITEM_39_NEW_BASEITEM_instanteneous_volt")
    public double ITEM_39_NEW_BASEITEM_instanteneous_volt = 0;
    @ColumnInfo(name = "ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere")
    public double ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
    @ColumnInfo(name = "ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral")
    public double ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
    @ColumnInfo(name = "ITEM_42_NEW_BASEITEM_reverse_Kwh")
    public double ITEM_42_NEW_BASEITEM_reverse_Kwh = 0;
    @ColumnInfo(name = "ITEM_43_NEW_BASEITEM_unbalance_Kwh")
    public double ITEM_43_NEW_BASEITEM_unbalance_Kwh = 0;
    @ColumnInfo(name = "ITEM_44_NEW_BASEITEM_current_month_consumption_KW")
    public double ITEM_44_NEW_BASEITEM_current_month_consumption_KW = 0;
    @ColumnInfo(name = "ITEM_45_NEW_BASEITEM_current_month_consumption_MONY")
    public double ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = 0;
    @ColumnInfo(name = "ITEM_46_NEW_BASEITEM_1_month_consumption_kWh")
    public double ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_47_NEW_BASEITEM_2_month_consumption_kWh")
    public double ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_48_NEW_BASEITEM_3_month_consumption_kWh")
    public double ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_49_NEW_BASEITEM_4_month_consumption_kWh")
    public double ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_50_NEW_BASEITEM_5_month_consumption_kWh")
    public double ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_51_NEW_BASEITEM_6_month_consumption_kWh")
    public double ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_52_NEW_BASEITEM_7_month_consumption_kWh")
    public double ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_53_NEW_BASEITEM_8_month_consumption_kWh")
    public double ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_54_NEW_BASEITEM_9_month_consumption_kWh")
    public double ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_55_NEW_BASEITEM_10_month_consumption_kWh")
    public double ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_56_NEW_BASEITEM_11_month_consumption_kWh")
    public double ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_57_NEW_BASEITEM_12_month_consumption_kWh")
    public double ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = 0;
    @ColumnInfo(name = "ITEM_58_NEW_BASEITEM_1_month_consumption_Mony")
    public double ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_59_NEW_BASEITEM_2_month_consumption_Mony")
    public double ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_60_NEW_BASEITEM_3_month_consumption_Mony")
    public double ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_61_NEW_BASEITEM_4_month_consumption_Mony")
    public double ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_62_NEW_BASEITEM_5_month_consumption_Mony")
    public double ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_63_NEW_BASEITEM_6_month_consumption_Mony")
    public double ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_64_NEW_BASEITEM_7_month_consumption_Mony")
    public double ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_65_NEW_BASEITEM_8_month_consumption_Mony")
    public double ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_66_NEW_BASEITEM_9_month_consumption_Mony")
    public double ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_67_NEW_BASEITEM_10_month_consumption_Mony")
    public double ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_68_NEW_BASEITEM_11_month_consumption_Mony")
    public double ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_69_NEW_BASEITEM_12_month_consumption_Mony")
    public double ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = 0;
    @ColumnInfo(name = "ITEM_70_NEW_BASEITEM_maxim_demand_month_1")
    public double ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = 0;
    @ColumnInfo(name = "ITEM_71_NEW_BASEITEM_maxim_demand_month_2")
    public double ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = 0;
    @ColumnInfo(name = "ITEM_72_NEW_BASEITEM_maxim_demand_month_3")
    public double ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = 0;
    @ColumnInfo(name = "ITEM_73_NEW_BASEITEM_maxim_demand_month_4")
    public double ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = 0;
    @ColumnInfo(name = "ITEM_74_NEW_BASEITEM_maxim_demand_month_5")
    public double ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = 0;
    @ColumnInfo(name = "ITEM_75_NEW_BASEITEM_maxim_demand_month_6")
    public double ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = 0;
    @ColumnInfo(name = "ITEM_76_NEW_BASEITEM_maxim_demand_month_7")
    public double ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = 0;
    @ColumnInfo(name = "ITEM_77_NEW_BASEITEM_maxim_demand_month_8")
    public double ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = 0;
    @ColumnInfo(name = "ITEM_78_NEW_BASEITEM_maxim_demand_month_9")
    public double ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = 0;
    @ColumnInfo(name = "ITEM_79_NEW_BASEITEM_maxim_demand_month_10")
    public double ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = 0;
    @ColumnInfo(name = "ITEM_80_NEW_BASEITEM_maxim_demand_month_11")
    public double ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = 0;
    @ColumnInfo(name = "ITEM_81_NEW_BASEITEM_maxim_demand_month_12")
    public double ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = 0;
    @ColumnInfo(name = "ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1")
    public String ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = "0";
    @ColumnInfo(name = "ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2")
    public String ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = "0";
    @ColumnInfo(name = "ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3")
    public String ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = "0";
    @ColumnInfo(name = "ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4")
    public String ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = "0";
    @ColumnInfo(name = "ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5")
    public String ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = "0";
    @ColumnInfo(name = "ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6")
    public String ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = "0";
    @ColumnInfo(name = "ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7")
    public String ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = "0";
    @ColumnInfo(name = "ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8")
    public String ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = "0";
    @ColumnInfo(name = "ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9")
    public String ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = "0";
    @ColumnInfo(name = "ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10")
    public String ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = "0";
    @ColumnInfo(name = "ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11")
    public String ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = "0";
    @ColumnInfo(name = "ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12")
    public String ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = "0";
    @ColumnInfo(name = "ITEM_94_NEW_BASEITEM_kvar_consumption_month_1")
    public double ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = 0;
    @ColumnInfo(name = "ITEM_95_NEW_BASEITEM_kvar_consumption_month_2")
    public double ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = 0;
    @ColumnInfo(name = "ITEM_96_NEW_BASEITEM_kvar_consumption_month_3")
    public double ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = 0;
    @ColumnInfo(name = "ITEM_97_NEW_BASEITEM_kvar_consumption_month_4")
    public double ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = 0;
    @ColumnInfo(name = "ITEM_98_NEW_BASEITEM_kvar_consumption_month_5")
    public double ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = 0;
    @ColumnInfo(name = "ITEM_99_NEW_BASEITEM_kvar_consumption_month_6")
    public double ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = 0;
    @ColumnInfo(name = "ITEM_100_NEW_BASEITEM_kvar_consumption_month_7")
    public double ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = 0;
    @ColumnInfo(name = "ITEM_101_NEW_BASEITEM_kvar_consumption_month_8")
    public double ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = 0;
    @ColumnInfo(name = "ITEM_102_NEW_BASEITEM_kvar_consumption_month_9")
    public double ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = 0;
    @ColumnInfo(name = "ITEM_103_NEW_BASEITEM_kvar_consumption_month_10")
    public double ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = 0;
    @ColumnInfo(name = "ITEM_104_NEW_BASEITEM_kvar_consumption_month_11")
    public double ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = 0;
    @ColumnInfo(name = "ITEM_105_NEW_BASEITEM_kvar_consumption_month_12")
    public double ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = 0;
    @ColumnInfo(name = "IMSGE_BASE64")
    public String IMSGE_BASE64 = "";


    public DatabaseMeterData() {
    }

    public DatabaseMeterData(
            Context context,
            AppUser appUser,
            @NonNull Client client,
            AppLocationManager appLocationManager,
            CycleDate cycleDate,
            long barcode,
            int FACTORY_CODE,
            int READING_CODE,
            int LOST_READING_CODE,
            int DAMAGE_CODE,
            int READING_DATA_FLAG,
            int OPTICAL_SERIAL_NUMBER,
            ReadingResponse readingResponse,
            Enums.ActivityType activityType,
            String READER_NOTS,
            String image_path) {
        setBarcode(String.valueOf(barcode));
        if (READING_DATA_FLAG == 1)
            parseReadingResponse(readingResponse);
        READING_DATE = DateUtil.toApiFormat(new Date());
        if (this.FACTORY_TYPE == 0) {
            if (client.MeterTypeName != null && !client.MeterTypeName.isEmpty()) {
                this.FACTORY_TYPE = client.MeterTypeName.contains("ثلاث") ? 2 : 1;
            } else {
                this.FACTORY_TYPE = 1;
            }
        }
        setFromClientData(client);
        setREADING_CODE(READING_CODE);
        setLOST_READING_CODE(LOST_READING_CODE);
        setDAMAGE_CODE(DAMAGE_CODE);
        setREADING_DATA_FLAG(READING_DATA_FLAG);
        setUSER_ID(appUser);
        this.FACTORY_CODE = FACTORY_CODE;
        this.OPTICAL_SERIAL_NUMBER = OPTICAL_SERIAL_NUMBER;
        setMETER_LOCATION(appLocationManager);
        setCycleDate(cycleDate);
        setREADER_NOTES(activityType, READER_NOTS);
        setIMAGE(context, image_path);
    }

    private void parseReadingResponse(ReadingResponse readingResponse) {
        if (readingResponse instanceof GlobalResponse) {
            this.FACTORY_CODE = 6;
            if (!((GlobalResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((GlobalResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = ((GlobalResponse) readingResponse).getMeterModel();
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((GlobalResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((GlobalResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((GlobalResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((GlobalResponse) readingResponse).getCustomerID());
            this.OPTICAL_Customer_ID = handleCustomerId(((GlobalResponse) readingResponse).getCustomerID());
            this.ITEM_3_NEW_BASEITEM_CardID = ((GlobalResponse) readingResponse).getCustomerID();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((GlobalResponse) readingResponse).getfWVersion();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((GlobalResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((GlobalResponse) readingResponse).getPowerFactor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = "0";
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((GlobalResponse) readingResponse).getTechnicalCode();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getInstallingDate()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getDate()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }
            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((GlobalResponse) readingResponse).getTarrifID();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getTarrifActivationDate()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }
            String temperList = ((GlobalResponse) readingResponse).getTempresList().toLowerCase(Locale.ROOT).replaceAll(" ", "").replaceAll("-", "");
            int meterStatusIndex = temperList.indexOf("m") + 1;
            int relayStatusIndex = temperList.indexOf("r") + 1;
            int batteryStatusIndex = temperList.indexOf("b") + 1;
            int sideCoverStatusIndex = temperList.indexOf("s") + 1;
            int topCoverStatusIndex = temperList.indexOf("t") + 1;
            int meterStatus = 0;
            int relayStatus = 0;
            int batteryStatus = 0;
            int sideCoverStatus = 0;
            int topCoverStatus = 0;
            try {
                if (meterStatusIndex > 0 && meterStatusIndex < temperList.length())
                    meterStatus = Integer.parseInt(temperList.substring(meterStatusIndex, meterStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                if (relayStatusIndex > 0 && relayStatusIndex < temperList.length())
                    relayStatus = Integer.parseInt(temperList.substring(relayStatusIndex, relayStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                if (batteryStatusIndex > 0 && batteryStatusIndex < temperList.length())
                    batteryStatus = Integer.parseInt(temperList.substring(batteryStatusIndex, batteryStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                if (sideCoverStatusIndex > 0 && sideCoverStatusIndex < temperList.length())
                    sideCoverStatus = Integer.parseInt(temperList.substring(sideCoverStatusIndex, sideCoverStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                if (topCoverStatusIndex > 0 && topCoverStatusIndex < temperList.length())
                    topCoverStatus = Integer.parseInt(temperList.substring(topCoverStatusIndex, topCoverStatusIndex + 1).replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }

            this.ITEM_13_NEW_BASEITEM_Meter_status = meterStatus;
            this.ITEM_14_NEW_BASEITEM_Relay_status = relayStatus;
            this.ITEM_15_NEW_BASEITEM_battery_status = batteryStatus;
            this.ITEM_16_NEW_BASEITEM_Top_cover_status = topCoverStatus;
            this.ITEM_17_NEW_BASEITEM_Side_cover_status = sideCoverStatus;
            try {
                this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = ((GlobalResponse) readingResponse).getControlCardState().split(",")[0];
                this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = ((GlobalResponse) readingResponse).getControlCardState().split(",")[1];
                this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = ((GlobalResponse) readingResponse).getControlCardState().split(",")[2];
            } catch (Exception ignored) {
            }
            this.ITEM_19_NEW_BASEITEM_event_type_1 = "0";
            this.ITEM_20_NEW_BASEITEM_event_Date_1 = "0";
            this.ITEM_22_NEW_BASEITEM_event_type_2 = "0";
            this.ITEM_23_NEW_BASEITEM_event_Date_2 = "0";
            this.ITEM_25_NEW_BASEITEM_event_type_3 = "0";
            this.ITEM_26_NEW_BASEITEM_event_Date_3 = "0";
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((GlobalResponse) readingResponse).getChargeCount().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((GlobalResponse) readingResponse).getSumOfAllChargesPND().replaceAll("[^0-9.-]", "") + "." + ((GlobalResponse) readingResponse).getSumOfAllChargesPSTR().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }

            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getLastChargeDate()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }

            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }

            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((GlobalResponse) readingResponse).getRemainKW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((GlobalResponse) readingResponse).getRemainMoney().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((GlobalResponse) readingResponse).getDeon().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }

            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((GlobalResponse) readingResponse).getTotalConsum().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = 0.0;
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((GlobalResponse) readingResponse).getTotalConsumptionKvh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            this.ITEM_36_NEW_BASEITEM_Current_Demand = 0.0;
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandPH1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse((((GlobalResponse) readingResponse).getMaximumDemandDate())));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((GlobalResponse) readingResponse).getInstanteneousVolt().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandPH1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0.0;
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((GlobalResponse) readingResponse).getReverse().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((GlobalResponse) readingResponse).getUnblanceInKW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((GlobalResponse) readingResponse).getCurrentMonthConsumption().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = 0.0;
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((GlobalResponse) readingResponse).getConsm12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm01().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm02().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm03().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm04().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm05().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm06().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm07().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm08().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm09().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((GlobalResponse) readingResponse).getMoneyconsm12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth1().replaceAll("[^0-9.-]", ""));
            this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth2().replaceAll("[^0-9.-]", ""));
            this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth3().replaceAll("[^0-9.-]", ""));
            this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth4().replaceAll("[^0-9.-]", ""));
            this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth5().replaceAll("[^0-9.-]", ""));
            this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth6().replaceAll("[^0-9.-]", ""));
            this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth7().replaceAll("[^0-9.-]", ""));
            this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth8().replaceAll("[^0-9.-]", ""));
            this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth9().replaceAll("[^0-9.-]", ""));
            this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth10().replaceAll("[^0-9.-]", ""));
            this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth11().replaceAll("[^0-9.-]", ""));
            this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((GlobalResponse) readingResponse).getMaxDemandMonth12().replaceAll("[^0-9.-]", ""));
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth1Date()));
            } catch (ParseException e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth2Date()));
            } catch (ParseException e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth3Date()));
            } catch (ParseException e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth4Date()));
            } catch (ParseException e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth5Date()));
            } catch (ParseException e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth6Date()));
            } catch (ParseException e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth7Date()));
            } catch (ParseException e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth8Date()));
            } catch (ParseException e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth9Date()));
            } catch (ParseException e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth10Date()));
            } catch (ParseException e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth11Date()));
            } catch (ParseException e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((GlobalResponse) readingResponse).getMaxDemandMonth12Date()));
            } catch (ParseException e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((GlobalResponse) readingResponse).getConsm_kvar12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof EsmcResponse) {
            this.FACTORY_CODE = 1;
            if (!((EsmcResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((EsmcResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = ((EsmcResponse) readingResponse).getMeterModel();
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((EsmcResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((EsmcResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((EsmcResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((EsmcResponse) readingResponse).getCustomerId());
            this.OPTICAL_Customer_ID = handleCustomerId(((EsmcResponse) readingResponse).getCustomerId());
            this.ITEM_3_NEW_BASEITEM_CardID = ((EsmcResponse) readingResponse).getCardId();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((EsmcResponse) readingResponse).getFwVersion();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((EsmcResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((EsmcResponse) readingResponse).getCurentPowerFactor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((EsmcResponse) readingResponse).getLastYearPowerFactor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((EsmcResponse) readingResponse).getInstallingTechnicanCode();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getInstallingDateAndTime()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMeterDateAndTime()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }
            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((EsmcResponse) readingResponse).getCurrentTarrifInstalling();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getCurrentTariffActivationDate()));
            } catch (Exception e) {
                try {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getLastRechargeDateAndTime()));
                } catch (Exception we) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
                }
            }

            String[] meterStatus = ((EsmcResponse) readingResponse).getMeterStatus().split(",");
            for (String name : meterStatus) {
                if (name.toLowerCase(Locale.ROOT).contains("meterstatus")) {
                    int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
                    String s = name.toLowerCase(Locale.ROOT).substring(index);
                    int m = 1;
                    try {
                        m = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
                    } catch (Exception ignored) {
                    }
                    if (s.startsWith("normal") || m == 0)
                        this.ITEM_13_NEW_BASEITEM_Meter_status = 0;
                    else
                        this.ITEM_13_NEW_BASEITEM_Meter_status = 1;
                } else if (name.toLowerCase(Locale.ROOT).contains("relay")) {
                    int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
                    String s = name.toLowerCase(Locale.ROOT).substring(index);
                    int r = 1;
                    try {
                        r = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
                    } catch (Exception ignored) {
                    }
                    if (s.startsWith("connected") || r == 0)
                        this.ITEM_14_NEW_BASEITEM_Relay_status = 0;
                    else
                        this.ITEM_14_NEW_BASEITEM_Relay_status = 1;
                } else if (name.toLowerCase(Locale.ROOT).contains("battery")) {
                    int index = name.toLowerCase(Locale.ROOT).indexOf("=") + 1;
                    String s = name.toLowerCase(Locale.ROOT).substring(index);
                    int b = 1;
                    try {
                        b = Integer.parseInt(s.replaceAll("[^0-9.]", "").split("\\.")[0]);
                    } catch (Exception ignored) {
                    }
                    if (s.startsWith("normal") || b == 0)
                        this.ITEM_15_NEW_BASEITEM_battery_status = 0;
                    else
                        this.ITEM_15_NEW_BASEITEM_battery_status = 1;
                }
            }
            this.ITEM_16_NEW_BASEITEM_Top_cover_status = ((EsmcResponse) readingResponse).getTopCoverStatus().toLowerCase(Locale.ROOT).contains("top cover open") ? 1 : 0;
            this.ITEM_17_NEW_BASEITEM_Side_cover_status = ((EsmcResponse) readingResponse).getTopCoverStatus().toLowerCase(Locale.ROOT).contains("terminal cover open") ? 1 : 0;
            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = ((EsmcResponse) readingResponse).getTechnicalCodeEvent1();
            this.ITEM_19_NEW_BASEITEM_event_type_1 = ((EsmcResponse) readingResponse).getEventType1();
            this.ITEM_20_NEW_BASEITEM_event_Date_1 = ((EsmcResponse) readingResponse).getEventDate1();
            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = ((EsmcResponse) readingResponse).getTechnicalCodeEvent2();
            this.ITEM_22_NEW_BASEITEM_event_type_2 = ((EsmcResponse) readingResponse).getEventType2();
            this.ITEM_23_NEW_BASEITEM_event_Date_2 = ((EsmcResponse) readingResponse).getEventDate2();
            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = ((EsmcResponse) readingResponse).getTechnicalCodeEvent3();
            this.ITEM_25_NEW_BASEITEM_event_type_3 = ((EsmcResponse) readingResponse).getEventType3();
            this.ITEM_26_NEW_BASEITEM_event_Date_3 = ((EsmcResponse) readingResponse).getEventDate3();
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((EsmcResponse) readingResponse).getRechargeNumber().replaceAll("[^0-9.-]", "").split("\\.")[0]);
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((EsmcResponse) readingResponse).getRechargeAmount().replaceAll("[^0-9.-]", ""));
            } catch (Exception e) {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((EsmcResponse) readingResponse).getRechargeAmount().replaceAll("[^0-9.-]", ""));
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getLastRechargeDateAndTime()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((EsmcResponse) readingResponse).getRemainingCreditKw().replaceAll("[^0-9.-]", ""));
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((EsmcResponse) readingResponse).getRemainingCreditMoney().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((EsmcResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }

            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((EsmcResponse) readingResponse).getTotalConsumptionKw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((EsmcResponse) readingResponse).getTotalConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((EsmcResponse) readingResponse).getTotalConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((EsmcResponse) readingResponse).getCurrentDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((EsmcResponse) readingResponse).getMaximumDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximumDemandDate()));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((EsmcResponse) readingResponse).getInstanteneousVolt().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = Double.parseDouble(((EsmcResponse) readingResponse).getInstanteneousCurrentPhaseAmpere().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = Double.parseDouble(((EsmcResponse) readingResponse).getInstanteneousCurrentNeutral().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((EsmcResponse) readingResponse).getReverseKwh());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((EsmcResponse) readingResponse).getUnbalanceKwh());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((EsmcResponse) readingResponse).getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((EsmcResponse) readingResponse).getCurrentMonthConsumptionMoney());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((EsmcResponse) readingResponse).getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth1ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth2ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth3ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth4ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth5ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth6ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth7ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth8ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth9ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth10ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth11ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((EsmcResponse) readingResponse).getMonth12ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((EsmcResponse) readingResponse).getMaximDemandMonth12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth1Date()));
            } catch (ParseException e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth2Date()));
            } catch (ParseException e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth3Date()));
            } catch (ParseException e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth4Date()));
            } catch (ParseException e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth5Date()));
            } catch (ParseException e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth6Date()));
            } catch (ParseException e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth7Date()));
            } catch (ParseException e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth8Date()));
            } catch (ParseException e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth9Date()));
            } catch (ParseException e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth10Date()));
            } catch (ParseException e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth11Date()));
            } catch (ParseException e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((EsmcResponse) readingResponse).getMaximDemandMonth12Date()));
            } catch (ParseException e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth1ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth2ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth3ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth4ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth5ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth6ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth7ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth8ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth9ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth10ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth11ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((EsmcResponse) readingResponse).getMonth12ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof ElectrometerResponse) {
            this.FACTORY_CODE = 3;
            if (!((ElectrometerResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((ElectrometerResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = ((ElectrometerResponse) readingResponse).getMeterModel();
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((ElectrometerResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((ElectrometerResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((ElectrometerResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((ElectrometerResponse) readingResponse).getCustomerId());
            this.OPTICAL_Customer_ID = handleCustomerId(((ElectrometerResponse) readingResponse).getCustomerId());
            this.ITEM_3_NEW_BASEITEM_CardID = ((ElectrometerResponse) readingResponse).getCardId();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((ElectrometerResponse) readingResponse).getFwVersion();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((ElectrometerResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((ElectrometerResponse) readingResponse).getCurentPowerFactor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((ElectrometerResponse) readingResponse).getLastYearPowerFactor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((ElectrometerResponse) readingResponse).getInstallingTechnicanCode();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getInstallingDateAndTime()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMeterDateAndTime()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }

            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((ElectrometerResponse) readingResponse).getCurrentTarrifInstalling();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getCurrentTariffActivationDate()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }
            this.ITEM_13_NEW_BASEITEM_Meter_status = ((ElectrometerResponse) readingResponse).getMeterStatus().toLowerCase(Locale.ROOT).trim().equalsIgnoreCase("m0") ? 0 : 1;
            this.ITEM_14_NEW_BASEITEM_Relay_status = ((ElectrometerResponse) readingResponse).getRelayStatus().toLowerCase(Locale.ROOT).trim().equalsIgnoreCase("r0") ? 0 : 1;
            this.ITEM_15_NEW_BASEITEM_battery_status = ((ElectrometerResponse) readingResponse).getBatteryStatus().toLowerCase(Locale.ROOT).trim().equalsIgnoreCase("b0") ? 0 : 1;
            this.ITEM_16_NEW_BASEITEM_Top_cover_status = ((ElectrometerResponse) readingResponse).getTopCoverStatus().toLowerCase(Locale.ROOT).trim().equalsIgnoreCase("t0_0") ? 0 : 1;
            this.ITEM_17_NEW_BASEITEM_Side_cover_status = ((ElectrometerResponse) readingResponse).getSideCoverStatus().toLowerCase(Locale.ROOT).trim().equalsIgnoreCase("s0_0") ? 0 : 1;
            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = ((ElectrometerResponse) readingResponse).getTechnicalCodeEvent1();
            this.ITEM_19_NEW_BASEITEM_event_type_1 = ((ElectrometerResponse) readingResponse).getEventType1();
            this.ITEM_20_NEW_BASEITEM_event_Date_1 = ((ElectrometerResponse) readingResponse).getEventDate1();
            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = ((ElectrometerResponse) readingResponse).getTechnicalCodeEvent2();
            this.ITEM_22_NEW_BASEITEM_event_type_2 = ((ElectrometerResponse) readingResponse).getEventType2();
            this.ITEM_23_NEW_BASEITEM_event_Date_2 = ((ElectrometerResponse) readingResponse).getEventDate2();
            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = ((ElectrometerResponse) readingResponse).getTechnicalCodeEvent3();
            this.ITEM_25_NEW_BASEITEM_event_type_3 = ((ElectrometerResponse) readingResponse).getEventType3();
            this.ITEM_26_NEW_BASEITEM_event_Date_3 = ((ElectrometerResponse) readingResponse).getEventDate3();
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((ElectrometerResponse) readingResponse).getRechargeNumber().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((ElectrometerResponse) readingResponse).getRechargeAmount().replaceAll("[^0-9.]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getLastRechargeDateAndTime()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((ElectrometerResponse) readingResponse).getRemainingCreditKw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((ElectrometerResponse) readingResponse).getRemainingCreditMoney().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((ElectrometerResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }

            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((ElectrometerResponse) readingResponse).getTotalConsumptionKw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getTotalConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((ElectrometerResponse) readingResponse).getTotalConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((ElectrometerResponse) readingResponse).getCurrentDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximumDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximumDemandDate()));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            this.ITEM_39_NEW_BASEITEM_instanteneous_volt = 0;
            this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((ElectrometerResponse) readingResponse).getReverseKwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((ElectrometerResponse) readingResponse).getUnbalanceKwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((ElectrometerResponse) readingResponse).getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((ElectrometerResponse) readingResponse).getCurrentMonthConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth1ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth2ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth3ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth4ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth5ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth6ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth7ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth8ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth9ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth10ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth11ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth12ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMaximDemandMonth12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth1Date()));
            } catch (Exception e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth2Date()));
            } catch (Exception e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth3Date()));
            } catch (Exception e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth4Date()));
            } catch (Exception e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth5Date()));
            } catch (Exception e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }

            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth6Date()));
            } catch (Exception e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth7Date()));
            } catch (Exception e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth8Date()));
            } catch (Exception e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth9Date()));
            } catch (Exception e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth10Date()));
            } catch (Exception e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth11Date()));
            } catch (Exception e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((ElectrometerResponse) readingResponse).getMaximDemandMonth12Date()));
            } catch (Exception e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth1ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth2ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth3ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth4ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth5ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth6ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth7ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth8ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth9ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth10ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth11ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((ElectrometerResponse) readingResponse).getMonth12ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof MaasaraResponse) {
//            this.FACTORY_CODE = 4;
//            if (!((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
//            this.FACTORY_TYPE = ((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
//            this.METER_MODEL = ((MaasaraResponse) readingResponse).getMeterModel();
//            try {
//                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((MaasaraResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
//                this.OPTICAL_METER_ID = Long.parseLong(((MaasaraResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
//            } catch (Exception ignore) {
//                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
//                this.OPTICAL_METER_ID = this.COVER_METER_ID;
//            }
//
//            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((MaasaraResponse) readingResponse).getCustomerId());
//            this.OPTICAL_Customer_ID = handleCustomerId(((MaasaraResponse) readingResponse).getCustomerId());
//            this.ITEM_3_NEW_BASEITEM_CardID = ((MaasaraResponse) readingResponse).getCardId();
//            this.ITEM_4_NEW_BASEITEM_fw_version = ((MaasaraResponse) readingResponse).getFwVersion();
//            this.ITEM_5_NEW_BASEITEM_ActivityType = ((MaasaraResponse) readingResponse).getActivityType();
//            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((MaasaraResponse) readingResponse).getCurentPowerFactor();
//            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((MaasaraResponse) readingResponse).getLastYearPowerFactor();
//            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((MaasaraResponse) readingResponse).getInstallingTechnicanCode();
//            try {
//                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getInstallingDateAndTime()));
//            } catch (Exception e) {
//                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
//            }
//            try {
//                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMeterDateAndTime()));
//            } catch (Exception e) {
//                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
//            }
//            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((MaasaraResponse) readingResponse).getCurrentTarrifInstalling();
//            try {
//                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
//            } catch (Exception e) {
//                try {
//                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
//                } catch (Exception e1) {
//                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
//                }
//            }
//            try {
//                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getCurrentTariffActivationDate()));
//            } catch (Exception e) {
//                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
//            }
//
//            this.ITEM_13_NEW_BASEITEM_Meter_status = ((MaasaraResponse) readingResponse).getMeterStatus().equalsIgnoreCase("0") ? 0 : 1;
//            this.ITEM_14_NEW_BASEITEM_Relay_status = ((MaasaraResponse) readingResponse).getRelayStatus().equalsIgnoreCase("0") ? 0 : 1;
//            this.ITEM_15_NEW_BASEITEM_battery_status = ((MaasaraResponse) readingResponse).getBatteryStatus().equalsIgnoreCase("0") ? 0 : 1;
//            this.ITEM_16_NEW_BASEITEM_Top_cover_status = ((MaasaraResponse) readingResponse).getSideCoverStatus().equalsIgnoreCase("0") ? 0 : 1;
//            this.ITEM_17_NEW_BASEITEM_Side_cover_status = ((MaasaraResponse) readingResponse).getTopCoverStatus().equalsIgnoreCase("0") ? 0 : 1;
//            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = ((MaasaraResponse) readingResponse).getTechnicalCodeEvent1();
//            this.ITEM_19_NEW_BASEITEM_event_type_1 = ((MaasaraResponse) readingResponse).getEventType1();
//            this.ITEM_20_NEW_BASEITEM_event_Date_1 = ((MaasaraResponse) readingResponse).getEventDate1();
//            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = ((MaasaraResponse) readingResponse).getTechnicalCodeEvent2();
//            this.ITEM_22_NEW_BASEITEM_event_type_2 = ((MaasaraResponse) readingResponse).getEventType2();
//            this.ITEM_23_NEW_BASEITEM_event_Date_2 = ((MaasaraResponse) readingResponse).getEventDate2();
//            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = ((MaasaraResponse) readingResponse).getTechnicalCodeEvent3();
//            this.ITEM_25_NEW_BASEITEM_event_type_3 = ((MaasaraResponse) readingResponse).getEventType3();
//            this.ITEM_26_NEW_BASEITEM_event_Date_3 = ((MaasaraResponse) readingResponse).getEventDate3();
//            try {
//                this.ITEM_27_NEW_BASEITEM_recharge_number = (int) (Double.parseDouble(((MaasaraResponse) readingResponse).getRechargeNumber().replaceAll("[^0-9.]", "")) * 100);
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((MaasaraResponse) readingResponse).getRechargeAmount().replaceAll("[^0-9.]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getLastRechargeDateAndTime()));
//            } catch (Exception e) {
//                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
//            }
//            try {
//                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
//                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
//                }
//            } catch (Exception ignore) {
//            }
//
//            try {
//                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((MaasaraResponse) readingResponse).getRemainingCreditKw().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((MaasaraResponse) readingResponse).getRemainingCreditMoney().replaceAll("[^0-9.-]", ""))));
//                double deon;
//                if (remainMoney < 0) {
//                    deon = remainMoney * -1;
//                    remainMoney = 0;
//                } else {
//                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((MaasaraResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
//                    if (deon < 0)
//                        deon = deon * -1;
//                    if (deon > 0)
//                        remainMoney = 0;
//                }
//                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
//                this.ITEM_32_NEW_BASEITEM_Debts = deon;
//            } catch (Exception ignore) {
//            }
//            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
//                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
//            }
//            try {
//                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((MaasaraResponse) readingResponse).getTotalConsumptionKw().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((MaasaraResponse) readingResponse).getTotalConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((MaasaraResponse) readingResponse).getTotalConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrentDemand().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble((((MaasaraResponse) readingResponse).getMaximumDemand().split(",")[0]).split("-")[0].replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaximumDemandDate()));
//            } catch (Exception e) {
//                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
//            }
//            this.ITEM_39_NEW_BASEITEM_instanteneous_volt = 0;
//            this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
//            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
//            try {
//                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((MaasaraResponse) readingResponse).getReverseKwh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((MaasaraResponse) readingResponse).getUnbalanceKwh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrentMonthConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth1ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth2ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth3ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth4ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth5ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth6ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth7ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth8ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth9ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth10ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth11ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth12ConsumptionMoney().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth1().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth2().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth3().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth4().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth5().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth6().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth7().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth8().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth9().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth10().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth11().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximDemandMonth12().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth1ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth2ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth3ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth4ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth5ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth6ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth7ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth8ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth9ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth10ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth11ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }
//            try {
//                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth12ConsumptionKvar().replaceAll("[^0-9.-]", ""));
//            } catch (Exception ignore) {
//            }

            this.FACTORY_CODE = 4;
            if (!((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = "1";
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((MaasaraResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((MaasaraResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((MaasaraResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((MaasaraResponse) readingResponse).getCustomer_ID());
            this.OPTICAL_Customer_ID = handleCustomerId(((MaasaraResponse) readingResponse).getCustomer_ID());
            this.ITEM_3_NEW_BASEITEM_CardID = ((MaasaraResponse) readingResponse).getCardID();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((MaasaraResponse) readingResponse).getFw_version();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((MaasaraResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((MaasaraResponse) readingResponse).getCurent_Power_factor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((MaasaraResponse) readingResponse).getLast_year_Power_factor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((MaasaraResponse) readingResponse).getInstalling_technican_code();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getInstalling_Date_and_time()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMeter_Date() + " " + ((MaasaraResponse) readingResponse).getMeter_Time()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }

            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((MaasaraResponse) readingResponse).getItem_13_Current_tarrif_installing();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getCurrent_tariff_activation_date()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }


            try {
                this.ITEM_13_NEW_BASEITEM_Meter_status = Integer.parseInt(((MaasaraResponse) readingResponse).getMeter_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_14_NEW_BASEITEM_Relay_status = Integer.parseInt(((MaasaraResponse) readingResponse).getRelay_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_15_NEW_BASEITEM_battery_status = Integer.parseInt(((MaasaraResponse) readingResponse).getBattery_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_16_NEW_BASEITEM_Top_cover_status = Integer.parseInt(((MaasaraResponse) readingResponse).getTop_cover_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_17_NEW_BASEITEM_Side_cover_status = Integer.parseInt(((MaasaraResponse) readingResponse).getSide_cover_status());
            } catch (Exception ignore) {
            }
            String events = ((MaasaraResponse) readingResponse).getTechnical_events();
            String[] eventsArray = events.split(",");
            int index = 1;
            for (String s : eventsArray) {
                try {
                    if (!s.isEmpty()) {

                        if (index == 1)
                            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 2)
                            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 3)
                            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = s.substring(s.lastIndexOf(":") + 1);

                        String substring = s.substring(s.indexOf(":") + 1, s.lastIndexOf(":"));
                        if (index == 1)
                            this.ITEM_20_NEW_BASEITEM_event_Date_1 = substring;
                        if (index == 2)
                            this.ITEM_23_NEW_BASEITEM_event_Date_2 = substring;
                        if (index == 3)
                            this.ITEM_26_NEW_BASEITEM_event_Date_3 = substring;

                        int code = Integer.parseInt(s.substring(0, s.indexOf(":")));
                        if (code == 1) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "TOP COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "TOP COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "TOP COVER OPEN";
                        } else if (code == 2) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "SIDE COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "SIDE COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "SIDE COVER OPEN";
                        } else if (code == 3) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "battery undervoltage";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "battery undervoltage";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "battery undervoltage";
                        } else if (code == 4) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "current reverse";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "current reverse";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "current reverse";
                        } else if (code == 6) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "bypass";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "bypass";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "bypass";
                        } else if (code == 7) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "permanent overload";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "permanent overload";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "permanent overload";
                        } else if (code == 8) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Relay failure";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Relay failure";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Relay failure";
                        } else if (code == 9) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Modification date and time";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Modification date and time";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Modification date and time";
                        }
                        index++;
                    }
                } catch (Exception ignored) {
                }
            }
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((MaasaraResponse) readingResponse).getRecharge_number().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((MaasaraResponse) readingResponse).getRecharge_Amount().replaceAll("[^0-9.]", ""));
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getLast_recharge_Date() + " " + ((MaasaraResponse) readingResponse).getLast_recharge_Time()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((MaasaraResponse) readingResponse).getRemaining_credit_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((MaasaraResponse) readingResponse).getRemaining_credit_mony().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((MaasaraResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }

            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((MaasaraResponse) readingResponse).getTotal_consumption_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((MaasaraResponse) readingResponse).getTotal_consumption_mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((MaasaraResponse) readingResponse).getTotal_consumption_kvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrent_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((MaasaraResponse) readingResponse).getMaximum_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse((((MaasaraResponse) readingResponse).getMaximum_Demand_date().split(",")[1])));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((MaasaraResponse) readingResponse).getInstanteneous_volt().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignored) {
            }
            this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((MaasaraResponse) readingResponse).getReverse_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((MaasaraResponse) readingResponse).getUnbalance_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrent_month_consumption_KW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((MaasaraResponse) readingResponse).getCurrent_month_consumption_MONY().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_1_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_2_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_3_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_4_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_5_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_6_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_7_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_8_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_9_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_10_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_11_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_12_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_1_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_2_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_3_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_4_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_5_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_6_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_7_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_8_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_9_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_10_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_11_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((MaasaraResponse) readingResponse).getMonth_12_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((MaasaraResponse) readingResponse).getMaxim_demand_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_1()));
            } catch (Exception e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_2()));
            } catch (Exception e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_3()));
            } catch (Exception e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_4()));
            } catch (Exception e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_5()));
            } catch (Exception e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_6()));
            } catch (Exception e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_7()));
            } catch (Exception e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_8()));
            } catch (Exception e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_9()));
            } catch (Exception e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_10()));
            } catch (Exception e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_11()));
            } catch (Exception e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((MaasaraResponse) readingResponse).getMaxim_demand_month_Date_12()));
            } catch (Exception e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }

            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((MaasaraResponse) readingResponse).getKvar_consumption_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof Hay2aResponse) {
            this.FACTORY_CODE = 5;
            if (!((Hay2aResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((Hay2aResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = "1";
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((Hay2aResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((Hay2aResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((Hay2aResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((Hay2aResponse) readingResponse).getCustomer_ID());
            this.OPTICAL_Customer_ID = handleCustomerId(((Hay2aResponse) readingResponse).getCustomer_ID());
            this.ITEM_3_NEW_BASEITEM_CardID = ((Hay2aResponse) readingResponse).getCardID();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((Hay2aResponse) readingResponse).getFw_version();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((Hay2aResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((Hay2aResponse) readingResponse).getCurent_Power_factor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((Hay2aResponse) readingResponse).getLast_year_Power_factor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((Hay2aResponse) readingResponse).getInstalling_technican_code();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getInstalling_Date_and_time()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMeter_Date() + " " + ((Hay2aResponse) readingResponse).getMeter_Time()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }

            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((Hay2aResponse) readingResponse).getItem_13_Current_tarrif_installing();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getCurrent_tariff_activation_date()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }


            try {
                this.ITEM_13_NEW_BASEITEM_Meter_status = Integer.parseInt(((Hay2aResponse) readingResponse).getMeter_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_14_NEW_BASEITEM_Relay_status = Integer.parseInt(((Hay2aResponse) readingResponse).getRelay_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_15_NEW_BASEITEM_battery_status = Integer.parseInt(((Hay2aResponse) readingResponse).getBattery_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_16_NEW_BASEITEM_Top_cover_status = Integer.parseInt(((Hay2aResponse) readingResponse).getTop_cover_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_17_NEW_BASEITEM_Side_cover_status = Integer.parseInt(((Hay2aResponse) readingResponse).getSide_cover_status());
            } catch (Exception ignore) {
            }
            String events = ((Hay2aResponse) readingResponse).getTechnical_events();
            String[] eventsArray = events.split(",");
            int index = 1;
            for (String s : eventsArray) {
                try {
                    if (!s.isEmpty()) {

                        if (index == 1)
                            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 2)
                            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 3)
                            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = s.substring(s.lastIndexOf(":") + 1);

                        String substring = s.substring(s.indexOf(":") + 1, s.lastIndexOf(":"));
                        if (index == 1)
                            this.ITEM_20_NEW_BASEITEM_event_Date_1 = substring;
                        if (index == 2)
                            this.ITEM_23_NEW_BASEITEM_event_Date_2 = substring;
                        if (index == 3)
                            this.ITEM_26_NEW_BASEITEM_event_Date_3 = substring;

                        int code = Integer.parseInt(s.substring(0, s.indexOf(":")));
                        if (code == 1) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "TOP COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "TOP COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "TOP COVER OPEN";
                        } else if (code == 2) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "SIDE COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "SIDE COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "SIDE COVER OPEN";
                        } else if (code == 3) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "battery undervoltage";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "battery undervoltage";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "battery undervoltage";
                        } else if (code == 4) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "current reverse";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "current reverse";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "current reverse";
                        } else if (code == 6) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "bypass";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "bypass";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "bypass";
                        } else if (code == 7) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "permanent overload";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "permanent overload";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "permanent overload";
                        } else if (code == 8) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Relay failure";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Relay failure";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Relay failure";
                        } else if (code == 9) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Modification date and time";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Modification date and time";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Modification date and time";
                        }
                        index++;
                    }
                } catch (Exception ignored) {
                }
            }
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((Hay2aResponse) readingResponse).getRecharge_number().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((Hay2aResponse) readingResponse).getRecharge_Amount().replaceAll("[^0-9.]", ""));
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getLast_recharge_Date() + " " + ((Hay2aResponse) readingResponse).getLast_recharge_Time()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((Hay2aResponse) readingResponse).getRemaining_credit_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((Hay2aResponse) readingResponse).getRemaining_credit_mony().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((Hay2aResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }

            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((Hay2aResponse) readingResponse).getTotal_consumption_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((Hay2aResponse) readingResponse).getTotal_consumption_mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((Hay2aResponse) readingResponse).getTotal_consumption_kvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((Hay2aResponse) readingResponse).getCurrent_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((Hay2aResponse) readingResponse).getMaximum_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse((((Hay2aResponse) readingResponse).getMaximum_Demand_date().split(",")[1])));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((Hay2aResponse) readingResponse).getInstanteneous_volt().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignored) {
            }
            this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((Hay2aResponse) readingResponse).getReverse_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((Hay2aResponse) readingResponse).getUnbalance_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((Hay2aResponse) readingResponse).getCurrent_month_consumption_KW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((Hay2aResponse) readingResponse).getCurrent_month_consumption_MONY().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_1_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_2_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_3_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_4_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_5_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_6_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_7_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_8_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_9_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_10_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_11_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_12_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_1_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_2_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_3_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_4_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_5_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_6_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_7_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_8_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_9_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_10_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_11_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((Hay2aResponse) readingResponse).getMonth_12_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((Hay2aResponse) readingResponse).getMaxim_demand_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_1()));
            } catch (Exception e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_2()));
            } catch (Exception e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_3()));
            } catch (Exception e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_4()));
            } catch (Exception e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_5()));
            } catch (Exception e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_6()));
            } catch (Exception e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_7()));
            } catch (Exception e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_8()));
            } catch (Exception e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_9()));
            } catch (Exception e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_10()));
            } catch (Exception e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_11()));
            } catch (Exception e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((Hay2aResponse) readingResponse).getMaxim_demand_month_Date_12()));
            } catch (Exception e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }

            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((Hay2aResponse) readingResponse).getKvar_consumption_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof IskraResponse) {
            this.FACTORY_CODE = 2;
            if (!((IskraResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((IskraResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = ((IskraResponse) readingResponse).getMeterModel();
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((IskraResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((IskraResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((IskraResponse) readingResponse).getMeterId().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((IskraResponse) readingResponse).getCustomerId());
            this.OPTICAL_Customer_ID = handleCustomerId(((IskraResponse) readingResponse).getCustomerId());
            this.ITEM_3_NEW_BASEITEM_CardID = ((IskraResponse) readingResponse).getCardId();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((IskraResponse) readingResponse).getFwVersion();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((IskraResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((IskraResponse) readingResponse).getCurentPowerFactor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((IskraResponse) readingResponse).getLastYearPowerFactor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((IskraResponse) readingResponse).getInstallingTechnicanCode();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getInstallingDateAndTime()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMeterDateAndTime()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }

            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((IskraResponse) readingResponse).getCurrentTarrifInstalling();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getCurrentTariffActivationDate()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }


            try {
                this.ITEM_13_NEW_BASEITEM_Meter_status = Integer.parseInt(((IskraResponse) readingResponse).getMeterStatus());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_14_NEW_BASEITEM_Relay_status = Integer.parseInt(((IskraResponse) readingResponse).getRelayStatus());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_15_NEW_BASEITEM_battery_status = Integer.parseInt(((IskraResponse) readingResponse).getBatteryStatus());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_16_NEW_BASEITEM_Top_cover_status = Integer.parseInt(((IskraResponse) readingResponse).getTopCoverStatus());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_17_NEW_BASEITEM_Side_cover_status = Integer.parseInt(((IskraResponse) readingResponse).getSideCoverStatus());
            } catch (Exception ignore) {
            }
            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = ((IskraResponse) readingResponse).getTechnicalCodeEvent1();
            this.ITEM_19_NEW_BASEITEM_event_type_1 = ((IskraResponse) readingResponse).getEventType1();
            this.ITEM_20_NEW_BASEITEM_event_Date_1 = ((IskraResponse) readingResponse).getEventDate1();
            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = ((IskraResponse) readingResponse).getTechnicalCodeEvent2();
            this.ITEM_22_NEW_BASEITEM_event_type_2 = ((IskraResponse) readingResponse).getEventType2();
            this.ITEM_23_NEW_BASEITEM_event_Date_2 = ((IskraResponse) readingResponse).getEventDate2();
            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = ((IskraResponse) readingResponse).getTechnicalCodeEvent3();
            this.ITEM_25_NEW_BASEITEM_event_type_3 = ((IskraResponse) readingResponse).getEventType3();
            this.ITEM_26_NEW_BASEITEM_event_Date_3 = ((IskraResponse) readingResponse).getEventDate3();
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((IskraResponse) readingResponse).getRechargeNumber().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception e) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((IskraResponse) readingResponse).getRechargeAmount().replaceAll("[^0-9.]", ""));
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getLastRechargeDateAndTime()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((IskraResponse) readingResponse).getRemainingCreditKw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((IskraResponse) readingResponse).getRemainingCreditMoney().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((IskraResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }
            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((IskraResponse) readingResponse).getTotalConsumptionKw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((IskraResponse) readingResponse).getTotalConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((IskraResponse) readingResponse).getTotalConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((IskraResponse) readingResponse).getCurrentDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((IskraResponse) readingResponse).getMaximumDemand().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse((((IskraResponse) readingResponse).getMaximumDemandDate())));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((IskraResponse) readingResponse).getInstanteneousVolt());
                this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = Double.parseDouble(((IskraResponse) readingResponse).getInstanteneousCurrentPhaseAmpere());
                this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = Double.parseDouble(((IskraResponse) readingResponse).getInstanteneousCurrentNeutral());
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((IskraResponse) readingResponse).getReverseKwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((IskraResponse) readingResponse).getUnbalanceKwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((IskraResponse) readingResponse).getCurrentMonthConsumptionKW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((IskraResponse) readingResponse).getCurrentMonthConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth1ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth2ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth3ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth4ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth5ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth6ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth7ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth8ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth9ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth10ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth11ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((IskraResponse) readingResponse).getMonth12ConsumptionKWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth1ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth2ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth3ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth4ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth5ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth6ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth7ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth8ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth9ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth10ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth11ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((IskraResponse) readingResponse).getMonth12ConsumptionMoney().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((IskraResponse) readingResponse).getMaximDemandMonth12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth1Date()));
            } catch (Exception e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth2Date()));
            } catch (Exception e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth3Date()));
            } catch (Exception e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth4Date()));
            } catch (Exception e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth5Date()));
            } catch (Exception e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth6Date()));
            } catch (Exception e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth7Date()));
            } catch (Exception e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth8Date()));
            } catch (Exception e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth9Date()));
            } catch (Exception e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth10Date()));
            } catch (Exception e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth11Date()));
            } catch (Exception e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((IskraResponse) readingResponse).getMaximDemandMonth12Date()));
            } catch (Exception e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }

            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((IskraResponse) readingResponse).getMonth1ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((IskraResponse) readingResponse).getMonth2ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((IskraResponse) readingResponse).getMonth3ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((IskraResponse) readingResponse).getMonth4ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((IskraResponse) readingResponse).getMonth5ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((IskraResponse) readingResponse).getMonth6ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((IskraResponse) readingResponse).getMonth7ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((IskraResponse) readingResponse).getMonth8ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((IskraResponse) readingResponse).getMonth9ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((IskraResponse) readingResponse).getMonth10ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((IskraResponse) readingResponse).getMonth11ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((IskraResponse) readingResponse).getMonth12ConsumptionKvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        } else if (readingResponse instanceof GpiResponse) {
            this.FACTORY_CODE = 7;
            if (!((GpiResponse) readingResponse).getMeterType().equalsIgnoreCase("0"))
                this.FACTORY_TYPE = ((GpiResponse) readingResponse).getMeterType().equalsIgnoreCase("Single") ? 1 : 2;
            this.METER_MODEL = "1";
            try {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = Long.parseLong(((GpiResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.OPTICAL_METER_ID = Long.parseLong(((GpiResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
                this.ORIGINAL_OPTICAL_METER_ID = Long.parseLong(((GpiResponse) readingResponse).getMeter_ID().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception ignore) {
                this.ITEM_1_NEW_BASEITEM_Meter_ID = this.COVER_METER_ID;
                this.OPTICAL_METER_ID = this.COVER_METER_ID;
            }

            this.ITEM_2_NEW_BASEITEM_Customer_ID = handleCustomerId(((GpiResponse) readingResponse).getCustomer_ID());
            this.OPTICAL_Customer_ID = handleCustomerId(((GpiResponse) readingResponse).getCustomer_ID());
            this.ITEM_3_NEW_BASEITEM_CardID = ((GpiResponse) readingResponse).getCardID();
            this.ITEM_4_NEW_BASEITEM_fw_version = ((GpiResponse) readingResponse).getFw_version();
            this.ITEM_5_NEW_BASEITEM_ActivityType = ((GpiResponse) readingResponse).getActivityType();
            this.ITEM_6_NEW_BASEITEM_curent_Power_factor = ((GpiResponse) readingResponse).getCurent_Power_factor();
            this.ITEM_7_NEW_BASEITEM_last_year_Power_factor = ((GpiResponse) readingResponse).getLast_year_Power_factor();
            this.ITEM_8_NEW_BASEITEM_installing_technican_code = ((GpiResponse) readingResponse).getInstalling_technican_code();
            try {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getInstalling_Date_and_time()));
            } catch (Exception e) {
                this.ITEM_9_NEW_BASEITEM_installing_Date_and_time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMeter_Date() + " " + ((GpiResponse) readingResponse).getMeter_Time()));
            } catch (Exception e) {
                this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time = DateUtil.toApiFormat(new Date(0));
            }

            this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = ((GpiResponse) readingResponse).getItem_13_Current_tarrif_installing();
            try {
                this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(Integer.parseInt(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing));
            } catch (Exception e) {
                try {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = String.valueOf(((int) Double.parseDouble(this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing)));
                } catch (Exception e1) {
                    this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing = "1";
                }
            }
            try {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getCurrent_tariff_activation_date()));
            } catch (Exception e) {
                this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = DateUtil.toApiFormat(new Date(0));
            }


            try {
                this.ITEM_13_NEW_BASEITEM_Meter_status = Integer.parseInt(((GpiResponse) readingResponse).getMeter_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_14_NEW_BASEITEM_Relay_status = Integer.parseInt(((GpiResponse) readingResponse).getRelay_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_15_NEW_BASEITEM_battery_status = Integer.parseInt(((GpiResponse) readingResponse).getBattery_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_16_NEW_BASEITEM_Top_cover_status = Integer.parseInt(((GpiResponse) readingResponse).getTop_cover_status());
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_17_NEW_BASEITEM_Side_cover_status = Integer.parseInt(((GpiResponse) readingResponse).getSide_cover_status());
            } catch (Exception ignore) {
            }
            String events = ((GpiResponse) readingResponse).getTechnical_events();
            String[] eventsArray = events.split(",");
            int index = 1;
            for (String s : eventsArray) {
                try {
                    if (!s.isEmpty()) {

                        if (index == 1)
                            this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 2)
                            this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 = s.substring(s.lastIndexOf(":") + 1);
                        if (index == 3)
                            this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 = s.substring(s.lastIndexOf(":") + 1);

                        String substring = s.substring(s.indexOf(":") + 1, s.lastIndexOf(":"));
                        if (index == 1)
                            this.ITEM_20_NEW_BASEITEM_event_Date_1 = substring;
                        if (index == 2)
                            this.ITEM_23_NEW_BASEITEM_event_Date_2 = substring;
                        if (index == 3)
                            this.ITEM_26_NEW_BASEITEM_event_Date_3 = substring;

                        int code = Integer.parseInt(s.substring(0, s.indexOf(":")));
                        if (code == 1) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "TOP COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "TOP COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "TOP COVER OPEN";
                        } else if (code == 2) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "SIDE COVER OPEN";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "SIDE COVER OPEN";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "SIDE COVER OPEN";
                        } else if (code == 3) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "battery undervoltage";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "battery undervoltage";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "battery undervoltage";
                        } else if (code == 4) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "current reverse";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "current reverse";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "current reverse";
                        } else if (code == 6) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "bypass";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "bypass";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "bypass";
                        } else if (code == 7) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "permanent overload";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "permanent overload";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "permanent overload";
                        } else if (code == 8) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Relay failure";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Relay failure";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Relay failure";
                        } else if (code == 9) {
                            if (index == 1)
                                this.ITEM_19_NEW_BASEITEM_event_type_1 = "Modification date and time";
                            if (index == 2)
                                this.ITEM_22_NEW_BASEITEM_event_type_2 = "Modification date and time";
                            if (index == 3)
                                this.ITEM_25_NEW_BASEITEM_event_type_3 = "Modification date and time";
                        }
                        index++;
                    }
                } catch (Exception ignored) {
                }
            }
            try {
                this.ITEM_27_NEW_BASEITEM_recharge_number = Integer.parseInt(((GpiResponse) readingResponse).getRecharge_number().replaceAll("[^0-9.]", "").split("\\.")[0]);
            } catch (Exception e) {
            }
            try {
                this.ITEM_28_NEW_BASEITEM_Recharge_Amount = Double.parseDouble(((GpiResponse) readingResponse).getRecharge_Amount().replaceAll("[^0-9.]", ""));
            } catch (Exception ignored) {
            }
            try {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getLast_recharge_Date() + " " + ((GpiResponse) readingResponse).getLast_recharge_Time()));
            } catch (Exception e) {
                this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time = DateUtil.toApiFormat(new Date(0));
            }
            try {
                if (DateUtil.parse(this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date).equals(new Date(0))) {
                    this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date = this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time;
                }
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = Double.parseDouble(((GpiResponse) readingResponse).getRemaining_credit_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                double remainMoney = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((GpiResponse) readingResponse).getRemaining_credit_mony().replaceAll("[^0-9.-]", ""))));
                double deon;
                if (remainMoney < 0) {
                    deon = remainMoney * -1;
                    remainMoney = 0;
                } else {
                    deon = Double.parseDouble(String.format(Locale.US, "%.2f", Double.parseDouble(((GpiResponse) readingResponse).getDebts().replaceAll("[^0-9.-]", ""))));
                    if (deon < 0)
                        deon = deon * -1;
                    if (deon > 0)
                        remainMoney = 0;
                }
                this.ITEM_31_NEW_BASEITEM_remaining_credit_mony = remainMoney;
                this.ITEM_32_NEW_BASEITEM_Debts = deon;
            } catch (Exception ignore) {
            }
            if (this.ITEM_30_NEW_BASEITEM_remaining_credit_kw > 0 && this.ITEM_31_NEW_BASEITEM_remaining_credit_mony == 0 && this.ITEM_32_NEW_BASEITEM_Debts > 0) {
                this.ITEM_30_NEW_BASEITEM_remaining_credit_kw = this.ITEM_30_NEW_BASEITEM_remaining_credit_kw * -1;
            }
            try {
                this.ITEM_33_NEW_BASEITEM_Total_consumption_kw = Double.parseDouble(((GpiResponse) readingResponse).getTotal_consumption_kw().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_34_NEW_BASEITEM_Total_consumption_mony = Double.parseDouble(((GpiResponse) readingResponse).getTotal_consumption_mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar = Double.parseDouble(((GpiResponse) readingResponse).getTotal_consumption_kvar().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_36_NEW_BASEITEM_Current_Demand = Double.parseDouble(((GpiResponse) readingResponse).getCurrent_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_37_NEW_BASEITEM_Maximum_Demand = Double.parseDouble(((GpiResponse) readingResponse).getMaximum_Demand().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(DateUtil.parse((((GpiResponse) readingResponse).getMaximum_Demand_date().split(",")[1])));
            } catch (Exception e) {
                this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_39_NEW_BASEITEM_instanteneous_volt = Double.parseDouble(((GpiResponse) readingResponse).getInstanteneous_volt().split(",")[0].replaceAll("[^0-9.-]", ""));
            } catch (Exception ignored) {
            }
            this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere = 0;
            this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral = 0;
            try {
                this.ITEM_42_NEW_BASEITEM_reverse_Kwh = Double.parseDouble(((GpiResponse) readingResponse).getReverse_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_43_NEW_BASEITEM_unbalance_Kwh = Double.parseDouble(((GpiResponse) readingResponse).getUnbalance_Kwh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW = Double.parseDouble(((GpiResponse) readingResponse).getCurrent_month_consumption_KW().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY = Double.parseDouble(((GpiResponse) readingResponse).getCurrent_month_consumption_MONY().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_1_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_2_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_3_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_4_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_5_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_6_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_7_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_8_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_9_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_10_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_11_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh = Double.parseDouble(((GpiResponse) readingResponse).getMonth_12_consumption_kWh().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_1_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_2_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_3_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_4_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_5_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_6_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_7_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_8_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_9_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_10_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_11_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony = Double.parseDouble(((GpiResponse) readingResponse).getMonth_12_consumption_Mony().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 = Double.parseDouble(((GpiResponse) readingResponse).getMaxim_demand_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_1()));
            } catch (Exception e) {
                this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_2()));
            } catch (Exception e) {
                this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_3()));
            } catch (Exception e) {
                this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_4()));
            } catch (Exception e) {
                this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_5()));
            } catch (Exception e) {
                this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_6()));
            } catch (Exception e) {
                this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_7()));
            } catch (Exception e) {
                this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_8()));
            } catch (Exception e) {
                this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_9()));
            } catch (Exception e) {
                this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_10()));
            } catch (Exception e) {
                this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_11()));
            } catch (Exception e) {
                this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 = DateUtil.toApiFormat(new Date(0));
            }
            try {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(DateUtil.parse(((GpiResponse) readingResponse).getMaxim_demand_month_Date_12()));
            } catch (Exception e) {
                this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 = DateUtil.toApiFormat(new Date(0));
            }

            try {
                this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_1().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_2().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_3().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_4().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_5().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_6().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_7().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_8().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_9().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_10().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_11().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
            try {
                this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 = Double.parseDouble(((GpiResponse) readingResponse).getKvar_consumption_month_12().replaceAll("[^0-9.-]", ""));
            } catch (Exception ignore) {
            }
        }
    }

    private String handleCustomerId(String customerId) {
        String id = customerId;
        while (id.startsWith("0"))
            id = id.substring(1);
        if (id.isEmpty())
            id = "0";
        return id;
    }


    public String toWebFormat() {
        return "1;" + this.AUTHENTICATION_KEY + '\t' +
                "2;" + this.FACTORY_CODE + '\t' +
                "3;" + (this.FACTORY_TYPE == 0 ? 1 : this.FACTORY_TYPE) + '\t' +
                "4;" + this.METER_MODEL + '\t' +
                "5;" + this.METER_MODEL_NAME + '\t' +
                "6;" + this.COVER_METER_ID + '\t' +
                "7;" + this.OPTICAL_METER_ID + '\t' +
                "8;" + this.CUSTOMER_METER_ID + '\t' +
                "9;" + this.CUSTOMER_ROW_INDEX + '\t' +
                "10;" + this.CUSTOMER_BRANCH_CODE + '\t' +
                "11;" + this.CUSTOMER_AREA + '\t' +
                "12;" + this.CUSTOMER_DAY + '\t' +
                "13;" + this.CUSTOMER_MAIN + '\t' +
                "14;" + this.CUSTOMER_SUB + '\t' +
                "15;" + this.READING_CODE + '\t' +
                "16;" + this.LOST_READING_CODE + '\t' +
                "17;" + this.DAMAGE_CODE + '\t' +
                "18;" + this.READING_DATA_FLAG + '\t' +
                "19;" + this.USER_ID + '\t' +
                "20;" + this.OPTICAL_SERIAL_NUMBER + '\t' +
                "21;" + this.READING_DATE + '\t' +
                "22;" + this.METER_LOCATION + '\t' +
                "23;" + this.IMAGE_FLAG + '\t' +
                "24;" + this.IMAGE_NAME + '\t' +
                "25;" + this.CIRCLE_MONTH + '\t' +
                "26;" + this.CIRCLE_YEAR + '\t' +
                "27;" + this.READER_NOTS + '\t' +
                "28;" + this.ITEM_1_NEW_BASEITEM_Meter_ID + '\t' +
                "29;" + this.ITEM_2_NEW_BASEITEM_Customer_ID + '\t' +
                "30;" + this.ITEM_3_NEW_BASEITEM_CardID + '\t' +
                "31;" + this.ITEM_4_NEW_BASEITEM_fw_version + '\t' +
//                "32;" + this.ITEM_5_NEW_BASEITEM_ActivityType + '\t' +
                "32;" + "0" + '\t' +
                "33;" + this.ITEM_6_NEW_BASEITEM_curent_Power_factor + '\t' +
                "34;" + this.ITEM_7_NEW_BASEITEM_last_year_Power_factor + '\t' +
                "35;" + this.ITEM_8_NEW_BASEITEM_installing_technican_code + '\t' +
                "36;" + this.ITEM_9_NEW_BASEITEM_installing_Date_and_time + '\t' +
                "37;" + this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time + '\t' +
                "38;" + this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing + '\t' +
                "39;" + this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date + '\t' +
                "40;" + this.ITEM_13_NEW_BASEITEM_Meter_status + '\t' +
                "41;" + this.ITEM_14_NEW_BASEITEM_Relay_status + '\t' +
                "42;" + this.ITEM_15_NEW_BASEITEM_battery_status + '\t' +
                "43;" + this.ITEM_16_NEW_BASEITEM_Top_cover_status + '\t' +
                "44;" + this.ITEM_17_NEW_BASEITEM_Side_cover_status + '\t' +
                "45;" + this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 + '\t' +
                "46;" + this.ITEM_19_NEW_BASEITEM_event_type_1 + '\t' +
                "47;" + this.ITEM_20_NEW_BASEITEM_event_Date_1 + '\t' +
                "48;" + this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 + '\t' +
                "49;" + this.ITEM_22_NEW_BASEITEM_event_type_2 + '\t' +
                "50;" + this.ITEM_23_NEW_BASEITEM_event_Date_2 + '\t' +
                "51;" + this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 + '\t' +
                "52;" + this.ITEM_25_NEW_BASEITEM_event_type_3 + '\t' +
                "53;" + this.ITEM_26_NEW_BASEITEM_event_Date_3 + '\t' +
                "54;" + this.ITEM_27_NEW_BASEITEM_recharge_number + '\t' +
                "55;" + String.format(Locale.US, "%.16f", ITEM_28_NEW_BASEITEM_Recharge_Amount).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_28_NEW_BASEITEM_Recharge_Amount))).length(), 16)) + '\t' +
                "56;" + this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time + '\t' +
                "57;" + String.format(Locale.US, "%.16f", ITEM_30_NEW_BASEITEM_remaining_credit_kw).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_30_NEW_BASEITEM_remaining_credit_kw))).length(), 16)) + '\t' +
                "58;" + String.format(Locale.US, "%.16f", ITEM_31_NEW_BASEITEM_remaining_credit_mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_31_NEW_BASEITEM_remaining_credit_mony))).length(), 16)) + '\t' +
                "59;" + String.format(Locale.US, "%.16f", ITEM_32_NEW_BASEITEM_Debts).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_32_NEW_BASEITEM_Debts))).length(), 16)) + '\t' +
                "60;" + String.format(Locale.US, "%.16f", ITEM_33_NEW_BASEITEM_Total_consumption_kw).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_33_NEW_BASEITEM_Total_consumption_kw))).length(), 16)) + '\t' +
                "61;" + String.format(Locale.US, "%.16f", ITEM_34_NEW_BASEITEM_Total_consumption_mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_34_NEW_BASEITEM_Total_consumption_mony))).length(), 16)) + '\t' +
                "62;" + String.format(Locale.US, "%.16f", ITEM_35_NEW_BASEITEM_Total_consumption_kvar).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_35_NEW_BASEITEM_Total_consumption_kvar))).length(), 16)) + '\t' +
                "63;" + String.format(Locale.US, "%.16f", ITEM_36_NEW_BASEITEM_Current_Demand).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_36_NEW_BASEITEM_Current_Demand))).length(), 16)) + '\t' +
                "64;" + String.format(Locale.US, "%.16f", ITEM_37_NEW_BASEITEM_Maximum_Demand).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_37_NEW_BASEITEM_Maximum_Demand))).length(), 16)) + '\t' +
                "65;" + this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date + '\t' +
                "66;" + String.format(Locale.US, "%.16f", ITEM_39_NEW_BASEITEM_instanteneous_volt).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_39_NEW_BASEITEM_instanteneous_volt))).length(), 16)) + '\t' +
                "67;" + String.format(Locale.US, "%.16f", ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere))).length(), 16)) + '\t' +
                "68;" + String.format(Locale.US, "%.16f", ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral))).length(), 16)) + '\t' +
                "69;" + String.format(Locale.US, "%.16f", ITEM_42_NEW_BASEITEM_reverse_Kwh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_42_NEW_BASEITEM_reverse_Kwh))).length(), 16)) + '\t' +
                "70;" + String.format(Locale.US, "%.16f", ITEM_43_NEW_BASEITEM_unbalance_Kwh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_43_NEW_BASEITEM_unbalance_Kwh))).length(), 16)) + '\t' +
                "71;" + String.format(Locale.US, "%.16f", ITEM_44_NEW_BASEITEM_current_month_consumption_KW).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_44_NEW_BASEITEM_current_month_consumption_KW))).length(), 16)) + '\t' +
                "72;" + String.format(Locale.US, "%.16f", ITEM_45_NEW_BASEITEM_current_month_consumption_MONY).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_45_NEW_BASEITEM_current_month_consumption_MONY))).length(), 16)) + '\t' +
                "73;" + String.format(Locale.US, "%.16f", ITEM_46_NEW_BASEITEM_1_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_46_NEW_BASEITEM_1_month_consumption_kWh))).length(), 16)) + '\t' +
                "74;" + String.format(Locale.US, "%.16f", ITEM_47_NEW_BASEITEM_2_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_47_NEW_BASEITEM_2_month_consumption_kWh))).length(), 16)) + '\t' +
                "75;" + String.format(Locale.US, "%.16f", ITEM_48_NEW_BASEITEM_3_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_48_NEW_BASEITEM_3_month_consumption_kWh))).length(), 16)) + '\t' +
                "76;" + String.format(Locale.US, "%.16f", ITEM_49_NEW_BASEITEM_4_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_49_NEW_BASEITEM_4_month_consumption_kWh))).length(), 16)) + '\t' +
                "77;" + String.format(Locale.US, "%.16f", ITEM_50_NEW_BASEITEM_5_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_50_NEW_BASEITEM_5_month_consumption_kWh))).length(), 16)) + '\t' +
                "78;" + String.format(Locale.US, "%.16f", ITEM_51_NEW_BASEITEM_6_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_51_NEW_BASEITEM_6_month_consumption_kWh))).length(), 16)) + '\t' +
                "79;" + String.format(Locale.US, "%.16f", ITEM_52_NEW_BASEITEM_7_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_52_NEW_BASEITEM_7_month_consumption_kWh))).length(), 16)) + '\t' +
                "80;" + String.format(Locale.US, "%.16f", ITEM_53_NEW_BASEITEM_8_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_53_NEW_BASEITEM_8_month_consumption_kWh))).length(), 16)) + '\t' +
                "81;" + String.format(Locale.US, "%.16f", ITEM_54_NEW_BASEITEM_9_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_54_NEW_BASEITEM_9_month_consumption_kWh))).length(), 16)) + '\t' +
                "82;" + String.format(Locale.US, "%.16f", ITEM_55_NEW_BASEITEM_10_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_55_NEW_BASEITEM_10_month_consumption_kWh))).length(), 16)) + '\t' +
                "83;" + String.format(Locale.US, "%.16f", ITEM_56_NEW_BASEITEM_11_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_56_NEW_BASEITEM_11_month_consumption_kWh))).length(), 16)) + '\t' +
                "84;" + String.format(Locale.US, "%.16f", ITEM_57_NEW_BASEITEM_12_month_consumption_kWh).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_57_NEW_BASEITEM_12_month_consumption_kWh))).length(), 16)) + '\t' +
                "85;" + String.format(Locale.US, "%.16f", ITEM_58_NEW_BASEITEM_1_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_58_NEW_BASEITEM_1_month_consumption_Mony))).length(), 16)) + '\t' +
                "86;" + String.format(Locale.US, "%.16f", ITEM_59_NEW_BASEITEM_2_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_59_NEW_BASEITEM_2_month_consumption_Mony))).length(), 16)) + '\t' +
                "87;" + String.format(Locale.US, "%.16f", ITEM_60_NEW_BASEITEM_3_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_60_NEW_BASEITEM_3_month_consumption_Mony))).length(), 16)) + '\t' +
                "88;" + String.format(Locale.US, "%.16f", ITEM_61_NEW_BASEITEM_4_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_61_NEW_BASEITEM_4_month_consumption_Mony))).length(), 16)) + '\t' +
                "89;" + String.format(Locale.US, "%.16f", ITEM_62_NEW_BASEITEM_5_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_62_NEW_BASEITEM_5_month_consumption_Mony))).length(), 16)) + '\t' +
                "90;" + String.format(Locale.US, "%.16f", ITEM_63_NEW_BASEITEM_6_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_63_NEW_BASEITEM_6_month_consumption_Mony))).length(), 16)) + '\t' +
                "91;" + String.format(Locale.US, "%.16f", ITEM_64_NEW_BASEITEM_7_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_64_NEW_BASEITEM_7_month_consumption_Mony))).length(), 16)) + '\t' +
                "92;" + String.format(Locale.US, "%.16f", ITEM_65_NEW_BASEITEM_8_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_65_NEW_BASEITEM_8_month_consumption_Mony))).length(), 16)) + '\t' +
                "93;" + String.format(Locale.US, "%.16f", ITEM_66_NEW_BASEITEM_9_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_66_NEW_BASEITEM_9_month_consumption_Mony))).length(), 16)) + '\t' +
                "94;" + String.format(Locale.US, "%.16f", ITEM_67_NEW_BASEITEM_10_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_67_NEW_BASEITEM_10_month_consumption_Mony))).length(), 16)) + '\t' +
                "95;" + String.format(Locale.US, "%.16f", ITEM_68_NEW_BASEITEM_11_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_68_NEW_BASEITEM_11_month_consumption_Mony))).length(), 16)) + '\t' +
                "96;" + String.format(Locale.US, "%.16f", ITEM_69_NEW_BASEITEM_12_month_consumption_Mony).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_69_NEW_BASEITEM_12_month_consumption_Mony))).length(), 16)) + '\t' +
                "97;" + String.format(Locale.US, "%.16f", ITEM_70_NEW_BASEITEM_maxim_demand_month_1).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_70_NEW_BASEITEM_maxim_demand_month_1))).length(), 16)) + '\t' +
                "98;" + String.format(Locale.US, "%.16f", ITEM_71_NEW_BASEITEM_maxim_demand_month_2).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_71_NEW_BASEITEM_maxim_demand_month_2))).length(), 16)) + '\t' +
                "99;" + String.format(Locale.US, "%.16f", ITEM_72_NEW_BASEITEM_maxim_demand_month_3).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_72_NEW_BASEITEM_maxim_demand_month_3))).length(), 16)) + '\t' +
                "100;" + String.format(Locale.US, "%.16f", ITEM_73_NEW_BASEITEM_maxim_demand_month_4).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_73_NEW_BASEITEM_maxim_demand_month_4))).length(), 16)) + '\t' +
                "101;" + String.format(Locale.US, "%.16f", ITEM_74_NEW_BASEITEM_maxim_demand_month_5).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_74_NEW_BASEITEM_maxim_demand_month_5))).length(), 16)) + '\t' +
                "102;" + String.format(Locale.US, "%.16f", ITEM_75_NEW_BASEITEM_maxim_demand_month_6).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_75_NEW_BASEITEM_maxim_demand_month_6))).length(), 16)) + '\t' +
                "103;" + String.format(Locale.US, "%.16f", ITEM_76_NEW_BASEITEM_maxim_demand_month_7).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_76_NEW_BASEITEM_maxim_demand_month_7))).length(), 16)) + '\t' +
                "104;" + String.format(Locale.US, "%.16f", ITEM_77_NEW_BASEITEM_maxim_demand_month_8).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_77_NEW_BASEITEM_maxim_demand_month_8))).length(), 16)) + '\t' +
                "105;" + String.format(Locale.US, "%.16f", ITEM_78_NEW_BASEITEM_maxim_demand_month_9).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_78_NEW_BASEITEM_maxim_demand_month_9))).length(), 16)) + '\t' +
                "106;" + String.format(Locale.US, "%.16f", ITEM_79_NEW_BASEITEM_maxim_demand_month_10).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_79_NEW_BASEITEM_maxim_demand_month_10))).length(), 16)) + '\t' +
                "107;" + String.format(Locale.US, "%.16f", ITEM_80_NEW_BASEITEM_maxim_demand_month_11).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_80_NEW_BASEITEM_maxim_demand_month_11))).length(), 16)) + '\t' +
                "108;" + String.format(Locale.US, "%.16f", ITEM_81_NEW_BASEITEM_maxim_demand_month_12).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_81_NEW_BASEITEM_maxim_demand_month_12))).length(), 16)) + '\t' +
                "109;" + this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 + '\t' +
                "110;" + this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 + '\t' +
                "111;" + this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 + '\t' +
                "112;" + this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 + '\t' +
                "113;" + this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 + '\t' +
                "114;" + this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 + '\t' +
                "115;" + this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 + '\t' +
                "116;" + this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 + '\t' +
                "117;" + this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 + '\t' +
                "118;" + this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 + '\t' +
                "119;" + this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 + '\t' +
                "120;" + this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 + '\t' +
                "121;" + String.format(Locale.US, "%.16f", ITEM_94_NEW_BASEITEM_kvar_consumption_month_1).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_94_NEW_BASEITEM_kvar_consumption_month_1))).length(), 16)) + '\t' +
                "122;" + String.format(Locale.US, "%.16f", ITEM_95_NEW_BASEITEM_kvar_consumption_month_2).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_95_NEW_BASEITEM_kvar_consumption_month_2))).length(), 16)) + '\t' +
                "123;" + String.format(Locale.US, "%.16f", ITEM_96_NEW_BASEITEM_kvar_consumption_month_3).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_96_NEW_BASEITEM_kvar_consumption_month_3))).length(), 16)) + '\t' +
                "124;" + String.format(Locale.US, "%.16f", ITEM_97_NEW_BASEITEM_kvar_consumption_month_4).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_97_NEW_BASEITEM_kvar_consumption_month_4))).length(), 16)) + '\t' +
                "125;" + String.format(Locale.US, "%.16f", ITEM_98_NEW_BASEITEM_kvar_consumption_month_5).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_98_NEW_BASEITEM_kvar_consumption_month_5))).length(), 16)) + '\t' +
                "126;" + String.format(Locale.US, "%.16f", ITEM_99_NEW_BASEITEM_kvar_consumption_month_6).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_99_NEW_BASEITEM_kvar_consumption_month_6))).length(), 16)) + '\t' +
                "127;" + String.format(Locale.US, "%.16f", ITEM_100_NEW_BASEITEM_kvar_consumption_month_7).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_100_NEW_BASEITEM_kvar_consumption_month_7))).length(), 16)) + '\t' +
                "128;" + String.format(Locale.US, "%.16f", ITEM_101_NEW_BASEITEM_kvar_consumption_month_8).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_101_NEW_BASEITEM_kvar_consumption_month_8))).length(), 16)) + '\t' +
                "129;" + String.format(Locale.US, "%.16f", ITEM_102_NEW_BASEITEM_kvar_consumption_month_9).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_102_NEW_BASEITEM_kvar_consumption_month_9))).length(), 16)) + '\t' +
                "130;" + String.format(Locale.US, "%.16f", ITEM_103_NEW_BASEITEM_kvar_consumption_month_10).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_103_NEW_BASEITEM_kvar_consumption_month_10))).length(), 16)) + '\t' +
                "131;" + String.format(Locale.US, "%.16f", ITEM_104_NEW_BASEITEM_kvar_consumption_month_11).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_104_NEW_BASEITEM_kvar_consumption_month_11))).length(), 16)) + '\t' +
                "132;" + String.format(Locale.US, "%.16f", ITEM_105_NEW_BASEITEM_kvar_consumption_month_12).substring(0, Math.min(String.valueOf(Double.parseDouble(String.format(Locale.US, "%.16f", ITEM_105_NEW_BASEITEM_kvar_consumption_month_12))).length(), 16)) + '\t' +
                "133;" + this.IMSGE_BASE64;
    }

    private void setBarcode(String barcode) {
        this.COVER_METER_ID = Long.parseLong(barcode);
    }

    private void setCUSTOMER_METER_ID(long CUSTOMER_METER_ID) {
        this.CUSTOMER_METER_ID = CUSTOMER_METER_ID;
    }

    private void setCUSTOMER_ROW_INDEX(long CDID) {
        this.CUSTOMER_ROW_INDEX = CDID;
    }

    private void setCUSTOMER_BRANCH_CODE(int mBRANCH_CODE) {
        this.CUSTOMER_BRANCH_CODE = mBRANCH_CODE;
    }

    private void setCUSTOMER_AREA(int CUSTOMER_AREA) {
        this.CUSTOMER_AREA = CUSTOMER_AREA;
    }

    private void setCUSTOMER_DAY(int CUSTOMER_DAY) {
        this.CUSTOMER_DAY = CUSTOMER_DAY;
    }

    public void setITEM_2_NEW_BASEITEM_Customer_ID(String ITEM_2_NEW_BASEITEM_Customer_ID) {
        this.ITEM_2_NEW_BASEITEM_Customer_ID = ITEM_2_NEW_BASEITEM_Customer_ID;
    }

    private void setCUSTOMER_MAIN(int CUSTOMER_MAIN) {
        this.CUSTOMER_MAIN = CUSTOMER_MAIN;
    }

    private void setCUSTOMER_SUB(int CUSTOMER_SUB) {
        this.CUSTOMER_SUB = CUSTOMER_SUB;
    }

    private void setREADING_CODE(int READING_CODE) {
        this.READING_CODE = READING_CODE;
    }

    private void setLOST_READING_CODE(int LOST_READING_CODE) {
        this.LOST_READING_CODE = LOST_READING_CODE;
    }

    private void setDAMAGE_CODE(int DAMAGE_CODE) {
        this.DAMAGE_CODE = DAMAGE_CODE;
    }

    private void setREADING_DATA_FLAG(int READING_DATA_FLAG) {
        this.READING_DATA_FLAG = READING_DATA_FLAG;
    }

    private void setUSER_ID(AppUser appUser) {
        this.USER_ID = Long.parseLong(appUser.getUSER_ID());
    }


    private void setMETER_LOCATION(AppLocationManager appLocationManager) {
        this.METER_LOCATION = appLocationManager.toString();
    }

    private void setIMAGE_FLAG(int IMAGE_FLAG) {
        this.IMAGE_FLAG = IMAGE_FLAG;
    }

    private void setIMAGE_NAME(String mFACTORY_CODE,
                               String mCOVER_METER_ID,
                               String mUSER_ID,
                               String mCIRCLE_MONTH,
                               String mCIRCLE_YEAR) {
        this.IMAGE_NAME = mFACTORY_CODE + "_" + mCOVER_METER_ID + "_" + mUSER_ID + "_" + mCIRCLE_MONTH + "_" + mCIRCLE_YEAR + ".jpg";
    }

    private void setCIRCLE_MONTH(int CIRCLE_MONTH) {
        this.CIRCLE_MONTH = CIRCLE_MONTH;
    }

    private void setCIRCLE_YEAR(int CIRCLE_YEAR) {

        this.CIRCLE_YEAR = CIRCLE_YEAR;
    }

    private void setREADER_NOTES(Enums.ActivityType activityType, String note) {
        this.READER_NOTS = this.CUSTOMER_BRANCH_CODE
                + "," + this.CUSTOMER_AREA
                + "," + this.CUSTOMER_DAY
                + "," + this.CUSTOMER_MAIN
                + "," + this.CUSTOMER_SUB
                + "," + "اقرب جار"
                + "," + activityType.toString().replaceAll(";", ":").replaceAll("\t", "_")
                + "," + "غير معروف"
                + "," + (note.isEmpty() ? "0" : note).replaceAll(";", ":").replaceAll("\t", "_");
    }

    private void setCycleDate(CycleDate cycleDate) {
        setCIRCLE_MONTH(cycleDate.CycleMonth);
        setCIRCLE_YEAR(cycleDate.CycleYear);
    }

    private void setIMAGE(Context context, String path) {
        try {
            if (path.isEmpty()) {
                throw new Exception("path is empty");
            }
            if (!(new File(path).exists())) {
                throw new Exception("file not found");
            }
            if (!(new File(path).canRead())) {
                throw new Exception("can not read");
            }
            Bitmap bitmap = decodeFile(new File(path));
            if (bitmap == null) {
                throw new Exception("null bitmap");
            }
            int width = bitmap.getWidth();
            int height = bitmap.getHeight();
            float scaleWidth = (350f / width);
//            float scaleHeight =  (512f / height);
            Matrix matrix = new Matrix();
            matrix.postScale(scaleWidth, scaleWidth);
            Bitmap resizedBitmap = Bitmap.createBitmap(
                    bitmap, 0, 0, width, height, matrix, false
            );
            bitmap.recycle();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            BitmapHelper bitmapHelper = new BitmapHelper();
            //bitmapHelper.drawTextToBitmap(context, resizedBitmap, "الحالة : " + getReadingSate(), "العداد : " + getApiFactoryCodeToName() + " - " + getFactoryType(), "باركود : " + COVER_METER_ID, "اصدار البرنامج : " + BuildConfig.VERSION_NAME, "الوقت : " + new SimpleDateFormat("dd/MM/yyyy hh:mm:ss aa").format(new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").parse(READING_DATE)), "اصدار : " + CIRCLE_YEAR + "/" + CIRCLE_MONTH);
            bitmapHelper.drawTextToBitmap(context, resizedBitmap, "الحالة : " + getReadingSate(), "العداد : " + getApiFactoryCodeToName() + " - " + getFactoryType(), "باركود : " + COVER_METER_ID, "اصدار البرنامج : " + BuildConfig.VERSION_NAME, "الوقت : " + new SimpleDateFormat("dd/MM/yyyy hh:mm:ss aa", Locale.US).format(new SimpleDateFormat("yyyy/MM/dd hh:mm:ss aa", Locale.US).parse(READING_DATE)), "اصدار : " + CIRCLE_YEAR + "/" + CIRCLE_MONTH);

            resizedBitmap.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            this.IMSGE_BASE64 = BaseEncoding.base64().encode(byteArray);
            setIMAGE_FLAG(1);
            setIMAGE_NAME(String.valueOf(FACTORY_CODE), String.valueOf(COVER_METER_ID), String.valueOf(USER_ID), String.valueOf(CIRCLE_MONTH), String.valueOf(CIRCLE_YEAR));
        } catch (Exception e) {
            setIMAGE_FLAG(0);
            try {
                ((MainActivity) context).runOnUiThread(() -> {
                    FirebaseCrashlytics.getInstance().recordException(e);
                    Toast.makeText(context, "خطأ في حفظ الصوره يرجي اعاده قراءه العداد", Toast.LENGTH_LONG).show();
                    AlertDialog.Builder builder = new AlertDialog.Builder(context);
                    builder.setMessage(e.getMessage() + "\n" + e.getCause() + "\n" + e);
                    builder.setTitle("خطأ في حفظ الصورة");
                    builder.setCancelable(false);
                    builder.setNegativeButton("اغلاق", (dialog, which) -> dialog.cancel());
                    AlertDialog alert = builder.create();
                    alert.show();
                });
            } catch (Exception ignored) {
            }
        }
    }

    public Bitmap decodeFile(File f) {
        Bitmap b = null;
        try {
            // Decode image size
            BitmapFactory.Options o = new BitmapFactory.Options();
            o.inJustDecodeBounds = true;

            FileInputStream fis = new FileInputStream(f);
            BitmapFactory.decodeStream(fis, null, o);
            fis.close();
            int IMAGE_MAX_SIZE = 1000;
            int scale = 1;
            if (o.outHeight > IMAGE_MAX_SIZE || o.outWidth > IMAGE_MAX_SIZE) {
                scale = (int) Math.pow(
                        2,
                        (int) Math.round(Math.log(IMAGE_MAX_SIZE
                                / (double) Math.max(o.outHeight, o.outWidth))
                                / Math.log(0.5)));
            }

            // Decode with inSampleSize
            BitmapFactory.Options o2 = new BitmapFactory.Options();
            o2.inSampleSize = scale;
            fis = new FileInputStream(f);
            b = BitmapFactory.decodeStream(fis, null, o2);
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return b;
    }

    public String getReadingSate() {
        String state = "";
        switch (READING_CODE) {
            case 0:
                state = "مقروء";
                break;
            case 1:
                state = "مغلق";
                break;
            case 2:
                if (DAMAGE_CODE == 1)
                    state = "غير قادر علي الوصول";
                else if (DAMAGE_CODE == 2)
                    state = "غير قادر علي القراءة";
                else
                    state = "معطل";
                break;
            case 3:
                state = "كسر";
                break;
            case 4:
                state = "سرقة تيار";
                break;
            case 5:
                state = "مرفوع - " + (DAMAGE_CODE == 0 ? "مرفوع" : "إحلال");
                break;
            case 6:
                state = "غير موجود على الطبيعة";
                break;
            case 7:
                state = "ساقط ليست";
                state += " - ";
                switch (LOST_READING_CODE) {
                    case 0:
                        state += "مقروء";
                        break;
                    case 1:
                        state += "مغلق";
                        break;
                    case 2:
                        if (DAMAGE_CODE == 1)
                            state += "غير قادر علي الوصول";
                        else if (DAMAGE_CODE == 2)
                            state += "غير قادر علي القراءة";
                        else
                            state += "معطل";
                        break;
                    case 3:
                        state += "كسر";
                        break;
                    case 4:
                        state += "سرقة تيار";
                        break;
                    case 5:
                        state += "مرفوع";
                        break;
                    case 6:
                        state += "غير موجود على الطبيعة";
                        break;
                    case 8:
                        state += "مصيف";
                        break;
                    case 9:
                        state += "غير متصل";
                        break;
                    case 10:
                        state += "ممتنع";
                        break;
                    case 11:
                        state += "ممتنع عن التصوير";
                        break;
                    case 12:
                        state += "مخالفة شروط تعاقد";
                        break;
                    case 13:
                        state += "هدم وبناء";
                        break;
                    case 14:
                        state += "ممارسة";
                        break;
                }
                break;
            case 8:
                state = "مصيف";
                break;
            case 9:
                state = "غير متصل";
                break;
            case 10:
                state = "ممتنع";
                break;
            case 11:
                state = "ممتنع عن التصوير";
                break;
            case 12:
                state = "مخالفة شروط تعاقد";
                break;
            case 13:
                state = "هدم وبناء";
                break;
            case 14:
                state = "ممارسة";
                break;
        }
        return state;
    }

    public String getApiFactoryCodeToName() {
        String state = "";
        switch (FACTORY_CODE) {
            case 1:
                state = "المصرية";
                break;
            case 2:
                state = "اسكرا";
                break;
            case 3:
                state = "الكتروميتر";
                break;
            case 4:
                state = "المعصرة";
                break;
            case 5:
                state = "الهيئة العربية";
                break;
            case 6:
                state = "جلوبال";
                break;
            case 7:
                state = "جيزة باور";
                break;
        }
        return state;
    }

    private String getFactoryType() {
        String state = "";
        switch (FACTORY_TYPE) {
            case 1:
                state = "احادي";
                break;
            case 2:
            case 3:
                state = "ثلاثي";
                break;
        }
        return state;
    }

    private void setFromClientData(Client client) {
        try {
            setCUSTOMER_BRANCH_CODE(client.getBrHndsa());
        } catch (Exception e) {
            try {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String sStackTrace = sw.toString(); // stack trace as a string
                FirebaseCrashlytics.getInstance().recordException(new Exception((BuildConfig.VERSION_NAME + " , " + BuildConfig.coName + " , " + "setCUSTOMER_BRANCH_CODE" + "\n" + client.toString() + "\n" + e.getMessage() + "\n" + sStackTrace)));
            } catch (Exception ignored) {
            }
        }
        try {
            setCUSTOMER_AREA(Integer.parseInt(client.getMntka().trim()));
        } catch (Exception e) {
            try {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String sStackTrace = sw.toString(); // stack trace as a string
                FirebaseCrashlytics.getInstance().recordException(new Exception(BuildConfig.VERSION_NAME + " , " + BuildConfig.coName + " , " + "setCUSTOMER_AREA" + "\n" + client.toString() + "\n" + e.getMessage() + "\n" + sStackTrace));
            } catch (Exception ignored) {
            }
        }
        try {
            setCUSTOMER_DAY(Integer.parseInt(client.getDay().trim()));
        } catch (Exception e) {
            try {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String sStackTrace = sw.toString(); // stack trace as a string
                FirebaseCrashlytics.getInstance().recordException(new Exception(BuildConfig.VERSION_NAME + " , " + BuildConfig.coName + " , " + "setCUSTOMER_DAY" + "\n" + client.toString() + "\n" + e.getMessage() + "\n" + sStackTrace));
            } catch (Exception ignored) {
            }
        }
        try {
            setCUSTOMER_MAIN(Integer.parseInt(client.getMain().trim()));
        } catch (Exception e) {
            try {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String sStackTrace = sw.toString(); // stack trace as a string
                FirebaseCrashlytics.getInstance().recordException(new Exception(BuildConfig.VERSION_NAME + " , " + BuildConfig.coName + " , " + "setCUSTOMER_MAIN" + "\n" + client.toString() + "\n" + e.getMessage() + "\n" + sStackTrace));
            } catch (Exception ignored) {
            }
        }
        try {
            setCUSTOMER_SUB(Integer.parseInt(client.getFary().trim()));
        } catch (Exception e) {
            try {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String sStackTrace = sw.toString(); // stack trace as a string
                FirebaseCrashlytics.getInstance().recordException(new Exception(BuildConfig.VERSION_NAME + " , " + BuildConfig.coName + " , " + "setCUSTOMER_SUB" + "\n" + client.toString() + "\n" + e.getMessage() + "\n" + sStackTrace));
            } catch (Exception ignored) {
            }
        }
        if (client.getCustomerID() != null && !client.getCustomerID().isEmpty())
            setITEM_2_NEW_BASEITEM_Customer_ID(client.getCustomerID().trim());
        if (client.getMeterID() != 0)
            setCUSTOMER_METER_ID(client.getMeterID());
        else
            setCUSTOMER_METER_ID(this.COVER_METER_ID);
        setCUSTOMER_ROW_INDEX(client.getCDID());
    }

    public String toFileFormate() {
        return
                "ITEM_1_NEW_BASEITEM_Meter_ID\t" + this.ITEM_1_NEW_BASEITEM_Meter_ID + "\n" +
                        "ITEM_2_NEW_BASEITEM_Customer_ID\t" + this.ITEM_2_NEW_BASEITEM_Customer_ID + "\n" +
                        "ITEM_3_NEW_BASEITEM_CardID\t" + this.ITEM_3_NEW_BASEITEM_CardID + "\n" +
                        "ITEM_4_NEW_BASEITEM_fw_version\t" + this.ITEM_4_NEW_BASEITEM_fw_version + "\n" +
                        "ITEM_5_NEW_BASEITEM_ActivityType\t" + this.ITEM_5_NEW_BASEITEM_ActivityType + "\n" +
                        "ITEM_6_NEW_BASEITEM_curent_Power_factor\t" + this.ITEM_6_NEW_BASEITEM_curent_Power_factor + "\n" +
                        "ITEM_7_NEW_BASEITEM_last_year_Power_factor\t" + this.ITEM_7_NEW_BASEITEM_last_year_Power_factor + "\n" +
                        "ITEM_8_NEW_BASEITEM_installing_technican_code\t" + this.ITEM_8_NEW_BASEITEM_installing_technican_code + "\n" +
                        "ITEM_9_NEW_BASEITEM_installing_Date_and_time\t" + this.ITEM_9_NEW_BASEITEM_installing_Date_and_time + "\n" +
                        "ITEM_10_NEW_BASEITEM_Meter_Date_and_Time\t" + this.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time + "\n" +
                        "ITEM_11_NEW_BASEITEM_Current_tarrif_installing\t" + this.ITEM_11_NEW_BASEITEM_Current_tarrif_installing + "\n" +
                        "ITEM_12_NEW_BASEITEM_Current_tariff_activation_date\t" + this.ITEM_12_NEW_BASEITEM_Current_tariff_activation_date + "\n" +
                        "ITEM_13_NEW_BASEITEM_Meter_status\t" + this.ITEM_13_NEW_BASEITEM_Meter_status + "\n" +
                        "ITEM_14_NEW_BASEITEM_Relay_status\t" + this.ITEM_14_NEW_BASEITEM_Relay_status + "\n" +
                        "ITEM_15_NEW_BASEITEM_battery_status\t" + this.ITEM_15_NEW_BASEITEM_battery_status + "\n" +
                        "ITEM_16_NEW_BASEITEM_Top_cover_status\t" + this.ITEM_16_NEW_BASEITEM_Top_cover_status + "\n" +
                        "ITEM_17_NEW_BASEITEM_Side_cover_status\t" + this.ITEM_17_NEW_BASEITEM_Side_cover_status + "\n" +
                        "ITEM_18_NEW_BASEITEM_Technical_code_event_1\t" + this.ITEM_18_NEW_BASEITEM_Technical_code_event_1 + "\n" +
                        "ITEM_19_NEW_BASEITEM_event_type_1\t" + this.ITEM_19_NEW_BASEITEM_event_type_1 + "\n" +
                        "ITEM_20_NEW_BASEITEM_event_Date_1\t" + this.ITEM_20_NEW_BASEITEM_event_Date_1 + "\n" +
                        "ITEM_21_NEW_BASEITEM_Technical_code_event_2\t" + this.ITEM_21_NEW_BASEITEM_Technical_code_event_2 + "\n" +
                        "ITEM_22_NEW_BASEITEM_event_type_2\t" + this.ITEM_22_NEW_BASEITEM_event_type_2 + "\n" +
                        "ITEM_23_NEW_BASEITEM_event_Date_2\t" + this.ITEM_23_NEW_BASEITEM_event_Date_2 + "\n" +
                        "ITEM_24_NEW_BASEITEM_Technical_code_event_3\t" + this.ITEM_24_NEW_BASEITEM_Technical_code_event_3 + "\n" +
                        "ITEM_25_NEW_BASEITEM_event_type_3\t" + this.ITEM_25_NEW_BASEITEM_event_type_3 + "\n" +
                        "ITEM_26_NEW_BASEITEM_event_Date_3\t" + this.ITEM_26_NEW_BASEITEM_event_Date_3 + "\n" +
                        "ITEM_27_NEW_BASEITEM_recharge_number\t" + this.ITEM_27_NEW_BASEITEM_recharge_number + "\n" +
                        "ITEM_28_NEW_BASEITEM_Recharge_Amount\t" + this.ITEM_28_NEW_BASEITEM_Recharge_Amount + "\n" +
                        "ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time\t" + this.ITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time + "\n" +
                        "ITEM_30_NEW_BASEITEM_remaining_credit_kw\t" + this.ITEM_30_NEW_BASEITEM_remaining_credit_kw + "\n" +
                        "ITEM_31_NEW_BASEITEM_remaining_credit_mony\t" + this.ITEM_31_NEW_BASEITEM_remaining_credit_mony + "\n" +
                        "ITEM_32_NEW_BASEITEM_Debts\t" + this.ITEM_32_NEW_BASEITEM_Debts + "\n" +
                        "ITEM_33_NEW_BASEITEM_Total_consumption_kw\t" + this.ITEM_33_NEW_BASEITEM_Total_consumption_kw + "\n" +
                        "ITEM_34_NEW_BASEITEM_Total_consumption_mony\t" + this.ITEM_34_NEW_BASEITEM_Total_consumption_mony + "\n" +
                        "ITEM_35_NEW_BASEITEM_Total_consumption_kvar\t" + this.ITEM_35_NEW_BASEITEM_Total_consumption_kvar + "\n" +
                        "ITEM_36_NEW_BASEITEM_Current_Demand\t" + this.ITEM_36_NEW_BASEITEM_Current_Demand + "\n" +
                        "ITEM_37_NEW_BASEITEM_Maximum_Demand\t" + this.ITEM_37_NEW_BASEITEM_Maximum_Demand + "\n" +
                        "ITEM_38_NEW_BASEITEM_Maximum_Demand_date\t" + this.ITEM_38_NEW_BASEITEM_Maximum_Demand_date + "\n" +
                        "ITEM_39_NEW_BASEITEM_instanteneous_volt\t" + this.ITEM_39_NEW_BASEITEM_instanteneous_volt + "\n" +
                        "ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere\t" + this.ITEM_40_NEW_BASEITEM_instanteneous_current_Phase_Ampere + "\n" +
                        "ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral\t" + this.ITEM_41_NEW_BASEITEM_instanteneous_current_Neutral + "\n" +
                        "ITEM_42_NEW_BASEITEM_reverse_Kwh\t" + this.ITEM_42_NEW_BASEITEM_reverse_Kwh + "\n" +
                        "ITEM_43_NEW_BASEITEM_unbalance_Kwh\t" + this.ITEM_43_NEW_BASEITEM_unbalance_Kwh + "\n" +
                        "ITEM_44_NEW_BASEITEM_current_month_consumption_KW\t" + this.ITEM_44_NEW_BASEITEM_current_month_consumption_KW + "\n" +
                        "ITEM_45_NEW_BASEITEM_current_month_consumption_MONY\t" + this.ITEM_45_NEW_BASEITEM_current_month_consumption_MONY + "\n" +
                        "ITEM_46_NEW_BASEITEM_1_month_consumption_kWh\t" + this.ITEM_46_NEW_BASEITEM_1_month_consumption_kWh + "\n" +
                        "ITEM_47_NEW_BASEITEM_2_month_consumption_kWh\t" + this.ITEM_47_NEW_BASEITEM_2_month_consumption_kWh + "\n" +
                        "ITEM_48_NEW_BASEITEM_3_month_consumption_kWh\t" + this.ITEM_48_NEW_BASEITEM_3_month_consumption_kWh + "\n" +
                        "ITEM_49_NEW_BASEITEM_4_month_consumption_kWh\t" + this.ITEM_49_NEW_BASEITEM_4_month_consumption_kWh + "\n" +
                        "ITEM_50_NEW_BASEITEM_5_month_consumption_kWh\t" + this.ITEM_50_NEW_BASEITEM_5_month_consumption_kWh + "\n" +
                        "ITEM_51_NEW_BASEITEM_6_month_consumption_kWh\t" + this.ITEM_51_NEW_BASEITEM_6_month_consumption_kWh + "\n" +
                        "ITEM_52_NEW_BASEITEM_7_month_consumption_kWh\t" + this.ITEM_52_NEW_BASEITEM_7_month_consumption_kWh + "\n" +
                        "ITEM_53_NEW_BASEITEM_8_month_consumption_kWh\t" + this.ITEM_53_NEW_BASEITEM_8_month_consumption_kWh + "\n" +
                        "ITEM_54_NEW_BASEITEM_9_month_consumption_kWh\t" + this.ITEM_54_NEW_BASEITEM_9_month_consumption_kWh + "\n" +
                        "ITEM_55_NEW_BASEITEM_10_month_consumption_kWh\t" + this.ITEM_55_NEW_BASEITEM_10_month_consumption_kWh + "\n" +
                        "ITEM_56_NEW_BASEITEM_11_month_consumption_kWh\t" + this.ITEM_56_NEW_BASEITEM_11_month_consumption_kWh + "\n" +
                        "ITEM_57_NEW_BASEITEM_12_month_consumption_kWh\t" + this.ITEM_57_NEW_BASEITEM_12_month_consumption_kWh + "\n" +
                        "ITEM_58_NEW_BASEITEM_1_month_consumption_Mony\t" + this.ITEM_58_NEW_BASEITEM_1_month_consumption_Mony + "\n" +
                        "ITEM_59_NEW_BASEITEM_2_month_consumption_Mony\t" + this.ITEM_59_NEW_BASEITEM_2_month_consumption_Mony + "\n" +
                        "ITEM_60_NEW_BASEITEM_3_month_consumption_Mony\t" + this.ITEM_60_NEW_BASEITEM_3_month_consumption_Mony + "\n" +
                        "ITEM_61_NEW_BASEITEM_4_month_consumption_Mony\t" + this.ITEM_61_NEW_BASEITEM_4_month_consumption_Mony + "\n" +
                        "ITEM_62_NEW_BASEITEM_5_month_consumption_Mony\t" + this.ITEM_62_NEW_BASEITEM_5_month_consumption_Mony + "\n" +
                        "ITEM_63_NEW_BASEITEM_6_month_consumption_Mony\t" + this.ITEM_63_NEW_BASEITEM_6_month_consumption_Mony + "\n" +
                        "ITEM_64_NEW_BASEITEM_7_month_consumption_Mony\t" + this.ITEM_64_NEW_BASEITEM_7_month_consumption_Mony + "\n" +
                        "ITEM_65_NEW_BASEITEM_8_month_consumption_Mony\t" + this.ITEM_65_NEW_BASEITEM_8_month_consumption_Mony + "\n" +
                        "ITEM_66_NEW_BASEITEM_9_month_consumption_Mony\t" + this.ITEM_66_NEW_BASEITEM_9_month_consumption_Mony + "\n" +
                        "ITEM_67_NEW_BASEITEM_10_month_consumption_Mony\t" + this.ITEM_67_NEW_BASEITEM_10_month_consumption_Mony + "\n" +
                        "ITEM_68_NEW_BASEITEM_11_month_consumption_Mony\t" + this.ITEM_68_NEW_BASEITEM_11_month_consumption_Mony + "\n" +
                        "ITEM_69_NEW_BASEITEM_12_month_consumption_Mony\t" + this.ITEM_69_NEW_BASEITEM_12_month_consumption_Mony + "\n" +
                        "ITEM_70_NEW_BASEITEM_maxim_demand_month_1\t" + this.ITEM_70_NEW_BASEITEM_maxim_demand_month_1 + "\n" +
                        "ITEM_71_NEW_BASEITEM_maxim_demand_month_2\t" + this.ITEM_71_NEW_BASEITEM_maxim_demand_month_2 + "\n" +
                        "ITEM_72_NEW_BASEITEM_maxim_demand_month_3\t" + this.ITEM_72_NEW_BASEITEM_maxim_demand_month_3 + "\n" +
                        "ITEM_73_NEW_BASEITEM_maxim_demand_month_4\t" + this.ITEM_73_NEW_BASEITEM_maxim_demand_month_4 + "\n" +
                        "ITEM_74_NEW_BASEITEM_maxim_demand_month_5\t" + this.ITEM_74_NEW_BASEITEM_maxim_demand_month_5 + "\n" +
                        "ITEM_75_NEW_BASEITEM_maxim_demand_month_6\t" + this.ITEM_75_NEW_BASEITEM_maxim_demand_month_6 + "\n" +
                        "ITEM_76_NEW_BASEITEM_maxim_demand_month_7\t" + this.ITEM_76_NEW_BASEITEM_maxim_demand_month_7 + "\n" +
                        "ITEM_77_NEW_BASEITEM_maxim_demand_month_8\t" + this.ITEM_77_NEW_BASEITEM_maxim_demand_month_8 + "\n" +
                        "ITEM_78_NEW_BASEITEM_maxim_demand_month_9\t" + this.ITEM_78_NEW_BASEITEM_maxim_demand_month_9 + "\n" +
                        "ITEM_79_NEW_BASEITEM_maxim_demand_month_10\t" + this.ITEM_79_NEW_BASEITEM_maxim_demand_month_10 + "\n" +
                        "ITEM_80_NEW_BASEITEM_maxim_demand_month_11\t" + this.ITEM_80_NEW_BASEITEM_maxim_demand_month_11 + "\n" +
                        "ITEM_81_NEW_BASEITEM_maxim_demand_month_12\t" + this.ITEM_81_NEW_BASEITEM_maxim_demand_month_12 + "\n" +
                        "ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1\t" + this.ITEM_82_NEW_BASEITEM_maxim_demand_month_Date_1 + "\n" +
                        "ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2\t" + this.ITEM_83_NEW_BASEITEM_maxim_demand_month_Date_2 + "\n" +
                        "ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3\t" + this.ITEM_84_NEW_BASEITEM_maxim_demand_month_Date_3 + "\n" +
                        "ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4\t" + this.ITEM_85_NEW_BASEITEM_maxim_demand_month_Date_4 + "\n" +
                        "ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5\t" + this.ITEM_86_NEW_BASEITEM_maxim_demand_month_Date_5 + "\n" +
                        "ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6\t" + this.ITEM_87_NEW_BASEITEM_maxim_demand_month_Date_6 + "\n" +
                        "ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7\t" + this.ITEM_88_NEW_BASEITEM_maxim_demand_month_Date_7 + "\n" +
                        "ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8\t" + this.ITEM_89_NEW_BASEITEM_maxim_demand_month_Date_8 + "\n" +
                        "ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9\t" + this.ITEM_90_NEW_BASEITEM_maxim_demand_month_Date_9 + "\n" +
                        "ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10\t" + this.ITEM_91_NEW_BASEITEM_maxim_demand_month_Date_10 + "\n" +
                        "ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11\t" + this.ITEM_92_NEW_BASEITEM_maxim_demand_month_Date_11 + "\n" +
                        "ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12\t" + this.ITEM_93_NEW_BASEITEM_maxim_demand_month_Date_12 + "\n" +
                        "ITEM_94_NEW_BASEITEM_kvar_consumption_month_1\t" + this.ITEM_94_NEW_BASEITEM_kvar_consumption_month_1 + "\n" +
                        "ITEM_95_NEW_BASEITEM_kvar_consumption_month_2\t" + this.ITEM_95_NEW_BASEITEM_kvar_consumption_month_2 + "\n" +
                        "ITEM_96_NEW_BASEITEM_kvar_consumption_month_3\t" + this.ITEM_96_NEW_BASEITEM_kvar_consumption_month_3 + "\n" +
                        "ITEM_97_NEW_BASEITEM_kvar_consumption_month_4\t" + this.ITEM_97_NEW_BASEITEM_kvar_consumption_month_4 + "\n" +
                        "ITEM_98_NEW_BASEITEM_kvar_consumption_month_5\t" + this.ITEM_98_NEW_BASEITEM_kvar_consumption_month_5 + "\n" +
                        "ITEM_99_NEW_BASEITEM_kvar_consumption_month_6\t" + this.ITEM_99_NEW_BASEITEM_kvar_consumption_month_6 + "\n" +
                        "ITEM_100_NEW_BASEITEM_kvar_consumption_month_7\t" + this.ITEM_100_NEW_BASEITEM_kvar_consumption_month_7 + "\n" +
                        "ITEM_101_NEW_BASEITEM_kvar_consumption_month_8\t" + this.ITEM_101_NEW_BASEITEM_kvar_consumption_month_8 + "\n" +
                        "ITEM_102_NEW_BASEITEM_kvar_consumption_month_9\t" + this.ITEM_102_NEW_BASEITEM_kvar_consumption_month_9 + "\n" +
                        "ITEM_103_NEW_BASEITEM_kvar_consumption_month_10\t" + this.ITEM_103_NEW_BASEITEM_kvar_consumption_month_10 + "\n" +
                        "ITEM_104_NEW_BASEITEM_kvar_consumption_month_11\t" + this.ITEM_104_NEW_BASEITEM_kvar_consumption_month_11 + "\n" +
                        "ITEM_105_NEW_BASEITEM_kvar_consumption_month_12\t" + this.ITEM_105_NEW_BASEITEM_kvar_consumption_month_12 + "\n";
    }
}
