package com.esmc.protocol.model;

import com.alibaba.fastjson.annotation.JSONField;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/ResponseInfo.class */
public class ResponseInfo {
    @J<PERSON><PERSON>ield(ordinal = 1)
    private String service;
    @<PERSON><PERSON><PERSON>ield(ordinal = 2)
    private String pdu;
    @J<PERSON>NField(ordinal = 3)
    private Object value;
    @JSO<PERSON>ield(ordinal = 4)
    private String result;

    public static ResponseInfo failedResponseInfo(String service) {
        ResponseInfo responseInfo = new ResponseInfo();
        responseInfo.setService(service);
        responseInfo.setResult("failure");
        return responseInfo;
    }

    public String getPdu() {
        return this.pdu;
    }

    public void setPdu(String pdu) {
        this.pdu = pdu;
    }

    public Object getValue() {
        return this.value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getService() {
        return this.service;
    }

    public void setService(String service) {
        this.service = service;
    }
}
