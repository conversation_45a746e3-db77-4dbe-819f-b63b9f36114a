package shoaa.opticalsmartreader.ui;

import static shoaa.common.Utils.writeStringAsFile;

import android.Manifest;
import android.app.AlertDialog;
import android.app.Dialog;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.Base64;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.app.ActivityCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import shoaa.common.GsonDateSerialization;
import shoaa.connectionmanager.Common;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.connectionmanager.ConnectionNotifier;
import shoaa.connectionmanager.DisplayDevice;
import shoaa.connectionmanager.usbserial.UsbDeviceItem;
import shoaa.electrometerreader.ElectrometerResponse;
import shoaa.esmcreader.EsmcResponse;
import shoaa.globalreader.GlobalResponse;
import shoaa.globalreader.GlobalVersion;
import shoaa.gpireader.GpiResponse;
import shoaa.hay2areader.Hay2aResponse;
import shoaa.iskrareader.IskraResponse;
import shoaa.maasarareader.MaasaraResponse;
import shoaa.opticalsmartreader.BuildConfig;
import shoaa.opticalsmartreader.MainApplication;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.Utils;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.AppLocationManager;
import shoaa.opticalsmartreader.logic.CycleDate.CycleDateManager;
import shoaa.opticalsmartreader.logic.Enums;
import shoaa.opticalsmartreader.logic.MeterData.DatabaseMeterData;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.CycleDate;
import shoaa.opticalsmartreader.models.DataView;
import shoaa.opticalsmartreader.models.MeterCheck;
import shoaa.opticalsmartreader.models.ShowReadedMeterData;
import shoaa.smartmeterreader.MeterCo;
import shoaa.smartmeterreader.MeterReader;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;

public class ReaderFragment extends AppFragment {
    private final ArrayList<DisplayDevice> listItems = new ArrayList<>();
    public Client selectedClient = null;
    int selectedIndex = -1;
    AlertDialog alertDialog = null;
    AlertDialog.Builder builder = null;
    BluetoothListDialog builderSingle = null;
    ArrayList<String> dataArrayList = new ArrayList<>();
    String[] metersCoStringArray;
    String[] meterPositionStringArray = {"", "داخلى", "خارجى"};
    ArrayAdapter<String> meterPositionAdapter;
    Spinner meterPositionSpinner;
    String[] readingStateStringArray = {"0", "مقروء", "مغلق", "غير قادر علي الوصول", "غير قادر علي القراءة", "معطل", "", "", "كسر", "سرقه تيار", "مرفوع", "غير موجود علي الطبيعة", "مصيف", "غير متصل", "ممتنع", "مخالفة شروط تعاقد", "هدم و بناء"};
    String[] upStateStringArray = {"0", "مرفوع", "إحلال"};
    String[] globalVersions = {Calendar.getInstance().get(Calendar.YEAR) + "←2020", "2019←2013", "2012←2008"};
    Map<String, Integer> meterCoMap = new HashMap<>();
    Spinner readingStateSpinner;
    Spinner upTypeSpinner;
    LinearLayout globalVersionView;
    Spinner globalVersionSpinner;
    ArrayAdapter<String> meterCoAdapter;
    ArrayAdapter<String> readingStateAdapter;
    ArrayAdapter<String> upStateAdapter;
    ArrayAdapter<String> globalVersionAdapter;
    ArrayAdapter<String> dataAdapter;
    LinearLayout scrollView;
    ListView stringListView;
    View meterCoDivider;
    Spinner meterCoSpinner;
    Button btnRead;
    Button showMeterImageBtn;
    TextView deviceName;
    Button pickBluetooth;
    String selectedDeviceAddress = "";
    boolean reading = false;
    int tryCount = 0;
    ///////////////////////////////////////////
    private long mBarcode = 0;
    private int mReadingCode = 0;
    private int mLostReadingCode = 0;
    private int mDamageCode = 0;
    private int mReadingDataFlag = 0;
    private int mOpticalSerialNumber = 0;
    private ReadingResponse mReadingResponse = new ReadingResponse();
    private Enums.ActivityType mActivityType = null;
    private String mReadingNotes = "";
    private String mBarCodeImagePath = "";

    private ShowReadedMeterData showReadedMeterData = null;

    public ReaderFragment() {
        // Required empty public constructor
    }

//    public static void writeStringAsFile(Context context, final String fileContents, String fileName) {
//        try {
//            File outDir = context.getExternalFilesDir("dataDirs");
//            if (!outDir.exists())
//                outDir.mkdirs();
//            if (new File(outDir, fileName).exists())
//                new File(outDir, fileName).delete();
//            FileWriter out = new FileWriter(new File(outDir, fileName));
//            out.write(fileContents);
//            out.close();
//        } catch (IOException e) {
//
//        }
//    }

//    private int getFactoryVisibility(int position) {
////                BuildConfig.CODE
////                شمال القاهرة 1
////                جنوب القاهرة 2
////                البحيرة 3
////                شمال الدلتا 4
////                جنوب الدلتا 5
////                مصر الوسطى 6
////                مصر العليا 7
////                القناة 8
////                الإسكندرية 9
//
//        // 1 جلوبال
//        // 2 المصريه
//        // 3 اسكرا
//        // 4 الكتروميتر
//        // 5 المعصره
//        // 6 جيزة باور
//        switch (position) {
//            case 1:
//                return View.VISIBLE;
//            case 2:
//                if (BuildConfig.COMPANY_CODE == 8 ||
//                        BuildConfig.COMPANY_CODE == 9 ||
//                        BuildConfig.COMPANY_CODE == 6 ||
//                        BuildConfig.COMPANY_CODE == 5 ||
//                        BuildConfig.COMPANY_CODE == 1 ||
//                        BuildConfig.COMPANY_CODE == 3)
//                    return View.VISIBLE;
//                else
//                    return View.GONE;
//            case 3:
//                if (BuildConfig.COMPANY_CODE == 4 ||
//                        BuildConfig.COMPANY_CODE == 7)
//                    return View.GONE;
//                else
//                    return View.VISIBLE;
//            case 4:
//                if (BuildConfig.COMPANY_CODE == 7)
//                    return View.GONE;
//                else
//                    return View.VISIBLE;
//            case 5:
//                if (BuildConfig.COMPANY_CODE == 8 ||
//                        BuildConfig.COMPANY_CODE == 9 ||
//                        BuildConfig.COMPANY_CODE == 6 ||
//                        BuildConfig.COMPANY_CODE == 3)
//                    return View.VISIBLE;
//                else
//                    return View.GONE;
//
//            case 6:
//                if (BuildConfig.COMPANY_CODE == 1 ||
//                        BuildConfig.COMPANY_CODE == 2 ||
//                        BuildConfig.COMPANY_CODE == 3 ||
//                        BuildConfig.COMPANY_CODE == 4)
//                    return View.VISIBLE;
//                else
//                    return View.GONE;
//            default:
//                return View.GONE;
//        }
//    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ArrayList<String> availableBeachFlavors = new ArrayList<>();
        availableBeachFlavors.add("AlexandriaVPN");
        availableBeachFlavors.add("AlBuhayrah");
        availableBeachFlavors.add("NorthDelta");
        availableBeachFlavors.add("Canal");
        if (!availableBeachFlavors.contains(BuildConfig.coName)) {
            readingStateStringArray[12] = "";
        }
        metersCoStringArray = new String[]{"0", "جلوبال", "المصريه", "اسكرا", "الكتروميتر", "المعصره", "جيزة باور", "الهيئة العربية"};
        meterCoMap.put("0", 0);
        meterCoMap.put("جلوبال", 1);
        meterCoMap.put("المصريه", 2);
        meterCoMap.put("اسكرا", 3);
        meterCoMap.put("الكتروميتر", 4);
        meterCoMap.put("المعصره", 5);
        meterCoMap.put("جيزة باور", 6);
        meterCoMap.put("الهيئة العربية", 7);

//        List<String> meterCoArrList = new ArrayList<>();
//        for (int i = 0; i < metersCoStringArray.length; i++) {
//            if (i == 0)
//                meterCoArrList.add(metersCoStringArray[i]);
//            else {
//                if (getFactoryVisibility(i) != View.GONE) {
//                    meterCoArrList.add(metersCoStringArray[i]);
//                }
//            }
//        }
        List<String> meterCoArrList = new ArrayList<>(Arrays.asList(metersCoStringArray));
        metersCoStringArray = new String[meterCoArrList.size()];
        metersCoStringArray = meterCoArrList.toArray(metersCoStringArray);

        meterCoAdapter = new ArrayAdapter<String>(MainApplication.getInstance().getCurrentActivity(), android.R.layout.simple_spinner_dropdown_item, metersCoStringArray) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                if (position == 0) {
                    view.findViewById(R.id.text1).setVisibility(View.GONE);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                } else {
                    String meterCo = metersCoStringArray[position];
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 25);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setText(meterCo);
                    text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.black));
                    view.setBackgroundColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                }
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                ImageView imageView = view.findViewById(R.id.image);
                imageView.setVisibility(View.VISIBLE);
                switch (position) {
                    case 0:
                        imageView.setVisibility(View.GONE);
                        break;
                    case 1:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.global, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 2:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.esmc, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 3:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.iskra, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 4:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.electrometer, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 5:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.maasara, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 6:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.gpi, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                    case 7:
                        imageView.setImageDrawable(ResourcesCompat.getDrawable((MainApplication.getInstance().getCurrentActivity()).getResources(), R.drawable.hay2a, (MainApplication.getInstance().getCurrentActivity()).getTheme()));
                        break;
                }

                return view;
            }


            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                String meterCo;
                if (position == 0) {
                    meterCo = "برجاء اختيار شركه العداد";
                } else {
                    meterCo = metersCoStringArray[position];
                }
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(meterCo);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                return view;
            }
        };
        readingStateAdapter = new ArrayAdapter<String>((MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, readingStateStringArray) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                if (position == 0 || readingStateStringArray[position].isEmpty()) {
                    view.findViewById(R.id.text1).setVisibility(View.GONE);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                } else {
                    String state = readingStateStringArray[position];
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    text1.setText(state);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.black));
                    view.setBackgroundColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                }
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                String state;
                if (position == 0 || readingStateStringArray[position].isEmpty()) {
                    state = "برجاء اختيار حالة القراءة";
                } else {
                    state = readingStateStringArray[position];
                }
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                return view;
            }
        };

        upStateAdapter = new ArrayAdapter<String>((MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, upStateStringArray) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                if (position == 0 || upStateStringArray[position].isEmpty()) {
                    view.findViewById(R.id.text1).setVisibility(View.GONE);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                } else {
                    String state = upStateStringArray[position];
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    text1.setText(state);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.black));
                    view.setBackgroundColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                }
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                String state;
                if (position == 0 || upStateStringArray[position].isEmpty()) {
                    state = "برجاء اختيار حالة الرفع";
                } else {
                    state = upStateStringArray[position];
                }
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                return view;
            }
        };

        globalVersionAdapter = new ArrayAdapter<String>((MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, globalVersions) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                String state = globalVersions[position];
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.black));
                text1.setTextDirection(View.TEXT_DIRECTION_LTR);
                text1.setGravity(Gravity.END);
                view.setBackgroundColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white));
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                return view;
            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                String state = globalVersions[position];

                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                text1.setTextDirection(View.TEXT_DIRECTION_LTR);
                text1.setGravity(Gravity.END);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                return view;
            }
        };

        meterPositionAdapter = new ArrayAdapter<String>((MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_spinner_dropdown_item, meterPositionStringArray) {
            @Override
            public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
                View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                if (position == 0 || meterPositionStringArray[position].isEmpty()) {
                    view.findViewById(R.id.text1).setVisibility(View.GONE);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                } else {
                    //View view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                    String state = meterPositionStringArray[position];
                    TextView text1 = view.findViewById(R.id.text1);
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    text1.setText(state);
                    text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 17);
                    text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                    text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.black));
                    text1.setTextDirection(View.TEXT_DIRECTION_LTR);
                    text1.setGravity(Gravity.END);
                    view.setBackgroundColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white));
                    view.findViewById(R.id.text2).setVisibility(View.GONE);
                    view.findViewById(R.id.image).setVisibility(View.GONE);
                    view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                }
                return view;

            }

            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {

                String state;
                if (position == 0 || meterPositionStringArray[position].isEmpty()) {
                    state = "برجاء اختيار موقع العداد";
                } else {
                    state = meterPositionStringArray[position];
                }
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(state);
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                text1.setTextDirection(View.TEXT_DIRECTION_LTR);
                text1.setGravity(Gravity.END);
                text1.setTextColor((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary));
                return view;
            }
        };


        dataAdapter = new ArrayAdapter<String>((MainApplication.getInstance().getCurrentActivity()), android.R.layout.simple_list_item_1, dataArrayList) {
            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                DataView dataView = new Gson().fromJson(dataArrayList.get(position), DataView.class);
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);


                text1.setText(dataView.data);
                text1.setGravity(Gravity.START);
                text1.setTextColor(dataView.color);
                if (dataView.data.contains("ــــــــــــــــ")) text1.setMaxLines(1);
                else text1.setMaxLines(5);
                text1.setTextSize(14);
                view.setTextDirection(View.TEXT_DIRECTION_ANY_RTL);
                view.setClickable(false);
                view.setFocusable(false);
                view.setEnabled(false);
                view.setForegroundGravity(Gravity.START);
                return view;
            }
        };

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View layout = inflater.inflate(R.layout.fragment_reader, container, false);
        readingStateSpinner = layout.findViewById(R.id.stateSpinner);
        upTypeSpinner = layout.findViewById(R.id.upTypeSpinner);
        globalVersionView = layout.findViewById(R.id.globalVersionView);
        globalVersionSpinner = layout.findViewById(R.id.globalVersionSpinner);
        meterPositionSpinner = layout.findViewById(R.id.meterPositionSpinner);
        stringListView = layout.findViewById(R.id.stringListView);
        scrollView = layout.findViewById(R.id.scrollView);
        stringListView.setDivider(null);
        stringListView.setDividerHeight(0);
        btnRead = layout.findViewById(R.id.btn_read);
        showMeterImageBtn = layout.findViewById(R.id.showMeterImageBtn);
        meterCoSpinner = layout.findViewById(R.id.meterTypeSpinner);
        meterCoDivider = layout.findViewById(R.id.divider2);
        readingStateSpinner.setAdapter(readingStateAdapter);
        meterCoSpinner.setAdapter(meterCoAdapter);
        upTypeSpinner.setAdapter(upStateAdapter);
        globalVersionSpinner.setAdapter(globalVersionAdapter);

        meterPositionSpinner.setAdapter(meterPositionAdapter);

        stringListView.setAdapter(dataAdapter);
        deviceName = layout.findViewById(R.id.bluetoothName);
        pickBluetooth = layout.findViewById(R.id.btn_pick_bluetooth);

//        stringListView.setFastScrollAlwaysVisible(true);
//        scrollView.setScrollContainer(true);

        scrollView.setOnTouchListener(new OnSwipeTouchListener((MainApplication.getInstance().getCurrentActivity())) {
            public void onSwipeRight() {
                if (!reading) {
                    selectedIndex++;
                    Client client = ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.getClientByIndex(selectedIndex);
                    if (client != null) {
                        onClientSelected(client, selectedIndex);
                    }
                }
            }

            public void onSwipeLeft() {
                if (!reading) {
                    selectedIndex--;
                    if (selectedIndex < -1) selectedIndex = -1;
                    Client client = ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.getClientByIndex(selectedIndex);
                    if (client != null) {
                        onClientSelected(client, selectedIndex);
                    }
                }
            }
        });


        stringListView.setOnTouchListener(new OnSwipeTouchListener((MainApplication.getInstance().getCurrentActivity())) {
            public void onSwipeRight() {
                if (!reading) {
                    selectedIndex++;
                    Client client = ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.getClientByIndex(selectedIndex);
                    if (client != null) {
                        onClientSelected(client, selectedIndex);
                    }
                }
            }

            public void onSwipeLeft() {
                if (!reading) {
                    selectedIndex--;
                    if (selectedIndex < -1) selectedIndex = -1;
                    Client client = ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.getClientByIndex(selectedIndex);
                    if (client != null) {
                        onClientSelected(client, selectedIndex);
                    }
                }
            }
        });


        // Set an item click listener
        showMeterImageBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                    showCustomDialog();
                });
            }
        });

        MainApplication.getInstance().listAdapter = new ArrayAdapter<DisplayDevice>((MainApplication.getInstance().getCurrentActivity()), R.layout.device_list_item, listItems) {
            @NonNull
            @Override
            public View getView(int position, View view, @NonNull ViewGroup parent) {
                if (view == null)
                    view = getLayoutInflater().inflate(R.layout.device_list_item, parent, false);
                TextView text1 = view.findViewById(R.id.text1);
                view.findViewById(R.id.text2).setVisibility(View.GONE);
                view.findViewById(R.id.image).setVisibility(View.GONE);
                text1.setText(listItems.get(position).getName());
                text1.setTextColor(AppCompatResources.getColorStateList((MainApplication.getInstance().getCurrentActivity()), R.color.on_primary));
                view.setBackgroundColor(position % 2 == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.primary_light));
                view.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                text1.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
                text1.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
                ViewGroup.LayoutParams params = text1.getLayoutParams();
                params.width = ViewGroup.LayoutParams.MATCH_PARENT;

                text1.setLayoutParams(params);
                text1.setTypeface(text1.getTypeface(), Typeface.BOLD);
                return view;
            }
        };

        pickBluetooth.setOnClickListener(view -> {
            new Thread(() -> {
                ConnectionManager.Companion.getInstance().init(getActivity(), BuildConfig.enableUSB, new ConnectionNotifier() {
                    @Override
                    public void notifyConnection() {
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                refresh();
                            }
                        });
                    }
                });
                AppLocationManager.getInstance(getActivity()).listen();
            }).start();

            builderSingle = new BluetoothListDialog((MainApplication.getInstance().getCurrentActivity()), MainApplication.getInstance().listAdapter, (adapterView, view1, i, l) -> {
                DisplayDevice device = MainApplication.getInstance().listAdapter.getItem(i);
                if (device != null) {
                    selectedDeviceAddress = device.getAddress();
                    if (device.getDevice() instanceof BluetoothDevice) {
                        shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putBoolean("useBluetooth", true).apply();
                        shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("bluetooth", selectedDeviceAddress).apply();

                        deviceName.setText(device.getName());
                    } else {
                        shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putBoolean("useBluetooth", false).apply();
                        shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("bluetooth", "").apply();

                        deviceName.setText(extractNumberFromSerial(device.getName()));
                    }
                    ConnectionManager.Companion.getInstance().setDevice(device);
                    if (builderSingle != null) builderSingle.dismiss();
                }
            });
            builderSingle.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            builderSingle.show();
        });

        readingStateSpinner.getBackground().setColorFilter((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        meterCoSpinner.getBackground().setColorFilter((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        meterPositionSpinner.getBackground().setColorFilter((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);
        upTypeSpinner.getBackground().setColorFilter((MainApplication.getInstance().getCurrentActivity()).getColor(R.color.on_primary), PorterDuff.Mode.SRC_ATOP);

        readingStateSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                setUI(i);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
        meterCoSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                if (i == 0) {
                    btnRead.setEnabled(false);
                    btnRead.setVisibility(View.GONE);
                    showMeterImageBtn.setEnabled(false);
                    showMeterImageBtn.setVisibility(View.GONE);
                    showReadedMeterData = null;
                    deleteKey(getContext(), Utils.METER_DATA_KEY);
                } else {
                    globalVersionView.setVisibility(Common.ENABLE_GLOBAL_VERSION_SELECTION && i == 1 && (readingStateSpinner.getSelectedItemPosition() == 1 || readingStateSpinner.getSelectedItemPosition() == 15) ? View.VISIBLE : View.GONE);
                    btnRead.setEnabled(true);
                    btnRead.setVisibility(View.VISIBLE);
                    showMeterImageBtn.setEnabled(true);
                    showMeterImageBtn.setVisibility(View.GONE);
                    showReadedMeterData = null;
                    deleteKey(getContext(), Utils.METER_DATA_KEY);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        upTypeSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                if (i == 0) {
                    btnRead.setEnabled(false);
                    btnRead.setVisibility(View.GONE);

                    showMeterImageBtn.setEnabled(false);
                    showMeterImageBtn.setVisibility(View.GONE);
                    showReadedMeterData = null;
                    deleteKey(getContext(), Utils.METER_DATA_KEY);
                } else {
                    btnRead.setEnabled(true);
                    btnRead.setVisibility(View.VISIBLE);

                    showMeterImageBtn.setEnabled(true);
                    showMeterImageBtn.setVisibility(View.GONE);
                    showReadedMeterData = null;
                    deleteKey(getContext(), Utils.METER_DATA_KEY);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });

        btnRead.setOnClickListener(view -> {
            if (meterPositionSpinner.getVisibility() == View.VISIBLE) {
                if (meterPositionSpinner.getSelectedItemPosition() != 0) {
                    startBtnReadCode();
                } else {
                    Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يرجي اختيار موقع العداد", Toast.LENGTH_LONG).show();
                }
            } else {
                startBtnReadCode();
            }
        });
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.customers);
        return layout;
    }

    private void startBtnReadCode() {
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).checkTimeAutomatic();
        AppUser appUser = AppUser.getInstance((MainApplication.getInstance().getCurrentActivity()));
        if (appUser == null || appUser.getUSER_ID() == null || appUser.getUSER_ID().isEmpty() || appUser.getUserName() == null || appUser.getUserName().isEmpty() || appUser.getPassword() == null || appUser.getPassword().isEmpty()) {
            shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent((MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            (MainApplication.getInstance().getCurrentActivity()).finish();
            startActivity(i);
        }


        btnRead.setEnabled(false);
        showMeterImageBtn.setEnabled(false);
        showReadedMeterData = null;
        deleteKey(getContext(), Utils.METER_DATA_KEY);
        showMeterImageBtn.setVisibility(View.GONE);
        pickBluetooth.setEnabled(false);
        readingStateSpinner.setEnabled(false);
        meterCoSpinner.setEnabled(false);
        meterPositionSpinner.setEnabled(false);
        upTypeSpinner.setEnabled(false);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).enableBottomNav = false;
        clearData();
        if (selectedDeviceAddress.isEmpty()) {
            Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يرجي اختيار جهاز الاوبتيكال", Toast.LENGTH_LONG).show();
            btnRead.setEnabled(true);
            showMeterImageBtn.setEnabled(false);
            pickBluetooth.setEnabled(true);
            readingStateSpinner.setEnabled(true);
            meterCoSpinner.setEnabled(true);
            meterPositionSpinner.setEnabled(true);
            upTypeSpinner.setEnabled(true);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).enableBottomNav = true;
        } else {

            newRead();
            try {

//                    (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
//                        Toast.makeText(getContext(), String.valueOf(ConnectionManager.Companion.getInstance().getConnectedDeviceSerial()), Toast.LENGTH_SHORT).show();
//
//                    });

                Log.d("sayed-serial-number", ConnectionManager.Companion.getInstance().getConnectedDeviceSerial() + "");
                if (ConnectionManager.Companion.getInstance().getConnectedDeviceSerial() > 0) {
                    setmOpticalSerialNumber(ConnectionManager.Companion.getInstance().getConnectedDeviceSerial());

                } else setmOpticalSerialNumber(99999);
                startRead();
            } catch (Exception e) {
                Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يرجي اختيار جهاز الاوبتيكال" + e.getMessage(), Toast.LENGTH_LONG).show();
                btnRead.setEnabled(true);
                showMeterImageBtn.setEnabled(false);
                pickBluetooth.setEnabled(true);
                readingStateSpinner.setEnabled(true);
                meterCoSpinner.setEnabled(true);
                meterPositionSpinner.setEnabled(true);
                upTypeSpinner.setEnabled(true);
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).enableBottomNav = true;
            }

        }
    }
//    private static void showAttachmentImage(Context context, Bitmap imageBitmap) {
//
//        AlertDialog attachment_image_dialog;
//
//        AlertDialog.Builder builder = new AlertDialog.Builder(context);
//
//        ImageView attachmentImage = new ImageView(context);
//        attachmentImage.setScaleType(ImageView.ScaleType.FIT_XY);
//        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
//        attachmentImage.setLayoutParams(lp);
//
//        attachmentImage.setImageBitmap(imageBitmap);
//
//        builder.setView(attachmentImage);
//
//        attachment_image_dialog = builder.create();
//        attachment_image_dialog.show();
//        attachment_image_dialog.getWindow().setLayout(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
//        attachment_image_dialog.setCanceledOnTouchOutside(false);
//
//        attachment_image_dialog.setOnKeyListener((dialog, keyCode, event) -> {
//            if (keyCode == KeyEvent.KEYCODE_BACK) {
//                attachment_image_dialog.dismiss();
//                attachment_image_dialog.cancel();
//                return true;
//            }
//            return false;
//        });
//    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).checkTimeAutomatic();
        AppUser appUser = AppUser.getInstance((MainApplication.getInstance().getCurrentActivity()));
        if (appUser == null || appUser.getUSER_ID() == null || appUser.getUSER_ID().isEmpty() || appUser.getUserName() == null || appUser.getUserName().isEmpty() || appUser.getPassword() == null || appUser.getPassword().isEmpty()) {
            shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent((MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            (MainApplication.getInstance().getCurrentActivity()).finish();
            startActivity(i);
        }
        if (!hidden) {
            new Thread(() -> {
                CycleDate cycleDate = CycleDateManager.getCycleDate((MainApplication.getInstance().getCurrentActivity()));
                if (cycleDate == null || cycleDate.CycleYear == 0 || cycleDate.CycleMonth == 0) {
                    (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                        Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يجب المزامنة اولا", Toast.LENGTH_LONG).show();
                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.sync);
                    });
                }
                enableUI();
            }).start();
        }
    }

    private void setUI(int i) {
        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            meterCoSpinner.setSelection(0);
            upTypeSpinner.setSelection(0);
            globalVersionSpinner.setSelection(0);
            meterPositionSpinner.setSelection(0);
            showReadedMeterData = null;
            deleteKey(getContext(), Utils.METER_DATA_KEY);
            showMeterImageBtn.setVisibility(View.GONE);
            showMeterImageBtn.setEnabled(false);
            globalVersionView.setVisibility(Common.ENABLE_GLOBAL_VERSION_SELECTION && i == 1 && (readingStateSpinner.getSelectedItemPosition() == 1 || readingStateSpinner.getSelectedItemPosition() == 15) ? View.VISIBLE : View.GONE);

            switch (i) {
                case 0:
                    meterCoSpinner.setVisibility(View.GONE);
                    upTypeSpinner.setVisibility(View.GONE);
                    meterCoDivider.setVisibility(View.GONE);
                    globalVersionView.setVisibility(View.GONE);
                    btnRead.setVisibility(View.GONE);
                    btnRead.setEnabled(false);
                    meterPositionSpinner.setVisibility(View.GONE);
                    break;
                case 1:
                    globalVersionView.setVisibility(View.GONE);
                case 8:
                case 15:
                    meterCoSpinner.setVisibility(View.VISIBLE);
                    meterPositionSpinner.setVisibility(View.VISIBLE);
                    meterCoDivider.setVisibility(View.VISIBLE);
                    upTypeSpinner.setVisibility(View.GONE);
                    btnRead.setText(R.string.read);
                    btnRead.setVisibility(View.GONE);
                    btnRead.setEnabled(false);

                    break;
                case 10:
                    upTypeSpinner.setVisibility(View.VISIBLE);
                    meterCoSpinner.setVisibility(View.GONE);
                    meterPositionSpinner.setVisibility(View.GONE);
                    meterCoDivider.setVisibility(View.VISIBLE);
                    btnRead.setText(R.string.capture);
                    btnRead.setVisibility(View.GONE);
                    btnRead.setEnabled(false);

                    break;
                case 2:
                case 6:
                case 7:
                case 11:
                case 12:
                case 14:
                case 16:
                    meterCoSpinner.setVisibility(View.GONE);
                    meterPositionSpinner.setVisibility(View.GONE);
                    meterCoDivider.setVisibility(View.GONE);
                    upTypeSpinner.setVisibility(View.GONE);
                    btnRead.setText(R.string.capture);

                    btnRead.setVisibility(View.VISIBLE);
                    btnRead.setEnabled(true);

                    break;
                case 9:
                case 3:
                case 4:
                case 5:
                case 13:
                    meterCoSpinner.setVisibility(View.VISIBLE);
                    meterPositionSpinner.setVisibility(View.VISIBLE);
                    meterCoDivider.setVisibility(View.VISIBLE);
                    upTypeSpinner.setVisibility(View.GONE);
                    btnRead.setText(R.string.capture);
                    btnRead.setVisibility(View.GONE);
                    btnRead.setEnabled(false);

                    break;
                default:
                    meterCoSpinner.setVisibility(View.GONE);
                    meterPositionSpinner.setVisibility(View.GONE);
                    meterCoDivider.setVisibility(View.GONE);
                    upTypeSpinner.setVisibility(View.GONE);
                    globalVersionView.setVisibility(View.GONE);
                    btnRead.setEnabled(false);
                    break;

            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();


    }

    private void addToList(String data, int color, String imagePath) {
        String newData = new Gson().toJson(new DataView(data, color, imagePath));
        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            if (stringListView != null) {
                dataArrayList.add(newData);
                dataAdapter.notifyDataSetChanged();
            }
        });

    }


///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////

    private void editLastItem(String data, int color) {
        String newData = new Gson().toJson(new DataView(data, color, null));

        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            if (stringListView != null) {
                if (!dataArrayList.isEmpty()) dataArrayList.set(dataArrayList.size() - 1, newData);
                else dataArrayList.set(0, newData);
                dataAdapter.notifyDataSetChanged();
            }
        });

    }

    private void clearData() {
        if (stringListView != null) {
            (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                dataArrayList.clear();
                dataAdapter.notifyDataSetChanged();
            });
        }
    }

    private void startRead() {
        if (AppUser.getInstance((MainApplication.getInstance().getCurrentActivity())) == null || AppUser.getInstance((MainApplication.getInstance().getCurrentActivity())).getUSER_ID() == null || AppUser.getInstance((MainApplication.getInstance().getCurrentActivity())).getUSER_ID().isEmpty()) {
            shoaa.opticalsmartreader.logic.Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent((MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            (MainApplication.getInstance().getCurrentActivity()).finish();  //Kill the activity from which you will go to next activity
            startActivity(i);
        } else new Thread(() -> {
            reading = true;
            tryCount = 0;
            clearData();
            switch (readingStateSpinner.getSelectedItemPosition()) {
                case 1: // readed
                    startReaded();
                    break;
                case 2: // closed
                    startClosed();
                    break;
                case 3: // ErrorCanNotRead
                    startCanNotReach();
                    break;
                case 4: // ErrorReadedWithCard
                    startCanNotRead();
                    break;
                case 5: // ErrorMeterCorrupted
                    startErrorMeterCorrupted();
                    break;
                case 8: // broken
                    startBroken();
                    break;
                case 9: // Stolen
                    startStolen();
                    break;
                case 10: // Up
                    startUp();
                    break;
                case 11: // NotFound
                    startNotFound();
                    break;
                case 12: // Beach
                    startBeach();
                    break;
                case 13: // NotConnected
                    startNotConnected();
                    break;
                case 14: // Refused
                    startRefused();
                    break;
                case 15: // Violation
                    startViolation();
                    break;
                case 16: // Destroy
                    startDestroy();
                    break;
            }
        }).start();
    }

    private void startReaded() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (showStartReadMessageAsync()) {
                ReadingResponse readingResponse = startReadOptical();
                if (readingResponse.readingResult == ReadingResult.SUCCESS) {

                    /*---------------------------------
                     * This Fot test to make Hoda Send File To Test The Meter
                     * ---------------------------------
                     * */
                    //writeStringAsFile(getActivity().getApplicationContext(), readingResponse.toFileFormate(), "response.txt");

                    setmReadingResponse(readingResponse);
                    if (selectedClient != null) {
                        showNoteMessageAsync(!isCustomerId(selectedClient.getCustomerID(), getRealFactoryCodeFromSpinner() == 6));
                        setmReadingCode(0);
                    } else {
                        boolean showCustomerId = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (isCustomerId(((GlobalResponse) readingResponse).getCustomerID(), true))
                                showCustomerId = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (isCustomerId(((IskraResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (isCustomerId(((ElectrometerResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (isCustomerId(((MaasaraResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (isCustomerId(((Hay2aResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (isCustomerId(((EsmcResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (isCustomerId(((GpiResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        }

                        boolean showMeterType = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (!(((GlobalResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (!(((IskraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (!(((ElectrometerResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (!(((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (!(((Hay2aResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (!(((EsmcResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (!(((GpiResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        }
                        showLostMessageAsync(showCustomerId, showMeterType);
                        setmReadingCode(7);
                    }
                    setmLostReadingCode(0);
                    setmDamageCode(0);
                    setmReadingDataFlag(1);
                    saveToDatabase();
                }
            }
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startClosed() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(1);
                setmLostReadingCode(0);
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void startCanNotReach() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (selectedClient != null) {
                showNoteMessageAsync(false);
                setmReadingCode(2);
                setmLostReadingCode(0);
            } else {
                showLostMessageAsync(false, true);
                setmReadingCode(7);
                setmLostReadingCode(2);
            }
            setmDamageCode(1);
            setmReadingDataFlag(0);
            setmReadingResponse(new ReadingResponse());
            saveToDatabase();
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startCanNotRead() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (selectedClient != null) {
                showNoteMessageAsync(selectedClient.getCustomerID() == null || selectedClient.getCustomerID().trim().isEmpty() || selectedClient.getCustomerID().trim().equalsIgnoreCase("0") || selectedClient.getCustomerID().trim().length() <= 2);
                setmReadingCode(2);
                setmLostReadingCode(0);
            } else {
                showLostMessageAsync(true, true);
                setmReadingCode(7);
                setmLostReadingCode(2);
            }
            setmDamageCode(2);
            setmReadingDataFlag(0);
            setmReadingResponse(new ReadingResponse());
            saveToDatabase();
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startErrorMeterCorrupted() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (selectedClient != null) {
                showNoteMessageAsync(false);
                setmReadingCode(2);
                setmLostReadingCode(0);
            } else {
                showLostMessageAsync(false, true);
                setmReadingCode(7);
                setmLostReadingCode(2);
            }
            setmDamageCode(3);
            setmReadingDataFlag(0);
            setmReadingResponse(new ReadingResponse());
            saveToDatabase();
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startBroken() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (showStartReadMessageAsync()) {
                ReadingResponse readingResponse = startReadOptical();
                if (readingResponse.readingResult == ReadingResult.SUCCESS) {
                    setmReadingResponse(readingResponse);
                    if (selectedClient != null) {
                        showNoteMessageAsync(!isCustomerId(selectedClient.getCustomerID(), getRealFactoryCodeFromSpinner() == 6));
                        setmReadingCode(3);
                        setmLostReadingCode(0);
                    } else {
                        boolean showCustomerId = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (isCustomerId(((GlobalResponse) readingResponse).getCustomerID(), true))
                                showCustomerId = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (isCustomerId(((IskraResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (isCustomerId(((ElectrometerResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (isCustomerId(((MaasaraResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (isCustomerId(((Hay2aResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (isCustomerId(((EsmcResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (isCustomerId(((GpiResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        }

                        boolean showMeterType = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (!(((GlobalResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (!(((IskraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (!(((ElectrometerResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (!(((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (!(((Hay2aResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (!(((EsmcResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (!(((GpiResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        }

                        showLostMessageAsync(showCustomerId, showMeterType);
                        setmReadingCode(7);
                        setmLostReadingCode(3);
                    }
                    setmDamageCode(0);
                    setmReadingDataFlag(1);
                    saveToDatabase();
                }
            }
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startStolen() {
        if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
            shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            setmBarCodeImagePath("");
            (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يرجي تصوير حالة السرقة", Toast.LENGTH_LONG).show());
            (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> Toast.makeText((MainApplication.getInstance().getCurrentActivity()), "يرجي تصوير حالة السرقة", Toast.LENGTH_LONG).show());
            takeImageAsync();
            if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
                selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
                if (selectedClient != null) {
                    showNoteMessageAsync(selectedClient.getCustomerID() == null || selectedClient.getCustomerID().trim().isEmpty() || selectedClient.getCustomerID().trim().equalsIgnoreCase("0") || selectedClient.getCustomerID().trim().length() <= 2);
                    setmReadingCode(4);
                    setmLostReadingCode(0);
                } else {
                    showLostMessageAsync(true, true);
                    setmReadingCode(7);
                    setmLostReadingCode(4);
                }
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startUp() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(5);
                setmLostReadingCode(0);
                if (upTypeSpinner.getSelectedItemPosition() == 2) {
                    setmDamageCode(1);
                } else {
                    setmDamageCode(0);
                }
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void startNotFound() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(6);
                setmLostReadingCode(0);
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void startBeach() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(8);
                setmLostReadingCode(0);
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void startNotConnected() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (selectedClient != null) {
                showNoteMessageAsync(false);
                setmReadingCode(9);
                setmLostReadingCode(0);
            } else {
                showLostMessageAsync(false, true);
                setmReadingCode(7);
                setmLostReadingCode(9);
            }
            setmDamageCode(0);
            setmReadingDataFlag(0);
            setmReadingResponse(new ReadingResponse());
            saveToDatabase();
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startRefused() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(10);
                setmLostReadingCode(0);
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void startViolation() {
        readBarcodeAsync();
        if (getmBarcode() != 0 && !getmBarCodeImagePath().isEmpty()) {
            selectedClient = findClient(getmBarcode(), getRealFactoryCodeFromSpinner());
            if (showStartReadMessageAsync()) {
                ReadingResponse readingResponse = startReadOptical();
                if (readingResponse.readingResult == ReadingResult.SUCCESS) {
                    setmReadingResponse(readingResponse);
                    if (selectedClient != null) {
                        showNoteMessageAsync(!isCustomerId(selectedClient.getCustomerID(), getRealFactoryCodeFromSpinner() == 6));
                        setmReadingCode(12);
                        setmLostReadingCode(0);
                    } else {
                        boolean showCustomerId = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (isCustomerId(((GlobalResponse) readingResponse).getCustomerID(), true))
                                showCustomerId = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (isCustomerId(((IskraResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (isCustomerId(((ElectrometerResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (isCustomerId(((MaasaraResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (isCustomerId(((Hay2aResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (isCustomerId(((EsmcResponse) readingResponse).getCustomerId(), false))
                                showCustomerId = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (isCustomerId(((GpiResponse) readingResponse).getCustomer_ID(), false))
                                showCustomerId = false;
                        }

                        boolean showMeterType = true;
                        if (readingResponse instanceof GlobalResponse) {
                            if (!(((GlobalResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof IskraResponse) {
                            if (!(((IskraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof ElectrometerResponse) {
                            if (!(((ElectrometerResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof MaasaraResponse) {
                            if (!(((MaasaraResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof Hay2aResponse) {
                            if (!(((Hay2aResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof EsmcResponse) {
                            if (!(((EsmcResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        } else if (readingResponse instanceof GpiResponse) {
                            if (!(((GpiResponse) readingResponse).getMeterType().equalsIgnoreCase("0")))
                                showMeterType = false;
                        }

                        showLostMessageAsync(showCustomerId, showMeterType);
                        setmReadingCode(7);
                        setmLostReadingCode(12);
                    }
                    setmDamageCode(0);
                    setmReadingDataFlag(1);
                    saveToDatabase();
                }
            }
        }
        selectedClient = null;
        reading = false;
        enableUI();
    }

    private void startDestroy() {
        if (selectedClient != null) {
            takeImageAsync();
            if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()) {
                if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
                    shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                showNoteMessageAsync(false);
                setmBarcode(selectedClient.getMeterID());
                setmReadingCode(13);
                setmLostReadingCode(0);
                setmDamageCode(0);
                setmReadingDataFlag(0);
                setmReadingResponse(new ReadingResponse());
                saveToDatabase();
            }
        } else {
            addToList("يجب اختيار المشترك اولا", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        ////////////////////////////
        reading = false;
        selectedClient = null;
        enableUI();
    }

    private void showCustomDialog() {
        // Create the dialog and set its custom layout
        Dialog dialog = new Dialog(getContext());
        dialog.setContentView(R.layout.show_meter_readed_custom_layout);

        // Set the dialog to match parent width and height
        WindowManager.LayoutParams lp = new WindowManager.LayoutParams();
        lp.copyFrom(dialog.getWindow().getAttributes());
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().setAttributes(lp);

        // Initialize the UI elements in the dialog
        ImageView imageView = dialog.findViewById(R.id.imageView);
        TextView customerIdValue = dialog.findViewById(R.id.customer_id_value);
        TextView mantekaValue = dialog.findViewById(R.id.manteka_value);
        TextView yomeaValue = dialog.findViewById(R.id.yomea_value);
        TextView hesabValue = dialog.findViewById(R.id.hesab_value);
        TextView far3yValue = dialog.findViewById(R.id.far3y_value);
        TextView addressValue = dialog.findViewById(R.id.address_value);

        showReadedMeterData = getSavedMeterData(getContext(), Utils.METER_DATA_KEY);

        if (showReadedMeterData != null) {
            // Set values to the TextViews (You can set real data here)
            if (showReadedMeterData.getCustomerId() != null && !showReadedMeterData.getCustomerId().isEmpty()) {
                customerIdValue.setText(showReadedMeterData.getCustomerId());
            } else {
                customerIdValue.setText("-");
            }

            if (showReadedMeterData.getMnteka() != null && !showReadedMeterData.getMnteka().isEmpty()) {
                mantekaValue.setText(showReadedMeterData.getMnteka() + "");
            } else {
                mantekaValue.setText("-");
            }

            if (showReadedMeterData.getYomea() != null && !showReadedMeterData.getYomea().isEmpty()) {
                yomeaValue.setText(showReadedMeterData.getYomea() + "");
            } else {
                yomeaValue.setText("-");
            }

            if (showReadedMeterData.getHesab() != null && !showReadedMeterData.getHesab().isEmpty()) {
                hesabValue.setText(showReadedMeterData.getHesab() + "");
            } else {
                hesabValue.setText("-");
            }

            if (showReadedMeterData.getFar3y() != null && !showReadedMeterData.getFar3y().isEmpty()) {
                far3yValue.setText(showReadedMeterData.getFar3y() + "");
            } else {
                far3yValue.setText("-");
            }

            if (showReadedMeterData.getAddress() != null && !showReadedMeterData.getAddress().isEmpty()) {
                addressValue.setText(showReadedMeterData.getAddress() + "");
            } else {
                addressValue.setText("-");
            }

            if (getBitmapFromSharedPreferences(getContext(), Utils.METER_IMAGE_KEY) != null) {
                imageView.setImageBitmap(getBitmapFromSharedPreferences(getContext(), Utils.METER_IMAGE_KEY));
            }
        }

        // Display the dialog
        dialog.show();
    }


    private void showReport(DatabaseMeterData databaseMeterData) {
        clearData();
        addToList("تم حفظ البيانات بنجاح", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
        addToList("ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("باركود :" + " " + databaseMeterData.COVER_METER_ID, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
        addToList("نوع العداد :" + " " + (databaseMeterData.getApiFactoryCodeToName()) + " - " + (databaseMeterData.FACTORY_TYPE == 1 ? "احادي" : "ثلاثي"), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
        addToList("رقم العداد :" + " " + (databaseMeterData.READING_DATA_FLAG == 1 ? databaseMeterData.OPTICAL_METER_ID : databaseMeterData.COVER_METER_ID), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
        if (selectedClient != null) {
            if (!selectedClient.getPlaceName().equalsIgnoreCase("ساقط ليست")) {
                addToList("الوصف و النشاط :" + " " + selectedClient.getPlaceName().trim() + " - " + selectedClient.getActivityName().trim(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
            } else {
                selectedClient.setPlaceName("شقـــــة");
            }
        }
        if (databaseMeterData.READING_DATA_FLAG == 1) {
            addToList("الوقت و التاريخ :" + " " + databaseMeterData.ITEM_10_NEW_BASEITEM_Meter_Date_and_Time, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
            addToList("الشريحة الحالية :" + " " + databaseMeterData.ITEM_11_NEW_BASEITEM_Current_tarrif_installing, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
            addToList("الديون :" + " " + databaseMeterData.ITEM_32_NEW_BASEITEM_Debts, databaseMeterData.ITEM_32_NEW_BASEITEM_Debts == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
            addToList("الرصيد المتبقي بالجنيه :" + " " + databaseMeterData.ITEM_31_NEW_BASEITEM_remaining_credit_mony, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
            addToList("استهلاك الشهر الحالي :" + " " + databaseMeterData.ITEM_44_NEW_BASEITEM_current_month_consumption_KW, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
            addToList("حالة البطارية :" + " " + (databaseMeterData.ITEM_15_NEW_BASEITEM_battery_status == 0 ? "صالحة" : "غير صالحة"), databaseMeterData.ITEM_15_NEW_BASEITEM_battery_status == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
            addToList("حالة الريلاي :" + " " + (databaseMeterData.ITEM_14_NEW_BASEITEM_Relay_status == 0 ? "صالحة" : "غير صالحة"), databaseMeterData.ITEM_14_NEW_BASEITEM_Relay_status == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
            addToList("حالة الغطاء الامامي:" + " " + (databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status == 0 ? "سليم" : "مفتوح"), databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
            addToList("حالة الغطاء الجانبي:" + " " + (databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status == 0 ? "سليم" : "مفتوح"), databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status == 0 ? (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green) : (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        }
        addToList("حالة القراءة : " + databaseMeterData.getReadingSate(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);
        addToList("تاريخ القراءة : " + databaseMeterData.READING_DATE, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.green), null);

        ShowReadedMeterData showReadedMeterData =
                new ShowReadedMeterData(selectedClient.getMntka(), selectedClient.getDay()
                        , selectedClient.getMain(), selectedClient.getFary(),
                        selectedClient.getCustomerID(), selectedClient.getAddress());

        saveStringData(getContext(), Utils.METER_DATA_KEY, showReadedMeterData.toJson());

//        addToList(" منطقه : " + selectedClient.getMntka() + "  " + " يوميه : " + selectedClient.getDay(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
//        addToList(" حساب : " + selectedClient.getMain() + "  " + " فرعى : " + selectedClient.getFary(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
//        addToList(" رقم الإشتراك : " + selectedClient.getCustomerID() + " - ", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
//        addToList(" العنوان : " + selectedClient.getAddress(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);


        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            showMeterImageBtn.setVisibility(View.VISIBLE);
            showMeterImageBtn.setEnabled(true);
        });


//        if (getmBarCodeImagePath() != null && !getmBarCodeImagePath().isEmpty()){
//
//            try {
//                (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
//                    showMeterImageBtn.setImageBitmap(getBitmapFromSharedPreferences(getContext(),"imageBitmapKey"));
//
//
//                    Picasso.get()
//                            .load(new File(getmBarCodeImagePath()))
//                            .placeholder(R.drawable.bg)
//                            .resize(400, 400)
//                            .into(meterImageView);
//                });
//
//            }  catch (Exception e){
//                Log.d("skfdhgisgf", "getView: "+e.getMessage() + "    ttttt" );
//            }
//
//        }
// addToList("", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), getmBarCodeImagePath());

    }

    private void saveToDatabase() {
        setmActivityType(Enums.ActivityType.HOME);
        if (selectedClient.getActivityName().trim().equalsIgnoreCase(Enums.ActivityType.HOME.toString())) {
            setmActivityType(Enums.ActivityType.HOME);
        } else if (selectedClient.getActivityName().trim().equalsIgnoreCase(Enums.ActivityType.SHOP.toString())) {
            setmActivityType(Enums.ActivityType.SHOP);
        } else if (selectedClient.getActivityName().trim().equalsIgnoreCase(Enums.ActivityType.POWER.toString())) {
            setmActivityType(Enums.ActivityType.POWER);
        }
        // Set The Meter Location at the end of notes
        if (meterPositionSpinner.getSelectedItemPosition() == 1) { // داخلى
            if (getmReadingNotes().endsWith(",")){
                setmReadingNotes(getmReadingNotes() + "1");
            }else {
                setmReadingNotes(getmReadingNotes() + ",1");
            }
        } else if (meterPositionSpinner.getSelectedItemPosition() == 2) { //خارجى
            if (getmReadingNotes().endsWith(",")){
                setmReadingNotes(getmReadingNotes() + "2");
            }else {
                setmReadingNotes(getmReadingNotes() + ",2");
            }
        }
        DatabaseMeterData databaseMeterData = new DatabaseMeterData((MainApplication.getInstance().getCurrentActivity()),
                AppUser.getInstance((MainApplication.getInstance().getCurrentActivity())), selectedClient,
                AppLocationManager.getInstance((MainApplication.getInstance().getCurrentActivity())),
                CycleDateManager.getCycleDate((MainApplication.getInstance().getCurrentActivity())), getmBarcode(),
                getRealFactoryCodeFromSpinner() == 0 ? selectedClient.FactoryCode : getRealFactoryCodeFromSpinner(),
                getmReadingCode(), getmLostReadingCode(), getmDamageCode(), getmReadingDataFlag(),
                getmOpticalSerialNumber(), getmReadingResponse(), getmActivityType(), getmReadingNotes(), getmBarCodeImagePath());

        if (selectedClient != null && databaseMeterData.OPTICAL_METER_ID == 0) {
            databaseMeterData.OPTICAL_METER_ID = selectedClient.MeterID;
        }

        ///////////////////////
//        writeStringAsFile(((MainActivity)MainApplication.getInstance().getCurrentActivity()), databaseMeterData.toFileFormate(), "new_meter_data.txt");
        //////////////////////

        if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
            shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();

        DatabaseMeterData databaseMeterData1 = shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().find(databaseMeterData.COVER_METER_ID, databaseMeterData.FACTORY_CODE);
        if (databaseMeterData1 != null && databaseMeterData1.READING_DATA_FLAG == 1 && databaseMeterData.READING_DATA_FLAG != 1) {
            addToList("تم تسجيل حالة العداد من قبل", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
        } else {
            if (databaseMeterData1 != null) {
                boolean res = checkDuplicateSync(databaseMeterData);
                if (!res) {
                    shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().delete(databaseMeterData1);
                    shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().insert(databaseMeterData);

                    saveBitmapToSharedPreferences(getContext(), Utils.METER_IMAGE_KEY, BitmapFactory.decodeFile(new File(getmBarCodeImagePath()).getAbsolutePath()));

                    File image = new File(getmBarCodeImagePath());
                    if (image.exists()) image.delete();
                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.updateClientsState();


                    showReport(databaseMeterData);


                } else {
                    addToList("تم مزامنة هذا العداد من قبل", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                }
            } else {
                DatabaseMeterData databaseMeterData2 = shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().find(databaseMeterData.FACTORY_CODE, databaseMeterData.ITEM_1_NEW_BASEITEM_Meter_ID, databaseMeterData.OPTICAL_Customer_ID, databaseMeterData.ITEM_3_NEW_BASEITEM_CardID, databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status, databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status);
                if (databaseMeterData2 != null && databaseMeterData2.READING_DATA_FLAG == 1) {
                    addToList("تم تسجيل حالة العداد من قبل", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                } else {
                    boolean res = checkDuplicateSync(databaseMeterData);
                    if (!res) {
                        if (databaseMeterData2 != null && databaseMeterData2.COVER_METER_ID == databaseMeterData.COVER_METER_ID) {
                            shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().delete(databaseMeterData2);
                        }
                        shoaa.opticalsmartreader.logic.Utils.appDatabase.metersDataDao().insert(databaseMeterData);

                        saveBitmapToSharedPreferences(getContext(), Utils.METER_IMAGE_KEY, BitmapFactory.decodeFile(new File(getmBarCodeImagePath()).getAbsolutePath()));

                        File image = new File(getmBarCodeImagePath());
                        if (image.exists()) image.delete();
                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.updateClientsState();

                        showReport(databaseMeterData);


                    } else {
                        addToList("تم مزامنة هذا العداد من قبل", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    }
                }
            }
        }
    }

    public ShowReadedMeterData getSavedMeterData(Context context, String key) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE);
        String stringData = sharedPreferences.getString(key, null);
        if (stringData != null) {
            return new Gson().fromJson(stringData, ShowReadedMeterData.class);
        }
        return null;
    }


    public void saveStringData(Context context, String key, String data) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();

        editor.putString(key, data);
        editor.apply();
    }


    public Bitmap getBitmapFromSharedPreferences(Context context, String key) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE);
        String bitmapString = sharedPreferences.getString(key, null);
        if (bitmapString != null) {
            return base64ToBitmap(bitmapString);
        }
        return null;
    }

    public void saveBitmapToSharedPreferences(Context context, String key, Bitmap bitmap) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();

        String bitmapString = bitmapToBase64(bitmap);
        editor.putString(key, bitmapString);
        editor.apply();
    }

    public Bitmap base64ToBitmap(String base64String) {
        byte[] decodedBytes = Base64.decode(base64String, Base64.DEFAULT);
        return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
    }

    public String bitmapToBase64(Bitmap bitmap) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 50, byteArrayOutputStream);
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        return Base64.encodeToString(byteArray, Base64.DEFAULT);
    }

    public static void deleteKey(Context context, String key) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove(key);
        editor.apply();
    }

    public static boolean isKeyExist(Context context, String key) {
        return context.getSharedPreferences(Utils.SHARED_PREFERENCE, Context.MODE_PRIVATE)
                .getAll().containsKey(key);
    }


    private Client findClient(long barcode, int factoryCode) {
        if (!shoaa.opticalsmartreader.logic.Utils.appDatabase.isOpen())
            shoaa.opticalsmartreader.logic.Utils.appDatabase = Room.databaseBuilder((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();

        return shoaa.opticalsmartreader.logic.Utils.appDatabase.clientDao().find(barcode, factoryCode);
    }

    private int getRealFactoryCodeFromSpinner() {
        int index = meterCoMap.get(metersCoStringArray[meterCoSpinner.getSelectedItemPosition()]);
        if (index == 1) return 6;
        else if (index == 2) return 1;
        else if (index == 3) return 2;
        else if (index == 4) return 3;
        else if (index == 5) return 4;
        else if (index == 6)//GPI
            return 7;
        else if (index == 7)//الهيئة العربية
            return 5;
        else return 0;
    }

    @Override
    public void onResume() {
        super.onResume();
        reload();
//        new Thread(() -> {
//            List<Client> clientList = ClientsManager.getLocalClients(((MainActivity)MainApplication.getInstance().getCurrentActivity()));
//            if (clientList.isEmpty()) {
//                ((MainActivity)MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
//                    Toast.makeText(((MainActivity)MainApplication.getInstance().getCurrentActivity()), "يجب المزامنة اولا", Toast.LENGTH_LONG).show();
//                    ((MainActivity)MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.sync);
//                });
//            }
//            enableUI();
//        }).start();
    }

    @Override
    public void refresh() {
        if (MainApplication.getInstance().listAdapter != null) {
            MainApplication.getInstance().listAdapter.clear();
            MainApplication.getInstance().listAdapter.addAll(ConnectionManager.Companion.getInstance().getConnectedDisplayDeviceList());
            if ((MainApplication.getInstance().getCurrentActivity()) != null) {
                (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> MainApplication.getInstance().listAdapter.notifyDataSetChanged());
            }
            boolean removed = true;
            for (int i = 0; i < MainApplication.getInstance().listAdapter.getCount(); i++) {
                if (MainApplication.getInstance().listAdapter.getItem(i) != null && MainApplication.getInstance().listAdapter.getItem(i).getAddress().equalsIgnoreCase(selectedDeviceAddress)) {
                    removed = false;
                }
            }
            if (removed) {
                if ((MainApplication.getInstance().getCurrentActivity()) != null) {
                    (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                        selectedDeviceAddress = "";
                        deviceName.setText("");
                    });
                }
            }
            reload();
        }
    }

    private void reload() {
        String savedBluetooth = shoaa.opticalsmartreader.logic.Utils.sharedPreferences.getString("bluetooth", "");
        boolean useBluetooth = shoaa.opticalsmartreader.logic.Utils.sharedPreferences.getBoolean("useBluetooth", true);
        if (!savedBluetooth.isEmpty() && useBluetooth) {
            for (int i = 0; i < listItems.size(); i++) {
                DisplayDevice device = listItems.get(i);
                if (device.getAddress().equalsIgnoreCase(savedBluetooth)) {
                    if ((MainApplication.getInstance().getCurrentActivity()) != null) {
                        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            selectedDeviceAddress = savedBluetooth;
                            deviceName.setText(device.getName());
                        });
                    }
                    ConnectionManager.Companion.getInstance().setDevice(device);
                }
            }
        } else {
            if (!useBluetooth) {
                for (int i = 0; i < listItems.size(); i++) {
                    DisplayDevice device = listItems.get(i);
                    if (device.getDevice() instanceof UsbDeviceItem) {
                        if ((MainApplication.getInstance().getCurrentActivity()) != null) {
                            (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                                selectedDeviceAddress = device.getAddress();
                                deviceName.setText(extractNumberFromSerial(device.getName()));
                                //deviceName.setText(device.getName());
                            });
                        }
                        ConnectionManager.Companion.getInstance().setDevice(device);
                    }
                }
            }
        }
    }

    public static String extractNumberFromSerial(String input) {
        String prefix = "ShoaaPort_";
        if (input.startsWith(prefix)) {
            String numberPart = input.substring(prefix.length());
            try {
                Integer.parseInt(numberPart);
                return numberPart;
            } catch (NumberFormatException e) {
                return "Error: The part after '_' is not a valid number.";
            }
        } else {
            return "New Optical Port";
        }
    }


    ReadingResponse startReadOptical() {
        ReadingResponse readingResponse = new ReadingResponse();
        while (tryCount <= 3) {
            addToList("جاري التحقق من الاتصال ", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
            boolean connected = ConnectionManager.Companion.getInstance().isConnected();
            int connectionRes = 0;
            if (!connected) {
                connectionRes = ConnectionManager.Companion.getInstance().connectAsync((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), 5000);
            }
            if (connectionRes != 0) {
                editLastItem("غير قادر علي الإتصال" + " " + connectionRes, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red));
                tryCount++;
                if (tryCount <= 3)
                    addToList("جاري اعادة المحاولة يرجي الإنتظار", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        return readingResponse;
                    }
                }
                editLastItem(("تم الإتصال بــ  " + ConnectionManager.Companion.getInstance().getConnectedDeviceName()), MainApplication.getInstance().getCurrentActivity().getColor(R.color.green));
                switch (meterCoMap.get(metersCoStringArray[meterCoSpinner.getSelectedItemPosition()])) {
                    case 1: // global
                        readingResponse = MeterReader.read(MainApplication.getInstance().getCurrentActivity().getApplicationContext(), MeterCo.GLOBAL, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> MainApplication.getInstance().getCurrentActivity().runOnUiThread(() -> {
                            if (progress > 100) Log.d("TAG", "startReadOptical: ");
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", MainApplication.getInstance().getCurrentActivity().getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", MainApplication.getInstance().getCurrentActivity().getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                    case 2: // esmc
                        readingResponse = MeterReader.read(MainApplication.getInstance().getCurrentActivity().getApplicationContext(), MeterCo.ESMC, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                    case 3: //iskra
                        readingResponse = MeterReader.read((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), MeterCo.ISKRA, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                    case 4: //electrometer
                        readingResponse = MeterReader.read((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), MeterCo.ELECTROMETER, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                    case 5: //maasara
                        readingResponse = MeterReader.read((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), MeterCo.MAASARA, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                    case 6: //GPI
                        readingResponse = MeterReader.read((MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), MeterCo.GPI, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                            }
                        }));
                        break;

                    case 7: //hay2a
                        readingResponse = MeterReader.read(MainApplication.getInstance().getCurrentActivity().getApplicationContext(), MeterCo.Hay2a, getGlobalVersion(meterCoSpinner, globalVersionSpinner), progress -> MainApplication.getInstance().getCurrentActivity().runOnUiThread(() -> {
                            if (!dataArrayList.isEmpty() && dataArrayList.get(dataArrayList.size() - 1).contains("جاري قراءة البيانات")) {
                                editLastItem("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", MainApplication.getInstance().getCurrentActivity().getColor(R.color.yellow));
                            } else {
                                addToList("جاري قراءة البيانات" + " " + (progress > 100 ? 99 : progress) + "%", MainApplication.getInstance().getCurrentActivity().getColor(R.color.yellow), null);
                            }
                        }));
                        break;
                }


                if (readingResponse != null && readingResponse.readingResult != ReadingResult.SUCCESS) {
                    if (readingResponse.readingResult == ReadingResult.CAN_NOT_CONNECT) {
                        addToList("غير قادر علي الاتصال", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    } else if (readingResponse.readingResult == ReadingResult.CAN_NOT_GET_DATA) {
                        addToList("غير قادر علي القراءة", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    } else if (readingResponse.readingResult == ReadingResult.CAN_NOT_PARSE_DATA) {
                        addToList("غير قادر علي تحليل البيانات", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    } else if (readingResponse.readingResult == ReadingResult.CAN_NOT_SET_BaudRate) {
                        addToList("خطأ سرعه الإتصال", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    } else if (readingResponse.readingResult == ReadingResult.JSON_ERROR) {
                        addToList("خطأ json", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    } else {
                        addToList("خطأ غير معروف", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                        addToList(readingResponse.message, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.red), null);
                    }
                    if (readingResponse.readingResult != ReadingResult.CAN_NOT_CONNECT)
                        return readingResponse;
                    else {
                        tryCount++;
                        if (tryCount <= 3)
                            addToList("جاري اعادة المحاولة يرجي النتظار", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.yellow), null);
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (tryCount > 3) return readingResponse;


                    }
                } else {
                    if (readingResponse != null) {
                        clearData();
                        return readingResponse;
                    } else {
                        ReadingResponse rs = new ReadingResponse();
                        rs.readingResult = ReadingResult.OTHER;
                        rs.message = "null object";
                        return rs;
                    }
                }
            }
        }
        return readingResponse;
    }

    @Nullable
    private static GlobalVersion getGlobalVersion(Spinner spinner_meterCo, Spinner spinner_GlobalVersions) {
        if (!Common.ENABLE_GLOBAL_VERSION_SELECTION) {
            return null;
        }
        GlobalVersion globalVersion = null;
        if (spinner_meterCo.getSelectedItemPosition() == 1) {
            if (spinner_GlobalVersions.getSelectedItemPosition() == 0) {
                globalVersion = GlobalVersion.VERSION_2019;
            } else if (spinner_GlobalVersions.getSelectedItemPosition() == 1) {
                globalVersion = GlobalVersion.VERSION_2013;
            } else {
                globalVersion = GlobalVersion.VERSION_2008;
            }
        }
        return globalVersion;
    }

    void readBarcodeAsync() {
        AtomicBoolean done = new AtomicBoolean(false);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).barcodeScanner.setScanCallback(barcodeResult -> {
            if (barcodeResult.getBarCode().isEmpty() && !barcodeResult.getImagePath().isEmpty()) {
                BarcodeDialog barcodeDialog = new BarcodeDialog(((MainActivity) MainApplication.getInstance().getCurrentActivity()), barcode -> {
                    if (barcode.isEmpty()) {
                        enableUI();
                        setmBarCodeImagePath("");
                    } else {
                        setmBarcode(Long.parseLong(barcode));
                        setmBarCodeImagePath(barcodeResult.getImagePath());

                    }
                    done.set(true);
                });
                barcodeDialog.setCancelable(false);
                barcodeDialog.show();
            } else if (!barcodeResult.getBarCode().isEmpty() && !barcodeResult.getImagePath().isEmpty()) {
                try {
                    setmBarcode(Long.parseLong(barcodeResult.getBarCode()));
                    setmBarCodeImagePath(barcodeResult.getImagePath());
                } catch (Exception ignore) {
                    setmBarcode(0);
                    setmBarCodeImagePath("");
                    enableUI();
                }
                done.set(true);
            } else {
                setmBarcode(0);
                setmBarCodeImagePath("");
                enableUI();
                done.set(true);
            }
        });
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).barcodeScanner.start(true, false);
        while (!done.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }


    void takeImageAsync() {
        AtomicBoolean done = new AtomicBoolean(false);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).barcodeScanner.setScanCallback(barcodeResult -> {
            if (!barcodeResult.getImagePath().isEmpty()) {
                setmBarCodeImagePath(barcodeResult.getImagePath());
            } else {
                setmBarCodeImagePath("");
            }
            enableUI();
            done.set(true);
        });
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).barcodeScanner.start(false, false);
        while (!done.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean isCustomerId(String text, boolean hasHex) {
        if (hasHex) {
            return text != null && text.length() > 2 && text.length() <= 32;
        } else {
            return text != null && (text.matches("[0-9]+") && text.length() > 2 && text.length() <= 32);
        }
    }

    private void enableUI() {
        if (!reading) {
            (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                btnRead.setEnabled(true);
                showMeterImageBtn.setEnabled(true);
                pickBluetooth.setEnabled(true);
                readingStateSpinner.setEnabled(true);
                meterCoSpinner.setEnabled(true);
                meterPositionSpinner.setEnabled(true);
                upTypeSpinner.setEnabled(true);
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).enableBottomNav = true;
            });
        }
    }

    boolean showStartReadMessageAsync() {
        AtomicBoolean done = new AtomicBoolean(false);
        AtomicBoolean res = new AtomicBoolean(false);
        builder = new AlertDialog.Builder((MainApplication.getInstance().getCurrentActivity()));
        if (selectedClient == null) {
            TextView textView = new TextView((MainApplication.getInstance().getCurrentActivity()));
            textView.setText("ساقط ليست  " + getmBarcode());
            textView.setTextSize(20);
            textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
            textView.setForegroundGravity(Gravity.RIGHT);
            textView.setTextColor(0xffff0000);
            textView.setPadding(40, 30, 40, 30);
            builder.setCustomTitle(textView);
        }
        builder.setMessage("هل تريد بدأ القراءة ؟");
        builder.setCancelable(false);
        builder.setPositiveButton("بدأ القراءة", (dialogInterface, i) -> {
            done.set(true);
            res.set(true);
        });

        builder.setNegativeButton("الغاء", (dialogInterface, i) -> {
            if (alertDialog != null) alertDialog.dismiss();
            btnRead.setEnabled(true);
            showMeterImageBtn.setEnabled(true);
            pickBluetooth.setEnabled(true);
            readingStateSpinner.setEnabled(true);
            meterCoSpinner.setEnabled(true);
            meterPositionSpinner.setEnabled(true);
            upTypeSpinner.setEnabled(true);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).enableBottomNav = true;
            done.set(true);
            res.set(false);
        });
        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> alertDialog = builder.show());
        while (!done.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException ignore) {
            }
        }
        return res.get();
    }

    private void showNoteMessageAsync(boolean askForClientId) {
        AtomicBoolean done = new AtomicBoolean(false);

        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            NoteDialog noteDialog = new NoteDialog(((MainActivity) MainApplication.getInstance().getCurrentActivity()), askForClientId, text -> {
                if (askForClientId) {
                    String note = text.split("\t")[0].trim();
                    String customerId = text.split("\t")[1].trim();
                    setmReadingNotes(note);
                    selectedClient.setCustomerID(customerId);
                } else {
                    setmReadingNotes(text);
                }
                done.set(true);
            });
            noteDialog.setCancelable(false);
            noteDialog.show();
        });
        while (!done.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException ignore) {
            }
        }
    }

    private void showLostMessageAsync(boolean showClientId, boolean showMeterType) {
        AtomicBoolean done = new AtomicBoolean(false);
        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() ->
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).openLostActivityForResult(lostActivityResultObject -> {
                    if (lostActivityResultObject != null) {
                        setmActivityType(lostActivityResultObject.activityType);
                        setmReadingNotes(lostActivityResultObject.note);
                        selectedClient = lostActivityResultObject.client;
                        done.set(true);
                    }
                }, showClientId, showMeterType));
        while (!done.get()) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException ignore) {
            }
        }
    }

    public void onClientSelected(Client client, int index) {
        selectedClient = client;
        selectedIndex = index;
        clearData();
        addToList("مسلسل : " + index, (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ", (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("الحساب : " + client.getReference(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("عداد : " + (Enums.FactoryCodes.values()[client.getFactoryCode() - 1].toString()) + " - " + client.getMeterTypeName(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("رقم العداد : " + client.getMeterID(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("تاريخ الاصدار : " + client.getCycleMonth() + " / " + client.getCycleYear(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("اسم المشترك : " + client.getName(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("النشاط : " + client.getActivityName(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        addToList("وصف المكان : " + client.getPlaceName(), (MainApplication.getInstance().getCurrentActivity()).getColor(R.color.white), null);
        (MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
            readingStateSpinner.setSelection(0);
            meterCoSpinner.setSelection(0);
            upTypeSpinner.setSelection(0);
            meterPositionSpinner.setSelection(0);
            showReadedMeterData = null;
            deleteKey(getContext(), Utils.METER_DATA_KEY);
            showMeterImageBtn.setVisibility(View.GONE);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).bottomNavigationView.setSelectedItemId(R.id.read);
        });
    }

    public void newRead() {
        mBarcode = 0;
        mReadingCode = 0;
        mLostReadingCode = 0;
        mDamageCode = 0;
        mReadingDataFlag = 0;
        mOpticalSerialNumber = 0;
        mReadingResponse = new ReadingResponse();
        mActivityType = null;
        mReadingNotes = "";
        mBarCodeImagePath = "";
    }

    public long getmBarcode() {
        return mBarcode;
    }

    public void setmBarcode(long mBarcode) {
        this.mBarcode = mBarcode;
    }

    public int getmReadingCode() {
        return mReadingCode;
    }

    public void setmReadingCode(int mReadingCode) {
        this.mReadingCode = mReadingCode;
    }

    public int getmLostReadingCode() {
        return mLostReadingCode;
    }

    public void setmLostReadingCode(int mLostReadingCode) {
        this.mLostReadingCode = mLostReadingCode;
    }

    public int getmDamageCode() {
        return mDamageCode;
    }

    public void setmDamageCode(int mDamageCode) {
        this.mDamageCode = mDamageCode;
    }

    public int getmReadingDataFlag() {
        return mReadingDataFlag;
    }

    public void setmReadingDataFlag(int mReadingDataFlag) {
        this.mReadingDataFlag = mReadingDataFlag;
    }

    public int getmOpticalSerialNumber() {
        return mOpticalSerialNumber;
    }

    public void setmOpticalSerialNumber(int mOpticalSerialNumber) {
        this.mOpticalSerialNumber = mOpticalSerialNumber;
    }

    public ReadingResponse getmReadingResponse() {
        return mReadingResponse;
    }

    public void setmReadingResponse(ReadingResponse mReadingResponse) {
        this.mReadingResponse = mReadingResponse;
    }

    public Enums.ActivityType getmActivityType() {
        return mActivityType;
    }

    public void setmActivityType(Enums.ActivityType mActivityType) {
        this.mActivityType = mActivityType;
    }

    public String getmReadingNotes() {
        return mReadingNotes;
    }

    public void setmReadingNotes(String mReadingNotes) {
        this.mReadingNotes = mReadingNotes;
    }

    public String getmBarCodeImagePath() {
        return mBarCodeImagePath;
    }

    /////////////////////////////////////////

    public void setmBarCodeImagePath(String mBarCodeImagePath) {
        this.mBarCodeImagePath = mBarCodeImagePath;
    }

    public boolean checkDuplicateSync(DatabaseMeterData databaseMeterData) {
        if (databaseMeterData.READING_DATA_FLAG != 1) return false;
        try {

            Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new GsonDateSerialization()).create();
//            boolean ignore = false;
//            try {
//                ignore = databaseMeterData.FACTORY_CODE == 6 && Integer.parseInt(databaseMeterData.METER_MODEL) < 2016;
//            } catch (Exception ignored) {
//            }
//            if (ignore) {
//                return true;
//            }
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList = new ArrayList<>();
            File syncDataFile = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), ".ShoaaData/data.db");
            String syncData = Utils.readFile(syncDataFile);
            CycleDate cycleDate = CycleDateManager.getCycleDate((MainApplication.getInstance().getCurrentActivity()));
            if (!syncData.isEmpty()) {
                Type type = new TypeToken<List<MeterCheck>>() {
                }.getType();
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList = gson.fromJson(syncData, type);
                Date dateNow = Calendar.getInstance().getTime();
                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.removeIf(meterCheck -> {
                    try {
                        Calendar c = Calendar.getInstance();
                        c.setTime(meterCheck.syncDate);
                        c.add(Calendar.DATE, 30);
                        return dateNow.after(c.getTime()) || (cycleDate != null && (meterCheck.cycleDate.CycleYear != cycleDate.CycleYear || meterCheck.cycleDate.CycleMonth != cycleDate.CycleMonth));
                    } catch (Exception e) {
                        Exception ee = new Exception("local cycleDate : " + cycleDate == null ? "null" : cycleDate.toString() + " , meterCheck.cycleDate : " + meterCheck.cycleDate == null ? "null" : meterCheck.cycleDate.toString());
                        FirebaseCrashlytics.getInstance().recordException(ee);
                        FirebaseCrashlytics.getInstance().recordException(e);
                        AlertDialog.Builder builder = new AlertDialog.Builder((MainApplication.getInstance().getCurrentActivity()));
                        builder.setMessage(ee.getMessage());
                        builder.setTitle("خطأ ");
                        builder.setCancelable(false);
                        builder.setNegativeButton("اغلاق", (dialog, which) -> {
                            throw e;
                        });
                        AlertDialog alert = builder.create();
                        alert.show();
                        return false;
                    }
                });
            }
            if (syncDataFile.exists()) {
                syncDataFile.delete();
            }
            String newSyncData = gson.toJson(((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList);
            Utils.writeStringAsFile(Objects.requireNonNull(syncDataFile.getParentFile()), newSyncData, syncDataFile.getName());
        } catch (IOException e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(e);
        }
        if (databaseMeterData.READING_DATA_FLAG == 1) {
            try {
                if (((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList != null && !((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.isEmpty()) {
                    for (int i = 0; i < ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.size(); i++) {
                        MeterCheck meterCheck = ((MainActivity) MainApplication.getInstance().getCurrentActivity()).meterCheckArrayList.get(i);
                        if (databaseMeterData.CIRCLE_YEAR == meterCheck.cycleDate.CycleYear && databaseMeterData.CIRCLE_MONTH == meterCheck.cycleDate.CycleMonth && databaseMeterData.FACTORY_CODE == meterCheck.factoryCode && databaseMeterData.COVER_METER_ID != meterCheck.coverMeterId && databaseMeterData.ORIGINAL_OPTICAL_METER_ID == meterCheck.originalOpticalMeterId && databaseMeterData.OPTICAL_Customer_ID.equalsIgnoreCase(meterCheck.opticalCustomerId) && databaseMeterData.ITEM_3_NEW_BASEITEM_CardID.equalsIgnoreCase(meterCheck.cardID) && databaseMeterData.ITEM_17_NEW_BASEITEM_Side_cover_status == meterCheck.sideCover && databaseMeterData.ITEM_16_NEW_BASEITEM_Top_cover_status == meterCheck.topCover) {
                            AppUser appUser = AppUser.getInstance((MainApplication.getInstance().getCurrentActivity()));
                            FirebaseCrashlytics.getInstance().recordException(new Exception("user id = " + appUser.getUSER_ID() + " , handas : " + appUser.getBrHndsa() + ", \n\n " + meterCheck.toString() + ", \n\n " + databaseMeterData.toFileFormate()));
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                e.getStackTrace();
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
        return false;
    }
}