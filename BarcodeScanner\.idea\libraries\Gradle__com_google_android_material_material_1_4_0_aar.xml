<component name="libraryTable">
  <library name="Gradle: com.google.android.material:material:1.4.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/7d489f9451f087276f695add34554abb/transformed/material-1.4.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7d489f9451f087276f695add34554abb/transformed/material-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/7d489f9451f087276f695add34554abb/transformed/material-1.4.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/7d489f9451f087276f695add34554abb/transformed/material-1.4.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.material/material/1.4.0/51035a8f6342c20d4d8f242d100e6f25e0c271ac/material-1.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>