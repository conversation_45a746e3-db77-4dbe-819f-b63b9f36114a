package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/HdlcResponseInfo.class */
public class HdlcResponseInfo {
    private int hdlcFrameType;
    private String info;
    private String next;
    private boolean result;

    public HdlcResponseInfo() {
    }

    public HdlcResponseInfo(boolean result) {
        this.result = result;
    }

    public HdlcResponseInfo(int frameType, String info, String next, boolean result) {
        this.hdlcFrameType = frameType;
        this.info = info;
        this.next = next;
        this.result = result;
    }

    public int getHdlcFrameType() {
        return this.hdlcFrameType;
    }

    public void setHdlcFrameType(int hdlcFrameType) {
        this.hdlcFrameType = hdlcFrameType;
    }

    public String getInfo() {
        return this.info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getNext() {
        return this.next;
    }

    public void setNext(String next) {
        this.next = next;
    }

    public boolean getResult() {
        return this.result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }
}
