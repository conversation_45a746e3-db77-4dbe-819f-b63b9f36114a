package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/Conformance.class */
public enum Conformance {
    ATTRIBUTE0_SET_SUPPORTED(8),
    PRIORITY_SUPPORTED(9),
    ATTRIBUTE0_GET_SUPPORTED(10),
    BL<PERSON>K_TRANSFER_GET(11),
    BLOCK_TRANSFER_SET(12),
    BLOCK_TRANSFER_ACTION(13),
    MULTIPLE_REFERENCES(14),
    GET(19),
    SET(20),
    SELECTIVE_ACCESSS(21),
    EVENT_NOTIFICATION(22),
    ACTION(23),
    NONE(-1);

    private int bitPosition;

    Conformance(int bitPosition) {
        this.bitPosition = bitPosition;
    }

    public static Conformance create(int bitPosition) {
        Conformance[] values;
        for (Conformance c : values()) {
            if (bitPosition == c.getBitPosition()) {
                return c;
            }
        }
        return NONE;
    }

    public int getBitPosition() {
        return this.bitPosition;
    }
}
