package shoaa.opticalsmartreader.logic;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;

public class BitmapHelper {
    public Bitmap drawTextToBitmap(Context mContext, Bitmap bitmap, String mText1, String mText2, String mText3, String mText4, String mText5, String mText6) {
        try {
            Resources resources = mContext.getResources();
            float scale = resources.getDisplayMetrics().density;
            Canvas canvas = new Canvas(bitmap);
            // new antialised Paint
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            Paint paint5 = new Paint(Paint.ANTI_ALIAS_FLAG);
            // text color - #3D3D3D
            paint.setColor(Color.rgb(255, 0, 0));
            paint5.setColor(Color.rgb(255, 0, 0));
            // text size in pixels
            paint.setTextSize((int) (7.4 * scale));
            paint5.setTextSize((int) (7.4 * scale));
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(2);
            paint.setColor(Color.RED);
            paint5.setStyle(Paint.Style.STROKE);
            paint5.setStrokeWidth(2);
            paint5.setColor(Color.RED);

            // draw text to the Canvas center
            Rect bounds1 = new Rect();
            paint.getTextBounds(mText1, 0, mText1.length(), bounds1);
            Rect bounds2 = new Rect();
            paint.getTextBounds(mText2, 0, mText2.length(), bounds2);
            Rect bounds3 = new Rect();
            paint.getTextBounds(mText3, 0, mText3.length(), bounds3);
            Rect bounds4 = new Rect();
            paint.getTextBounds(mText4, 0, mText4.length(), bounds4);
            Rect bounds5 = new Rect();
            paint5.getTextBounds(mText5, 0, mText5.length(), bounds5);
            Rect bounds6 = new Rect();
            paint.getTextBounds(mText6, 0, mText6.length(), bounds6);
            int x1 = bitmap.getWidth() - bounds1.width() - 2;
            int x2 = bitmap.getWidth() - bounds2.width() - 2;
            int x3 = bitmap.getWidth() - bounds3.width() - 2;
            int x4 = bitmap.getWidth() - bounds4.width() - 2;
            int x5 = bitmap.getWidth() - bounds5.width() - 2;
            int x6 = bitmap.getWidth() - bounds6.width() - 2;

            int y6 = bitmap.getHeight() - bounds6.height() - 10;
            int y5 = y6 - bounds5.height() - 10;
            int y4 = y5 - bounds4.height() - 10;
            int y3 = y4 - bounds3.height() - 10;
            int y2 = y3 - bounds2.height() - 10;
            int y1 = y2 - bounds1.height() - 10;

            canvas.drawText(mText1, x1, y1, paint);
            canvas.drawText(mText2, x2, y2, paint);
            canvas.drawText(mText3, x3, y3, paint);
            canvas.drawText(mText4, x4, y4, paint);
            canvas.drawText(mText5, x5, y5, paint5);
            canvas.drawText(mText6, x6, y6, paint);

            return bitmap;
        } catch (Exception e) {
            return null;
        }

    }
}
