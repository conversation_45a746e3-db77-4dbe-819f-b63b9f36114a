package com.sewedy.electrometerparser.protocol.connection.usbserial;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDeviceConnection;
import android.os.Handler;

import com.sewedy.electrometerparser.protocol.connection.usbserial.driver.UsbSerialPort;
import com.sewedy.electrometerparser.protocol.connection.usbserial.util.SerialInputOutputManager;

import org.apache.commons.lang3.ArrayUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class UsbSerialConnector implements SerialInputOutputManager.Listener {
    public static final String INTENT_ACTION_DISCONNECT = "Disconnect";
    private static UsbSerialConnector usbSerialConnector;
    private final BroadcastReceiver disconnectBroadcastReceiver;
    public int data_len = 0;
    public ArrayList<Byte> expectedData;
    public SerialReadListener readListener;
    public Runnable runnable;
    public Handler handler;
    protected SerialListener listener;
    protected UsbDeviceConnection connection;
    protected Context context;
    UsbSerialPort serialPort;
    boolean isAppendingData = false;
    private boolean isConnected;
    private SerialInputOutputManager ioManager;

    private UsbSerialConnector() {
        expectedData = new ArrayList<>();
        handler = new Handler();

        disconnectBroadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (listener != null)
                    listener.onSerialIoError(new IOException("background disconnect"));
                disconnect(); // disconnect now, else would be queued until UI re-attached
            }
        };
    }

    public static UsbSerialConnector getInstance() {
        if (usbSerialConnector == null) {
            usbSerialConnector = new UsbSerialConnector();
        }
        return usbSerialConnector;
    }

    @Override
    public void onNewData(byte[] data) {
        expectedData.addAll(Arrays.asList(ArrayUtils.toObject(data)));
//        Log.i("readListener1", "" + expectedData.size());
//        Log.i("readListener1Data", Utils.bytesToHex(Utils.convertBuffArrayListToArr(expectedData)));
        if (expectedData.size() >= data_len) {
            isAppendingData = false;
//            Log.i("readListener2", "" + expectedData.size());
//            Log.i("readListener2Data", Utils.bytesToHex(Utils.convertBuffArrayListToArr(expectedData)));
            if (readListener != null) {

                try {
                    readListener.onSerialRead(ArrayUtils.toPrimitive(expectedData.toArray(new Byte[expectedData.size()])));
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    @Override
    public void onRunError(Exception e) {
        if (listener != null)
            listener.onSerialIoError(e);
    }

    public void runReadListener(int duration) {
        data_len = 10000;
        handler.removeCallbacksAndMessages(null);
        handler.postDelayed(() -> {
            if (readListener != null) {
                try {
                    readListener.onSerialRead(ArrayUtils.toPrimitive(expectedData.toArray(new Byte[0])));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }, duration);
    }

    public void disconnect() {
        isConnected = false;
        listener = null; // ignore remaining data and errors
        if (ioManager != null) {
            ioManager.setListener(null);
            ioManager.stop();
            ioManager = null;
        }
        if (serialPort != null) {
            try {
                serialPort.setDTR(false);
                serialPort.setRTS(false);
            } catch (Exception ignored) {
            }
            try {
                serialPort.close();
            } catch (Exception ignored) {
            }
            serialPort = null;
        }
        if (connection != null) {
            connection.close();
            connection = null;
        }
        try {
            context.unregisterReceiver(disconnectBroadcastReceiver);
        } catch (Exception ignored) {
        }
    }

    public void connect(Context context, SerialListener serialListener, UsbDeviceConnection connection, UsbSerialPort serialPort) throws IOException {
        int baudRate = 9600;
        if (this.serialPort != null)
            return;
        //throw new IOException("already connected");
        this.context = context;
        this.listener = serialListener;
        this.connection = connection;
        this.serialPort = serialPort;
        context.registerReceiver(disconnectBroadcastReceiver, new IntentFilter(INTENT_ACTION_DISCONNECT));
        serialPort.open(connection);
        serialPort.setParameters(baudRate, UsbSerialPort.DATABITS_8, UsbSerialPort.STOPBITS_1, UsbSerialPort.PARITY_EVEN);
        ioManager = new SerialInputOutputManager(serialPort, this);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(ioManager);
    }

    public void setParameters(int baudRate, int dataBits, int stopBits, int parity) throws IOException {
        serialPort.setParameters(baudRate, dataBits, stopBits, parity);
        serialPort.setRTS(true);
        serialPort.setDTR(false);
    }

    public void write(byte[] data) throws IOException {
        if (serialPort == null)
            throw new IOException("not connected");
        serialPort.write(data, 4000);
    }
}
