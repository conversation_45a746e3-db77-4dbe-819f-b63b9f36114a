package shoaa.hay2areader;

import static shoaa.common.Utils.writeStringAsFile;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.ParcelUuid;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import org.aoi.jdlms.CommunicationPoint;
import org.aoi.jdlms.KaiFaMeter;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.UUID;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;


public class Hay2aReader {

    //    private static Response response = null;
//    private static Request request = null;
    private static String requestString = null;
    private static String responseString = null;
    private static String sendApdu = null;
    private static String res = null;
    private static int sendErrorCount = 0;
    private static String session = "";

    public static ReadingResponse read(Context context, ProgressCallback progressCallback) {
        try {

            Hay2aResponse readingResponse = new Hay2aResponse();
            session = "";
            progressCallback.update(1);
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                Log.d("repppppp", "0000: " + ConnectionManager.Companion.getInstance().isConnected() + "\n");
                if (ConnectionManager.Companion.getInstance().connectAsync(context, 10000) != 0) {
                    readingResponse = new Hay2aResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
            }

            if (ConnectionManager.Companion.getInstance().isConnected()) {
                Log.d("repppppp", ": " + ConnectionManager.Companion.getInstance().isConnected() + "\n");

                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {

                    BluetoothManager bluetoothManager = new BluetoothManager(context);
                    // UUID DEVICE_UUID = UUID.fromString("00001101-0000-1000-8000-00805f9b34fb"); // Replace if needed

                    ParcelUuid[] uuids = ConnectionManager.Companion.getInstance().getBluetoothDevice().getUuids();

                    UUID DEVICE_UUID = UUID.fromString(uuids[1].getUuid().toString());

                    Log.d("MainActivity", "Bluetooth connected successfully!");

                    CommunicationPoint point = new CommunicationPoint() {

                        @Override
                        public OutputStream getOutputStream() throws IOException {
                            return bluetoothManager.getOutPutStream();
                        }

                        @Override
                        public InputStream getInputStream() throws IOException {
                            return bluetoothManager.getInputStream();
                        }

                        @Override
                        public void connect() throws IOException {
                            close();
                            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                                return;
                            }
                            bluetoothManager.connectWithRetry(ConnectionManager.Companion.getInstance().getBluetoothDevice().getAddress(), DEVICE_UUID, 3, 0);

                        }

                        @Override
                        public void close() throws IOException {
                            if (bluetoothManager.getBluetoothSocket() != null) {
                                bluetoothManager.getBluetoothSocket().close();
                            }
                        }
                    };

                    try {
                        // Implemente the ProgressCallback
                        org.aoi.jdlms.ProgressCallback progressPercentage = (readingItemCount, readingItemIndex, progress) -> {

                            progressCallback.update(Integer.parseInt(progress.replace("%", "")));
                            Log.d("flngjkdngjk", "read: " + progress);
                        };

                        String json = KaiFaMeter.read(point, progressPercentage);

                        readingResponse.message = json;
                        readingResponse.readingResult = ReadingResult.SUCCESS;
                        Log.d("BluetoothUUID222222", "json: " + json);
                        session = json;
                        writeStringAsFile(context, session, "session.txt");
                    } catch (IOException e) {
                        Log.e("BluetoothUUID222222", "Error reading data: " + e.getMessage());
                    }
                }
            }
            return readingResponse;
        } catch (Exception e) {
            writeStringAsFile(context, session, "session.txt");
            Hay2aResponse readingResponse = new Hay2aResponse();
            readingResponse.readingResult = ReadingResult.OTHER;
            readingResponse.message = e.getMessage();
            return readingResponse;
        }

    }


}
//
//    private static void release() {
//        request = new Request(Constant.RELEASE_SERVICE, "", null);
//        requestString = new Gson().toJson(request);
//        responseString = maasraHelper.handle(requestString);
//        response = new Gson().fromJson(responseString, Response.class);
//        sendApdu = response.getPdu();
//        ConnectionManager.Companion.getInstance().sendAsync(sendApdu, 0, Common.TIME_OUT_1000, Common.TIME_OUT_500);
//        maasraHelper = new MaasraHelper();
//        response = new Response();
//        responseString = "";
//    }
//
//    private static String getItem(String itemCode) {
//        try {
//            if (itemCode.equalsIgnoreCase("0"))
//                return null;
//            request = new Request(Constant.GET_SERVICE, null, itemCode);
//            requestString = new Gson().toJson(request);
//            responseString = maasraHelper.handle(requestString);
//            response = new Gson().fromJson(responseString, Response.class);
//            sendApdu = response.getPdu();
//            session += "OBIS : " + itemCode + "\n";
//            res = ConnectionManager.Companion.getInstance().sendAsync(sendApdu, 1, Common.TIME_OUT_1000, Common.TIME_OUT_300);
//            session += "sendApdu : " + sendApdu + "\n";
//            session += "res : " + res + "\n";
//            if (res.isEmpty()) {
//                sendErrorCount++;
//                return null;
//            }
//            request = new Request(Constant.GET_SERVICE, res, itemCode);
//            requestString = new Gson().toJson(request);
//            responseString = maasraHelper.handle(requestString);
//            response = new Gson().fromJson(responseString, Response.class);
//            if (response == null || response.getValue() == null || response.getValue().isEmpty())
//                return null;
//            if (response.getResult().equalsIgnoreCase("success")) {
//                String value = response.getValue().replaceAll(";", "_").replaceAll("\t", "_");
//                session += "value : " + value + "\n\n";
//                response = null;
//                request = null;
//                requestString = null;
//                responseString = null;
//                sendApdu = null;
//                res = null;
//                return value;
//            } else {
//                session += "value : error" + "\n\n";
//                return null;
//            }
//        } catch (Exception e) {
//            return null;
//        }
//    }
//}
