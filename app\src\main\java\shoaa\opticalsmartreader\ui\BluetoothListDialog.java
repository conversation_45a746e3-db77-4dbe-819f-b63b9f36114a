package shoaa.opticalsmartreader.ui;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListView;

import java.util.Map;

import shoaa.connectionmanager.DisplayDevice;
import shoaa.opticalsmartreader.R;

public class BluetoothListDialog extends Dialog {

    ListView listView;
    ArrayAdapter<DisplayDevice> listAdapter;
    AdapterView.OnItemClickListener onItemClickListener;

    public BluetoothListDialog(Activity a, ArrayAdapter<DisplayDevice> listAdapter, AdapterView.OnItemClickListener onItemClickListener) {
        super(a);
        this.listAdapter = listAdapter;
        this.onItemClickListener = onItemClickListener;
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.bluetooth_list_dialog);
        listView = findViewById(R.id.lvList);
        listView.setAdapter(listAdapter);
        listView.setOnItemClickListener(this.onItemClickListener);
        listView.setBackgroundColor(0xff0000);
    }
}