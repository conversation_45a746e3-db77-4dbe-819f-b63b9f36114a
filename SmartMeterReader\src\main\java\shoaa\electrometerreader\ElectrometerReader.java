package shoaa.electrometerreader;

import android.content.Context;
import android.util.Log;

import com.sewedy.electrometerparser.protocol.MeterData;
import com.sewedy.electrometerparser.protocol.Shared;
import com.sewedy.electrometerparser.protocol.ShoaaMeterDataRetriever;

import shoaa.common.ProgressCallback;
import shoaa.connectionmanager.ConnectionManager;
import shoaa.smartmeterreader.ReadingResponse;
import shoaa.smartmeterreader.ReadingResult;

/**
 * Created by Islam Darwish
 */

public class ElectrometerReader {
    static MeterData meterData = null;
    static ShoaaMeterDataRetriever meterDataRetriever;

    public static ReadingResponse read(Context context, ProgressCallback progressCallback) {
        try {
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                    ElectrometerResponse readingResponse = new ElectrometerResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
            }
            if (!ConnectionManager.Companion.getInstance().isConnected()) {
                ElectrometerResponse readingResponse = new ElectrometerResponse();
                readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                readingResponse.message = "";
                return readingResponse;
            }
            if (progressCallback != null) {
                progressCallback.update(5);
            }

            int totalTryCount = 0;
            int tryCount = 0;
            int maxTryCount = 3;
            String lastError = "";

            while (totalTryCount < 5 && tryCount < maxTryCount) {
                if (!ConnectionManager.Companion.getInstance().isConnected()) {
                    if (ConnectionManager.Companion.getInstance().connectAsync(context, 5000) != 0) {
                        ElectrometerResponse readingResponse = new ElectrometerResponse();
                        readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                        readingResponse.message = "";
                        return readingResponse;
                    }
                }
                if (!ConnectionManager.Companion.getInstance().isConnected()) {
                    ElectrometerResponse readingResponse = new ElectrometerResponse();
                    readingResponse.readingResult = ReadingResult.CAN_NOT_CONNECT;
                    readingResponse.message = "";
                    return readingResponse;
                }
                totalTryCount++;
                meterData = null;
                meterDataRetriever = new ShoaaMeterDataRetriever(progressCallback);
                Thread dataRetrieverThread = new Thread(() -> {
                    try {
                        meterDataRetriever.getAllData(meterData1 -> meterData = meterData1);
                    } catch (Exception e) {
                        meterData = MeterData.getInstance();
                        meterData.setResult("False");
                        meterData.setMessage("Connection Error");
                    }
                });
                dataRetrieverThread.start();
                while (meterData == null) {
                    try {
                        Thread.sleep(100);
                    } catch (Exception ignored) {
                    }
                }
                dataRetrieverThread.interrupt();
                if (Boolean.parseBoolean(meterData.getResult())) {
                    break;
                } else {
                    if (lastError.equalsIgnoreCase(meterData.getMessage())) {
                        tryCount++;
                    } else {
                        tryCount = 1;
                    }
                    if (tryCount < maxTryCount) {
                        ConnectionManager.Companion.getInstance().reset();
                        try {
                            Thread.sleep(3000);
                        } catch (Exception ignored) {
                        }
                    }
                    lastError = meterData.getMessage();
                }
            }


            ElectrometerResponse readingResponse = new ElectrometerResponse();
            readingResponse.readingResult = Boolean.parseBoolean(meterData.getResult()) ? ReadingResult.SUCCESS : ReadingResult.OTHER;
            if (progressCallback != null && readingResponse.readingResult == ReadingResult.SUCCESS) {
                progressCallback.update(100);
            }
            readingResponse.message = meterData.getMessage();

            if (readingResponse.readingResult == ReadingResult.SUCCESS) {
                Shared.DataNewDataListPacket.MeterType meterType = null;
                try {
                    meterType = meterDataRetriever.getMeterType();
                } catch (Exception ignored) {

                }
                readingResponse.fromMeterData(meterData);
                if (meterType != null) {
                    switch (meterType) {
                        case Single:
                            readingResponse.setMeterType("Single");
                            break;
                        case Direct:
                        case InDirect:
                            readingResponse.setMeterType("3Phase");
                            break;
                        default:
                            readingResponse.setMeterType("0");
                            break;
                    }
                } else {
                    readingResponse.setMeterType("0");
                }
            }
            return readingResponse;
        } catch (Exception e) {
            e.printStackTrace();
            ElectrometerResponse readingResponse = new ElectrometerResponse();
            readingResponse.readingResult = ReadingResult.OTHER;
            readingResponse.message = Log.getStackTraceString(e);
            return readingResponse;
        }
    }
}
