package shoaa.connectionmanager

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.PendingIntent
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.hardware.usb.UsbManager
import android.os.Build
import android.os.IBinder
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import shoaa.connectionmanager.bluetooth.BluetoothHandler
import shoaa.connectionmanager.bluetooth.SerialListener
import shoaa.connectionmanager.bluetooth.SerialService
import shoaa.connectionmanager.usbserial.CustomProber
import shoaa.connectionmanager.usbserial.UsbDeviceItem
import shoaa.connectionmanager.usbserial.UsbHandler
import shoaa.connectionmanager.usbserial.driver.UsbSerialDriver
import shoaa.connectionmanager.usbserial.driver.UsbSerialProber
import shoaa.connectionmanager.usbserial.util.UsbDeviceIds

class ConnectionManager private constructor() : ServiceConnection, SerialListener {
    private val mainScope = CoroutineScope(Dispatchers.Main)
    private val IO_Dispatcher = Dispatchers.IO.limitedParallelism(1)
    lateinit var usbManager: UsbManager
    private lateinit var actionUsbPermission: String

    private val PENDING_INTENT_FLAG_MUTABLE =
        if (Build.VERSION.SDK_INT >= 34) PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_ALLOW_UNSAFE_IMPLICIT_INTENT else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_MUTABLE else 0

    var connectionType: ConnectionType = ConnectionType.BLUETOOTH

    private val usbDeviceItems: ArrayList<UsbDeviceItem> = ArrayList()
    private var bluetoothAdapter: BluetoothAdapter? = null

    private var mBluetoothDevice: BluetoothDevice? = null
    private var mUsbDevice: UsbDeviceItem? = null

    private val bluetoothHandler: BluetoothHandler = BluetoothHandler.getInstance()
    private val usbHandler = UsbHandler()
    private var connectionNotifier: ConnectionNotifier? = null

    private val connectedDisplayDevice = arrayListOf<DisplayDevice>()
    private lateinit var mActivity: Activity
    private var enableUsb: Boolean = false
    fun init(activity: Activity, enableUsb: Boolean, notifier: ConnectionNotifier) {
        this.mActivity = activity
        this.enableUsb = enableUsb
        actionUsbPermission = "${activity.packageName}.USB_PERMISSION"
        mainScope.launch {
            usbManager = activity.getSystemService(Context.USB_SERVICE) as UsbManager
            withContext(IO_Dispatcher) {
                connectionNotifier = notifier
                if (!attachBluetoothService(instance!!)) {
//            startService(new Intent(this, SerialService.class)); // prevents service destroy on unbind from recreated activity caused by orientation change
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        activity.startForegroundService(
                            Intent(
                                activity,
                                SerialService::class.java
                            )
                        )
                    } else {
                        activity.startService(Intent(activity, SerialService::class.java))
                    }
                }
                activity.bindService(
                    Intent(activity, SerialService::class.java),
                    instance!!,
                    Context.BIND_AUTO_CREATE
                )
                if (enableUsb) {
                    val intentFilter = IntentFilter()
                    intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
                    intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
                    intentFilter.addAction(actionUsbPermission)
                    val listenToBroadcastsFromOtherApps = true
                    val receiverFlags = if (listenToBroadcastsFromOtherApps) {
                        ContextCompat.RECEIVER_EXPORTED
                    } else {
                        ContextCompat.RECEIVER_NOT_EXPORTED
                    }
                    ContextCompat.registerReceiver(
                        activity,
                        usbReceiver,
                        intentFilter,
                        receiverFlags
                    )
                }
                refresh()
                connectedDisplayDevice.sortWith { device: DisplayDevice?, t1: DisplayDevice? ->
                    device?.name
                        ?.compareTo(t1?.name ?: "") ?: 0
                }
                connectionNotifier?.notifyConnection()
            }
        }
    }

    private fun getUsbList(): ArrayList<UsbDeviceItem> {
        return usbDeviceItems
    }

    fun getConnectedDisplayDeviceList(): ArrayList<DisplayDevice> {
        return connectedDisplayDevice
    }

    @SuppressLint("MissingPermission")
    fun getConnectedDeviceSerial(): Int {
        try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                val deviceName = mBluetoothDevice?.name ?: ""
                if (deviceName.isNotEmpty()) {
                    return if (deviceName.contains("_")) {
                        deviceName.substringAfterLast("_").trim()
                            .replace("[^0-9]".toRegex(), "").toInt()
                    } else {
                        deviceName.trim()
                            .replace("[^0-9]".toRegex(), "").toInt()
                    }
                }
            } else {
                return 999999999
            }
        } catch (_: Exception) {
        }
        return 0
    }

    @SuppressLint("MissingPermission")
    fun getConnectedDeviceName(): String {
        try {
            return if (connectionType == ConnectionType.BLUETOOTH) {
                val deviceName = mBluetoothDevice?.name ?: ""
                deviceName
            } else {
                mUsbDevice?.device?.manufacturerName ?: ""
            }
        } catch (_: Exception) {
        }
        return ""
    }

    fun setDevice(device: DisplayDevice) {
        if (device.device is BluetoothDevice) {
            if (mBluetoothDevice == null || mBluetoothDevice!!.address != (device.device as BluetoothDevice).address) {
                disconnect()
            }
            mBluetoothDevice = device.device as BluetoothDevice
            mUsbDevice = null
        } else {
            if (mUsbDevice == null || mUsbDevice!!.device.deviceId != (device.device as UsbDeviceItem).device.deviceId) {
                disconnect()
            }
            mBluetoothDevice = null
            mUsbDevice = device.device as UsbDeviceItem
        }
    }


    fun connectAsync(context: Context, timeOut: Int): Int {
        return try {
            if (mBluetoothDevice != null && mUsbDevice == null) {
                while (bluetoothHandler.connected == Connected.Pending) {
                    Thread.sleep(10)
                }
                if (bluetoothHandler.connected != Connected.True) {

                    bluetoothHandler.connectAsync(context, mBluetoothDevice!!, timeOut)
                    while (bluetoothHandler.connected == Connected.Pending) {
                        Thread.sleep(10)
                    }
                    val res = if (bluetoothHandler.connected == Connected.True) 0 else -1
                    if (res == 0) {
                        connectionType = ConnectionType.BLUETOOTH
                    }
                    res
                } else {
                    0
                }
            } else if (mBluetoothDevice == null && mUsbDevice != null) {
                if (!usbManager.hasPermission(mUsbDevice!!.device)) {
                    val permissionIntent = PendingIntent.getBroadcast(
                        context, 0, Intent(
                            actionUsbPermission
                        ), PENDING_INTENT_FLAG_MUTABLE
                    )
                    usbManager.requestPermission(mUsbDevice!!.device, permissionIntent)
                }
                val res = usbHandler.connect(mUsbDevice)
                if (res == 0) {
                    connectionType = ConnectionType.USB
                }
                res
            } else {
                -100
            }
        } catch (_: Exception) {
            -500
        }
    }

    fun isConnected(): Boolean {
        return when (connectionType) {
            ConnectionType.BLUETOOTH -> {
                bluetoothHandler.connected == Connected.True
            }

            ConnectionType.USB -> {
                usbHandler.connected
            }
        }
    }

    fun reset(): Boolean {
        return try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.reset()
            } else {
                usbHandler.reset()
            }
        } catch (_: Exception) {
            false
        }
    }

    fun disconnect() {
        try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.disconnect()
            } else {
                usbHandler.disconnect()
            }
        } catch (_: Exception) {
        }
    }

    fun setBaudRateAsync(baudRate: BaudRate): Boolean {
        return try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.setBaudRateAsync(baudRate)
            } else {
                usbHandler.setBaudRateAsync(baudRate)
            }
        } catch (_: Exception) {
            return false
        }
    }

    fun sendAsync(
        hexString: String,
        retryCount: Int,
        sendTimeOut: Int,
        readTimeOut: Int
    ): String {
        return try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.sendAsync(hexString, retryCount, sendTimeOut, readTimeOut)
            } else {
                usbHandler.sendAsync(hexString, retryCount, sendTimeOut, readTimeOut)
            }
        } catch (_: Exception) {
            ""
        }
    }


    fun sendAsyncNoRes(hexString: String) {
        try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.sendAsyncNoRes(hexString)
            } else {
                usbHandler.sendAsyncNoRes(hexString)
            }
        } catch (_: Exception) {
        }
    }

    fun sendWithLengthAsync(
        hexString: String,
        retryCount: Int,
        sendTimeOut: Int,
        expectedLength: Int
    ): String {
        return try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                bluetoothHandler.sendWithLengthAsync(
                    hexString,
                    retryCount,
                    sendTimeOut,
                    expectedLength
                )
            } else {
                usbHandler.sendWithLengthAsync(
                    hexString,
                    retryCount,
                    sendTimeOut,
                    expectedLength
                )
            }
        } catch (_: Exception) {
            ""
        }
    }

    /**usb device only*/
    fun sendAndSwitchBaudRateWithLengthAsync(
        hexString: String,
        retryCount: Int,
        sendTimeOut: Int,
        expectedLength: Int,
        oldBaudRate: BaudRate,
        newBaudRate: BaudRate
    ): String {
        return try {
            if (connectionType == ConnectionType.BLUETOOTH) {
                ""
            } else {
                usbHandler.sendAndSwitchBaudRateWithLengthAsync(
                    hexString,
                    retryCount,
                    sendTimeOut,
                    expectedLength,
                    oldBaudRate,
                    newBaudRate
                )
            }
        } catch (_: Exception) {
            ""
        }
    }

    fun writeSession(context: Context) {
        if (connectionType == ConnectionType.BLUETOOTH) {
            bluetoothHandler.writeSession(context)
        } else {
            usbHandler.writeSession(context)
        }
    }

    ///////////////////////////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////
    /////////////BLUETOOTH/////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////

    private fun enableBT(activity: Activity) {
        if (bluetoothAdapter?.isEnabled != true) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            // The REQUEST_ENABLE_BT constant passed to startActivityForResult() is a locally defined integer (which must be greater than 0), that the system passes back to you in your onActivityResult()
            // implementation as the requestCode parameter.
            if (ActivityCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                activity.startActivityForResult(intentBtEnabled, 1)
            }
        }
    }

    private fun attachBluetoothService(listener: SerialListener): Boolean {
        return if (bluetoothHandler.service != null) {
            mainScope.launch {
                bluetoothHandler.service!!.attach(listener)
            }
            true
        } else {
            false
        }
    }

    fun deAttachBluetoothService(): Boolean {
        return if (bluetoothHandler.service != null) {
            bluetoothHandler.service!!.detach()
            true
        } else {
            false
        }
    }

    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        bluetoothHandler.onServiceConnected(name!!, service!!)
    }

    override fun onServiceDisconnected(name: ComponentName?) {
        bluetoothHandler.onServiceDisconnected(name!!)
    }

    override fun onSerialConnect() {
        bluetoothHandler.onSerialConnect()
    }

    override fun onSerialConnectError(e: java.lang.Exception?) {
        bluetoothHandler.onSerialConnectError(e)
    }

    override fun onSerialRead(data: ByteArray?) {
        bluetoothHandler.onSerialRead(data)
    }

    override fun onSerialIoError(e: java.lang.Exception?) {
        bluetoothHandler.onSerialIoError(e)
    }

    ///////////////////////////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////
    ////////////////USB////////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////
    ///////////////////////////////////////

    private val usbReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            onBroadcastReceive(context, intent)
        }

    }

    fun onBroadcastReceive(context: Context, intent: Intent) {
        val action = intent.action ?: return
        when (action) {
            UsbManager.ACTION_USB_DEVICE_ATTACHED -> try {
                refresh()
            } catch (_: Exception) {
            }

            UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                try {
                    refresh()
                } catch (_: Exception) {
                }
            }
        }

        connectedDisplayDevice.sortWith { device: DisplayDevice?, t1: DisplayDevice? ->
            device?.name
                ?.compareTo(t1?.name ?: "") ?: 0
        }
        connectionNotifier?.notifyConnection()
    }

    private fun refresh() {
        connectedDisplayDevice.clear()
        refreshBT(mActivity)
        if (enableUsb) {
            refreshUsb(mActivity)
        }
        connectionNotifier?.notifyConnection()
    }

    private fun refreshBT(activity: Activity) {
        bluetoothAdapter =
            (activity.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).adapter
        if (bluetoothAdapter == null) {
            mainScope.launch {
                Toast.makeText(
                    activity,
                    "<bluetooth not supported>",
                    Toast.LENGTH_LONG
                ).show()
            }
        } else if (!bluetoothAdapter!!.isEnabled) {
            mainScope.launch {
                Toast.makeText(
                    activity,
                    "<bluetooth is disabled>",
                    Toast.LENGTH_LONG
                ).show()
            }
            enableBT(activity)
        }
        if (bluetoothAdapter != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(
                        activity,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    activity.requestPermissions(
                        arrayOf<String>(Manifest.permission.BLUETOOTH_CONNECT),
                        0
                    )
                    return
                }
            }
            for (device in bluetoothAdapter!!.bondedDevices) {
                connectedDisplayDevice.add(
                    DisplayDevice(
                        activity,
                        device
                    )
                )
            }
        }
    }

    private fun refreshUsb(context: Context) {
        val usbDefaultProber: UsbSerialProber = UsbSerialProber.getDefaultProber()
        val usbCustomProber: UsbSerialProber = CustomProber.getCustomProber()
        usbDeviceItems.clear()
        for (device in usbManager.deviceList.values) {
            var driver: UsbSerialDriver? = usbDefaultProber.probeDevice(device)
            if (driver == null) {
                driver = usbCustomProber.probeDevice(device)
            }
            if (driver != null) {
                for (port in 0 until driver.ports.size) {
                    if (UsbDeviceIds.allowedDevice.contains(
                            UsbDeviceIds(
                                device.vendorId,
                                device.productId
                            )
                        )
                    ) {
                        usbDeviceItems.add(
                            UsbDeviceItem(
                                device,
                                port,
                                driver
                            )
                        )
                    }
                }
            }
            usbDeviceItems.forEach {
                if (!usbManager.hasPermission(it.device)) {
                    val permissionIntent = PendingIntent.getBroadcast(
                        context, 0, Intent(
                            actionUsbPermission
                        ), PENDING_INTENT_FLAG_MUTABLE
                    )
                    usbManager.requestPermission(it.device, permissionIntent)
                }
                connectedDisplayDevice.add(DisplayDevice(context, it))
            }
        }
    }

    fun calculateSendingTimeMs(
        baudRate: BaudRate,
        packetLength: Int
    ): Int {
        return if (Common.CALCULATE_SEND_TIME) {
            val parity = baudRate.name.split("_")[3]
            val transmissionBits =
                baudRate.dataBit() + 1 + (if (parity == "E" || parity == "O") 1 else 0) + baudRate.stopBit() // One for start bit

            val bitsPerSecond = baudRate.getBaud().toDouble()
            val timePerBit = 1.0 / bitsPerSecond
            val transmissionTimeInSeconds = timePerBit * transmissionBits * packetLength
            (transmissionTimeInSeconds * 1000).toInt()
        } else {
            0
        }
    }

    fun getBluetoothDevice(): BluetoothDevice? {
        return mBluetoothDevice;
    }


    companion object {
        @Volatile
        private var instance: ConnectionManager? = null

        fun getInstance() =
            instance ?: synchronized(this) {
                instance ?: ConnectionManager().also { instance = it }
            }
    }
}