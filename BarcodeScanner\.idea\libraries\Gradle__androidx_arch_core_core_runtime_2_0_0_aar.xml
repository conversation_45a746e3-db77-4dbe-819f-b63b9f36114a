<component name="libraryTable">
  <library name="Gradle: androidx.arch.core:core-runtime:2.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ba3bfcff5c9f877dc18f42b53494979d/transformed/core-runtime-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2a5fd92a7c89860b3dae9b6ce08341ea/transformed/core-runtime-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4450048210df2fd495d11cf314d41869/transformed/core-runtime-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/21dc89122266b28ac8e998f25ca518cd/transformed/core-runtime-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/6481e2be3ec524bac0283e66bd47655a/transformed/core-runtime-2.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/21dc89122266b28ac8e998f25ca518cd/transformed/core-runtime-2.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/21dc89122266b28ac8e998f25ca518cd/transformed/core-runtime-2.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-runtime/2.0.0/bc41b287c95bc50a3cd27cb1b7cfb301805ba7f1/core-runtime-2.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>