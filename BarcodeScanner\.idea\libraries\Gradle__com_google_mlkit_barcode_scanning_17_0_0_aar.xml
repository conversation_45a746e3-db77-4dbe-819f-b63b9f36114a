<component name="libraryTable">
  <library name="Gradle: com.google.mlkit:barcode-scanning:17.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/c1917c45c45a9f7daf9fa5b3e6181515/transformed/jetified-barcode-scanning-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/4f691aa02e657595a0960c551ce288da/transformed/jetified-barcode-scanning-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/edb61d3d36f2ef8be5970e10af27cb1b/transformed/jetified-barcode-scanning-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/384f9d1e22d23844ca4c7f9f0dd062b2/transformed/jetified-barcode-scanning-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d850b4abc34c82ce7710caf19f2f05f1/transformed/jetified-barcode-scanning-17.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/384f9d1e22d23844ca4c7f9f0dd062b2/transformed/jetified-barcode-scanning-17.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/384f9d1e22d23844ca4c7f9f0dd062b2/transformed/jetified-barcode-scanning-17.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.mlkit/barcode-scanning/17.0.0/d1e51f04a4e0b3df1cf1e84e95c5aa758e6ad905/barcode-scanning-17.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>