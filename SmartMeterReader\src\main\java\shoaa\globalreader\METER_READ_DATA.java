package shoaa.globalreader;


public class METER_READ_DATA {
    public int MeterPhase;
    public int TotalConsum;
    public int RemainPound;
    public int RemainPiasters;
    public int CurrConsum;
    public int CurrentMonthlyConsum;
    public int CurrentsliceConsum;
    public int TotalBalance;
    public int OpenTrials;
    public int Battery;
    public int EarthFrauds;
    public int Cardreads;
    public float MaxLoadKW;
    public float MaxLoadAM;
    public int DateR;
    public int Time;
    public int Operator;
    public int overload;
    public int RemainKW;
    public int RemainCurrSlice;
    public int SliceNo;
    public int consm1;
    public int consm2;
    public int consm3;
    public int consm4;
    public int consm5;
    public int consm6;
    public int consm7;
    public int consm8;
    public int consm9;
    public int consm10;
    public int consm11;
    public int consm12;
    public int MeterStat;
    public int chargeCnt;
    public int RemainInDays;
    public int RSeconds;
    public int RMinutes;
    public int RHours;
    public int ReadDate;
    public int EarthFraudDuration;
    public int AvergDailyConsum;
    public int ReadID;
    public int PrevMonthConsum;
    public int OpenCoverDate;
    public int PowerFactor;
    public int DebitsInMeterPnd;
    public int DebitsInMeterPstr;
    public int SumOfAllChargesPND;
    public int SumOfAllChargesPSTR;
    public int OpenKW;
    public int chargeID;
    public int LastChargeCodeSentBySystem;
    public int CrdLastBalance;
    public int LastChargeDate;
    public int MeterSerialNumber;
    public int DebitConsum;
    public int TotalConsumVAR;
    public int CurrMonthConsumVAR;
    public int PreviousMonthConsumVAR;
    public int PH1_Fail_Count;
    public int PH1_Fail_Duration;
    public int PH2_Fail_Count;
    public int PH2_Fail_Duration;
    public int PH3_Fail_Count;
    public int PH3_Fail_Duration;
    public int All_Fail_Count;
    public int CoverEventDate;
    public int DTLastChargeDate;
    public int DTReadDate;
    public int OpenKiloWatt;
    public int readcount;
    public int chargeCount;
    public int ChargeID;
    public int ConsumInCurrSlic;
    public int coveropen;
    public int CoverOpenMinute;
    public int CoverOpenHours;
    public int CoverOpenDays;
    public int CoverOpenMonthes;
    public int CoverOpenYears;
    public int RelayOpen;
    public int EarthOpenMinute;
    public int EarthOpenHours;
    public int EarthOpenDays;
    public int EarthOpenMonthes;
    public int EarthOpenYears;
    public int EarthOpen;

    public int ReverseOpenMinute;
    public int ReverseOpenHours;
    public int ReverseOpenDays;
    public int ReverseOpenMonthes;
    public int ReverseOpenYears;
    public int ReverseOpen;

    public int Ph1OpenMinute;
    public int Ph1OpenHours;
    public int Ph1OpenDays;
    public int Ph1OpenMonthes;
    public int Ph1OpenYears;
    public int Ph1Open;

    public int Ph2OpenMinute;
    public int Ph2OpenHours;

    public int Ph2OpenDays;
    public int Ph2OpenMonthes;
    public int Ph2OpenYears;
    public int Ph2Open;

    public int Ph3OpenMinute;
    public int Ph3OpenHours;
    public int Ph3OpenDays;
    public int Ph3OpenMonthes;
    public int Ph3OpenYears;
    public int Ph3Open;

    public int RelayOpenMinute;
    public int RelayOpenHours;
    public int RelayOpenDays;
    public int RelayOpenMonthes;
    public int RelayOpenYears;

    public int ROverLoad;
    public int MeterState;

    public int ControlCardID1;
    public int ControlCardID2;
    public int ControlCardID3;
    public int Rdays;
    public int Rmonthes;
    public int Ryears;
    public int DebitsInMeterPstrPrevious;
    public int DebitsInMeterPndPrevious;

    public int PowerFactor_1;
    public int PowerFactor_2;
    public int PowerFactor_3;
    public int PowerFactor_4;
    public int MaximumDemand_1;
    public int MaximumDemand_2;
    public int MaximumDemand_3;
    public int MaximumDemand_4;
    public int MaximumDemand_5;
    public int MaximumDemand_6;
    public int MaximumDemand_7;
    public int MaximumDemand_8;
    public int MaximumDemand_9;
    public int MaximumDemand_10;
    public int MaximumDemand_11;
    public int MaximumDemand_12;
    public int ConsumptionKVARPos;
    public int ConsumptionKVARNeg;
    public int SettledDepitsPiasters;

    public int ClientID;

    public int FirstHalfID;

    public int LastHalfID;

    public int CT;
    public int DoorOpenMinute;
    public int DoorOpenHours;
    public int DoorOpenDays;
    public int DoorOpenMonthes;
    public int DoorOpenYears;
    public int DoorOpen;
    public int consm_kvar1;
    public int consm_kvar2;
    public int consm_kvar3;
    public int consm_kvar4;
    public int consm_kvar5;
    public int consm_kvar6;
    public int consm_kvar7;
    public int consm_kvar8;
    public int consm_kvar9;
    public int consm_kvar10;
    public int consm_kvar11;
    public int consm_kvar12;
    public int PowerFactor_month1;
    public int PowerFactor_month2;
    public int PowerFactor_month3;
    public int PowerFactor_month4;
    public int PowerFactor_month5;
    public int PowerFactor_month6;
    public int PowerFactor_month7;
    public int PowerFactor_month8;
    public int PowerFactor_month9;
    public int PowerFactor_month10;
    public int PowerFactor_month11;
    public int PowerFactor_month12;

    public int Challenge1;

    public int Challenge2;

    public int Moneyconsm01;

    public int Moneyconsm02;

    public int Moneyconsm03;

    public int Moneyconsm04;

    public int Moneyconsm05;

    public int Moneyconsm06;

    public int Moneyconsm07;

    public int Moneyconsm08;

    public int Moneyconsm09;

    public int Moneyconsm10;

    public int Moneyconsm11;

    public int Moneyconsm12;

    public int CurrentMonthMoneyConsum;

    public byte ControlCardStateBefore1;

    public byte ControlCardStateAfter1;

    public byte ControlCardStateBefore2;

    public byte ControlCardStateAfter2;

    public byte ControlCardStateBefore3;

    public byte ControlCardStateAfter3;

    public int InstallCardID;

    public byte MeterInstallSeconds;

    public byte MeterInstallMinute;

    public byte MeterInstallHours;

    public byte MeterInstallDays;

    public byte MeterInstallMonthes;

    public short MeterInstallYears;
    public int ConsumptionInReverse;
    public int ConsumptionInEarth;

    public int MaxLoadMinute;
    public int MaxLoadHours;
    public int MaxLoadDays;
    public int MaxLoadMonthes;
    public int MaxLoadYears;

    public int LastChargeDateSecond;
    public int LastChargeDateMinute;
    public int LastChargeDateHour;
    public int LastChargeDateDay;
    public int LastChargeDateMonth;
    public int LastChargeDateYear;
    public String CardId;
}