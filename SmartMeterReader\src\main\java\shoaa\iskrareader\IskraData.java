package shoaa.iskrareader;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> Darwish
 */

public class IskraData {

    public static String data = "serial\titem\tISKRA_1011_V1\tISKRA_1012_V1\tISKRA_1012_V3\tISKRA_1012_V4\tISKRA_1020_V5\tISKRA_3010\tISKRA_3012\n" +
            "1\tITEM_1_NEW_BASEITEM_Meter_ID\tD051\tD051\tD051\tD051\tD051\tC128\tC128\n" +
            "2\tITEM_2_NEW_BASEITEM_Customer_ID\tD050\tD050\tD050\tD050\tD050\tC127\tC127\n" +
            "3\tITEM_3_NEW_BASEITEM_CardID\t0\t0\t0\t0\t0\t0\t0\n" +
            "4\tITEM_4_NEW_BASEITEM_fw_version\t0\t0\t0\t0\t0\t0\t0\n" +
            "5\tITEM_5_NEW_BASEITEM_ActivityType\t0\t0\t0\t0\t0\t0\t0\n" +
            "6\tITEM_6_NEW_BASEITEM_curent_Power_factor\t0\t0\t0\t0\t0\tC106\tC106\n" +
            "7\tITEM_7_NEW_BASEITEM_last_year_Power_factor\t0\t0\t0\t0\t0\t0\t0\n" +
            "8\tITEM_8_NEW_BASEITEM_installing_technican_code\t0\t0\t0\t0\t0\t0\t0\n" +
            "9\tITEM_9_NEW_BASEITEM_installing_Date_and_time\t0\t0\t0\t0\t0\t0\tC09A\n" +
            "10\tITEM_10_NEW_BASEITEM_Meter_Date_and_Time\tC001\tC001\tC001\tC001\tC001\tC001\tC001\n" +
            "11\tITEM_11_NEW_BASEITEM_Current_tarrif_installing\t0\t0\t0\t0\t0\t0\t0\n" +
            "12\tITEM_12_NEW_BASEITEM_Current_tariff_activation_date\tC004\tC004\t0\tC004\tC004\t0\t0\n" +
            "13\tITEM_13_NEW_BASEITEM_Meter_status\tD053\tD053\tC13C\tD053\tD053\tC1F2\tC1F2\n" +
            "14\tITEM_14_NEW_BASEITEM_Relay_status\tD053\tD053\tC13C\tD053\tD053\tC1F2\tC1F2\n" +
            "15\tITEM_15_NEW_BASEITEM_battery_status\tD053\tD053\tC13C\tD053\tD053\tC1F2\tC1F2\n" +
            "16\tITEM_16_NEW_BASEITEM_Top_cover_status\tD053\tD053\tC13C\tD053\tD053\tC1F2\tC1F2\n" +
            "17\tITEM_17_NEW_BASEITEM_Side_cover_status\tD053\tD053\tC13C\tD053\tD053\tC1F2\tC1F2\n" +
            "18\tITEM_18_NEW_BASEITEM_Technical_code_event_1\t0\t0\t0\t0\t0\t0\t0\n" +
            "19\tITEM_19_NEW_BASEITEM_event_type_1\t0\t0\t0\t0\t0\t0\t0\n" +
            "20\tITEM_20_NEW_BASEITEM_event_Date_1\t0\t0\t0\t0\t0\t0\t0\n" +
            "21\tITEM_21_NEW_BASEITEM_Technical_code_event_2\t0\t0\t0\t0\t0\t0\t0\n" +
            "22\tITEM_22_NEW_BASEITEM_event_type_2\t0\t0\t0\t0\t0\t0\t0\n" +
            "23\tITEM_23_NEW_BASEITEM_event_Date_2\t0\t0\t0\t0\t0\t0\t0\n" +
            "24\tITEM_24_NEW_BASEITEM_Technical_code_event_3\t0\t0\t0\t0\t0\t0\t0\n" +
            "25\tITEM_25_NEW_BASEITEM_event_type_3\t0\t0\t0\t0\t0\t0\t0\n" +
            "26\tITEM_26_NEW_BASEITEM_event_Date_3\t0\t0\t0\t0\t0\t0\t0\n" +
            "27\tITEM_27_NEW_BASEITEM_recharge_number\tF019\tF019\tC122\tF019\tF019\tC122\tC122\n" +
            "28\tITEM_28_NEW_BASEITEM_Recharge_Amount\tF020\tF020\tC120\tF020\tF020\tC129\tC129\n" +
            "29\tITEM_29_NEW_BASEITEM_Last_recharge_Date_And_Time\tC0A0\tC0A0\tC0A0\tC0A0\tC0A0\tC090\tC090\n" +
            "30\tITEM_30_NEW_BASEITEM_remaining_credit_kw\tF004\tF004\tF004\tF004\tF004\t0\t0\n" +
            "31\tITEM_31_NEW_BASEITEM_remaining_credit_mony\tF021\tF021\tF021\tF021\tF021\tC12A\tC12A\n" +
            "32\tITEM_32_NEW_BASEITEM_Debts\t0\t0\t0\t0\t0\t0\t0\n" +
            "33\tITEM_33_NEW_BASEITEM_Total_consumption_kw\tF002\tF002\tF002\tF002\tF002\tF002\tF002\n" +
            "34\tITEM_34_NEW_BASEITEM_Total_consumption_mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "35\tITEM_35_NEW_BASEITEM_Total_consumption_kvar\t0\t0\t0\t0\t0\tF300\tF300\n" +
            "36\tITEM_36_NEW_BASEITEM_Current_Demand\tF501\tF501\tF501\tF501\tF501\tF500\tF500\n" +
            "37\tITEM_37_NEW_BASEITEM_Maximum_Demand\t0\t0\t0\t0\t0\t0\t0\n" +
            "38\tITEM_38_NEW_BASEITEM_Maximum_Demand_date\t0\t0\t0\t0\t0\t0\t0\n" +
            "39\tITEM_39_NEW_BASEITEM_instanteneous_pahase_A_volt\t0\t0\t0\t0\t0\t0\t0\n" +
            "40\tITEM_40_NEW_BASEITEM_instanteneous_phase_B_volt\t0\t0\t0\t0\t0\t0\t0\n" +
            "41\tITEM_41_NEW_BASEITEM_instanteneous_phaase_C_volt\t0\t0\t0\t0\t0\t0\t0\n" +
            "42\tITEM_42_NEW_BASEITEM_instanteneous_phase_A_current\t0\t0\t0\t0\t0\t0\t0\n" +
            "43\tITEM_43_NEW_BASEITEM_instanteneous_phase_B_current\t0\t0\t0\t0\t0\t0\t0\n" +
            "44\tITEM_44_NEW_BASEITEM_instanteneous_phase_C_current\t0\t0\t0\t0\t0\t0\t0\n" +
            "45\tITEM_45_NEW_BASEITEM_instanteneous_volt\t0\t0\t0\t0\t0\t0\t0\n" +
            "46\tITEM_46_NEW_BASEITEM_instanteneous_current_Phase_Ampere\t0\t0\t0\t0\t0\t0\t0\n" +
            "47\tITEM_47_NEW_BASEITEM_instanteneous_current_Neutral\t0\t0\t0\t0\t0\t0\t0\n" +
            "48\tITEM_48_NEW_BASEITEM_reverse_Kwh\tC360\tC360\t0\tC360\tC360\tC360\tC360\n" +
            "49\tITEM_49_NEW_BASEITEM_unbalance_Kwh\tC290\tC290\t0\tC290\tC290\t0\t0\n" +
            "50\tITEM_50_NEW_BASEITEM_current_month_consumption_KW\tF029\tF029\tF029\tF029\tF029\tF600\tF600\n" +
            "51\tITEM_51_NEW_BASEITEM_current_month_consumption_MONY\t0\t0\t0\t0\t0\t0\t0\n" +
            "52\tITEM_52_NEW_BASEITEM_1_month_consumption_kWh\tF610\tF610\tF110\tF610\tF610\tF610\tF610\n" +
            "53\tITEM_53_NEW_BASEITEM_2_month_consumption_kWh\tF620\tF620\tF120\tF620\tF620\tF620\tF620\n" +
            "54\tITEM_54_NEW_BASEITEM_3_month_consumption_kWh\tF630\tF630\tF130\tF630\tF630\tF630\tF630\n" +
            "55\tITEM_55_NEW_BASEITEM_4_month_consumption_kWh\tF640\tF640\tF140\tF640\tF640\tF640\tF640\n" +
            "56\tITEM_56_NEW_BASEITEM_5_month_consumption_kWh\tF650\tF650\tF150\tF650\tF650\tF650\tF650\n" +
            "57\tITEM_57_NEW_BASEITEM_6_month_consumption_kWh\tF660\tF660\tF160\tF660\tF660\tF660\tF660\n" +
            "58\tITEM_58_NEW_BASEITEM_7_month_consumption_kWh\tF670\tF670\tF170\tF670\tF670\tF670\tF670\n" +
            "59\tITEM_59_NEW_BASEITEM_8_month_consumption_kWh\tF680\tF680\tF180\tF680\tF680\tF680\tF680\n" +
            "60\tITEM_60_NEW_BASEITEM_9_month_consumption_kWh\tF690\tF690\tF190\tF690\tF690\tF690\tF690\n" +
            "61\tITEM_61_NEW_BASEITEM_10_month_consumption_kWh\tF6A0\tF6A0\tF1A0\tF6A0\tF6A0\tF6A0\tF6A0\n" +
            "62\tITEM_62_NEW_BASEITEM_11_month_consumption_kWh\tF6B0\tF6B0\tF1B0\tF6B0\tF6B0\tF6B0\tF6B0\n" +
            "63\tITEM_63_NEW_BASEITEM_12_month_consumption_kWh\tF6C0\tF6C0\tF1C0\tF6C0\tF6C0\tF6C0\tF6C0\n" +
            "64\tITEM_64_NEW_BASEITEM_1_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "65\tITEM_65_NEW_BASEITEM_2_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "66\tITEM_66_NEW_BASEITEM_3_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "67\tITEM_67_NEW_BASEITEM_4_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "68\tITEM_68_NEW_BASEITEM_5_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "69\tITEM_69_NEW_BASEITEM_6_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "70\tITEM_70_NEW_BASEITEM_7_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "71\tITEM_71_NEW_BASEITEM_8_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "72\tITEM_72_NEW_BASEITEM_9_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "73\tITEM_73_NEW_BASEITEM_10_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "74\tITEM_74_NEW_BASEITEM_11_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "75\tITEM_75_NEW_BASEITEM_12_month_consumption_Mony\t0\t0\t0\t0\t0\t0\t0\n" +
            "76\tITEM_76_NEW_BASEITEM_maxim_demand_month_1\t0\t0\t0\t0\t0\tF510\tF510\n" +
            "77\tITEM_77_NEW_BASEITEM_maxim_demand_month_2\t0\t0\t0\t0\t0\tF520\tF520\n" +
            "78\tITEM_78_NEW_BASEITEM_maxim_demand_month_3\t0\t0\t0\t0\t0\tF530\tF530\n" +
            "79\tITEM_79_NEW_BASEITEM_maxim_demand_month_4\t0\t0\t0\t0\t0\tF540\tF540\n" +
            "80\tITEM_80_NEW_BASEITEM_maxim_demand_month_5\t0\t0\t0\t0\t0\tF550\tF550\n" +
            "81\tITEM_81_NEW_BASEITEM_maxim_demand_month_6\t0\t0\t0\t0\t0\tF560\tF560\n" +
            "82\tITEM_82_NEW_BASEITEM_maxim_demand_month_7\t0\t0\t0\t0\t0\tF570\tF570\n" +
            "83\tITEM_83_NEW_BASEITEM_maxim_demand_month_8\t0\t0\t0\t0\t0\tF580\tF580\n" +
            "84\tITEM_84_NEW_BASEITEM_maxim_demand_month_9\t0\t0\t0\t0\t0\tF590\tF590\n" +
            "85\tITEM_85_NEW_BASEITEM_maxim_demand_month_10\t0\t0\t0\t0\t0\tF5A0\tF5A0\n" +
            "86\tITEM_86_NEW_BASEITEM_maxim_demand_month_11\t0\t0\t0\t0\t0\tF5B0\tF5B0\n" +
            "87\tITEM_87_NEW_BASEITEM_maxim_demand_month_12\t0\t0\t0\t0\t0\tF5C0\tF5C0\n" +
            "88\tITEM_88_NEW_BASEITEM_maxim_demand_month_Date_1\t0\t0\t0\t0\t0\tF510\tF510\n" +
            "89\tITEM_89_NEW_BASEITEM_maxim_demand_month_Date_2\t0\t0\t0\t0\t0\tF520\tF520\n" +
            "90\tITEM_90_NEW_BASEITEM_maxim_demand_month_Date_3\t0\t0\t0\t0\t0\tF530\tF530\n" +
            "91\tITEM_91_NEW_BASEITEM_maxim_demand_month_Date_4\t0\t0\t0\t0\t0\tF540\tF540\n" +
            "92\tITEM_92_NEW_BASEITEM_maxim_demand_month_Date_5\t0\t0\t0\t0\t0\tF550\tF550\n" +
            "93\tITEM_93_NEW_BASEITEM_maxim_demand_month_Date_6\t0\t0\t0\t0\t0\tF560\tF560\n" +
            "94\tITEM_94_NEW_BASEITEM_maxim_demand_month_Date_7\t0\t0\t0\t0\t0\tF570\tF570\n" +
            "95\tITEM_95_NEW_BASEITEM_maxim_demand_month_Date_8\t0\t0\t0\t0\t0\tF580\tF580\n" +
            "96\tITEM_96_NEW_BASEITEM_maxim_demand_month_Date_9\t0\t0\t0\t0\t0\tF590\tF590\n" +
            "97\tITEM_97_NEW_BASEITEM_maxim_demand_month_Date_10\t0\t0\t0\t0\t0\tF5A0\tF5A0\n" +
            "98\tITEM_98_NEW_BASEITEM_maxim_demand_month_Date_11\t0\t0\t0\t0\t0\tF5B0\tF5B0\n" +
            "99\tITEM_99_NEW_BASEITEM_maxim_demand_month_Date_12\t0\t0\t0\t0\t0\tF5C0\tF5C0\n" +
            "100\tITEM_100_NEW_BASEITEM_kvar_consumption_month_1\t0\t0\t0\t0\t0\tF310\tF310\n" +
            "101\tITEM_101_NEW_BASEITEM_kvar_consumption_month_2\t0\t0\t0\t0\t0\tF320\tF320\n" +
            "102\tITEM_102_NEW_BASEITEM_kvar_consumption_month_3\t0\t0\t0\t0\t0\tF330\tF330\n" +
            "103\tITEM_103_NEW_BASEITEM_kvar_consumption_month_4\t0\t0\t0\t0\t0\tF340\tF340\n" +
            "104\tITEM_104_NEW_BASEITEM_kvar_consumption_month_5\t0\t0\t0\t0\t0\tF350\tF350\n" +
            "105\tITEM_105_NEW_BASEITEM_kvar_consumption_month_6\t0\t0\t0\t0\t0\tF360\tF360\n" +
            "106\tITEM_106_NEW_BASEITEM_kvar_consumption_month_7\t0\t0\t0\t0\t0\tF370\tF370\n" +
            "107\tITEM_107_NEW_BASEITEM_kvar_consumption_month_8\t0\t0\t0\t0\t0\tF380\tF380\n" +
            "108\tITEM_108_NEW_BASEITEM_kvar_consumption_month_9\t0\t0\t0\t0\t0\tF390\tF390\n" +
            "109\tITEM_109_NEW_BASEITEM_kvar_consumption_month_10\t0\t0\t0\t0\t0\tF3A0\tF3A0\n" +
            "110\tITEM_110_NEW_BASEITEM_kvar_consumption_month_11\t0\t0\t0\t0\t0\tF3B0\tF3B0\n" +
            "111\tITEM_111_NEW_BASEITEM_kvar_consumption_month_12\t0\t0\t0\t0\t0\tF3C0\tF3C0\n" +
            "112\tITEM_112_Tariff1\tD040\tD040\tD272\tD040\tD040\tD272\tD272\n" +
            "113\tITEM_112_Tariff2\tD041\tD041\tD272\tD041\tD041\tD272\tD272\n" +
            "114\tITEM_112_Tariff3\tD042\tD042\tD272\tD042\tD042\tD272\tD272\n" +
            "115\tITEM_112_Tariff4\tD043\tD043\tD272\tD043\tD043\tD272\tD272\n" +
            "116\tITEM_112_Tariff5\tD044\tD044\tD272\tD044\tD044\tD272\tD272\n" +
            "117\tITEM_112_Tariff6\tD045\tD045\tD272\tD045\tD045\tD272\tD272\n" +
            "118\tITEM_112_Tariff7\tD046\tD046\tD272\tD046\tD046\tD272\tD272\n" +
            "119\tITEM_112_Tariff8\tD047\tD047\tD272\tD047\tD047\tD272\tD272\n" +
            "120\tITEM_112_Tariff9\tD048\tD048\tD272\tD048\tD048\tD272\tD272\n" +
            "121\tITEM_112_Tariff10\tD049\tD049\tD272\tD049\tD049\tD272\tD272\n";

    private static String[] lines = null;
    private static Map<String, String> ISKRA_1011_V1 = null;
    private static Map<String, String> ISKRA_1012_V1 = null;
    private static Map<String, String> ISKRA_1012_V3 = null;
    private static Map<String, String> ISKRA_1012_V4 = null;
    private static Map<String, String> ISKRA_1020_V5 = null;
    private static Map<String, String> ISKRA_3010 = null;
    private static Map<String, String> ISKRA_3012 = null;

    public static String getCode(String fileName, String key) {
        if (lines == null) {
            lines = data.split("\n");
        }
        if (ISKRA_1011_V1 == null) {
            ISKRA_1011_V1 = new HashMap<>();
            for (String line : lines) {
                ISKRA_1011_V1.put(line.split("\t")[1], line.split("\t")[2]);
            }
        }
        if (ISKRA_1012_V1 == null) {
            ISKRA_1012_V1 = new HashMap<>();
            for (String line : lines) {
                ISKRA_1012_V1.put(line.split("\t")[1], line.split("\t")[3]);
            }
        }
        if (ISKRA_1012_V3 == null) {
            ISKRA_1012_V3 = new HashMap<>();
            for (String line : lines) {
                ISKRA_1012_V3.put(line.split("\t")[1], line.split("\t")[4]);
            }
        }
        if (ISKRA_1012_V4 == null) {
            ISKRA_1012_V4 = new HashMap<>();
            for (String line : lines) {
                ISKRA_1012_V4.put(line.split("\t")[1], line.split("\t")[5]);
            }
        }
        if (ISKRA_1020_V5 == null) {
            ISKRA_1020_V5 = new HashMap<>();
            for (String line : lines) {
                ISKRA_1020_V5.put(line.split("\t")[1], line.split("\t")[6]);
            }
        }
        if (ISKRA_3010 == null) {
            ISKRA_3010 = new HashMap<>();
            for (String line : lines) {
                ISKRA_3010.put(line.split("\t")[1], line.split("\t")[7]);
            }
        }
        if (ISKRA_3012 == null) {
            ISKRA_3012 = new HashMap<>();
            for (String line : lines) {
                ISKRA_3012.put(line.split("\t")[1], line.split("\t")[8]);
            }
        }
        if (fileName.equalsIgnoreCase("ISKRA_1011_V1")) {
            if (!ISKRA_1011_V1.containsKey(key))
                return "0";
            return ISKRA_1011_V1.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_1012_V1")) {
            if (!ISKRA_1012_V1.containsKey(key))
                return "0";
            return ISKRA_1012_V1.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_1012_V3")) {
            if (!ISKRA_1012_V3.containsKey(key))
                return "0";
            return ISKRA_1012_V3.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_1012_V4")) {
            if (!ISKRA_1012_V4.containsKey(key))
                return "0";
            return ISKRA_1012_V4.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_1020_V5")) {
            if (!ISKRA_1020_V5.containsKey(key))
                return "0";
            return ISKRA_1020_V5.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_3010")) {
            if (!ISKRA_3010.containsKey(key))
                return "0";
            return ISKRA_3010.getOrDefault(key, "0");
        } else if (fileName.equalsIgnoreCase("ISKRA_3012")) {
            if (!ISKRA_3012.containsKey(key))
                return "0";
            return ISKRA_3012.getOrDefault(key, "0");
        }
        return "0";
    }
}
