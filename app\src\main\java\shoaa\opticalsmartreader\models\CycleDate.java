package shoaa.opticalsmartreader.models;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "cycleDate")
public class CycleDate {
    @ColumnInfo(name = "Lock")
    public String Lock = "";
    @ColumnInfo(name = "CycleYear")
    public int CycleYear = 0;
    @PrimaryKey
    public int CycleMonth = 0;

    @NonNull
    @Override
    public String toString() {
        return "lock : " + String.valueOf(Lock) + " , CycleYear : " + String.valueOf(CycleYear) + " , CycleMonth : " + String.valueOf(CycleMonth);
    }
}
