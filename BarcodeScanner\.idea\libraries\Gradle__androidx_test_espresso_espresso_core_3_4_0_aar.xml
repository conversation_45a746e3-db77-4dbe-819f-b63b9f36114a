<component name="libraryTable">
  <library name="Gradle: androidx.test.espresso:espresso-core:3.4.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/35c44ef0b23422722e932db04a439693/transformed/espresso-core-3.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a5517ddad1f0110e128b5382073f073d/transformed/espresso-core-3.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/abdbaf52e42a2bbf0a7d3eb8a34ed439/transformed/espresso-core-3.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d437e681741d0cb5f34715590ce1d6be/transformed/espresso-core-3.4.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/abdbaf52e42a2bbf0a7d3eb8a34ed439/transformed/espresso-core-3.4.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/abdbaf52e42a2bbf0a7d3eb8a34ed439/transformed/espresso-core-3.4.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.espresso/espresso-core/3.4.0/527848e2722cd3ada150b991c0e620ec4f0a8b/espresso-core-3.4.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test.espresso/espresso-core/3.4.0/8c2dc35c200b749281e0fbdc1262359e19a99cc3/espresso-core-3.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>