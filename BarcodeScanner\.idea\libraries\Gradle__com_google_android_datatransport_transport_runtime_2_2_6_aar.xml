<component name="libraryTable">
  <library name="Gradle: com.google.android.datatransport:transport-runtime:2.2.6@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ae7033f22d9a3a238a1bc04bb2c26746/transformed/jetified-transport-runtime-2.2.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/2a3e8cf26a046b553616815ff232281c/transformed/jetified-transport-runtime-2.2.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/3b86b967ba6f3029d27b06c86892728b/transformed/jetified-transport-runtime-2.2.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ab487c8c9f1bc5536d5a25e0fef957bb/transformed/jetified-transport-runtime-2.2.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/f87513e83c47b07dd5f6c224f56ae91f/transformed/jetified-transport-runtime-2.2.6/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/ab487c8c9f1bc5536d5a25e0fef957bb/transformed/jetified-transport-runtime-2.2.6/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/ab487c8c9f1bc5536d5a25e0fef957bb/transformed/jetified-transport-runtime-2.2.6/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.datatransport/transport-runtime/2.2.6/7509bfbb3df6c8d1e8b5ab57eb22a40042d3ef64/transport-runtime-2.2.6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>