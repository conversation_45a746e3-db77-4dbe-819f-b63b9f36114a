<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
        <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
        <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
        <uses-feature android:name="android.hardware.usb.host" />
        <uses-permission android:name="android.permission.USB_PERMISSION" />

    <application
        android:name=".MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher_main"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_main"
        android:supportsRtl="true"
        android:theme="@style/Theme.OpticalSmartReader"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".ui.MomarsaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OpticalSmartReader.NoActionBar" />
        <activity
            android:name=".ui.LostActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OpticalSmartReader.NoActionBar" />
        <activity
            android:name=".ui.LoginActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OpticalSmartReader.NoActionBar"
            tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OpticalSmartReader.NoActionBar"
            tools:ignore="LockedOrientationActivity">
        </activity>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="shoaa.opticalsmartreader.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>
    </application>

</manifest>