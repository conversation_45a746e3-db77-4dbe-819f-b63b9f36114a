<component name="libraryTable">
  <library name="Gradle: androidx.legacy:legacy-support-core-ui:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/871673b8f8cc868587bffeaa6148555e/transformed/legacy-support-core-ui-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/483df48e5a6f17ba9ff1630b5484e01e/transformed/legacy-support-core-ui-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d84fdd4130cd2f3ffb025c9cbca0c2c2/transformed/legacy-support-core-ui-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/efeeff8fd3a5474c1a290a21df4ef150/transformed/legacy-support-core-ui-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/74887f67f682d0e52384d90a23ac1fb8/transformed/legacy-support-core-ui-1.0.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/efeeff8fd3a5474c1a290a21df4ef150/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/efeeff8fd3a5474c1a290a21df4ef150/transformed/legacy-support-core-ui-1.0.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.legacy/legacy-support-core-ui/1.0.0/f6044eaebe354c778f1f147ddb9e92a3f1e22fc7/legacy-support-core-ui-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>