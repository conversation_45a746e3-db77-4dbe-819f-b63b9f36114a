package com.esmc.protocol.model;

import com.alibaba.fastjson.annotation.JSONField;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/StatusObject.class */
public class StatusObject {
    @J<PERSON>NField(ordinal = 1)
    private String meterStatus;
    @J<PERSON><PERSON>ield(ordinal = 2)
    private String relayStatus;
    @J<PERSON>NField(ordinal = 3)
    private String batteryStatus;

    public String getBatteryStatus() {
        return this.batteryStatus;
    }

    public void setBatteryStatus(String batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public String getMeterStatus() {
        return this.meterStatus;
    }

    public void setMeterStatus(String meterStatus) {
        this.meterStatus = meterStatus;
    }

    public String getRelayStatus() {
        return this.relayStatus;
    }

    public void setRelayStatus(String relayStatus) {
        this.relayStatus = relayStatus;
    }
}
