package shoaa.opticalsmartreader.models;

import androidx.annotation.NonNull;

import com.google.gson.Gson;

public class ShowReadedMeterData {
    private String mnteka;
    private String yomea;
    private String hesab;
    private String far3y;
    private String customerId;
    private String address;

    public ShowReadedMeterData(String mnteka, String yomea, String hesab, String far3y,
                               String customerId, String address ) {
        this.mnteka = mnteka;
        this.yomea = yomea;
        this.hesab = hesab;
        this.far3y = far3y;
        this.customerId = customerId;
        this.address = address;
    }

    public String getMnteka() {
        return mnteka;
    }

    public void setMnteka(String mnteka) {
        this.mnteka = mnteka;
    }

    public String getYomea() {
        return yomea;
    }

    public void setYomea(String yomea) {
        this.yomea = yomea;
    }

    public String getHesab() {
        return hesab;
    }

    public void setHesab(String hesab) {
        this.hesab = hesab;
    }

    public String getFar3y() {
        return far3y;
    }

    public void setFar3y(String far3y) {
        this.far3y = far3y;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String toJson() {
        return new Gson().toJson(this);
    }

}
