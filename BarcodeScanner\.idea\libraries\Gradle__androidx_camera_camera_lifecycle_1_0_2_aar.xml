<component name="libraryTable">
  <library name="Gradle: androidx.camera:camera-lifecycle:1.0.2@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/395519036fb20931340ce0a0201edae3/transformed/jetified-camera-lifecycle-1.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/897790323694fe8dd960109f3cf2d71f/transformed/jetified-camera-lifecycle-1.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/57cf83cd1f73184594dc47dd364bc52a/transformed/jetified-camera-lifecycle-1.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d67f18b3a0db3a622c3dcee48b9ab63b/transformed/jetified-camera-lifecycle-1.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dac2ec731f8604830121e51d8dc13b20/transformed/jetified-camera-lifecycle-1.0.2/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d67f18b3a0db3a622c3dcee48b9ab63b/transformed/jetified-camera-lifecycle-1.0.2/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/d67f18b3a0db3a622c3dcee48b9ab63b/transformed/jetified-camera-lifecycle-1.0.2/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.camera/camera-lifecycle/1.0.2/f71daf6982e489ab25e2bdafbe4bd4e6155fd6f0/camera-lifecycle-1.0.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>