package shoaa.opticalsmartreader.logic;

import androidx.room.Database;
import androidx.room.RoomDatabase;

import shoaa.opticalsmartreader.logic.CycleDate.CycleDateDao;
import shoaa.opticalsmartreader.logic.MeterData.DatabaseMeterData;
import shoaa.opticalsmartreader.logic.MeterData.MetersDataDao;
import shoaa.opticalsmartreader.logic.getAllClients.ClientDao;
import shoaa.opticalsmartreader.models.Client;
import shoaa.opticalsmartreader.models.CycleDate;

@Database(entities = {Client.class, DatabaseMeterData.class, CycleDate.class}, version = 5, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    public abstract ClientDao clientDao();

    public abstract MetersDataDao metersDataDao();

    public abstract CycleDateDao cycleDateDao();
}