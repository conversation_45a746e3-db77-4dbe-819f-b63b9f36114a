<component name="libraryTable">
  <library name="Gradle: androidx.transition:transition:1.2.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/1b4e909d27bbec08f0e1c86d945dbdcf/transformed/transition-1.2.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/1b4e909d27bbec08f0e1c86d945dbdcf/transformed/transition-1.2.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1b4e909d27bbec08f0e1c86d945dbdcf/transformed/transition-1.2.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/1b4e909d27bbec08f0e1c86d945dbdcf/transformed/transition-1.2.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.transition/transition/1.2.0/65d2a5dab39f120d3f584fdead252ce81ec7dbee/transition-1.2.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>