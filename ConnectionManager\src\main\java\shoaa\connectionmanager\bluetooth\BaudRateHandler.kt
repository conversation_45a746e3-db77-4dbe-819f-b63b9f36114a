package shoaa.connectionmanager.bluetooth

import shoaa.connectionmanager.BaudRate
import shoaa.connectionmanager.TextUtil.toHexString
import java.nio.charset.StandardCharsets

/**
 * Created by <PERSON> Darwish
 */
internal class BaudRateHandler {
    companion object {

        var res = ""
        fun setBaudRateAsync(
            bluetoothHandler: BluetoothHandler,
            baudRate: BaudRate,
            bluetoothDeviceCo: BluetoothDeviceCo?
        ): Boolean {
            res = ""
            when (bluetoothDeviceCo) {
                BluetoothDeviceCo.ZPA -> {
                    when (baudRate) {
                        BaudRate.BAUD_9600_8_N_1 -> {
                            bluetoothHandler.sendAsync("FEFE5A50413130384E3035FF", 0, 500, 500)
                        }

                        BaudRate.BAUD_2400_7_E_1 -> {
                            bluetoothHandler.sendAsync("FEFE5A5041313037453033FF", 0, 500, 500)
                        }

                        BaudRate.BAUD_300_7_E_1 -> {
                            bluetoothHandler.sendAsync("FEFE5A5041313037453030FF", 0, 500, 500)
                        }

                        BaudRate.BAUD_4800_8_N_1 -> {
                            bluetoothHandler.sendAsync("FEFE5A50413130384E3134FF", 0, 500, 500)
                        }
                    }
                    return true
                }

                BluetoothDeviceCo.TESPRO -> {
                    if (baudRate == BaudRate.BAUD_9600_8_N_1) {
                        res = bluetoothHandler.sendWithLengthAsync(
                            "426175645472616E2C393630302C4E2C382C300D0A", 3, 3000, 12
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063035310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_2400_7_E_1) {
                        res = bluetoothHandler.sendWithLengthAsync(
                            "426175645472616E2C323430302C452C372C300D0A", 3, 3000, 12
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063033310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_300_7_E_1) {
                        res = bluetoothHandler.sendWithLengthAsync(
                            "426175645472616E2C3330302C452C372C300D0A", 3, 3000, 12
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063030300D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_4800_8_N_1) {
                        res = bluetoothHandler.sendWithLengthAsync(
                            "426175645472616D2C343830302C4E2C382C300D0A", 3, 3000, 12
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063034310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else {
                        return false
                    }
                    return res.contains("4F4B2C", ignoreCase = true)
                }

                BluetoothDeviceCo.BLUESKY -> {
                    if (baudRate == BaudRate.BAUD_9600_8_N_1) {
                        bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"BAUDRATE\":\"9600\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
                        res = bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"FRAME\":\"8N1\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063035310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_2400_7_E_1) {
                        bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"BAUDRATE\":\"2400\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
                        res = bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"FRAME\":\"7E1\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063033310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_300_7_E_1) {
                        bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"BAUDRATE\":\"300\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
                        res = bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"FRAME\":\"7E1\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063030300D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else if (baudRate == BaudRate.BAUD_4800_8_N_1) {
                        bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"BAUDRATE\":\"4800\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
                        res = bluetoothHandler.sendAsync(
                            toHexString(
                                "{\"FRAME\":\"8N1\"}".toByteArray(
                                    StandardCharsets.UTF_8
                                )
                            ), 3, 3000, 500
                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063034310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
                    } else {
                        return false
                    }
                    return res.contains("224F4B22", ignoreCase = true)
                }

                else -> return false
            }
        }

        fun getSendData(
            baudRate: BaudRate,
            bluetoothDeviceCo: BluetoothDeviceCo?
        ): String {
            when (bluetoothDeviceCo) {
                BluetoothDeviceCo.ZPA -> {
                    return when (baudRate) {
                        BaudRate.BAUD_9600_8_N_1 -> {
                            "FEFE5A50413130384E3035FF"
                        }

                        BaudRate.BAUD_2400_7_E_1 -> {
                            "FEFE5A5041313037453033FF"
                        }

                        BaudRate.BAUD_300_7_E_1 -> {
                            "FEFE5A5041313037453030FF"
                        }

                        BaudRate.BAUD_4800_8_N_1 -> {
                            "FEFE5A50413130384E3134FF"
                        }
                    }
                }

                BluetoothDeviceCo.TESPRO -> {
                    return when (baudRate) {
                        BaudRate.BAUD_9600_8_N_1 -> {
                            "426175645472616E2C393630302C4E2C382C300D0A"
                        }

                        BaudRate.BAUD_2400_7_E_1 -> {
                            "426175645472616E2C323430302C452C372C300D0A"

                        }

                        BaudRate.BAUD_300_7_E_1 -> {
                            "426175645472616E2C3330302C452C372C300D0A"
                        }

                        BaudRate.BAUD_4800_8_N_1 -> {
                            "426175645472616D2C343830302C4E2C382C300D0A"
                        }
                    }
                }

//                BluetoothDeviceCo.BLUESKY -> {
//                    if (baudRate == BaudRate.BaudTran_9600_8_N_1) {
//                        bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"BAUDRATE\":\"9600\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        res = bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"FRAME\":\"8N1\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063035310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
//                    } else if (baudRate == BaudRate.BaudTran_2400_7_E_1) {
//                        bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"BAUDRATE\":\"2400\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        res = bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"FRAME\":\"7E1\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063033310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
//                    } else if (baudRate == BaudRate.BaudTran_300_7_E_1) {
//                        bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"BAUDRATE\":\"300\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        res = bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"FRAME\":\"7E1\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063030300D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
//                    } else if (baudRate == BaudRate.BaudTran_4800_8_N_1) {
//                        bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"BAUDRATE\":\"4800\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        res = bluetoothHandler.sendAsync(
//                            toHexString(
//                                "{\"FRAME\":\"8N1\"}".toByteArray(
//                                    StandardCharsets.UTF_8
//                                )
//                            ), 3, 3000, 500
//                        )
//                        if (init) {
//                            bluetoothHandler.sendAsync("2F3F210D0A", 0, 600, 300)
//                            bluetoothHandler.sendAsync("063034310D0A", 0, 600, 300)
//                            bluetoothHandler.setBaudRateAsync(baudRate, false)
//                        }
//                    } else {
//                        return false
//                    }
//                    return res.contains("224F4B22", ignoreCase = true)
//                }

                else -> return ""
            }
        }
    }
}
