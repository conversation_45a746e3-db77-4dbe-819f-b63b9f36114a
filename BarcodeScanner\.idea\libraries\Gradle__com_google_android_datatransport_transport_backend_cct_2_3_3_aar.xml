<component name="libraryTable">
  <library name="Gradle: com.google.android.datatransport:transport-backend-cct:2.3.3@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/efc041471abbafc271ecd2528b4002f6/transformed/jetified-transport-backend-cct-2.3.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/d879be0017873b61fcffbe2903e6c147/transformed/jetified-transport-backend-cct-2.3.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/a261bb67233a27858a469b722fc9259e/transformed/jetified-transport-backend-cct-2.3.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/55fe4c7af5ba4fd8c97a541930e8b936/transformed/jetified-transport-backend-cct-2.3.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/0a5f324fce96ed4104ccb3771ee15757/transformed/jetified-transport-backend-cct-2.3.3/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/55fe4c7af5ba4fd8c97a541930e8b936/transformed/jetified-transport-backend-cct-2.3.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/55fe4c7af5ba4fd8c97a541930e8b936/transformed/jetified-transport-backend-cct-2.3.3/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.datatransport/transport-backend-cct/2.3.3/9fa8e4ca037ec96af8366608c0fe6ee01ee96b40/transport-backend-cct-2.3.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>