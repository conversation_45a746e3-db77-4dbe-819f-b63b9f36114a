package shoaa.common;

import android.content.Context;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class Utils {

    public static void writeStringAsFile(Context context, final String fileContents, String fileName) {
        try {
            if (!context.getPackageName().equalsIgnoreCase("shoaa.opticalsmartreader")) {
                return;
            }
            File outDir = new File(context.getFilesDir(), "dataDir");
            if (!outDir.exists())
                outDir.mkdirs();
            File outFile = new File(outDir, fileName);
            if (outFile.exists()) {
                outFile.delete();
            }
            FileWriter out = new FileWriter(outFile);
            out.write(fileContents);
            out.close();
        } catch (IOException ignored) {

        }
    }
}
