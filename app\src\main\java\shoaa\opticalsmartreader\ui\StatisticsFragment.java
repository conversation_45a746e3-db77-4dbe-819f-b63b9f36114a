package shoaa.opticalsmartreader.ui;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import shoaa.opticalsmartreader.FileEncryptionUtil;
import shoaa.opticalsmartreader.MainApplication;
import shoaa.opticalsmartreader.R;
import shoaa.opticalsmartreader.logic.AppDatabase;
import shoaa.opticalsmartreader.logic.Utils;
import shoaa.opticalsmartreader.models.AppUser;
import shoaa.opticalsmartreader.models.MiniDatabaseMeterData;

public class StatisticsFragment extends Fragment {

    int readed = 0;
    int closed = 0;
    int error = 0;
    int notRead = 0;
    int notReach = 0;
    int broken = 0;
    int stolen = 0;
    int up = 0;
    int notFound = 0;
    int lost = 0;
    int beach = 0;
    int notConnected = 0;
    int refused = 0;
    int violation = 0;
    int destroy = 0;

    int lost_readed = 0;
    int lost_error = 0;
    int lost_notRead = 0;
    int lost_notReach = 0;
    int lost_broken = 0;
    int lost_stolen = 0;
    int lost_notConnected = 0;
    int lost_violation = 0;
    int lost_momarsat = 0;

    Button btnShowLostDetails;
    TextView tvTotal;
    TextView tvReaded;
    TextView tvClosed;
    TextView tvCanNotReach;
    TextView tvCanNotRead;
    TextView tvError;
    TextView tvBroken;
    TextView tvStolen;
    TextView tvUp;
    TextView tvNotFound;
    TextView tvLost;
    TextView tvBeach;
    TextView tvNotConnected;
    TextView tvRefused;
    TextView tvViolation;
    TextView tvDestroy;

    public StatisticsFragment() {
        // Required empty public constructor
    }

    public static void copy(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src)) {
            try (OutputStream out = new FileOutputStream(dst)) {
                // Transfer bytes from in to out
                byte[] buf = new byte[1024];
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
            }
        }catch (Exception e){
            Log.d("popopopop00", "copy: "+ e.getMessage());
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View layout = inflater.inflate(R.layout.fragment_statistics, container, false);
        tvTotal = layout.findViewById(R.id.tvTotal);
        tvReaded = layout.findViewById(R.id.tvReaded);
        tvClosed = layout.findViewById(R.id.tvClosed);
        tvCanNotReach = layout.findViewById(R.id.tvCanNotReach);
        tvCanNotRead = layout.findViewById(R.id.tvCanNotRead);
        tvError = layout.findViewById(R.id.tvError);
        tvBroken = layout.findViewById(R.id.tvBroken);
        tvStolen = layout.findViewById(R.id.tvStolen);
        tvUp = layout.findViewById(R.id.tvUp);
        tvNotFound = layout.findViewById(R.id.tvNotFound);
        tvLost = layout.findViewById(R.id.tvLost);
        tvBeach = layout.findViewById(R.id.tvBeach);
        tvNotConnected = layout.findViewById(R.id.tvNotConnected);
        tvRefused = layout.findViewById(R.id.tvRefused);
        tvViolation = layout.findViewById(R.id.tvViolation);
        tvDestroy = layout.findViewById(R.id.tvDestroy);
        btnShowLostDetails = layout.findViewById(R.id.btn_show_lost_details);
        ImageButton btnErase = layout.findViewById(R.id.btnErase);
        TextView txtErase = layout.findViewById(R.id.txtErase);
        layout.findViewById(R.id.btn_logout).setOnClickListener(view -> showLogoutDialog());
        refresh();
        View.OnClickListener onClickListener = view -> new AlertDialog.Builder(((MainActivity) MainApplication.getInstance().getCurrentActivity()))
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setTitle("محو بيانات القراءة")
                .setMessage("هل انت متاكد من محو البيانات ؟")
                .setPositiveButton("نعم", (dialog, which) -> {
                    PasswordDialog passwordDialog = new PasswordDialog(((MainActivity) MainApplication.getInstance().getCurrentActivity()), text -> {
                        if (text != null && !text.isEmpty()) {
                            if (text.equals(AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getPassword())) {
                                new AlertDialog.Builder(((MainActivity) MainApplication.getInstance().getCurrentActivity()))
                                        .setIcon(android.R.drawable.ic_dialog_alert)
                                        .setTitle("محو بيانات القراءة")
                                        .setMessage("هل انت متاكد من محو البيانات ؟")
                                        .setPositiveButton("نعم", (dialog1, which1) -> {

                                            new Thread(() -> {
                                                if (!Utils.appDatabase.isOpen())
                                                    Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                                Utils.appDatabase.cycleDateDao().delete();
                                                if (!Utils.appDatabase.isOpen())
                                                    Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                                Utils.appDatabase.clientDao().deleteAll();
                                                if (!Utils.appDatabase.isOpen())
                                                    Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                                Utils.appDatabase.metersDataDao().deleteAll();

                                                File dir = new File(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getFilesDir(), "Backup");
                                                if (!dir.exists())
                                                    dir.mkdirs();
                                                File dbFile = new File(dir, "backup_" + AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getBrHndsa() + "_" + AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).getUSER_ID() + ".db");
                                                if (dbFile.exists())
                                                    dbFile.delete();
//
                                                try {
                                                    if (!Utils.appDatabase.isOpen())
                                                        Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                                                    copy(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getDatabasePath("shoaa_database"), dbFile);
                                                    //FileEncryptionUtil.encryptFile(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getDatabasePath("shoaa_database"), dbFile);
                                                    if (Utils.appDatabase.isOpen())
                                                        Utils.appDatabase.close();
                                                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).getDatabasePath("shoaa_database").delete();
                                                    refresh();
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                } catch (Exception e) {
                                                    throw new RuntimeException(e);
                                                }

                                                ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.loadClientsData();

                                            }).start();
                                        })
                                        .setNegativeButton("لا", null)
                                        .show();
                            } else {
                                new AlertDialog.Builder(((MainActivity) MainApplication.getInstance().getCurrentActivity()))
                                        .setIcon(android.R.drawable.ic_dialog_alert)
                                        .setTitle("خطأ")
                                        .setMessage("كلمة المرور غير صحيحة")
                                        .setPositiveButton("اغلاق", null)
                                        .show();
                            }
                        }
                    });
                    passwordDialog.show();
                })
                .setNegativeButton("لا", null)
                .show();
        btnErase.setOnClickListener(onClickListener);
        txtErase.setOnClickListener(onClickListener);

        btnShowLostDetails.setOnClickListener(v -> {
            final Dialog dialog = new Dialog(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            dialog.setCancelable(true);
            dialog.setContentView(R.layout.lost_statistics_dialog);

            dialog.findViewById(R.id.btn_back).setOnClickListener(v1 -> dialog.dismiss());
            ((TextView) dialog.findViewById(R.id.tvTotal_lost)).setText(String.valueOf(lost));
            ((TextView) dialog.findViewById(R.id.tvReaded_lost)).setText(String.valueOf(lost_readed));
            ((TextView) dialog.findViewById(R.id.tvCanNotReach_lost)).setText(String.valueOf(lost_notReach));
            ((TextView) dialog.findViewById(R.id.tvCanNotRead_lost)).setText(String.valueOf(lost_notRead));
            ((TextView) dialog.findViewById(R.id.tvError_lost)).setText(String.valueOf(lost_error));
            ((TextView) dialog.findViewById(R.id.tvBroken_lost)).setText(String.valueOf(lost_broken));
            ((TextView) dialog.findViewById(R.id.tvStolen_lost)).setText(String.valueOf(lost_stolen));
            ((TextView) dialog.findViewById(R.id.tvNotConnected_lost)).setText(String.valueOf(lost_notConnected));
            ((TextView) dialog.findViewById(R.id.tvViolation_lost)).setText(String.valueOf(lost_violation));
            ((TextView) dialog.findViewById(R.id.tvMomarsa_lost)).setText(String.valueOf(lost_momarsat));

            dialog.show();
            if (dialog.getWindow() != null) {
                WindowManager.LayoutParams lp = new WindowManager.LayoutParams();
                lp.copyFrom(dialog.getWindow().getAttributes());
                lp.width = WindowManager.LayoutParams.MATCH_PARENT;
                lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
                dialog.getWindow().setAttributes(lp);
            }


        });
        return layout;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).checkTimeAutomatic();
        AppUser appUser = AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
        if (appUser == null ||
                appUser.getUSER_ID() == null ||
                appUser.getUSER_ID().isEmpty() ||
                appUser.getUserName() == null ||
                appUser.getUserName().isEmpty() ||
                appUser.getPassword() == null ||
                appUser.getPassword().isEmpty()) {
            Utils.sharedPreferences.edit().putString("app_user", "").apply();
            Intent i = new Intent(((MainActivity) MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
            ((MainActivity) MainApplication.getInstance().getCurrentActivity()).finish();
            startActivity(i);
        }
        if (!hidden) {
            refresh();
        }
    }

    private void refresh() {
        readed = 0;
        closed = 0;
        error = 0;
        notRead = 0;
        notReach = 0;
        broken = 0;
        stolen = 0;
        up = 0;
        notFound = 0;
        lost = 0;
        beach = 0;
        notConnected = 0;
        refused = 0;
        violation = 0;
        destroy = 0;
        lost_readed = 0;
        lost_error = 0;
        lost_notRead = 0;
        lost_notReach = 0;
        lost_broken = 0;
        lost_stolen = 0;
        lost_notConnected = 0;
        lost_violation = 0;
        lost_momarsat = 0;

        if (!Utils.appDatabase.isOpen())
            Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").allowMainThreadQueries().enableMultiInstanceInvalidation().setJournalMode(RoomDatabase.JournalMode.TRUNCATE).fallbackToDestructiveMigration().build();

        new Thread(() -> {
            List<MiniDatabaseMeterData> databaseMeterDataList = Utils.appDatabase.metersDataDao().getAllMini();
            for (MiniDatabaseMeterData databaseMeterData : databaseMeterDataList) {
                switch (databaseMeterData.READING_CODE) {
                    case 0:
                        readed++;
                        break;
                    case 1:
                        closed++;
                        break;
                    case 2:
                        if (databaseMeterData.DAMAGE_CODE == 1)
                            notReach++;
                        else if (databaseMeterData.DAMAGE_CODE == 2)
                            notRead++;
                        else
                            error++;
                        break;
                    case 3:
                        broken++;
                        break;
                    case 4:
                        stolen++;
                        break;
                    case 5:
                        up++;
                        break;
                    case 6:
                        notFound++;
                        break;
                    case 7:
                        lost++;
                        switch (databaseMeterData.LOST_READING_CODE) {
                            case 0:
                                lost_readed++;
                                break;
                            case 2:
                                if (databaseMeterData.DAMAGE_CODE == 1)
                                    lost_notReach++;
                                else if (databaseMeterData.DAMAGE_CODE == 2)
                                    lost_notRead++;
                                else
                                    lost_error++;
                                break;
                            case 3:
                                lost_broken++;
                                break;
                            case 4:
                                lost_stolen++;
                                break;
                            case 9:
                                lost_notConnected++;
                                break;
                            case 12:
                                lost_violation++;
                                break;
                            case 14:
                                lost_momarsat++;
                                break;
                        }
                        break;
                    case 8:
                        beach++;
                        break;
                    case 9:
                        notConnected++;
                        break;
                    case 10:
                        refused++;
                        break;
                    case 12:
                        violation++;
                        break;
                    case 13:
                        destroy++;
                        break;
                }
            }
            MainApplication.getInstance().getCurrentActivity().runOnUiThread(() -> {
                tvTotal.setText(String.valueOf(databaseMeterDataList.size()));
                tvReaded.setText(String.valueOf(readed));
                tvClosed.setText(String.valueOf(closed));
                tvError.setText(String.valueOf(error));
                tvCanNotRead.setText(String.valueOf(notRead));
                tvCanNotReach.setText(String.valueOf(notReach));
                tvBroken.setText(String.valueOf(broken));
                tvStolen.setText(String.valueOf(stolen));
                tvUp.setText(String.valueOf(up));
                tvNotFound.setText(String.valueOf(notFound));
                tvLost.setText(String.valueOf(lost));
                tvBeach.setText(String.valueOf(beach));
                tvNotConnected.setText(String.valueOf(notConnected));
                tvRefused.setText(String.valueOf(refused));
                tvViolation.setText(String.valueOf(violation));
                tvDestroy.setText(String.valueOf(destroy));
            });
        }).start();
    }

    void showLogoutDialog() {
        new AlertDialog.Builder(((MainActivity) MainApplication.getInstance().getCurrentActivity()))
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setTitle("تسجيل خروج")
                .setMessage("هل انت متاكد من تسجيل الخروج ؟")
                .setPositiveButton("نعم", (dialog, which) -> new Thread(() -> {
                    if (!Utils.appDatabase.isOpen())
                        Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                    Utils.appDatabase.cycleDateDao().delete();
                    if (!Utils.appDatabase.isOpen())
                        Utils.appDatabase = Room.databaseBuilder(((MainActivity) MainApplication.getInstance().getCurrentActivity()).getApplicationContext(), AppDatabase.class, "shoaa_database").setJournalMode(RoomDatabase.JournalMode.TRUNCATE).allowMainThreadQueries().enableMultiInstanceInvalidation().fallbackToDestructiveMigration().build();
                    Utils.appDatabase.clientDao().deleteAll();
                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).customersDataFragment.loadClientsData();
                    refresh();
                    ((MainActivity) MainApplication.getInstance().getCurrentActivity()).runOnUiThread(() -> {
                        Utils.sharedPreferences.edit().putString("app_user", "").apply();
                        AppUser.getInstance(((MainActivity) MainApplication.getInstance().getCurrentActivity())).logut(((MainActivity) MainApplication.getInstance().getCurrentActivity()));
                        Intent i = new Intent(((MainActivity) MainApplication.getInstance().getCurrentActivity()), LoginActivity.class);
                        startActivity(i);
                        ((MainActivity) MainApplication.getInstance().getCurrentActivity()).finish();  //Kill the activity from which you will go to next activity
                    });
                }).start())
                .setNegativeButton("لا", null)
                .show();
    }
}