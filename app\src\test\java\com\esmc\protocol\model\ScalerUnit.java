package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/ScalerUnit.class */
public class ScalerUnit {
    private int scaler;
    private String unit;

    public ScalerUnit() {
        this.scaler = 0;
        this.unit = "";
    }

    public ScalerUnit(int scaler, String unit) {
        this.scaler = scaler;
        this.unit = unit;
    }

    public int getScaler() {
        return this.scaler;
    }

    public void setScaler(int scaler) {
        this.scaler = scaler;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
