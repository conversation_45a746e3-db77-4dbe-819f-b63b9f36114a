package shoaa.opticalsmartreader.logic.MeterData;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import shoaa.opticalsmartreader.models.MiniDatabaseMeterData;

@Dao
public interface MetersDataDao {
    @Query("SELECT * FROM meters_data")
    List<DatabaseMeterData> getAll();

    @Query("SELECT COVER_METER_ID, READING_DATA_FLAG, READING_CODE ,LOST_READING_CODE,DAMAGE_CODE ,FACTORY_CODE FROM meters_data")
    List<MiniDatabaseMeterData> getAllMini();

    @Query("SELECT * FROM meters_data WHERE (COVER_METER_ID LIKE :barcode AND FACTORY_CODE LIKE :factoryCode) LIMIT 1")
    DatabaseMeterData find(long barcode, int factoryCode);

    @Query("SELECT * FROM meters_data WHERE (FACTORY_CODE LIKE :FACTORY_CODE AND ITEM_1_NEW_BASEITEM_Meter_ID LIKE :ITEM_1_NEW_BASEITEM_Meter_ID AND ITEM_3_NEW_BASEITEM_CardID LIKE :ITEM_3_NEW_BASEITEM_CardID  AND ITEM_16_NEW_BASEITEM_Top_cover_status LIKE :ITEM_16_NEW_BASEITEM_Top_cover_status AND ITEM_17_NEW_BASEITEM_Side_cover_status LIKE :ITEM_17_NEW_BASEITEM_Side_cover_status) LIMIT 1")
    DatabaseMeterData find(int FACTORY_CODE, long ITEM_1_NEW_BASEITEM_Meter_ID, String ITEM_3_NEW_BASEITEM_CardID, int ITEM_16_NEW_BASEITEM_Top_cover_status, int ITEM_17_NEW_BASEITEM_Side_cover_status);

    @Query("SELECT * FROM meters_data WHERE (FACTORY_CODE LIKE :FACTORY_CODE AND ITEM_1_NEW_BASEITEM_Meter_ID LIKE :ITEM_1_NEW_BASEITEM_Meter_ID AND OPTICAL_Customer_ID LIKE :OPTICAL_Customer_ID AND ITEM_3_NEW_BASEITEM_CardID LIKE :ITEM_3_NEW_BASEITEM_CardID AND  ITEM_16_NEW_BASEITEM_Top_cover_status LIKE :ITEM_16_NEW_BASEITEM_Top_cover_status AND ITEM_17_NEW_BASEITEM_Side_cover_status LIKE :ITEM_17_NEW_BASEITEM_Side_cover_status) LIMIT 1")
    DatabaseMeterData find(int FACTORY_CODE, long ITEM_1_NEW_BASEITEM_Meter_ID, String OPTICAL_Customer_ID, String ITEM_3_NEW_BASEITEM_CardID, int ITEM_16_NEW_BASEITEM_Top_cover_status, int ITEM_17_NEW_BASEITEM_Side_cover_status);

    @Query("SELECT COVER_METER_ID, READING_DATA_FLAG, READING_CODE,LOST_READING_CODE, DAMAGE_CODE, FACTORY_CODE FROM meters_data WHERE (COVER_METER_ID LIKE :barcode AND FACTORY_CODE LIKE :factoryCode ) LIMIT 1")
    MiniDatabaseMeterData findByMini(long barcode, int factoryCode);

    @Insert
    void insert(DatabaseMeterData databaseMeterData);

    @Delete
    void delete(DatabaseMeterData databaseMeterData);

    @Query("DELETE FROM meters_data")
    void deleteAll();

    @Update
    void update(DatabaseMeterData databaseMeterData);
}
