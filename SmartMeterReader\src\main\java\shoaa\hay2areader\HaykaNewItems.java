package shoaa.hay2areader;

import com.google.gson.Gson;

public class HaykaNewItems {

    private String Meter_ID;
    private String Technical_code_event_3;
    private String Technical_code_event_2;
    private String Technical_code_event_1;
    private String Current_Demand;
    private String event_type_1;
    private String Recharge_Amount;
    private String event_type_3;
    private String event_type_2;
    private String Total_consumption_kw;
    private String Maximum_Demand;
    private String unbalance_Kwh;
    private String last_year_Power_factor;
    private String Top_cover_status;
    private String CardID;
    private String Total_consumption_mony;
    private String battery_status;
    private String Side_cover_status;
    private String remaining_credit_kw;
    private String event_Date_1;
    private String Customer_ID;
    private String event_Date_2;
    private String Relay_status;
    private String event_Date_3;
    private String curent_Power_factor;
    private String Debts;
    private String reverse_Kwh;
    private String remaining_credit_mony;
    private String instanteneous_current_Neutral;
    private String installing_Date_and_time;
    private String Total_consumption_kvar;
    private String Current_tariff_activation_date;
    private String Maximum_Demand_date;
    private String instanteneous_volt;
    private String installing_technican_code;
    private String ActivityType;
    private String Current_tarrif_installing;
    private String Meter_Date_and_Time;
    private String instanteneous_current_Phase_Ampere;
    private String recharge_number;
    private String Last_recharge_Date_And_Time;
    private String current_month_consumption_KW;
    private String meterType;
    private String current_month_consumption_MONY;
    private String Meter_status;
    private String fw_version;

    private String _1_month_consumption_kWh;
    private String _1_month_consumption_Mony;
    private String _2_month_consumption_kWh;
    private String _2_month_consumption_Mony;
    private String _3_month_consumption_Mony;
    private String _3_month_consumption_kWh;
    private String _4_month_consumption_kWh;
    private String _4_month_consumption_Mony;
    private String _5_month_consumption_kWh;
    private String _5_month_consumption_Mony;
    private String _6_month_consumption_kWh;
    private String _6_month_consumption_Mony;
    private String _7_month_consumption_kWh;
    private String _7_month_consumption_Mony;
    private String _8_month_consumption_kWh;
    private String _8_month_consumption_Mony;
    private String _9_month_consumption_kWh;
    private String _9_month_consumption_Mony;
    private String _10_month_consumption_Mony;
    private String _10_month_consumption_kWh;
    private String _11_month_consumption_kWh;
    private String _11_month_consumption_Mony;
    private String _12_month_consumption_kWh;
    private String _12_month_consumption_Mony;



    public HaykaNewItems fromJson(String json){
        return new Gson().fromJson(json,HaykaNewItems.class);
    }


    public String get_12_month_consumption_Mony() {
        return _12_month_consumption_Mony;
    }

    public void set_12_month_consumption_Mony(String _12_month_consumption_Mony) {
        this._12_month_consumption_Mony = _12_month_consumption_Mony;
    }

    public String get_4_month_consumption_kWh() {
        return _4_month_consumption_kWh;
    }

    public void set_4_month_consumption_kWh(String _4_month_consumption_kWh) {
        this._4_month_consumption_kWh = _4_month_consumption_kWh;
    }

    public String get_2_month_consumption_Mony() {
        return _2_month_consumption_Mony;
    }

    public void set_2_month_consumption_Mony(String _2_month_consumption_Mony) {
        this._2_month_consumption_Mony = _2_month_consumption_Mony;
    }

    public String get_6_month_consumption_Mony() {
        return _6_month_consumption_Mony;
    }

    public void set_6_month_consumption_Mony(String _6_month_consumption_Mony) {
        this._6_month_consumption_Mony = _6_month_consumption_Mony;
    }

    public String getTechnical_code_event_3() {
        return Technical_code_event_3;
    }

    public void setTechnical_code_event_3(String technical_code_event_3) {
        Technical_code_event_3 = technical_code_event_3;
    }

    public String getTechnical_code_event_2() {
        return Technical_code_event_2;
    }

    public void setTechnical_code_event_2(String technical_code_event_2) {
        Technical_code_event_2 = technical_code_event_2;
    }

    public String getTechnical_code_event_1() {
        return Technical_code_event_1;
    }

    public void setTechnical_code_event_1(String technical_code_event_1) {
        Technical_code_event_1 = technical_code_event_1;
    }

    public String getCurrent_Demand() {
        return Current_Demand;
    }

    public void setCurrent_Demand(String current_Demand) {
        Current_Demand = current_Demand;
    }

    public String getEvent_type_1() {
        return event_type_1;
    }

    public void setEvent_type_1(String event_type_1) {
        this.event_type_1 = event_type_1;
    }

    public String getRecharge_Amount() {
        return Recharge_Amount;
    }

    public void setRecharge_Amount(String recharge_Amount) {
        Recharge_Amount = recharge_Amount;
    }

    public String getEvent_type_3() {
        return event_type_3;
    }

    public void setEvent_type_3(String event_type_3) {
        this.event_type_3 = event_type_3;
    }

    public String get_3_month_consumption_kWh() {
        return _3_month_consumption_kWh;
    }

    public void set_3_month_consumption_kWh(String _3_month_consumption_kWh) {
        this._3_month_consumption_kWh = _3_month_consumption_kWh;
    }

    public String getEvent_type_2() {
        return event_type_2;
    }

    public void setEvent_type_2(String event_type_2) {
        this.event_type_2 = event_type_2;
    }

    public String getTotal_consumption_kw() {
        return Total_consumption_kw;
    }

    public void setTotal_consumption_kw(String total_consumption_kw) {
        Total_consumption_kw = total_consumption_kw;
    }

    public String getMaximum_Demand() {
        return Maximum_Demand;
    }

    public void setMaximum_Demand(String maximum_Demand) {
        Maximum_Demand = maximum_Demand;
    }

    public String get_7_month_consumption_Mony() {
        return _7_month_consumption_Mony;
    }

    public void set_7_month_consumption_Mony(String _7_month_consumption_Mony) {
        this._7_month_consumption_Mony = _7_month_consumption_Mony;
    }

    public String getUnbalance_Kwh() {
        return unbalance_Kwh;
    }

    public void setUnbalance_Kwh(String unbalance_Kwh) {
        this.unbalance_Kwh = unbalance_Kwh;
    }

    public String getLast_year_Power_factor() {
        return last_year_Power_factor;
    }

    public void setLast_year_Power_factor(String last_year_Power_factor) {
        this.last_year_Power_factor = last_year_Power_factor;
    }

    public String getTop_cover_status() {
        return Top_cover_status;
    }

    public void setTop_cover_status(String top_cover_status) {
        Top_cover_status = top_cover_status;
    }

    public String getCardID() {
        return CardID;
    }

    public void setCardID(String cardID) {
        CardID = cardID;
    }

    public String getTotal_consumption_mony() {
        return Total_consumption_mony;
    }

    public void setTotal_consumption_mony(String total_consumption_mony) {
        Total_consumption_mony = total_consumption_mony;
    }

    public String get_3_month_consumption_Mony() {
        return _3_month_consumption_Mony;
    }

    public void set_3_month_consumption_Mony(String _3_month_consumption_Mony) {
        this._3_month_consumption_Mony = _3_month_consumption_Mony;
    }

    public String getBattery_status() {
        return battery_status;
    }

    public void setBattery_status(String battery_status) {
        this.battery_status = battery_status;
    }

    public String getSide_cover_status() {
        return Side_cover_status;
    }

    public void setSide_cover_status(String side_cover_status) {
        Side_cover_status = side_cover_status;
    }

    public String getRemaining_credit_kw() {
        return remaining_credit_kw;
    }

    public void setRemaining_credit_kw(String remaining_credit_kw) {
        this.remaining_credit_kw = remaining_credit_kw;
    }

    public String getEvent_Date_1() {
        return event_Date_1;
    }

    public void setEvent_Date_1(String event_Date_1) {
        this.event_Date_1 = event_Date_1;
    }

    public String get_5_month_consumption_kWh() {
        return _5_month_consumption_kWh;
    }

    public void set_5_month_consumption_kWh(String _5_month_consumption_kWh) {
        this._5_month_consumption_kWh = _5_month_consumption_kWh;
    }

    public String getCustomer_ID() {
        return Customer_ID;
    }

    public void setCustomer_ID(String customer_ID) {
        Customer_ID = customer_ID;
    }

    public String getEvent_Date_2() {
        return event_Date_2;
    }

    public void setEvent_Date_2(String event_Date_2) {
        this.event_Date_2 = event_Date_2;
    }

    public String getRelay_status() {
        return Relay_status;
    }

    public void setRelay_status(String relay_status) {
        Relay_status = relay_status;
    }

    public String getEvent_Date_3() {
        return event_Date_3;
    }

    public void setEvent_Date_3(String event_Date_3) {
        this.event_Date_3 = event_Date_3;
    }

    public String get_6_month_consumption_kWh() {
        return _6_month_consumption_kWh;
    }

    public void set_6_month_consumption_kWh(String _6_month_consumption_kWh) {
        this._6_month_consumption_kWh = _6_month_consumption_kWh;
    }

    public String getCurent_Power_factor() {
        return curent_Power_factor;
    }

    public void setCurent_Power_factor(String curent_Power_factor) {
        this.curent_Power_factor = curent_Power_factor;
    }

    public String get_4_month_consumption_Mony() {
        return _4_month_consumption_Mony;
    }

    public void set_4_month_consumption_Mony(String _4_month_consumption_Mony) {
        this._4_month_consumption_Mony = _4_month_consumption_Mony;
    }

    public String get_12_month_consumption_kWh() {
        return _12_month_consumption_kWh;
    }

    public void set_12_month_consumption_kWh(String _12_month_consumption_kWh) {
        this._12_month_consumption_kWh = _12_month_consumption_kWh;
    }

    public String getDebts() {
        return Debts;
    }

    public void setDebts(String debts) {
        Debts = debts;
    }

    public String getReverse_Kwh() {
        return reverse_Kwh;
    }

    public void setReverse_Kwh(String reverse_Kwh) {
        this.reverse_Kwh = reverse_Kwh;
    }

    public String get_8_month_consumption_Mony() {
        return _8_month_consumption_Mony;
    }

    public void set_8_month_consumption_Mony(String _8_month_consumption_Mony) {
        this._8_month_consumption_Mony = _8_month_consumption_Mony;
    }

    public String get_7_month_consumption_kWh() {
        return _7_month_consumption_kWh;
    }

    public void set_7_month_consumption_kWh(String _7_month_consumption_kWh) {
        this._7_month_consumption_kWh = _7_month_consumption_kWh;
    }

    public String getRemaining_credit_mony() {
        return remaining_credit_mony;
    }

    public void setRemaining_credit_mony(String remaining_credit_mony) {
        this.remaining_credit_mony = remaining_credit_mony;
    }

    public String get_9_month_consumption_kWh() {
        return _9_month_consumption_kWh;
    }

    public void set_9_month_consumption_kWh(String _9_month_consumption_kWh) {
        this._9_month_consumption_kWh = _9_month_consumption_kWh;
    }

    public String getInstanteneous_current_Neutral() {
        return instanteneous_current_Neutral;
    }

    public void setInstanteneous_current_Neutral(String instanteneous_current_Neutral) {
        this.instanteneous_current_Neutral = instanteneous_current_Neutral;
    }

    public String getInstalling_Date_and_time() {
        return installing_Date_and_time;
    }

    public void setInstalling_Date_and_time(String installing_Date_and_time) {
        this.installing_Date_and_time = installing_Date_and_time;
    }

    public String get_10_month_consumption_Mony() {
        return _10_month_consumption_Mony;
    }

    public void set_10_month_consumption_Mony(String _10_month_consumption_Mony) {
        this._10_month_consumption_Mony = _10_month_consumption_Mony;
    }

    public String getTotal_consumption_kvar() {
        return Total_consumption_kvar;
    }

    public void setTotal_consumption_kvar(String total_consumption_kvar) {
        Total_consumption_kvar = total_consumption_kvar;
    }

    public String get_8_month_consumption_kWh() {
        return _8_month_consumption_kWh;
    }

    public void set_8_month_consumption_kWh(String _8_month_consumption_kWh) {
        this._8_month_consumption_kWh = _8_month_consumption_kWh;
    }

    public String getCurrent_tariff_activation_date() {
        return Current_tariff_activation_date;
    }

    public void setCurrent_tariff_activation_date(String current_tariff_activation_date) {
        Current_tariff_activation_date = current_tariff_activation_date;
    }

    public String getMaximum_Demand_date() {
        return Maximum_Demand_date;
    }

    public void setMaximum_Demand_date(String maximum_Demand_date) {
        Maximum_Demand_date = maximum_Demand_date;
    }

    public String getInstanteneous_volt() {
        return instanteneous_volt;
    }

    public void setInstanteneous_volt(String instanteneous_volt) {
        this.instanteneous_volt = instanteneous_volt;
    }

    public String get_11_month_consumption_Mony() {
        return _11_month_consumption_Mony;
    }

    public void set_11_month_consumption_Mony(String _11_month_consumption_Mony) {
        this._11_month_consumption_Mony = _11_month_consumption_Mony;
    }

    public String getInstalling_technican_code() {
        return installing_technican_code;
    }

    public void setInstalling_technican_code(String installing_technican_code) {
        this.installing_technican_code = installing_technican_code;
    }

    public String getActivityType() {
        return ActivityType;
    }

    public void setActivityType(String activityType) {
        ActivityType = activityType;
    }

    public String getCurrent_tarrif_installing() {
        return Current_tarrif_installing;
    }

    public void setCurrent_tarrif_installing(String current_tarrif_installing) {
        Current_tarrif_installing = current_tarrif_installing;
    }

    public String getMeter_Date_and_Time() {
        return Meter_Date_and_Time;
    }

    public void setMeter_Date_and_Time(String meter_Date_and_Time) {
        Meter_Date_and_Time = meter_Date_and_Time;
    }

    public String get_2_month_consumption_kWh() {
        return _2_month_consumption_kWh;
    }

    public void set_2_month_consumption_kWh(String _2_month_consumption_kWh) {
        this._2_month_consumption_kWh = _2_month_consumption_kWh;
    }

    public String get_9_month_consumption_Mony() {
        return _9_month_consumption_Mony;
    }

    public void set_9_month_consumption_Mony(String _9_month_consumption_Mony) {
        this._9_month_consumption_Mony = _9_month_consumption_Mony;
    }

    public String get_5_month_consumption_Mony() {
        return _5_month_consumption_Mony;
    }

    public void set_5_month_consumption_Mony(String _5_month_consumption_Mony) {
        this._5_month_consumption_Mony = _5_month_consumption_Mony;
    }

    public String getInstanteneous_current_Phase_Ampere() {
        return instanteneous_current_Phase_Ampere;
    }

    public void setInstanteneous_current_Phase_Ampere(String instanteneous_current_Phase_Ampere) {
        this.instanteneous_current_Phase_Ampere = instanteneous_current_Phase_Ampere;
    }

    public String getRecharge_number() {
        return recharge_number;
    }

    public void setRecharge_number(String recharge_number) {
        this.recharge_number = recharge_number;
    }

    public String get_1_month_consumption_Mony() {
        return _1_month_consumption_Mony;
    }

    public void set_1_month_consumption_Mony(String _1_month_consumption_Mony) {
        this._1_month_consumption_Mony = _1_month_consumption_Mony;
    }

    public String getLast_recharge_Date_And_Time() {
        return Last_recharge_Date_And_Time;
    }

    public void setLast_recharge_Date_And_Time(String last_recharge_Date_And_Time) {
        Last_recharge_Date_And_Time = last_recharge_Date_And_Time;
    }

    public String getCurrent_month_consumption_KW() {
        return current_month_consumption_KW;
    }

    public void setCurrent_month_consumption_KW(String current_month_consumption_KW) {
        this.current_month_consumption_KW = current_month_consumption_KW;
    }

    public String get_10_month_consumption_kWh() {
        return _10_month_consumption_kWh;
    }

    public void set_10_month_consumption_kWh(String _10_month_consumption_kWh) {
        this._10_month_consumption_kWh = _10_month_consumption_kWh;
    }

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public String getMeter_ID() {
        return Meter_ID;
    }

    public void setMeter_ID(String meter_ID) {
        Meter_ID = meter_ID;
    }

    public String getCurrent_month_consumption_MONY() {
        return current_month_consumption_MONY;
    }

    public void setCurrent_month_consumption_MONY(String current_month_consumption_MONY) {
        this.current_month_consumption_MONY = current_month_consumption_MONY;
    }

    public String get_11_month_consumption_kWh() {
        return _11_month_consumption_kWh;
    }

    public void set_11_month_consumption_kWh(String _11_month_consumption_kWh) {
        this._11_month_consumption_kWh = _11_month_consumption_kWh;
    }

    public String getMeter_status() {
        return Meter_status;
    }

    public void setMeter_status(String meter_status) {
        Meter_status = meter_status;
    }

    public String getFw_version() {
        return fw_version;
    }

    public void setFw_version(String fw_version) {
        this.fw_version = fw_version;
    }

    public String get_1_month_consumption_kWh() {
        return _1_month_consumption_kWh;
    }

    public void set_1_month_consumption_kWh(String _1_month_consumption_kWh) {
        this._1_month_consumption_kWh = _1_month_consumption_kWh;
    }
}
