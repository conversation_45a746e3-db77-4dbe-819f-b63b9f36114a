#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=21224, tid=35580
#
# JRE version: Java(TM) SE Runtime Environment (17.0.7+8) (build 17.0.7+8-LTS-224)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0

Host: Intel(R) Core(TM) i5-10200H CPU @ 2.40GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
Time: Mon May 13 12:29:45 2024 Egypt Daylight Time elapsed time: 28.335441 seconds (0d 0h 0m 28s)

---------------  T H R E A D  ---------------

Current thread (0x000001e4fc273550):  JavaThread "Daemon worker" [_thread_in_vm, id=35580, stack(0x00000002b4a00000,0x00000002b4b00000)]

Stack: [0x00000002b4a00000,0x00000002b4b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x676a4a]
V  [jvm.dll+0x7d74f4]
V  [jvm.dll+0x7d8c9e]
V  [jvm.dll+0x7d9303]
V  [jvm.dll+0x2452c5]
V  [jvm.dll+0x7d35fb]
V  [jvm.dll+0x61ca36]
V  [jvm.dll+0x1bfbf7]
V  [jvm.dll+0x61f390]
V  [jvm.dll+0x61d3f6]
V  [jvm.dll+0x242d9c]
V  [jvm.dll+0x6cd2bd]
V  [jvm.dll+0x6cdcbf]
V  [jvm.dll+0x36df95]
V  [jvm.dll+0x40a6af]
V  [jvm.dll+0x41587e]
C  0x000001e487bf307f

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 2208  java.lang.Class.getDeclaredConstructors0(Z)[Ljava/lang/reflect/Constructor; java.base@17.0.7 (0 bytes) @ 0x000001e487bf2fe3 [0x000001e487bf2fa0+0x0000000000000043]
J 2126 c1 java.lang.Class.privateGetDeclaredConstructors(Z)[Ljava/lang/reflect/Constructor; java.base@17.0.7 (79 bytes) @ 0x000001e48042cf8c [0x000001e48042cd00+0x000000000000028c]
j  java.lang.Class.getConstructor0([Ljava/lang/Class;I)Ljava/lang/reflect/Constructor;+14 java.base@17.0.7
j  java.lang.Class.getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;+24 java.base@17.0.7
j  java.util.ServiceLoader$1.run()Ljava/lang/reflect/Constructor;+8 java.base@17.0.7
j  java.util.ServiceLoader$1.run()Ljava/lang/Object;+1 java.base@17.0.7
J 1227 c1 java.security.AccessController.executePrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; java.base@17.0.7 (65 bytes) @ 0x000001e48028683c [0x000001e4802866e0+0x000000000000015c]
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;+9 java.base@17.0.7
j  java.util.ServiceLoader.getConstructor(Ljava/lang/Class;)Ljava/lang/reflect/Constructor;+13 java.base@17.0.7
j  java.util.ServiceLoader$LazyClassPathLookupIterator.hasNextService()Z+59 java.base@17.0.7
j  java.util.ServiceLoader$LazyClassPathLookupIterator.hasNext()Z+11 java.base@17.0.7
j  java.util.ServiceLoader$2.hasNext()Z+16 java.base@17.0.7
j  java.util.ServiceLoader$3.hasNext()Z+32 java.base@17.0.7
j  org.gradle.internal.impldep.com.google.common.collect.Iterators.addAll(Ljava/util/Collection;Ljava/util/Iterator;)Z+13
j  org.gradle.internal.impldep.com.google.common.collect.Lists.newArrayList(Ljava/util/Iterator;)Ljava/util/ArrayList;+6
j  org.gradle.internal.impldep.com.google.common.collect.Lists.newArrayList(Ljava/lang/Iterable;)Ljava/util/ArrayList;+32
j  org.jetbrains.plugins.gradle.tooling.internal.ExtraModelBuilder.<init>(Lorg/gradle/util/GradleVersion;)V+28
j  org.jetbrains.plugins.gradle.tooling.internal.ExtraModelBuilder.<init>()V+4
j  org.jetbrains.plugins.gradle.tooling.internal.ExtraModelBuilder$ForGradle44.<init>()V+1
v  ~StubRoutines::call_stub
J 2170  jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Ljava/lang/reflect/Constructor;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.7 (0 bytes) @ 0x000001e487bef28e [0x000001e487bef220+0x000000000000006e]
J 2169 c1 jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance([Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.7 (122 bytes) @ 0x000001e48043fbb4 [0x000001e48043f260+0x0000000000000954]
J 2250 c1 jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance([Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.7 (9 bytes) @ 0x000001e48047efc4 [0x000001e48047eec0+0x0000000000000104]
J 1937 c1 java.lang.reflect.Constructor.newInstanceWithCaller([Ljava/lang/Object;ZLjava/lang/Class;)Ljava/lang/Object; java.base@17.0.7 (75 bytes) @ 0x000001e4803c2e54 [0x000001e4803c2c00+0x0000000000000254]
J 2223 c1 java.lang.reflect.Constructor.newInstance([Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.7 (34 bytes) @ 0x000001e48046212c [0x000001e480461fc0+0x000000000000016c]
j  org.codehaus.groovy.reflection.CachedConstructor.invoke([Ljava/lang/Object;)Ljava/lang/Object;+34
j  org.codehaus.groovy.reflection.CachedConstructor.doConstructorInvoke([Ljava/lang/Object;)Ljava/lang/Object;+8
j  groovy.lang.MetaClassImpl.invokeConstructor(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+45
j  groovy.lang.MetaClassImpl.invokeConstructor([Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.codehaus.groovy.runtime.InvokerHelper.invokeConstructorOf(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;+15
j  org.codehaus.groovy.runtime.DefaultGroovyMethods.newInstance(Ljava/lang/Class;)Ljava/lang/Object;+2
j  org.codehaus.groovy.runtime.dgm$547.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+4
j  org.codehaus.groovy.runtime.callsite.StaticMetaMethodSite$StaticMetaMethodSiteNoUnwrapNoCoerce.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.codehaus.groovy.runtime.callsite.StaticMetaMethodSite.call(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+12
j  org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+3
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(Ljava/lang/Object;)Ljava/lang/Object;+5
j  JetGradlePlugin.getJetModelBuilderOrRegisterIfAbsent(Lorg/gradle/api/invocation/Gradle;Lorg/gradle/tooling/provider/model/ToolingModelBuilderRegistry;)Lorg/gradle/tooling/provider/model/ToolingModelBuilder;+250
j  JetGradlePlugin$getJetModelBuilderOrRegisterIfAbsent.callStatic(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+21
j  org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallStatic(Lorg/codehaus/groovy/runtime/callsite/CallSite;Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.callStatic(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+3
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.callStatic(Ljava/lang/Class;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+38
j  JetGradlePlugin.apply(Lorg/gradle/api/invocation/Gradle;)V+32
j  JetGradlePlugin.apply(Ljava/lang/Object;)V+5
j  org.gradle.api.internal.plugins.ImperativeOnlyPluginTarget.applyImperative(Ljava/lang/String;Lorg/gradle/api/Plugin;)V+13
j  org.gradle.api.internal.plugins.DefaultPluginManager.addPlugin(Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;)V+54
j  org.gradle.api.internal.plugins.DefaultPluginManager.access$100(Lorg/gradle/api/internal/plugins/DefaultPluginManager;Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;)V+6
j  org.gradle.api.internal.plugins.DefaultPluginManager$AddPluginBuildOperation.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+20
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+5
j  org.gradle.api.internal.plugins.DefaultPluginManager.lambda$doApply$0(Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;Lorg/gradle/configuration/internal/UserCodeApplicationId;)V+20
j  org.gradle.api.internal.plugins.DefaultPluginManager$$Lambda$577+0x00000008011165b0.execute(Ljava/lang/Object;)V+24
j  org.gradle.configuration.internal.DefaultUserCodeApplicationContext.apply(Lorg/gradle/internal/DisplayName;Lorg/gradle/api/Action;)V+37
j  org.gradle.api.internal.plugins.DefaultPluginManager.doApply(Lorg/gradle/api/internal/plugins/PluginImplementation;)V+132
j  org.gradle.api.internal.plugins.DefaultPluginManager.apply(Ljava/lang/Class;)V+11
j  org.gradle.api.internal.plugins.DefaultObjectConfigurationAction.applyType(Ljava/lang/Class;)V+43
j  org.gradle.api.internal.plugins.DefaultObjectConfigurationAction.applyPlugin(Ljava/lang/Class;)V+2
j  org.gradle.api.internal.plugins.DefaultObjectConfigurationAction.access$100(Lorg/gradle/api/internal/plugins/DefaultObjectConfigurationAction;Ljava/lang/Class;)V+2
j  org.gradle.api.internal.plugins.DefaultObjectConfigurationAction$2.run()V+8
j  org.gradle.api.internal.plugins.DefaultObjectConfigurationAction.execute()V+58
j  org.gradle.groovy.scripts.DefaultScript.apply(Ljava/util/Map;)V+12
j  org.gradle.api.Script$apply.callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;+19
j  org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallCurrent(Lorg/codehaus/groovy/runtime/callsite/CallSite;Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;+17
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(Lgroovy/lang/GroovyObject;[Ljava/lang/Object;)Ljava/lang/Object;+3
j  org.codehaus.groovy.runtime.callsite.AbstractCallSite.callCurrent(Lgroovy/lang/GroovyObject;Ljava/lang/Object;)Ljava/lang/Object;+33
j  ijInit1_35dm7hl684e41ngwa7vmcdovq.run()Ljava/lang/Object;+31
j  org.gradle.groovy.scripts.internal.DefaultScriptRunnerFactory$ScriptRunnerImpl.run(Ljava/lang/Object;Lorg/gradle/internal/service/ServiceRegistry;)V+57
j  org.gradle.configuration.DefaultScriptPluginFactory$ScriptPluginImpl.lambda$apply$0(Lorg/gradle/groovy/scripts/ScriptRunner;Ljava/lang/Object;Lorg/gradle/internal/service/DefaultServiceRegistry;)V+3
j  org.gradle.configuration.DefaultScriptPluginFactory$ScriptPluginImpl$$Lambda$421+0x0000000800fef778.run()V+12
j  org.gradle.configuration.DefaultScriptTarget.addConfiguration(Ljava/lang/Runnable;Z)V+1
j  org.gradle.configuration.DefaultScriptPluginFactory$ScriptPluginImpl.apply(Ljava/lang/Object;)V+389
j  org.gradle.configuration.BuildOperationScriptPlugin$1.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+11
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+5
j  org.gradle.configuration.BuildOperationScriptPlugin.lambda$apply$0(Ljava/lang/Object;Lorg/gradle/configuration/internal/UserCodeApplicationId;)V+14
j  org.gradle.configuration.BuildOperationScriptPlugin$$Lambda$409+0x0000000800fd9928.execute(Ljava/lang/Object;)V+12
j  org.gradle.configuration.internal.DefaultUserCodeApplicationContext.apply(Lorg/gradle/internal/DisplayName;Lorg/gradle/api/Action;)V+37
j  org.gradle.configuration.BuildOperationScriptPlugin.apply(Ljava/lang/Object;)V+61
j  org.gradle.configuration.DefaultInitScriptProcessor.process(Lorg/gradle/groovy/scripts/ScriptSource;Lorg/gradle/api/internal/GradleInternal;)V+119
j  org.gradle.initialization.InitScriptHandler$1.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+66
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+5
j  org.gradle.initialization.InitScriptHandler.executeScripts(Lorg/gradle/api/internal/GradleInternal;)V+34
j  org.gradle.initialization.InitScriptHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+5
j  org.gradle.api.internal.initialization.CacheConfigurationsHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+15
j  org.gradle.initialization.GradlePropertiesHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+39
j  org.gradle.initialization.DefaultSettingsPreparer.prepareSettings(Lorg/gradle/api/internal/GradleInternal;)V+33
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer$LoadBuild.doLoadBuild()V+11
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer$LoadBuild.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+1
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+5
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer.prepareSettings(Lorg/gradle/api/internal/GradleInternal;)V+31
j  org.gradle.initialization.VintageBuildModelController.lambda$prepareSettings$1()V+8
j  org.gradle.initialization.VintageBuildModelController$$Lambda$327+0x0000000800f34220.run()V+4
j  org.gradle.internal.model.StateTransitionController.lambda$doTransition$13(Ljava/lang/Runnable;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$329+0x0000000800f34660.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+9
j  org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$11(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+16
j  org.gradle.internal.model.StateTransitionController$$Lambda$328+0x0000000800f34440.run()V+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+13
j  org.gradle.initialization.VintageBuildModelController.prepareSettings()V+16
j  org.gradle.initialization.VintageBuildModelController.getLoadedSettings()Lorg/gradle/api/internal/SettingsInternal;+1
j  org.gradle.internal.build.DefaultBuildLifecycleController$$Lambda$325+0x0000000800f2fd28.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.lambda$notInState$4(Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+11
j  org.gradle.internal.model.StateTransitionController$$Lambda$326+0x0000000800f34000.create()Ljava/lang/Object;+12
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+6
j  org.gradle.internal.model.StateTransitionController.notInState(Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+12
j  org.gradle.internal.build.DefaultBuildLifecycleController.loadSettings()V+21
j  org.gradle.internal.build.AbstractBuildState.ensureProjectsLoaded()V+4
j  org.gradle.plugins.ide.internal.tooling.GradleBuildBuilder.create(Lorg/gradle/internal/build/BuildState;)Lorg/gradle/plugins/ide/internal/tooling/model/DefaultGradleBuild;+1
j  org.gradle.plugins.ide.internal.tooling.GradleBuildBuilder.create(Lorg/gradle/internal/build/BuildState;)Ljava/lang/Object;+2
j  org.gradle.tooling.provider.model.internal.DefaultToolingModelBuilderRegistry$BuildScopedBuilder.build(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.tooling.provider.model.internal.DefaultToolingModelBuilderRegistry$BuildOperationWrappingBuilder$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+11
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/CallableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+3
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+16
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+5
j  org.gradle.tooling.provider.model.internal.DefaultToolingModelBuilderRegistry$BuildOperationWrappingBuilder.build(Ljava/lang/Object;)Ljava/lang/Object;+13
j  org.gradle.internal.build.DefaultBuildToolingModelController$AbstractToolingScope.getModel(Ljava/lang/String;Ljava/util/function/Function;)Ljava/lang/Object;+11
j  org.gradle.tooling.internal.provider.runner.DefaultBuildController.getModel(Ljava/lang/Object;Lorg/gradle/tooling/internal/protocol/ModelIdentifier;Ljava/lang/Object;)Lorg/gradle/tooling/internal/protocol/BuildResult;+72
j  org.gradle.tooling.internal.consumer.connection.ParameterAwareBuildControllerAdapter.getModel(Ljava/lang/Object;Lorg/gradle/tooling/internal/protocol/ModelIdentifier;Ljava/lang/Object;)Lorg/gradle/tooling/internal/protocol/BuildResult;+7
j  org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getModel(Lorg/gradle/tooling/model/Model;Ljava/lang/Class;Ljava/lang/Class;Lorg/gradle/api/Action;)Ljava/lang/Object;+44
j  org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getModel(Lorg/gradle/tooling/model/Model;Ljava/lang/Class;Ljava/lang/Class;Lorg/gradle/api/Action;)Ljava/lang/Object;+6
j  org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getModel(Lorg/gradle/tooling/model/Model;Ljava/lang/Class;)Ljava/lang/Object;+5
j  org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getModel(Lorg/gradle/tooling/model/Model;Ljava/lang/Class;)Ljava/lang/Object;+3
j  org.gradle.tooling.internal.consumer.connection.UnparameterizedBuildController.getBuildModel()Lorg/gradle/tooling/model/gradle/GradleBuild;+4
j  org.gradle.tooling.internal.consumer.connection.NestedActionAwareBuildControllerAdapter.getBuildModel()Lorg/gradle/tooling/model/gradle/GradleBuild;+1
j  org.jetbrains.plugins.gradle.model.ProjectImportAction.execute(Lorg/gradle/tooling/BuildController;)Lorg/jetbrains/plugins/gradle/model/ProjectImportAction$AllModels;+96
j  org.jetbrains.plugins.gradle.model.ProjectImportAction.execute(Lorg/gradle/tooling/BuildController;)Ljava/lang/Object;+2
j  org.gradle.tooling.internal.consumer.connection.InternalBuildActionAdapter.execute(Lorg/gradle/tooling/internal/protocol/InternalBuildControllerVersion2;)Ljava/lang/Object;+11
j  org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner$ActionAdapter.runAction(Lorg/gradle/internal/buildtree/BuildTreeModelController;Ljava/lang/Object;Lorg/gradle/tooling/internal/protocol/PhasedActionResult$Phase;)Lorg/gradle/tooling/internal/provider/serialization/SerializedPayload;+39
j  org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner$ActionAdapter.beforeTasks(Lorg/gradle/internal/buildtree/BuildTreeModelController;)V+15
j  org.gradle.internal.buildtree.DefaultBuildTreeModelCreator.beforeTasks(Lorg/gradle/internal/buildtree/BuildTreeModelAction;)V+10
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$fromBuildModel$2(Lorg/gradle/internal/buildtree/BuildTreeModelAction;Z)Lorg/gradle/internal/build/ExecutionResult;+5
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController$$Lambda$319+0x0000000800f1c238.get()Ljava/lang/Object;+12
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$runBuild$4(Ljava/util/function/Supplier;)Ljava/lang/Object;+1
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController$$Lambda$320+0x0000000800f1c458.get()Ljava/lang/Object;+8
j  org.gradle.internal.model.StateTransitionController.lambda$transition$6(Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$322+0x0000000800f1c898.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.lambda$transition$7(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+9
j  org.gradle.internal.model.StateTransitionController$$Lambda$321+0x0000000800f1c678.create()Ljava/lang/Object;+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+6
j  org.gradle.internal.model.StateTransitionController.transition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+13
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.runBuild(Ljava/util/function/Supplier;)Ljava/lang/Object;+17
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.fromBuildModel(ZLorg/gradle/internal/buildtree/BuildTreeModelAction;)Ljava/lang/Object;+9
j  org.gradle.tooling.internal.provider.runner.AbstractClientProvidedBuildActionRunner.runClientAction(Lorg/gradle/tooling/internal/provider/runner/AbstractClientProvidedBuildActionRunner$ClientAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+22
j  org.gradle.tooling.internal.provider.runner.ClientProvidedPhasedActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+45
j  org.gradle.launcher.exec.ChainingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+34
j  org.gradle.internal.buildtree.ProblemReportingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+12
j  org.gradle.launcher.exec.BuildOutcomeReportingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+69
j  org.gradle.tooling.internal.provider.FileSystemWatchingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+335
j  org.gradle.launcher.exec.BuildCompletionNotifyingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor$$Lambda$216+0x0000000800ecd798.apply(Ljava/lang/Object;)Ljava/lang/Object;+12
j  org.gradle.composite.internal.DefaultRootBuildState.run(Ljava/util/function/Function;)Ljava/lang/Object;+87
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+28
j  org.gradle.internal.buildtree.DefaultBuildTreeContext.execute(Lorg/gradle/internal/invocation/BuildAction;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+64
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+2
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor$$Lambda$169+0x0000000800e2c040.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.internal.buildtree.BuildTreeState.run(Ljava/util/function/Function;)Ljava/lang/Object;+5
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+181
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+25
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/CallableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+3
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+16
j  org.gradle.internal.operations.DefaultBuildOperationExecutor.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+5
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+23
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor$$Lambda$168+0x0000000800e121b8.create()Ljava/lang/Object;+12
j  org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(Ljava/util/Collection;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+28
j  org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+33
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+12
j  org.gradle.tooling.internal.provider.continuous.ContinuousBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+58
j  org.gradle.tooling.internal.provider.SubscribableBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+30
j  org.gradle.internal.session.DefaultBuildSessionContext.execute(Lorg/gradle/internal/invocation/BuildAction;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+64
j  org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/launcher/exec/BuildActionResult;+6
j  org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.gradle.internal.session.BuildSessionState.run(Ljava/util/function/Function;)Ljava/lang/Object;+5
j  org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+95
j  org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+177
j  org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+7
j  org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+50
j  org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.launcher.daemon.server.exec.ExecuteBuild.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+117
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.WatchForDisconnection.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+19
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.ResetDeprecationLogger.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+7
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.RequestStopIfSingleUsedDaemon.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+33
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create()Ljava/lang/Void;+4
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create()Ljava/lang/Object;+1
j  org.gradle.util.internal.Swapper.swap(Ljava/lang/Object;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+21
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+63
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.LogAndCheckHealth.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+51
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.LogToClient.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+87
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+356
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy$1.run()V+44
j  org.gradle.launcher.daemon.server.DaemonStateCoordinator$1.run()V+4
j  org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(Ljava/lang/Runnable;)V+1
j  org.gradle.internal.concurrent.ManagedExecutorImpl$1.run()V+25
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.7
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.7
j  java.lang.Thread.run()V+11 java.base@17.0.7
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e4fdaa63e0, length=42, elements={
0x000001e4ea17b1a0, 0x000001e4f9387940, 0x000001e4f9388dc0, 0x000001e4f93a1170,
0x000001e4f93a1c30, 0x000001e4f93a27e0, 0x000001e4f93a51a0, 0x000001e4f93a5f30,
0x000001e4f93a69d0, 0x000001e4f9d503e0, 0x000001e4f9dc5300, 0x000001e4f9ddaef0,
0x000001e4fb9cb610, 0x000001e4fb982860, 0x000001e4fb92c210, 0x000001e4fb8ce890,
0x000001e4fc148460, 0x000001e4fc273080, 0x000001e4fc273550, 0x000001e4fc270530,
0x000001e4fc272210, 0x000001e4fc273a20, 0x000001e4fc272bb0, 0x000001e4fc270060,
0x000001e4fc270a00, 0x000001e4fc270ed0, 0x000001e4fc2713a0, 0x000001e4fc271870,
0x000001e4fc271d40, 0x000001e4fb3750f0, 0x000001e4fb374750, 0x000001e4fb376430,
0x000001e4fb370d90, 0x000001e4fb3725a0, 0x000001e4fb376900, 0x000001e4fb375f60,
0x000001e4fb371260, 0x000001e4fb373db0, 0x000001e4fb374c20, 0x000001e4fb373410,
0x000001e4fb3755c0, 0x000001e4fb372f40
}

Java Threads: ( => current thread )
  0x000001e4ea17b1a0 JavaThread "main" [_thread_blocked, id=25596, stack(0x00000002b2b00000,0x00000002b2c00000)]
  0x000001e4f9387940 JavaThread "Reference Handler" daemon [_thread_blocked, id=26804, stack(0x00000002b3300000,0x00000002b3400000)]
  0x000001e4f9388dc0 JavaThread "Finalizer" daemon [_thread_blocked, id=9852, stack(0x00000002b3400000,0x00000002b3500000)]
  0x000001e4f93a1170 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=30592, stack(0x00000002b3500000,0x00000002b3600000)]
  0x000001e4f93a1c30 JavaThread "Attach Listener" daemon [_thread_blocked, id=29932, stack(0x00000002b3600000,0x00000002b3700000)]
  0x000001e4f93a27e0 JavaThread "Service Thread" daemon [_thread_blocked, id=29504, stack(0x00000002b3700000,0x00000002b3800000)]
  0x000001e4f93a51a0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=21032, stack(0x00000002b3800000,0x00000002b3900000)]
  0x000001e4f93a5f30 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=8072, stack(0x00000002b3900000,0x00000002b3a00000)]
  0x000001e4f93a69d0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=12844, stack(0x00000002b3a00000,0x00000002b3b00000)]
  0x000001e4f9d503e0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=33904, stack(0x00000002b3b00000,0x00000002b3c00000)]
  0x000001e4f9dc5300 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=31376, stack(0x00000002b3c00000,0x00000002b3d00000)]
  0x000001e4f9ddaef0 JavaThread "Notification Thread" daemon [_thread_blocked, id=18036, stack(0x00000002b3d00000,0x00000002b3e00000)]
  0x000001e4fb9cb610 JavaThread "Daemon health stats" [_thread_blocked, id=35328, stack(0x00000002b4500000,0x00000002b4600000)]
  0x000001e4fb982860 JavaThread "Incoming local TCP Connector on port 59174" [_thread_in_native, id=35532, stack(0x00000002b4400000,0x00000002b4500000)]
  0x000001e4fb92c210 JavaThread "Daemon periodic checks" [_thread_blocked, id=35544, stack(0x00000002b4600000,0x00000002b4700000)]
  0x000001e4fb8ce890 JavaThread "Daemon" [_thread_blocked, id=35564, stack(0x00000002b4700000,0x00000002b4800000)]
  0x000001e4fc148460 JavaThread "Handler for socket connection from /127.0.0.1:59174 to /127.0.0.1:59175" [_thread_in_native, id=35568, stack(0x00000002b4800000,0x00000002b4900000)]
  0x000001e4fc273080 JavaThread "Cancel handler" [_thread_blocked, id=35576, stack(0x00000002b4900000,0x00000002b4a00000)]
=>0x000001e4fc273550 JavaThread "Daemon worker" [_thread_in_vm, id=35580, stack(0x00000002b4a00000,0x00000002b4b00000)]
  0x000001e4fc270530 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:59174 to /127.0.0.1:59175" [_thread_blocked, id=35596, stack(0x00000002b4b00000,0x00000002b4c00000)]
  0x000001e4fc272210 JavaThread "Stdin handler" [_thread_blocked, id=35604, stack(0x00000002b4c00000,0x00000002b4d00000)]
  0x000001e4fc273a20 JavaThread "Daemon client event forwarder" [_thread_blocked, id=35608, stack(0x00000002b4d00000,0x00000002b4e00000)]
  0x000001e4fc272bb0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=35020, stack(0x00000002b4e00000,0x00000002b4f00000)]
  0x000001e4fc270060 JavaThread "File lock request listener" [_thread_in_native, id=35036, stack(0x00000002b4f00000,0x00000002b5000000)]
  0x000001e4fc270a00 JavaThread "File lock release action executor" [_thread_blocked, id=30884, stack(0x00000002b5000000,0x00000002b5100000)]
  0x000001e4fc270ed0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.0\fileHashes)" [_thread_blocked, id=35204, stack(0x00000002b5100000,0x00000002b5200000)]
  0x000001e4fc2713a0 JavaThread "File watcher server" daemon [_thread_in_native, id=28608, stack(0x00000002b5200000,0x00000002b5300000)]
  0x000001e4fc271870 JavaThread "File watcher consumer" daemon [_thread_blocked, id=17068, stack(0x00000002b5300000,0x00000002b5400000)]
  0x000001e4fc271d40 JavaThread "jar transforms" [_thread_blocked, id=2140, stack(0x00000002b5500000,0x00000002b5600000)]
  0x000001e4fb3750f0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=3324, stack(0x00000002b5600000,0x00000002b5700000)]
  0x000001e4fb374750 JavaThread "jar transforms Thread 3" [_thread_blocked, id=25712, stack(0x00000002b5700000,0x00000002b5800000)]
  0x000001e4fb376430 JavaThread "jar transforms Thread 4" [_thread_blocked, id=30404, stack(0x00000002b5800000,0x00000002b5900000)]
  0x000001e4fb370d90 JavaThread "jar transforms Thread 5" [_thread_blocked, id=22876, stack(0x00000002b5900000,0x00000002b5a00000)]
  0x000001e4fb3725a0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=35416, stack(0x00000002b5a00000,0x00000002b5b00000)]
  0x000001e4fb376900 JavaThread "jar transforms Thread 7" [_thread_blocked, id=35412, stack(0x00000002b5b00000,0x00000002b5c00000)]
  0x000001e4fb375f60 JavaThread "jar transforms Thread 8" [_thread_blocked, id=35376, stack(0x00000002b5c00000,0x00000002b5d00000)]
  0x000001e4fb371260 JavaThread "Cache worker for checksums cache (D:\ShoaaProjects\FinalApps\mobile_app\TechnicalReviewSocket_With_Offline_edit\.gradle\8.0\checksums)" [_thread_blocked, id=35828, stack(0x00000002b5f00000,0x00000002b6000000)]
  0x000001e4fb373db0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.0\md-rule)" [_thread_blocked, id=35624, stack(0x00000002b6000000,0x00000002b6100000)]
  0x000001e4fb374c20 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.0\fileContent)" [_thread_blocked, id=9076, stack(0x00000002b6100000,0x00000002b6200000)]
  0x000001e4fb373410 JavaThread "Cache worker for file hash cache (D:\ShoaaProjects\FinalApps\mobile_app\TechnicalReviewSocket_With_Offline_edit\.gradle\8.0\fileHashes)" [_thread_blocked, id=20636, stack(0x00000002b6200000,0x00000002b6300000)]
  0x000001e4fb3755c0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.0\md-supplier)" [_thread_blocked, id=15764, stack(0x00000002b6300000,0x00000002b6400000)]
  0x000001e4fb372f40 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.0\executionHistory)" [_thread_blocked, id=284, stack(0x00000002b6400000,0x00000002b6500000)]

Other Threads:
  0x000001e4f937fdf0 VMThread "VM Thread" [stack: 0x00000002b3200000,0x00000002b3300000] [id=31160]
  0x000001e4f9e03590 WatcherThread [stack: 0x00000002b3e00000,0x00000002b3f00000] [id=15588]
  0x000001e4ea1d6560 GCTaskThread "GC Thread#0" [stack: 0x00000002b2d00000,0x00000002b2e00000] [id=27540]
  0x000001e4fb036120 GCTaskThread "GC Thread#1" [stack: 0x00000002b3f00000,0x00000002b4000000] [id=35116]
  0x000001e4fae54d00 GCTaskThread "GC Thread#2" [stack: 0x00000002b4000000,0x00000002b4100000] [id=35120]
  0x000001e4faf5a700 GCTaskThread "GC Thread#3" [stack: 0x00000002b4100000,0x00000002b4200000] [id=35124]
  0x000001e4faf5a9b0 GCTaskThread "GC Thread#4" [stack: 0x00000002b4200000,0x00000002b4300000] [id=35128]
  0x000001e4fa7881a0 GCTaskThread "GC Thread#5" [stack: 0x00000002b4300000,0x00000002b4400000] [id=35132]
  0x000001e4fbfee5d0 GCTaskThread "GC Thread#6" [stack: 0x00000002b5400000,0x00000002b5500000] [id=35648]
  0x000001e4fbfedb10 GCTaskThread "GC Thread#7" [stack: 0x00000002b5d00000,0x00000002b5e00000] [id=35832]
  0x000001e4ea1e74a0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000002b2e00000,0x00000002b2f00000] [id=18076]
  0x000001e4ea1e7eb0 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000002b2f00000,0x00000002b3000000] [id=31872]
  0x000001e4fbfeede0 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000002b5e00000,0x00000002b5f00000] [id=35796]
  0x000001e4ea23df40 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000002b3000000,0x00000002b3100000] [id=17300]
  0x000001e4fdac7820 ConcurrentGCThread "G1 Refine#1" [stack: 0x00000002b6600000,0x00000002b6700000] [id=35108]
  0x000001e4ea23e960 ConcurrentGCThread "G1 Service" [stack: 0x00000002b3100000,0x00000002b3200000] [id=21828]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001e4ea175970] Metaspace_lock - owner thread: 0x000001e4fc273550

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bd0000-0x0000000800bd0000), size 12386304, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16159M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 133120K, used 74978K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 4 survivors (4096K)
 Metaspace       used 37620K, committed 38080K, reserved 1089536K
  class space    used 5126K, committed 5376K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000, 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000, 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%|HS|  |TAMS 0x0000000080a00000, 0x0000000080a00000| Complete 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%|HS|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Complete 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%|HC|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Complete 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%|HS|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Complete 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%|HC|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Complete 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%|HC|  |TAMS 0x0000000081000000, 0x0000000080f00000| Complete 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%|HS|  |TAMS 0x0000000081100000, 0x0000000081000000| Complete 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%|HC|  |TAMS 0x0000000081200000, 0x0000000081100000| Complete 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%|HC|  |TAMS 0x0000000081300000, 0x0000000081200000| Complete 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%|HS|  |TAMS 0x0000000081400000, 0x0000000081300000| Complete 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%|HC|  |TAMS 0x0000000081500000, 0x0000000081400000| Complete 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%|HC|  |TAMS 0x0000000081600000, 0x0000000081500000| Complete 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082a8dc00, 0x0000000082b00000| 55%| O|  |TAMS 0x0000000082a8dc00, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%|HS|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Complete 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HC|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HC|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HC|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083000000, 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HC|  |TAMS 0x0000000083100000, 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x00000000854aac70, 0x0000000085500000| 66%| S|CS|TAMS 0x0000000085400000, 0x0000000085400000| Complete 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| S|CS|TAMS 0x0000000085500000, 0x0000000085500000| Complete 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| S|CS|TAMS 0x0000000085600000, 0x0000000085600000| Complete 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| S|CS|TAMS 0x0000000085700000, 0x0000000085700000| Complete 
|  88|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c87c10, 0x0000000086d00000| 53%| E|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Complete 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| E|CS|TAMS 0x0000000086d00000, 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| E|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| E|CS|TAMS 0x0000000086f00000, 0x0000000086f00000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| E|CS|TAMS 0x0000000088100000, 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| E|CS|TAMS 0x0000000088200000, 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| E|CS|TAMS 0x0000000088300000, 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| E|CS|TAMS 0x0000000088400000, 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| E|CS|TAMS 0x0000000088500000, 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| E|CS|TAMS 0x0000000088600000, 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| E|CS|TAMS 0x0000000088700000, 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| E|CS|TAMS 0x0000000088800000, 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| E|CS|TAMS 0x0000000088900000, 0x0000000088900000| Complete 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| E|CS|TAMS 0x0000000088a00000, 0x0000000088a00000| Complete 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| E|CS|TAMS 0x0000000088b00000, 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| E|CS|TAMS 0x0000000088c00000, 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| E|CS|TAMS 0x0000000088d00000, 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| E|CS|TAMS 0x0000000088e00000, 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| E|CS|TAMS 0x0000000088f00000, 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| E|CS|TAMS 0x0000000089000000, 0x0000000089000000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000, 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x000001e4f2130000,0x000001e4f2530000] _byte_map_base: 0x000001e4f1d30000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001e4ea1d6b70, (CMBitMap*) 0x000001e4ea1d6bb0
 Prev Bits: [0x000001e4f2930000, 0x000001e4f4930000)
 Next Bits: [0x000001e4f4930000, 0x000001e4f6930000)

Polling page: 0x000001e4e80a0000

Metaspace:

Usage:
  Non-class:     31.73 MB used.
      Class:      5.01 MB used.
       Both:     36.74 MB used.

Virtual space:
  Non-class space:       40.00 MB reserved,      31.94 MB ( 80%) committed,  5 nodes.
      Class space:        1.00 GB reserved,       5.25 MB ( <1%) committed,  1 nodes.
             Both:        1.04 GB reserved,      37.19 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  3.67 MB
       Class:  2.58 MB
        Both:  6.25 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.06 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 454.
num_arena_deaths: 0.
num_vsnodes_births: 6.
num_vsnodes_deaths: 0.
num_space_committed: 595.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1903.
num_chunk_merges: 6.
num_chunk_splits: 1229.
num_chunks_enlarged: 816.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2684Kb max_used=2684Kb free=117315Kb
 bounds [0x000001e487ad0000, 0x000001e487d70000, 0x000001e48f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=9379Kb max_used=9379Kb free=110620Kb
 bounds [0x000001e480000000, 0x000001e480930000, 0x000001e487530000]
CodeHeap 'non-nmethods': size=5760Kb used=2303Kb max_used=2365Kb free=3456Kb
 bounds [0x000001e487530000, 0x000001e4877a0000, 0x000001e487ad0000]
 total_blobs=5324 nmethods=4510 adapters=725
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 28.224 Thread 0x000001e4f93a69d0 nmethod 4511 0x000001e480927110 code [0x000001e4809272a0, 0x000001e4809273b8]
Event: 28.233 Thread 0x000001e4f93a5f30 nmethod 4487 0x000001e487d69e90 code [0x000001e487d6a0e0, 0x000001e487d6c8b8]
Event: 28.234 Thread 0x000001e4f93a5f30 4494       4       java.lang.Class::copyMethods (36 bytes)
Event: 28.244 Thread 0x000001e4f93a69d0 4512       3       jdk.internal.jimage.ImageStringsReader::hashCode (8 bytes)
Event: 28.244 Thread 0x000001e4f93a69d0 nmethod 4512 0x000001e480927490 code [0x000001e480927640, 0x000001e4809277f8]
Event: 28.244 Thread 0x000001e4f93a69d0 4513       3       jdk.internal.jimage.BasicImageReader::getLocationIndex (52 bytes)
Event: 28.244 Thread 0x000001e4f93a5f30 nmethod 4494 0x000001e487d6da10 code [0x000001e487d6dba0, 0x000001e487d6e2f8]
Event: 28.244 Thread 0x000001e4f93a5f30 4488       4       java.lang.reflect.Array::newInstance (6 bytes)
Event: 28.244 Thread 0x000001e4f93a69d0 nmethod 4513 0x000001e480927910 code [0x000001e480927b00, 0x000001e480927f98]
Event: 28.245 Thread 0x000001e4f93a69d0 4514       3       java.lang.Character::isJavaIdentifierPart (9 bytes)
Event: 28.245 Thread 0x000001e4f93a69d0 nmethod 4514 0x000001e480928190 code [0x000001e480928340, 0x000001e480928538]
Event: 28.245 Thread 0x000001e4f93a69d0 4515       3       java.lang.CharacterDataLatin1::isJavaIdentifierPart (20 bytes)
Event: 28.245 Thread 0x000001e4f93a69d0 nmethod 4515 0x000001e480928610 code [0x000001e4809287c0, 0x000001e4809289d8]
Event: 28.246 Thread 0x000001e4f93a5f30 nmethod 4488 0x000001e487d6e590 code [0x000001e487d6e700, 0x000001e487d6e8d8]
Event: 28.248 Thread 0x000001e4f93a69d0 4516       1       com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry::getKey (5 bytes)
Event: 28.248 Thread 0x000001e4f93a69d0 nmethod 4516 0x000001e487d6ea10 code [0x000001e487d6eba0, 0x000001e487d6ec78]
Event: 28.250 Thread 0x000001e4f93a5f30 4517       4       java.lang.ClassLoader::getClassLoadingLock (35 bytes)
Event: 28.251 Thread 0x000001e4f93a5f30 nmethod 4517 0x000001e487d6ed10 code [0x000001e487d6eea0, 0x000001e487d6efe8]
Event: 28.323 Thread 0x000001e4f93a69d0 4518       3       java.util.concurrent.locks.ReentrantLock::tryLock (8 bytes)
Event: 28.323 Thread 0x000001e4f93a69d0 nmethod 4518 0x000001e480928b10 code [0x000001e480928cc0, 0x000001e480928e08]

GC Heap History (12 events):
Event: 3.616 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 26624K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 3031K, committed 3200K, reserved 1056768K
  class space    used 381K, committed 448K, reserved 1048576K
}
Event: 3.688 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 10109K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 3031K, committed 3200K, reserved 1056768K
  class space    used 381K, committed 448K, reserved 1048576K
}
Event: 5.618 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 37757K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 29 young (29696K), 3 survivors (3072K)
 Metaspace       used 3943K, committed 4096K, reserved 1056768K
  class space    used 477K, committed 576K, reserved 1048576K
}
Event: 5.621 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 12589K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 3943K, committed 4096K, reserved 1056768K
  class space    used 477K, committed 576K, reserved 1048576K
}
Event: 17.260 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 130349K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 109 young (111616K), 3 survivors (3072K)
 Metaspace       used 21141K, committed 21504K, reserved 1073152K
  class space    used 3148K, committed 3328K, reserved 1048576K
}
Event: 17.347 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 36843K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 21141K, committed 21504K, reserved 1073152K
  class space    used 3148K, committed 3328K, reserved 1048576K
}
Event: 21.937 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 133120K, used 100331K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 78 young (79872K), 16 survivors (16384K)
 Metaspace       used 31396K, committed 31808K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 21.950 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 133120K, used 42327K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 31396K, committed 31808K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 21.963 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 133120K, used 43351K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 6 survivors (6144K)
 Metaspace       used 31396K, committed 31808K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 21.969 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 133120K, used 42951K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 31396K, committed 31808K, reserved 1081344K
  class space    used 4464K, committed 4672K, reserved 1048576K
}
Event: 25.023 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 133120K, used 85959K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 1 survivors (1024K)
 Metaspace       used 35736K, committed 36096K, reserved 1081344K
  class space    used 4971K, committed 5120K, reserved 1048576K
}
Event: 25.027 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 133120K, used 46306K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 35736K, committed 36096K, reserved 1081344K
  class space    used 4971K, committed 5120K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 28.045 Thread 0x000001e4fc273550 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e487c34b40 relative=0x0000000000004220
Event: 28.045 Thread 0x000001e4fc273550 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e487c34b40 method=java.lang.StringLatin1.regionMatchesCI([BI[BII)Z @ 72 c2
Event: 28.045 Thread 0x000001e4fc273550 DEOPT PACKING pc=0x000001e487c34b40 sp=0x00000002b4af9160
Event: 28.045 Thread 0x000001e4fc273550 DEOPT UNPACKING pc=0x000001e4875823a3 sp=0x00000002b4af8f18 mode 2
Event: 28.045 Thread 0x000001e4fc273550 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e487bc43c8 relative=0x0000000000000688
Event: 28.045 Thread 0x000001e4fc273550 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e487bc43c8 method=java.lang.StringLatin1.regionMatchesCI([BI[BII)Z @ 72 c2
Event: 28.045 Thread 0x000001e4fc273550 DEOPT PACKING pc=0x000001e487bc43c8 sp=0x00000002b4af8fe0
Event: 28.045 Thread 0x000001e4fc273550 DEOPT UNPACKING pc=0x000001e4875823a3 sp=0x00000002b4af8ea8 mode 2
Event: 28.129 Thread 0x000001e4fc273550 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e487d12104 relative=0x0000000000004b84
Event: 28.129 Thread 0x000001e4fc273550 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e487d12104 method=java.lang.ClassLoader.checkCerts(Ljava/lang/String;Ljava/security/CodeSource;)V @ 30 c2
Event: 28.130 Thread 0x000001e4fc273550 DEOPT PACKING pc=0x000001e487d12104 sp=0x00000002b4af8440
Event: 28.130 Thread 0x000001e4fc273550 DEOPT UNPACKING pc=0x000001e4875823a3 sp=0x00000002b4af8360 mode 2
Event: 28.178 Thread 0x000001e4fc273550 Uncommon trap: trap_request=0xffffffcc fr.pc=0x000001e487d68e84 relative=0x0000000000000364
Event: 28.178 Thread 0x000001e4fc273550 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001e487d68e84 method=java.util.Arrays.copyOf([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; @ 35 c2
Event: 28.178 Thread 0x000001e4fc273550 DEOPT PACKING pc=0x000001e487d68e84 sp=0x00000002b4af8560
Event: 28.178 Thread 0x000001e4fc273550 DEOPT UNPACKING pc=0x000001e4875823a3 sp=0x00000002b4af84f0 mode 2
Event: 28.215 Thread 0x000001e4fc273550 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001e487d5e2a8 relative=0x0000000000000ca8
Event: 28.215 Thread 0x000001e4fc273550 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e487d5e2a8 method=org.codehaus.groovy.reflection.ParameterTypes.getParametersTypes0()V @ 16 c2
Event: 28.215 Thread 0x000001e4fc273550 DEOPT PACKING pc=0x000001e487d5e2a8 sp=0x00000002b4af8750
Event: 28.215 Thread 0x000001e4fc273550 DEOPT UNPACKING pc=0x000001e4875823a3 sp=0x00000002b4af86d8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 28.165 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000885bf600}: org/gradle/internal/extensibility/DefaultConventionBeanInfo> (0x00000000885bf600) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.166 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000885d41c0}: org/gradle/internal/extensibility/DefaultConventionCustomizer> (0x00000000885d41c0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.172 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008841c470}: org/gradle/util/GradleVersionBeanInfo> (0x000000008841c470) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.174 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008842fef8}: org/gradle/util/GradleVersionCustomizer> (0x000000008842fef8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.179 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008846d0e8}: org/gradle/util/internal/DefaultGradleVersionBeanInfo> (0x000000008846d0e8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.180 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088481d28}: org/gradle/util/internal/DefaultGradleVersionCustomizer> (0x0000000088481d28) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.186 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000884ddd70}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilderBeanInfo> (0x00000000884ddd70) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.187 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000884e7f80}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilderCustomizer> (0x00000000884e7f80) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.193 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088212138}: java/lang/StringBeanInfo> (0x0000000088212138) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.193 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008821a408}: java/lang/StringCustomizer> (0x000000008821a408) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.203 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008828fe10}: org/gradle/internal/classloader/VisitableURLClassLoaderBeanInfo> (0x000000008828fe10) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.203 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x0000000088298130}: java/net/URLClassLoaderBeanInfo> (0x0000000088298130) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.204 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882a0748}: java/security/SecureClassLoaderBeanInfo> (0x00000000882a0748) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.204 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882a8d10}: java/lang/ClassLoaderBeanInfo> (0x00000000882a8d10) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.204 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882b12a8}: java/lang/ClassLoaderCustomizer> (0x00000000882b12a8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.206 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882c6b40}: java/security/SecureClassLoaderCustomizer> (0x00000000882c6b40) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.206 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882d1168}: java/net/URLClassLoaderCustomizer> (0x00000000882d1168) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.208 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000882ebd08}: org/gradle/internal/classloader/VisitableURLClassLoaderCustomizer> (0x00000000882ebd08) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.213 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x00000000881225c8}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilder$ForGradle44BeanInfo> (0x00000000881225c8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 28.213 Thread 0x000001e4fc273550 Exception <a 'java/lang/ClassNotFoundException'{0x000000008812d5c8}: org/jetbrains/plugins/gradle/tooling/internal/ExtraModelBuilder$ForGradle44Customizer> (0x000000008812d5c8) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 24.681 Executing VM operation: HandshakeAllThreads
Event: 24.681 Executing VM operation: HandshakeAllThreads done
Event: 24.713 Executing VM operation: HandshakeAllThreads
Event: 24.713 Executing VM operation: HandshakeAllThreads done
Event: 24.715 Executing VM operation: HandshakeAllThreads
Event: 24.715 Executing VM operation: HandshakeAllThreads done
Event: 25.023 Executing VM operation: CollectForMetadataAllocation
Event: 25.027 Executing VM operation: CollectForMetadataAllocation done
Event: 25.050 Executing VM operation: G1Concurrent
Event: 25.052 Executing VM operation: G1Concurrent done
Event: 25.060 Executing VM operation: G1Concurrent
Event: 25.060 Executing VM operation: G1Concurrent done
Event: 26.062 Executing VM operation: Cleanup
Event: 26.062 Executing VM operation: Cleanup done
Event: 27.076 Executing VM operation: Cleanup
Event: 27.076 Executing VM operation: Cleanup done
Event: 28.091 Executing VM operation: Cleanup
Event: 28.092 Executing VM operation: Cleanup done
Event: 28.146 Executing VM operation: HandshakeAllThreads
Event: 28.146 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 28.203 loading class java/security/SecureClassLoaderBeanInfo
Event: 28.203 loading class java/security/SecureClassLoaderBeanInfo done
Event: 28.204 loading class java/security/SecureClassLoaderBeanInfo
Event: 28.204 loading class java/security/SecureClassLoaderBeanInfo done
Event: 28.204 loading class java/lang/ClassLoaderBeanInfo
Event: 28.204 loading class java/lang/ClassLoaderBeanInfo done
Event: 28.204 loading class java/lang/ClassLoaderBeanInfo
Event: 28.204 loading class java/lang/ClassLoaderBeanInfo done
Event: 28.204 loading class java/lang/ClassLoaderCustomizer
Event: 28.204 loading class java/lang/ClassLoaderCustomizer done
Event: 28.204 loading class java/lang/ClassLoaderCustomizer
Event: 28.204 loading class java/lang/ClassLoaderCustomizer done
Event: 28.205 loading class java/security/SecureClassLoaderCustomizer
Event: 28.205 loading class java/security/SecureClassLoaderCustomizer done
Event: 28.206 loading class java/security/SecureClassLoaderCustomizer
Event: 28.206 loading class java/security/SecureClassLoaderCustomizer done
Event: 28.206 loading class java/net/URLClassLoaderCustomizer
Event: 28.206 loading class java/net/URLClassLoaderCustomizer done
Event: 28.206 loading class java/net/URLClassLoaderCustomizer
Event: 28.206 loading class java/net/URLClassLoaderCustomizer done


Dynamic libraries:
0x00007ff7693d0000 - 0x00007ff7693e0000 	C:\Program Files\Java\jdk-17\bin\java.exe
0x00007ff99a770000 - 0x00007ff99a986000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff952e20000 - 0x00007ff952e39000 	C:\Program Files\Avast Software\Avast\aswhook.dll
0x00007ff999bb0000 - 0x00007ff999c74000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff997ff0000 - 0x00007ff998397000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff997b00000 - 0x00007ff997c11000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff967000000 - 0x00007ff967019000 	C:\Program Files\Java\jdk-17\bin\jli.dll
0x00007ff9984c0000 - 0x00007ff998572000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff999b00000 - 0x00007ff999ba7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff998630000 - 0x00007ff9986d8000 	C:\WINDOWS\System32\sechost.dll
0x00007ff998470000 - 0x00007ff998498000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff99a330000 - 0x00007ff99a445000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff999550000 - 0x00007ff9996fe000 	C:\WINDOWS\System32\USER32.dll
0x00007ff9983a0000 - 0x00007ff9983c6000 	C:\WINDOWS\System32\win32u.dll
0x00007ff999d00000 - 0x00007ff999d29000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff997c20000 - 0x00007ff997d39000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff9983d0000 - 0x00007ff99846a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff97dec0000 - 0x00007ff97dedb000 	C:\Program Files\Java\jdk-17\bin\VCRUNTIME140.dll
0x00007ff95b3f0000 - 0x00007ff95b683000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ff98bea0000 - 0x00007ff98beaa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff998980000 - 0x00007ff9989b1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff97ddb0000 - 0x00007ff97ddbc000 	C:\Program Files\Java\jdk-17\bin\vcruntime140_1.dll
0x00007ff94dc40000 - 0x00007ff94dcce000 	C:\Program Files\Java\jdk-17\bin\msvcp140.dll
0x00007ff92fdf0000 - 0x00007ff9309cd000 	C:\Program Files\Java\jdk-17\bin\server\jvm.dll
0x00007ff99a5b0000 - 0x00007ff99a5b8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff980390000 - 0x00007ff980399000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff999320000 - 0x00007ff999391000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff98c7e0000 - 0x00007ff98c814000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff996bc0000 - 0x00007ff996bd8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff97d2a0000 - 0x00007ff97d2aa000 	C:\Program Files\Java\jdk-17\bin\jimage.dll
0x00007ff994f40000 - 0x00007ff995173000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff999770000 - 0x00007ff999af8000 	C:\WINDOWS\System32\combase.dll
0x00007ff9988a0000 - 0x00007ff998977000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff9891d0000 - 0x00007ff989202000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff997f70000 - 0x00007ff997fe9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff95fcc0000 - 0x00007ff95fce5000 	C:\Program Files\Java\jdk-17\bin\java.dll
0x00007ff92fd10000 - 0x00007ff92fde7000 	C:\Program Files\Java\jdk-17\bin\jsvml.dll
0x00007ff998a70000 - 0x00007ff9992cc000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff995a00000 - 0x00007ff9962f9000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff9958c0000 - 0x00007ff9959fe000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ff99a1b0000 - 0x00007ff99a2a3000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff99a2d0000 - 0x00007ff99a32e000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff9979c0000 - 0x00007ff9979e1000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff97c1d0000 - 0x00007ff97c1e9000 	C:\Program Files\Java\jdk-17\bin\net.dll
0x00007ff98df80000 - 0x00007ff98e0b6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff997030000 - 0x00007ff997099000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff97ad20000 - 0x00007ff97ad36000 	C:\Program Files\Java\jdk-17\bin\nio.dll
0x00007ff97acc0000 - 0x00007ff97acd8000 	C:\Program Files\Java\jdk-17\bin\zip.dll
0x00007ff97d290000 - 0x00007ff97d2a0000 	C:\Program Files\Java\jdk-17\bin\verify.dll
0x00007ff988db0000 - 0x00007ff988dd7000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007ff974b00000 - 0x00007ff974c44000 	C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
0x00007ff966de0000 - 0x00007ff966dea000 	C:\Program Files\Java\jdk-17\bin\management.dll
0x00007ff966dd0000 - 0x00007ff966ddb000 	C:\Program Files\Java\jdk-17\bin\management_ext.dll
0x00007ff997290000 - 0x00007ff9972ab000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff996b20000 - 0x00007ff996b55000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff997130000 - 0x00007ff997158000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff9972b0000 - 0x00007ff9972bc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff996550000 - 0x00007ff99657d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff998a60000 - 0x00007ff998a69000 	C:\WINDOWS\System32\NSI.dll
0x00007ff98de90000 - 0x00007ff98dea9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff98de70000 - 0x00007ff98de8f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff996580000 - 0x00007ff996679000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff966db0000 - 0x00007ff966dbd000 	C:\Program Files\Java\jdk-17\bin\sunmscapi.dll
0x00007ff997e00000 - 0x00007ff997f67000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff997430000 - 0x00007ff99745e000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff9973f0000 - 0x00007ff997427000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff98c180000 - 0x00007ff98c188000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\Program Files\Avast Software\Avast;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;C:\Program Files\Java\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64;C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.0
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.0-bin\ca5e32bp14vu59qr306oxotwh\gradle-8.0\lib\gradle-launcher-8.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=C:\Python310\Scripts\;C:\Python310\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\wamp64\bin\php\php7.3.33;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\MySQL\MySQL Utilities 1.6\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\DTS\Binn\;C:\cygwin64\bin;C:\protoc-24.3-win64\bin;C:\apache-ant-1.10.14\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\110\Tools\Binn\;C:\Program Files (x86)\Microsoft SQL Server\150\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\src\sdks\flutter\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Program Files\JetBrains\PhpStorm 2022.1.4\bin;;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\ArcGIS\License10.3\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Java\jdk-17\bin;
USERNAME=Mohamed Gamal
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 2, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3374)
OS uptime: 5 days 2:41 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 165 stepping 2 microcode 0xf8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 16159M (624M free)
TotalPageFile size 55003M (AvailPageFile size 343M)
current process WorkingSet (physical memory assigned to process): 271M, peak: 273M
current process commit charge ("private bytes"): 317M, peak: 424M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.7+8-LTS-224) for windows-amd64 JRE (17.0.7+8-LTS-224), built on Feb 28 2023 23:03:02 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
