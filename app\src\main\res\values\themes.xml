<resources>
    <!-- Base application theme. -->
    <style name="Theme.OpticalSmartReader" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorAccent">@color/primary_light</item>
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_light</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimary</item>
        <!-- Customize your theme here. -->

        <item name="android:editTextColor">@color/on_primary</item>
        <item name="android:textColorHint">@color/on_primary</item>
        <item name="colorControlHighlight">@color/primary_light</item>
        <item name="android:alertDialogTheme">@style/AlertDialogCustom</item>
    </style>

    <style name="Theme.checkBoxTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorOnSurface">@color/on_primary</item>
        <item name="colorSecondary">@color/on_primary</item>
    </style>

    <style name="Theme.OpticalSmartReader.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@color/primary</item>
    </style>

    <style name="Theme.OpticalSmartReader.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.OpticalSmartReader.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="AlertDialogCustom" parent="android:Theme.Material.Light.Dialog.Alert">
        <item name="android:buttonBarNegativeButtonStyle">@style/NegativeButtonStyle</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/PositiveButtonStyle</item>
        <item name="android:buttonBarNeutralButtonStyle">@style/NeutralButtonStyle</item>
    </style>

    <style name="NegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/btn_color</item>
    </style>

    <style name="PositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/btn_color</item>
    </style>

    <style name="NeutralButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/btn_color</item>
    </style>
</resources>