<component name="libraryTable">
  <library name="Gradle: androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/64af80d62d70d5bbaaff8a6818f2b95e/transformed/coordinatorlayout-1.1.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/64af80d62d70d5bbaaff8a6818f2b95e/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/64af80d62d70d5bbaaff8a6818f2b95e/transformed/coordinatorlayout-1.1.0/res" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/64af80d62d70d5bbaaff8a6818f2b95e/transformed/coordinatorlayout-1.1.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.coordinatorlayout/coordinatorlayout/1.1.0/a15529ac349d76a872ae5ef42b84c320c456cd7f/coordinatorlayout-1.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>