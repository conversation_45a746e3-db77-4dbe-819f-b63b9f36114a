package shoaa.globalreader;


public class MeterData {
    public String meterModel = "0";
    public float Deon;
    public float maxLoadKW;
    public String meterID;
    public String customerID;
    public String totalConsum;
    public String remainKW;
    public String remainMoney;
    public String installingDate;
    public String technicalCode;
    public String meterStatus;
    public int relayStatus;
    public int batteryStatus;
    public String tempresList;
    public int unblanceInKW;
    public int reverse;
    public String lastTechnicalCode;
    public String clearAllTampers;
    public String clearTampers;
    public String setTimeAndDate;
    public String controlCardState;
    public String fWVersion;
    public float maxDemandPH1;
    public float maxDemandPH2;
    public float maxDemandPH3;
    public String maximumDemandDate;
    public String instanteneousVolt2;
    public String instanteneousVolt3;
    public String instanteneousVolt;
    public float powerFactor;
    public float lastYearPowerFactor;
    public int totalConsumptionKvh;
    public int currentMonthConsumption;
    public float currentMonthConsumptionMoney;
    public int consm_kvar1;
    public int consm_kvar2;
    public int consm_kvar3;
    public int consm_kvar4;
    public int consm_kvar5;
    public int consm_kvar6;
    public int consm_kvar7;
    public int consm_kvar8;
    public int consm_kvar9;
    public int consm_kvar10;
    public int consm_kvar11;
    public int consm_kvar12;
    public String date;
    public int chargeCount;
    public Long SumOfAllChargesPND;
    public int SumOfAllChargesPSTR;
    public String LastChargeDate;
    public String TotalBalance;
    public String ActivityType;
    public String tarrifID;
    public String tarrifActivationDate;
    public int CurrentMonthlyConsum;
    public int SliceNo;
    public float Moneyconsm01;
    public float Moneyconsm02;
    public float Moneyconsm03;
    public float Moneyconsm04;
    public float Moneyconsm05;
    public float Moneyconsm06;
    public float Moneyconsm07;
    public float Moneyconsm08;
    public float Moneyconsm09;
    public float Moneyconsm10;
    public float Moneyconsm11;
    public float Moneyconsm12;
    public int consm1;
    public int consm2;
    public int consm3;
    public int consm4;
    public int consm5;
    public int consm6;
    public int consm7;
    public int consm8;
    public int consm9;
    public int consm10;
    public int consm11;
    public int consm12;
    public float maxim_demand_month_1;
    public float maxim_demand_month_2;
    public float maxim_demand_month_3;
    public float maxim_demand_month_4;
    public float maxim_demand_month_5;
    public float maxim_demand_month_6;
    public float maxim_demand_month_7;
    public float maxim_demand_month_8;
    public float maxim_demand_month_9;
    public float maxim_demand_month_10;
    public float maxim_demand_month_11;
    public float maxim_demand_month_12;
    public String maxim_demand_month_date_1;
    public String maxim_demand_month_date_2;
    public String maxim_demand_month_date_3;
    public String maxim_demand_month_date_4;
    public String maxim_demand_month_date_5;
    public String maxim_demand_month_date_6;
    public String maxim_demand_month_date_7;
    public String maxim_demand_month_date_8;
    public String maxim_demand_month_date_9;
    public String maxim_demand_month_date_10;
    public String maxim_demand_month_date_11;
    public String maxim_demand_month_date_12;
    public int meterPhase;

    public MeterData() {
    }
}
