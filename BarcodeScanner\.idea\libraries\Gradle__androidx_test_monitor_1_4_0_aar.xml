<component name="libraryTable">
  <library name="Gradle: androidx.test:monitor:1.4.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/471c1ae8828f3e10bd8d84b57841c078/transformed/monitor-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b57be9d3b5ab8db44b1c378421023987/transformed/monitor-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dd1b814601a8a9abeba05532b8f54d90/transformed/monitor-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/93cd294dd4eadc0d6effd17711f1a80c/transformed/monitor-1.4.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/dd1b814601a8a9abeba05532b8f54d90/transformed/monitor-1.4.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/dd1b814601a8a9abeba05532b8f54d90/transformed/monitor-1.4.0/jars/classes.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test/monitor/1.4.0/2770e38fa6d39242c2fa2e9d2ca3275b1d9debd8/monitor-1.4.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.test/monitor/1.4.0/5d892d39aae695079e3ecc7a841336ff3aeaf40/monitor-1.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>