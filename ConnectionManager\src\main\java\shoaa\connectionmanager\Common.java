package shoaa.connectionmanager;

public class Common {
    public static int TIME_OUT_200 = 200;
    public static int TIME_OUT_300 = 300;
    public static int TIME_OUT_500 = 500;
    public static int TIME_OUT_800 = 800;
    public static int TIME_OUT_1000 = 1000;
    public static int TIME_OUT_1200 = 1200;
    public static int TIME_OUT_1500 = 1500;
    public static int TIME_OUT_2000 = 2000;
    public static int TIME_OUT_2500 = 2500;
    public static boolean CALCULATE_SEND_TIME = true;
    public static boolean ENABLE_GLOBAL_VERSION_SELECTION = true;
}
