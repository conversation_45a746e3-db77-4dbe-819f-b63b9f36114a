package shoaa.opticalsmartreader.logic.getAllClients;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import shoaa.opticalsmartreader.models.Client;

@Dao
public interface ClientDao {
    @Query("SELECT * FROM clients")
    List<Client> getAll();

    @Query("SELECT * FROM clients WHERE BrHndsa LIKE :handasa ")
    List<Client> getAllHandasa(int handasa);

    @Query("SELECT * FROM clients WHERE (BrHndsa LIKE :handasa AND Mntka LIKE :manteka) ")
    List<Client> getAllHandasaManteka(int handasa, String manteka);

    @Query("SELECT * FROM clients WHERE (BrHndsa LIKE :handasa AND Mntka LIKE :manteka AND Day LIKE :yomeya) ")
    List<Client> getAllHandasaMantekaYomeya(int handasa, String manteka, String yomeya);

    @Query("SELECT * FROM clients WHERE MeterID IN (:meterID)")
    List<Client> loadAllByIds(long[] meterID);

    @Query("SELECT * FROM clients WHERE Address LIKE :address  LIMIT 1")
    Client findByAddress(String address);

    @Query("SELECT * FROM clients WHERE MeterID LIKE :barcode AND FactoryCode LIKE :factoryCode LIMIT 1")
    Client find(long barcode, int factoryCode);


    @Query("SELECT * FROM clients WHERE  CAST(MeterID AS String) LIKE '%' || :query || '%'  " +
            "OR CustomerMobile LIKE '%' || :query || '%' " +
            "OR BrHndsa LIKE '%' || :query || '%' " +
            "OR Name LIKE '%' || :query || '%' " +
            "OR BrHndsa LIKE '%' || :query || '%' " +
            "OR Mntka LIKE '%' || :query || '%' " +
            "OR Day LIKE '%' || :query || '%' " +
            "OR Main LIKE '%' || :query || '%' " +
            "OR Fary LIKE '%' || :query || '%' " +
            "OR ActivityName LIKE '%' || :query || '%' " +
            "OR PlaceName LIKE '%' || :query || '%' " +
            "OR CustomerID LIKE '%' || :query || '%' " +
            "OR CardID LIKE '%' || :query || '%' " +
            "OR Address LIKE '%' || :query || '%'")
    List<Client> findAny(String query);

    @Insert
    void insertAll(Client... clients);

    @Delete
    void delete(Client client);

    @Query("DELETE FROM clients")
    void deleteAll();

    @Update
    void updateClients(Client... clients);
}