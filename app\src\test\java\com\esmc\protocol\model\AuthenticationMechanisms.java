package com.esmc.protocol.model;

/* loaded from: EsmcProtocol.jar:com/esmc/protocol/model/AuthenticationMechanisms.class */
public enum AuthenticationMechanisms {
    NLS(0),
    LLS(1),
    HLS2(2),
    HLS3(3),
    HLS4(4),
    HLS5(5);

    private int mechanismId;

    AuthenticationMechanisms(int mechanismId) {
        this.mechanismId = mechanismId;
    }

    public byte[] getMechanismName() {
        return new byte[]{96, -123, 116, 5, 8, 2, (byte) this.mechanismId};
    }
}
