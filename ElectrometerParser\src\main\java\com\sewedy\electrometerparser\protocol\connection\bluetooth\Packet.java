package com.sewedy.electrometerparser.protocol.connection.bluetooth;

public class Packet {
    byte dataLength;
    byte startingByte = (byte) 0x02;
    String data;
    byte[] closerPacket = new byte[]{(byte) 0x21, (byte) 0x0D, (byte) 0x0A, (byte) 0x03};
    byte CRC;

    public byte getDataLength() {
        return dataLength;
    }

    public void setDataLength(byte dataLength) {
        this.dataLength = dataLength;
    }

    public byte getStartingByte() {
        return startingByte;
    }

    public void setStartingByte(byte startingByte) {
        this.startingByte = startingByte;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public byte[] getCloserPacket() {
        return closerPacket;
    }

    public void setCloserPacket(byte[] closerPacket) {
        this.closerPacket = closerPacket;
    }

    public byte getCRC() {
        return CRC;
    }

    public void setCRC(byte CRC) {
        this.CRC = CRC;
    }
}
